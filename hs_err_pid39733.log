#
# A fatal error has been detected by the Java Runtime Environment:
#
#  Internal Error (sharedRuntime.cpp:549), pid=39733, tid=0x000000000000ed03
#  guarantee(cb != NULL && cb->is_nmethod()) failed: safepoint polling: pc must refer to an nmethod
#
# JRE version: Java(TM) SE Runtime Environment (8.0_221-b11) (build 1.8.0_221-b11)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.221-b11 mixed mode bsd-amd64 compressed oops)
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   http://bugreport.java.com/bugreport/crash.jsp
#

---------------  T H R E A D  ---------------

Current thread (0x00007fc11c7f4800):  JavaThread "redisson-netty-3-10" [_thread_in_Java, id=60675, stack(0x000000030b974000,0x000000030ba74000)]

Stack: [0x000000030b974000,0x000000030ba74000],  sp=0x000000030ba70e50,  free space=1011k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [libjvm.dylib+0x5b6994]  VMError::report_and_die()+0x3f8
V  [libjvm.dylib+0x1e9bdd]  report_vm_error(char const*, int, char const*, char const*)+0x54
V  [libjvm.dylib+0x4ede8a]  SharedRuntime::get_poll_stub(unsigned char*)+0x46
V  [libjvm.dylib+0x490721]  JVM_handle_bsd_signal+0x4a6
V  [libjvm.dylib+0x48c92b]  signalHandler(int, __siginfo*, void*)+0x2f
C  [libasyncProfiler.dylib+0x461ff]  Profiler::segvHandler(int, __siginfo*, void*)+0x9f
C  [libsystem_platform.dylib+0x3fdd]  _sigtramp+0x1d
C  0x000000072f67d6c0
j  io.netty.buffer.PooledByteBuf.internalNioBuffer(II)Ljava/nio/ByteBuffer;+3
j  io.netty.buffer.PooledByteBuf.setBytes(ILjava/nio/channels/ScatteringByteChannel;I)I+4
j  io.netty.buffer.AbstractByteBuf.writeBytes(Ljava/nio/channels/ScatteringByteChannel;I)I+13
j  io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(Lio/netty/buffer/ByteBuf;)I+31
j  io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read()V+80
j  io.netty.channel.nio.NioEventLoop.processSelectedKey(Ljava/nio/channels/SelectionKey;Lio/netty/channel/nio/AbstractNioChannel;)V+113
j  io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized()V+51
j  io.netty.channel.nio.NioEventLoop.processSelectedKeys()V+8
j  io.netty.channel.nio.NioEventLoop.run()V+263
j  io.netty.util.concurrent.SingleThreadEventExecutor$4.run()V+44
j  io.netty.util.internal.ThreadExecutorMap$2.run()V+11
j  io.netty.util.concurrent.FastThreadLocalRunnable.run()V+4
j  java.lang.Thread.run()V+11
v  ~StubRoutines::call_stub
V  [libjvm.dylib+0x2f0d1e]  JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*)+0x6ae
V  [libjvm.dylib+0x2f14c2]  JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*)+0x164
V  [libjvm.dylib+0x2f166e]  JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*)+0x4a
V  [libjvm.dylib+0x34bf5d]  thread_entry(JavaThread*, Thread*)+0x7c
V  [libjvm.dylib+0x570b93]  JavaThread::thread_main_inner()+0x9b
V  [libjvm.dylib+0x57228e]  JavaThread::run()+0x1c2
V  [libjvm.dylib+0x48f30a]  java_start(Thread*)+0xf6
C  [libsystem_pthread.dylib+0x618b]  _pthread_start+0x63
C  [libsystem_pthread.dylib+0x1ae3]  thread_start+0xf
C  0x0000000000000000


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x00007fc11b826000 JavaThread "redisson-netty-3-14" [_thread_blocked, id=53879, stack(0x000000030be83000,0x000000030bf83000)]
  0x00007fc11f023800 JavaThread "redisson-timer-5-1" [_thread_blocked, id=58011, stack(0x000000030bd80000,0x000000030be80000)]
  0x00007fc11b825000 JavaThread "redisson-netty-3-13" [_thread_in_Java, id=59395, stack(0x000000030bc7d000,0x000000030bd7d000)]
  0x00007fc11bfab800 JavaThread "redisson-netty-3-12" [_thread_in_vm, id=60163, stack(0x000000030bb7a000,0x000000030bc7a000)]
  0x00007fc11bfab000 JavaThread "redisson-netty-3-11" [_thread_in_Java, id=46123, stack(0x000000030ba77000,0x000000030bb77000)]
=>0x00007fc11c7f4800 JavaThread "redisson-netty-3-10" [_thread_in_Java, id=60675, stack(0x000000030b974000,0x000000030ba74000)]
  0x00007fc11b824800 JavaThread "redisson-netty-3-9" [_thread_in_vm, id=60931, stack(0x000000030b871000,0x000000030b971000)]
  0x00007fc11b823800 JavaThread "redisson-netty-3-8" [_thread_in_Java, id=48131, stack(0x000000030b76e000,0x000000030b86e000)]
  0x00007fc11b823000 JavaThread "redisson-netty-3-7" [_thread_blocked, id=61699, stack(0x000000030b66b000,0x000000030b76b000)]
  0x00007fc11bc07000 JavaThread "redisson-netty-3-6" [_thread_in_vm, id=47875, stack(0x000000030b568000,0x000000030b668000)]
  0x00007fc11bf9b000 JavaThread "redisson-netty-3-5" [_thread_in_vm, id=47619, stack(0x000000030b465000,0x000000030b565000)]
  0x00007fc14cba4000 JavaThread "redisson-netty-3-4" [_thread_in_vm, id=47363, stack(0x000000030b362000,0x000000030b462000)]
  0x00007fc11bbff800 JavaThread "redisson-netty-3-3" [_thread_in_vm, id=63227, stack(0x000000030b25f000,0x000000030b35f000)]
  0x00007fc11bbfe800 JavaThread "redisson-netty-3-2" [_thread_blocked, id=45999, stack(0x000000030b15c000,0x000000030b25c000)]
  0x00007fc11bf9a800 JavaThread "redisson-netty-3-1" [_thread_blocked, id=64859, stack(0x000000030b059000,0x000000030b159000)]
  0x00007fc14b9e1800 JavaThread "commons-pool-evictor-thread" daemon [_thread_blocked, id=44715, stack(0x000000030af56000,0x000000030b056000)]
  0x00007fc12b34e000 JavaThread "kafka-producer-network-thread | producer-2" daemon [_thread_in_vm, id=45379, stack(0x000000030ae53000,0x000000030af53000)]
  0x00007fc12b34a000 JavaThread "kafka-producer-network-thread | producer-1" daemon [_thread_in_vm, id=64267, stack(0x000000030ad50000,0x000000030ae50000)]
  0x00007fc11bf5e000 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=43871, stack(0x000000030ac4d000,0x000000030ad4d000)]
  0x00007fc14b419000 JavaThread "Apollo-SpringValueRegistry-1" daemon [_thread_blocked, id=65283, stack(0x000000030ab4a000,0x000000030ac4a000)]
  0x00007fc12b078800 JavaThread "container-0" [_thread_blocked, id=35863, stack(0x000000030aa47000,0x000000030ab47000)]
  0x00007fc14bc50000 JavaThread "Catalina-utility-2" [_thread_blocked, id=36471, stack(0x000000030a944000,0x000000030aa44000)]
  0x00007fc14bc71800 JavaThread "Catalina-utility-1" [_thread_blocked, id=19279, stack(0x000000030a841000,0x000000030a941000)]
  0x00007fc11bbe1000 JavaThread "Apollo-RemoteConfigLongPollService-1" daemon [_thread_in_native, id=38167, stack(0x000000030a73e000,0x000000030a83e000)]
  0x00007fc14bdcc800 JavaThread "Apollo-RemoteConfigRepository-1" daemon [_thread_blocked, id=35439, stack(0x000000030a63b000,0x000000030a73b000)]
  0x00007fc14b3c7800 JavaThread "Apollo-ConfigServiceLocator-1" daemon [_thread_blocked, id=38735, stack(0x000000030a538000,0x000000030a638000)]
  0x00007fc14bda7000 JavaThread "Keep-Alive-Timer" daemon [_thread_blocked, id=34307, stack(0x000000030a435000,0x000000030a535000)]
  0x00007fc14bd2e800 JavaThread "vault-nio-worker-ELG-1-1" daemon [_thread_blocked, id=34691, stack(0x000000030a332000,0x000000030a432000)]
  0x00007fc14cc6e800 JavaThread "OkHttp TaskRunner" daemon [_thread_blocked, id=41339, stack(0x000000030a22f000,0x000000030a32f000)]
  0x00007fc11b83e000 JavaThread "Okio Watchdog" daemon [_thread_blocked, id=33359, stack(0x000000030a12c000,0x000000030a22c000)]
  0x00007fc14bcaf000 JavaThread "vault-default-executor-0" daemon [_thread_blocked, id=33835, stack(0x000000030a029000,0x000000030a129000)]
  0x00007fc12b14e800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=42015, stack(0x0000000309f26000,0x000000030a026000)]
  0x00007fc12b14a000 JavaThread "RMI TCP Connection(1)-127.0.0.1" daemon [_thread_in_native, id=42819, stack(0x0000000309e23000,0x0000000309f23000)]
  0x00007fc11bcb2800 JavaThread "Attach Listener" daemon [_thread_blocked, id=43299, stack(0x0000000309d20000,0x0000000309e20000)]
  0x00007fc14b1f4800 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=27579, stack(0x0000000309b1a000,0x0000000309c1a000)]
  0x00007fc11bc60800 JavaThread "Service Thread" daemon [_thread_blocked, id=28427, stack(0x0000000309a17000,0x0000000309b17000)]
  0x00007fc11bc55800 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=24323, stack(0x0000000309914000,0x0000000309a14000)]
  0x00007fc11bc54800 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=29955, stack(0x0000000309811000,0x0000000309911000)]
  0x00007fc11bc54000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=30211, stack(0x000000030970e000,0x000000030980e000)]
  0x00007fc11bc53000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=30723, stack(0x000000030960b000,0x000000030970b000)]
  0x00007fc14b1c6800 JavaThread "Async-profiler Timer" daemon [_thread_in_native, id=31239, stack(0x0000000309508000,0x0000000309608000)]
  0x00007fc11bc44000 JavaThread "Monitor Ctrl-Break" daemon [_thread_in_native, id=22083, stack(0x0000000309382000,0x0000000309482000)]
  0x00007fc14cb11000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=18179, stack(0x000000030927f000,0x000000030937f000)]
  0x00007fc14b9b1800 JavaThread "Finalizer" daemon [_thread_blocked, id=21023, stack(0x000000030906b000,0x000000030916b000)]
  0x00007fc14c809800 JavaThread "Reference Handler" daemon [_thread_blocked, id=21539, stack(0x0000000308f68000,0x0000000309068000)]
  0x00007fc14b964800 JavaThread "main" [_thread_blocked, id=8707, stack(0x000000030854a000,0x000000030864a000)]

Other Threads:
  0x00007fc14b9af000 VMThread [stack: 0x0000000308e65000,0x0000000308f65000] [id=13315]
  0x00007fc14b201000 WatcherThread [stack: 0x0000000309c1d000,0x0000000309d1d000] [id=26723]

VM state:synchronizing (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000060000380a000] Safepoint_lock - owner thread: 0x00007fc14b9af000
[0x000060000380a080] Threads_lock - owner thread: 0x00007fc14b9af000

heap address: 0x00000006c0000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 791552K, used 610662K [0x000000076ab00000, 0x000000079d280000, 0x00000007c0000000)
  eden space 771584K, 79% used [0x000000076ab00000,0x000000078ff59b68,0x0000000799c80000)
  from space 19968K, 0% used [0x000000079bf00000,0x000000079bf00000,0x000000079d280000)
  to   space 24064K, 0% used [0x000000079a380000,0x000000079a380000,0x000000079bb00000)
 ParOldGen       total 153600K, used 32278K [0x00000006c0000000, 0x00000006c9600000, 0x000000076ab00000)
  object space 153600K, 21% used [0x00000006c0000000,0x00000006c1f85b98,0x00000006c9600000)
 Metaspace       used 69989K, capacity 73230K, committed 73560K, reserved 1114112K
  class space    used 8637K, capacity 9269K, committed 9344K, reserved 1048576K

Card table byte_map: [0x000000011ce3b000,0x000000011d63c000] byte_map_base: 0x000000011983b000

Marking Bits: (ParMarkBitMap*) 0x000000010c4bfe40
 Begin Bits: [0x000000011db92000, 0x0000000121b92000)
 End Bits:   [0x0000000121b92000, 0x0000000125b92000)

Polling page: 0x000000010218f000

CodeCache: size=245760Kb used=15343Kb max_used=15343Kb free=230416Kb
 bounds [0x000000010da7b000, 0x000000010e97b000, 0x000000011ca7b000]
 total_blobs=7642 nmethods=6873 adapters=682
 compilation: enabled

Compilation events (10 events):
Event: 12.709 Thread 0x00007fc11bc55800 6972       1       io.netty.buffer.AbstractByteBuf::checkIndex0 (18 bytes)
Event: 12.709 Thread 0x00007fc11bc55800 nmethod 6972 0x000000010e9751d0 code [0x000000010e975340, 0x000000010e9754b8]
Event: 12.709 Thread 0x00007fc11bc55800 6973       1       sun.nio.ch.SelectionKeyImpl::ensureValid (16 bytes)
Event: 12.709 Thread 0x00007fc11bc55800 nmethod 6973 0x000000010e975590 code [0x000000010e975700, 0x000000010e975888]
Event: 12.792 Thread 0x00007fc11bc55800 6974       1       org.apache.kafka.clients.NetworkClient::ensureActive (38 bytes)
Event: 12.793 Thread 0x00007fc11bc55800 nmethod 6974 0x000000010e9759d0 code [0x000000010e975b80, 0x000000010e975fc8]
Event: 12.795 Thread 0x00007fc11bc55800 6975       1       io.netty.buffer.AbstractByteBuf::checkRangeBounds (52 bytes)
Event: 12.796 Thread 0x00007fc11bc55800 nmethod 6975 0x000000010e976890 code [0x000000010e976aa0, 0x000000010e9773f8]
Event: 12.802 Thread 0x00007fc11bc55800 6977       1       io.netty.channel.AbstractChannel::pipeline (5 bytes)
Event: 12.803 Thread 0x00007fc11bc55800 nmethod 6977 0x000000010e977d50 code [0x000000010e977ea0, 0x000000010e977fb0]

GC Heap History (10 events):
Event: 4.278 GC heap before
{Heap before GC invocations=7 (full 1):
 PSYoungGen      total 272896K, used 255040K [0x000000076ab00000, 0x0000000788f80000, 0x00000007c0000000)
  eden space 262144K, 93% used [0x000000076ab00000,0x0000000779996fe0,0x000000077ab00000)
  from space 10752K, 99% used [0x000000077ab00000,0x000000077b5792a0,0x000000077b580000)
  to   space 12800K, 0% used [0x0000000788300000,0x0000000788300000,0x0000000788f80000)
 ParOldGen       total 83968K, used 8678K [0x00000006c0000000, 0x00000006c5200000, 0x000000076ab00000)
  object space 83968K, 10% used [0x00000006c0000000,0x00000006c0879a68,0x00000006c5200000)
 Metaspace       used 33972K, capacity 35364K, committed 35456K, reserved 1079296K
  class space    used 4410K, capacity 4706K, committed 4736K, reserved 1048576K
Event: 4.291 GC heap after
Heap after GC invocations=7 (full 1):
 PSYoungGen      total 474112K, used 12790K [0x000000076ab00000, 0x0000000788f80000, 0x00000007c0000000)
  eden space 461312K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000786d80000)
  from space 12800K, 99% used [0x0000000788300000,0x0000000788f7db98,0x0000000788f80000)
  to   space 16384K, 0% used [0x0000000786f80000,0x0000000786f80000,0x0000000787f80000)
 ParOldGen       total 83968K, used 11903K [0x00000006c0000000, 0x00000006c5200000, 0x000000076ab00000)
  object space 83968K, 14% used [0x00000006c0000000,0x00000006c0b9fe20,0x00000006c5200000)
 Metaspace       used 33972K, capacity 35364K, committed 35456K, reserved 1079296K
  class space    used 4410K, capacity 4706K, committed 4736K, reserved 1048576K
}
Event: 4.291 GC heap before
{Heap before GC invocations=8 (full 2):
 PSYoungGen      total 474112K, used 12790K [0x000000076ab00000, 0x0000000788f80000, 0x00000007c0000000)
  eden space 461312K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000786d80000)
  from space 12800K, 99% used [0x0000000788300000,0x0000000788f7db98,0x0000000788f80000)
  to   space 16384K, 0% used [0x0000000786f80000,0x0000000786f80000,0x0000000787f80000)
 ParOldGen       total 83968K, used 11903K [0x00000006c0000000, 0x00000006c5200000, 0x000000076ab00000)
  object space 83968K, 14% used [0x00000006c0000000,0x00000006c0b9fe20,0x00000006c5200000)
 Metaspace       used 33972K, capacity 35364K, committed 35456K, reserved 1079296K
  class space    used 4410K, capacity 4706K, committed 4736K, reserved 1048576K
Event: 4.328 GC heap after
Heap after GC invocations=8 (full 2):
 PSYoungGen      total 474112K, used 0K [0x000000076ab00000, 0x0000000788f80000, 0x00000007c0000000)
  eden space 461312K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000786d80000)
  from space 12800K, 0% used [0x0000000788300000,0x0000000788300000,0x0000000788f80000)
  to   space 16384K, 0% used [0x0000000786f80000,0x0000000786f80000,0x0000000787f80000)
 ParOldGen       total 120320K, used 21161K [0x00000006c0000000, 0x00000006c7580000, 0x000000076ab00000)
  object space 120320K, 17% used [0x00000006c0000000,0x00000006c14aa7d8,0x00000006c7580000)
 Metaspace       used 33972K, capacity 35364K, committed 35456K, reserved 1079296K
  class space    used 4410K, capacity 4706K, committed 4736K, reserved 1048576K
}
Event: 7.938 GC heap before
{Heap before GC invocations=9 (full 2):
 PSYoungGen      total 474112K, used 461312K [0x000000076ab00000, 0x0000000788f80000, 0x00000007c0000000)
  eden space 461312K, 100% used [0x000000076ab00000,0x0000000786d80000,0x0000000786d80000)
  from space 12800K, 0% used [0x0000000788300000,0x0000000788300000,0x0000000788f80000)
  to   space 16384K, 0% used [0x0000000786f80000,0x0000000786f80000,0x0000000787f80000)
 ParOldGen       total 120320K, used 21161K [0x00000006c0000000, 0x00000006c7580000, 0x000000076ab00000)
  object space 120320K, 17% used [0x00000006c0000000,0x00000006c14aa7d8,0x00000006c7580000)
 Metaspace       used 49861K, capacity 52020K, committed 52224K, reserved 1095680K
  class space    used 6234K, capacity 6656K, committed 6656K, reserved 1048576K
Event: 7.960 GC heap after
Heap after GC invocations=9 (full 2):
 PSYoungGen      total 479744K, used 16353K [0x000000076ab00000, 0x000000079d480000, 0x00000007c0000000)
  eden space 463360K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000786f80000)
  from space 16384K, 99% used [0x0000000786f80000,0x0000000787f78558,0x0000000787f80000)
  to   space 22016K, 0% used [0x000000079bf00000,0x000000079bf00000,0x000000079d480000)
 ParOldGen       total 120320K, used 27158K [0x00000006c0000000, 0x00000006c7580000, 0x000000076ab00000)
  object space 120320K, 22% used [0x00000006c0000000,0x00000006c1a85858,0x00000006c7580000)
 Metaspace       used 49861K, capacity 52020K, committed 52224K, reserved 1095680K
  class space    used 6234K, capacity 6656K, committed 6656K, reserved 1048576K
}
Event: 9.434 GC heap before
{Heap before GC invocations=10 (full 2):
 PSYoungGen      total 479744K, used 443148K [0x000000076ab00000, 0x000000079d480000, 0x00000007c0000000)
  eden space 463360K, 92% used [0x000000076ab00000,0x0000000784bcacc0,0x0000000786f80000)
  from space 16384K, 99% used [0x0000000786f80000,0x0000000787f78558,0x0000000787f80000)
  to   space 22016K, 0% used [0x000000079bf00000,0x000000079bf00000,0x000000079d480000)
 ParOldGen       total 120320K, used 27158K [0x00000006c0000000, 0x00000006c7580000, 0x000000076ab00000)
  object space 120320K, 22% used [0x00000006c0000000,0x00000006c1a85858,0x00000006c7580000)
 Metaspace       used 56339K, capacity 59014K, committed 59096K, reserved 1101824K
  class space    used 7105K, capacity 7639K, committed 7680K, reserved 1048576K
Event: 9.458 GC heap after
Heap after GC invocations=10 (full 2):
 PSYoungGen      total 791552K, used 19869K [0x000000076ab00000, 0x000000079d280000, 0x00000007c0000000)
  eden space 771584K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000799c80000)
  from space 19968K, 99% used [0x000000079bf00000,0x000000079d267668,0x000000079d280000)
  to   space 24064K, 0% used [0x000000079a380000,0x000000079a380000,0x000000079bb00000)
 ParOldGen       total 120320K, used 27166K [0x00000006c0000000, 0x00000006c7580000, 0x000000076ab00000)
  object space 120320K, 22% used [0x00000006c0000000,0x00000006c1a87858,0x00000006c7580000)
 Metaspace       used 56339K, capacity 59014K, committed 59096K, reserved 1101824K
  class space    used 7105K, capacity 7639K, committed 7680K, reserved 1048576K
}
Event: 9.458 GC heap before
{Heap before GC invocations=11 (full 3):
 PSYoungGen      total 791552K, used 19869K [0x000000076ab00000, 0x000000079d280000, 0x00000007c0000000)
  eden space 771584K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000799c80000)
  from space 19968K, 99% used [0x000000079bf00000,0x000000079d267668,0x000000079d280000)
  to   space 24064K, 0% used [0x000000079a380000,0x000000079a380000,0x000000079bb00000)
 ParOldGen       total 120320K, used 27166K [0x00000006c0000000, 0x00000006c7580000, 0x000000076ab00000)
  object space 120320K, 22% used [0x00000006c0000000,0x00000006c1a87858,0x00000006c7580000)
 Metaspace       used 56339K, capacity 59014K, committed 59096K, reserved 1101824K
  class space    used 7105K, capacity 7639K, committed 7680K, reserved 1048576K
Event: 9.558 GC heap after
Heap after GC invocations=11 (full 3):
 PSYoungGen      total 791552K, used 0K [0x000000076ab00000, 0x000000079d280000, 0x00000007c0000000)
  eden space 771584K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000799c80000)
  from space 19968K, 0% used [0x000000079bf00000,0x000000079bf00000,0x000000079d280000)
  to   space 24064K, 0% used [0x000000079a380000,0x000000079a380000,0x000000079bb00000)
 ParOldGen       total 153600K, used 32278K [0x00000006c0000000, 0x00000006c9600000, 0x000000076ab00000)
  object space 153600K, 21% used [0x00000006c0000000,0x00000006c1f85b98,0x00000006c9600000)
 Metaspace       used 56339K, capacity 59014K, committed 59096K, reserved 1101824K
  class space    used 7105K, capacity 7639K, committed 7680K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 12.128 Thread 0x00007fc14b964800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078b6a1a08) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.128 Thread 0x00007fc14b964800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078b6a2cf8) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.131 Thread 0x00007fc14b964800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078b787bf0) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.131 Thread 0x00007fc14b964800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078b789870) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.131 Thread 0x00007fc14b964800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078b7a7be0) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.132 Thread 0x00007fc14b964800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078b7db690) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.132 Thread 0x00007fc14b964800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078b7dc9b0) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.276 Thread 0x00007fc11b824800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078e848df8) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.277 Thread 0x00007fc11b824800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078e868998) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]
Event: 12.278 Thread 0x00007fc11b825000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000078f0e47b8) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u221/13320/hotspot/src/share/vm/runtime/sharedRuntime.cpp, line 605]

Events (10 events):
Event: 12.798 loading class org/redisson/client/handler/PingConnectionHandler$1 done
Event: 12.798 Executing VM operation: RevokeBias
Event: 12.799 Executing VM operation: RevokeBias done
Event: 12.800 loading class org/redisson/connection/ClientConnectionsEntry$1
Event: 12.800 loading class org/redisson/connection/ClientConnectionsEntry$1 done
Event: 12.802 loading class org/redisson/connection/ClientConnectionsEntry$2
Event: 12.802 loading class org/redisson/connection/ClientConnectionsEntry$2 done
Event: 12.803 loading class org/redisson/connection/ConnectionEventsHub$Status
Event: 12.803 loading class org/redisson/connection/ConnectionEventsHub$Status done
Event: 12.803 Executing VM operation: RevokeBias


Dynamic libraries:
0x00007ff82a1a8000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x00007ff812012000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x00007ff81537a000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x00007ff80f93e000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x00007ff81c2d5000 	/usr/lib/libSystem.B.dylib
0x00007ff813440000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x00007ffb17e0e000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00007ff82398f000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x00007ff819bf2000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00007ff81eda9000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x00007ff81edfd000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x00007ffc19ced000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x00007ff80f5ed000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x00007ff81e122000 	/usr/lib/libspindump.dylib
0x00007ff81361d000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x00007ff817be1000 	/usr/lib/libapp_launch_measurement.dylib
0x00007ff8168c5000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00007ff817be4000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x00007ff81945e000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00007ff81a3fd000 	/usr/lib/liblangid.dylib
0x00007ff819bf7000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x00007ff813fdf000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x00007ff814440000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00007ff82405b000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00007ff81df83000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x00007ff81943f000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x00007ff8168f1000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x00007ff81c1ed000 	/usr/lib/libz.1.dylib
0x00007ff827a83000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x00007ff819bdf000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x00007ff8118c0000 	/usr/lib/libicucore.A.dylib
0x00007ff81fd4a000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00007ff81edb8000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00007ff91925b000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x00007ff813f38000 	/usr/lib/libMobileGestalt.dylib
0x00007ff819944000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x00007ff81753d000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x00007ff811512000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00007ff8239c7000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x00007ff817914000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x00007ff810ded000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x00007ff8169d1000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00007ff81e552000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00007ff813f37000 	/usr/lib/libenergytrace.dylib
0x00007ff90eb30000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x00007ff811ee9000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00007ff823d9a000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x00007ff817b79000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00007ffa27f34000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x00007ff817c2d000 	/usr/lib/libxml2.2.dylib
0x00007ff81b4c0000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x00007ff80e5d8000 	/usr/lib/libobjc.A.dylib
0x00007ff80e8da000 	/usr/lib/libc++.1.dylib
0x00007ff823d16000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x00007ff814cd4000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x00007ff80ea0d000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x00007ff819f6a000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x00007ff810be6000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00007ffa2e146000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00007ffa2e54b000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x00007ff819c30000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00007ffb127d0000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00007ff81c2da000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00007ff81f7ba000 	/usr/lib/swift/libswiftCore.dylib
0x00007ff915e3f000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00007ff913acc000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00007ff915e7d000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00007ff913ad3000 	/usr/lib/swift/libswiftDarwin.dylib
0x00007ff8256fd000 	/usr/lib/swift/libswiftDispatch.dylib
0x00007ff915e7e000 	/usr/lib/swift/libswiftIOKit.dylib
0x00007ff921e6d000 	/usr/lib/swift/libswiftMetal.dylib
0x00007ffa0f5c7000 	/usr/lib/swift/libswiftOSLog.dylib
0x00007ff827eee000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00007ff9263fe000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00007ff92a4e9000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00007ff915e4f000 	/usr/lib/swift/libswiftXPC.dylib
0x00007ffc223b7000 	/usr/lib/swift/libswift_Concurrency.dylib
0x00007ff827ef2000 	/usr/lib/swift/libswiftos.dylib
0x00007ff9191bd000 	/usr/lib/swift/libswiftsimd.dylib
0x00007ff81c473000 	/usr/lib/libcompression.dylib
0x00007ff81ece9000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x00007ff81db8e000 	/usr/lib/libate.dylib
0x00007ff81c2cf000 	/usr/lib/system/libcache.dylib
0x00007ff81c28b000 	/usr/lib/system/libcommonCrypto.dylib
0x00007ff81c2b3000 	/usr/lib/system/libcompiler_rt.dylib
0x00007ff81c2a8000 	/usr/lib/system/libcopyfile.dylib
0x00007ff80e711000 	/usr/lib/system/libcorecrypto.dylib
0x00007ff80e808000 	/usr/lib/system/libdispatch.dylib
0x00007ff80e9b2000 	/usr/lib/system/libdyld.dylib
0x00007ff81c2c5000 	/usr/lib/system/libkeymgr.dylib
0x00007ff81c26c000 	/usr/lib/system/libmacho.dylib
0x00007ff81b59b000 	/usr/lib/system/libquarantine.dylib
0x00007ff81c2c3000 	/usr/lib/system/libremovefile.dylib
0x00007ff813faa000 	/usr/lib/system/libsystem_asl.dylib
0x00007ff80e6ab000 	/usr/lib/system/libsystem_blocks.dylib
0x00007ff80e852000 	/usr/lib/system/libsystem_c.dylib
0x00007ff81c2bb000 	/usr/lib/system/libsystem_collections.dylib
0x00007ff81a3ed000 	/usr/lib/system/libsystem_configuration.dylib
0x00007ff819416000 	/usr/lib/system/libsystem_containermanager.dylib
0x00007ff81bebd000 	/usr/lib/system/libsystem_coreservices.dylib
0x00007ff811b61000 	/usr/lib/system/libsystem_darwin.dylib
0x00007ffc226e5000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x00007ff81c2c6000 	/usr/lib/system/libsystem_dnssd.dylib
0x00007ffc226e9000 	/usr/lib/system/libsystem_eligibility.dylib
0x00007ff80e84f000 	/usr/lib/system/libsystem_featureflags.dylib
0x00007ff80e9e2000 	/usr/lib/system/libsystem_info.dylib
0x00007ff81c201000 	/usr/lib/system/libsystem_m.dylib
0x00007ff80e7c8000 	/usr/lib/system/libsystem_malloc.dylib
0x00007ff813f1e000 	/usr/lib/system/libsystem_networkextension.dylib
0x00007ff811fad000 	/usr/lib/system/libsystem_notify.dylib
0x00007ff81a3f1000 	/usr/lib/system/libsystem_sandbox.dylib
0x00007ffc226ec000 	/usr/lib/system/libsystem_sanitizers.dylib
0x00007ff81c2c0000 	/usr/lib/system/libsystem_secinit.dylib
0x00007ff80e96b000 	/usr/lib/system/libsystem_kernel.dylib
0x00007ff80e9d7000 	/usr/lib/system/libsystem_platform.dylib
0x00007ff80e9a6000 	/usr/lib/system/libsystem_pthread.dylib
0x00007ff815c1a000 	/usr/lib/system/libsystem_symptoms.dylib
0x00007ff80e6f7000 	/usr/lib/system/libsystem_trace.dylib
0x00007ff81c297000 	/usr/lib/system/libunwind.dylib
0x00007ff80e6af000 	/usr/lib/system/libxpc.dylib
0x00007ff80e955000 	/usr/lib/libc++abi.dylib
0x00007ff81c2a0000 	/usr/lib/liboah.dylib
0x00007ff81d914000 	/usr/lib/liblzma.5.dylib
0x00007ff81c2d7000 	/usr/lib/libfakelink.dylib
0x00007ff813b6e000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x00007ff81c329000 	/usr/lib/libarchive.2.dylib
0x00007ff821c13000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x00007ffb17e23000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x00007ffb2d04b000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x00007ffb2d5c4000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x00007ffc224f2000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x00007ff811e67000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x00007ff81b5c0000 	/usr/lib/libbsm.0.dylib
0x00007ff81c272000 	/usr/lib/system/libkxld.dylib
0x00007ff817bac000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x00007ff811b6c000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00007ff816939000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x00007ff81bec3000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x00007ff81c3b2000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x00007ff815ba0000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x00007ff80eea7000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x00007ff81d8c5000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x00007ff817bb9000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x00007ff81c43d000 	/usr/lib/libapple_nghttp2.dylib
0x00007ff815815000 	/usr/lib/libsqlite3.dylib
0x00007ff815c22000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x00007ffc20d67000 	/usr/lib/libCoreEntitlements.dylib
0x00007ffb2884e000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x00007ff8157fc000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x00007ff81bde0000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x00007ff81b5a8000 	/usr/lib/libcoretls.dylib
0x00007ff81d933000 	/usr/lib/libcoretls_cfhelpers.dylib
0x00007ff81c46d000 	/usr/lib/libpam.2.dylib
0x00007ff81d9ac000 	/usr/lib/libxar.1.dylib
0x00007ff81df5c000 	/usr/lib/libheimdal-asn1.dylib
0x00007ff813b6d000 	/usr/lib/libnetwork.dylib
0x00007ff81c2db000 	/usr/lib/libpcap.A.dylib
0x00007ff815c10000 	/usr/lib/libdns_services.dylib
0x00007ff81a3f8000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00007ff81b2c1000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00007ffc22444000 	/usr/lib/swift/libswift_RegexParser.dylib
0x00007ff81beae000 	/usr/lib/libbz2.1.0.dylib
0x00007ff81b59e000 	/usr/lib/libCheckFix.dylib
0x00007ff813fc1000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00007ff81a3ff000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x00007ff8168f3000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x00007ff81b5d0000 	/usr/lib/libmecab.dylib
0x00007ff80f674000 	/usr/lib/libCRFSuite.dylib
0x00007ff81b629000 	/usr/lib/libgermantok.dylib
0x00007ff81c418000 	/usr/lib/libThaiTokenizer.dylib
0x00007ff8169d9000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00007ff823d70000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x00007ff81d9ee000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x00007ff81b091000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x00007ff80f2b4000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x00007ff81c555000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x00007ff81b62c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x00007ff81c458000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x00007ff81c54f000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00007ff81a515000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x00007ff80f575000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x00007ffb270f3000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x00007ff81c311000 	/usr/lib/libiconv.2.dylib
0x00007ff81c26b000 	/usr/lib/libcharset.1.dylib
0x00007ff817b90000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x00007ff817b84000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x00007ff81d935000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x00007ff81b4e3000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x00007ff81d9ba000 	/usr/lib/libutil.dylib
0x00007ffb25521000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x00007ff811ea9000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00007ffb15a3d000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x00007ff915e11000 	/usr/lib/libmis.dylib
0x00007ff9268da000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00007ffa23c07000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x00007ff81c41a000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00007ff810575000 	/System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling
0x00007ff81d9be000 	/usr/lib/libxslt.1.dylib
0x00007ff81c318000 	/usr/lib/libcmph.dylib
0x00007ff81b28f000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00007ff81a50f000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00007ff80f48c000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x00007ff81b56c000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00007ffc20ed6000 	/usr/lib/libTLE.dylib
0x00007ff81e41c000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x00007ff81df41000 	/usr/lib/libexpat.1.dylib
0x00007ff81eb44000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x00007ff81eb71000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x00007ff81ec63000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00007ff81e468000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x00007ff81ec03000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x00007ff81ebfa000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x00007ff8197e4000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x00007ff815b3d000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00007ff82a974000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x00007ff81e550000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x00007ff810742000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x00007ff8196b3000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x00007ff819455000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x00007ff817d0d000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x00007ff81c46b000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x00007ff81e58f000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x00007ff817d9a000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x00007ff815a66000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00007ff81a3f7000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00007ffb1583a000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x00007ff81ebf3000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x00007ff81ebd7000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x00007ff81ebfd000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00007ffb21552000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x00007ffa27f28000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x00007ffb1e6da000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x00007ff81ec69000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00007ffb0e6c9000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00007ff825f4d000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x00007ff81e10b000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x00007ff8206a3000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00007ff810870000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00007ff81968d000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x00007ff81ff3b000 	/usr/lib/libAudioStatistics.dylib
0x00007ff9151b6000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x00007ff8201fd000 	/usr/lib/libSMC.dylib
0x00007ff82a005000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x00007ff81eb0b000 	/usr/lib/libAudioToolboxUtility.dylib
0x00007ff9107ac000 	/System/Library/PrivateFrameworks/OSAServicesClient.framework/Versions/A/OSAServicesClient
0x00007ff8206b1000 	/usr/lib/libperfcheck.dylib
0x00007ff8259f9000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x00007ff81de21000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00007ff81b4d6000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00007ffa12928000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00007ffb2bff6000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x00007ffa28c07000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x00007ff81e0b6000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00007ffa27f85000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00007ffa27f47000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00007ffa28143000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00007ffa27f4f000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00007ffa27f44000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00007ffc20e92000 	/usr/lib/libRosetta.dylib
0x00007ffa27f2f000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00007ffb1ca3d000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x00007ff81e0c5000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x00007ff8178c8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x00007ff81e116000 	/System/Library/PrivateFrameworks/FontServices.framework/libhvf.dylib
0x00007ffb1ca3e000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00007ff81a336000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x00007ff81bd3a000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x00007ff81b642000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x00007ff81bbb1000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x00007ff81b963000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x00007ff81bbf0000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00007ffb0fd53000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00007ffb0fd36000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x00007ff80f164000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00007ff91a50b000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00007ff9267da000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00007ff915e33000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x00007ff825e22000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00007ff915de3000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x00007ff81dcc6000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x00007ff825de3000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x00007ff9251fa000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x00007ffc1820d000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x00007ff81def3000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00007ff81ff7b000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x00007ff814e80000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x00007ff81ec77000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x00007ff820351000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x00007ff82034a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x00007ff81ff51000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x00007ff81ec33000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x00007ff8202dc000 	/usr/lib/libcups.2.dylib
0x00007ff8206c0000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x00007ff8206cf000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00007ff81fff4000 	/usr/lib/libresolv.9.dylib
0x00007ff81e128000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00007ff827e60000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x00007ff82071c000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x00007ffa2d581000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00007ff9107f3000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x00007ffb268b5000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x00007ff81fec2000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00007ff8219d9000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x00007ff81e001000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x00007ff81fd15000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x00007ff82035c000 	/System/Library/PrivateFrameworks/AudioResourceArbitration.framework/Versions/A/AudioResourceArbitration
0x00007ff824a90000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00007ff8249ad000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00007ff827e61000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x00007ff81b349000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x00007ffb1b042000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00007ffb253b6000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00007ff82593b000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x00007ff82744a000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x00007ff81df65000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x00007ff8239b7000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x00007ff91080c000 	/usr/lib/libAccessibility.dylib
0x00007ff8240a4000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00007ffa16293000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00007ff915d6e000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x000000010bbc4000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/server/libjvm.dylib
0x00007ff915d1c000 	/usr/lib/libstdc++.6.dylib
0x000000010abd7000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/libverify.dylib
0x000000010ac77000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/libjava.dylib
0x000000010acec000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/libinstrument.dylib
0x000000010ae62000 	/private/var/folders/7x/_k3qtljd17vbq4t96j9x6js80000gp/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x000000010ac51000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/libzip.dylib
0x0000000129a36000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/libnet.dylib
0x0000000129b05000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/libmanagement.dylib
0x0000000129b87000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/libnio.dylib
0x00007ff81f757000 	/usr/lib/libusrtcp.dylib
0x00007ff813a99000 	/usr/lib/libboringssl.dylib
0x000000012c1a3000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/libsunec.dylib
0x000000012f4ec000 	/private/var/folders/7x/_k3qtljd17vbq4t96j9x6js80000gp/T/libnetty_resolver_dns_native_macos_x86_647638786547265728172.dylib

VM Arguments:
jvm_args: -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar=63311 -agentpath:/private/var/folders/7x/_k3qtljd17vbq4t96j9x6js80000gp/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=start,jfr,event=wall,interval=10ms,jfrsync=profile,cstack=no,file=/Users/<USER>/IdeaSnapshots/BrandBusinessServiceApplication_2025_05_16_114201.jfr,log=/private/var/folders/7x/_k3qtljd17vbq4t96j9x6js80000gp/T/BrandBusinessServiceApplication_2025_05_16_114201.jfr.log.txt,logLevel=DEBUG -Dfile.encoding=UTF-8 
java_command: com.wosai.cua.brand.business.service.BrandBusinessServiceApplication
java_class_path (initial): /Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/charsets.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/deploy.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/cldrdata.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/dnsns.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/jaccess.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/jfxrt.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/localedata.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/nashorn.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/sunec.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/ext/zipfs.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/javaws.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/jce.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/jfr.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/jfxswt.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/jsse.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/management-agent.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/plugin.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/resources.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home/jre/lib/rt.jar:/Users/<USER>/code/brand-business/brand-business-service/target/classes:/Users/<USER>/code/brand-business/brand-business-api/target/classes:/usr/l
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk1.8.0_221.jdk/Contents/Home
PATH=/opt/anaconda3/bin:/opt/anaconda3/condabin:/usr/local/bin:/usr/local/apache-maven-3.6.2/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/usr/local/go/bin:⁩:/Users/<USER>/.local/bin
SHELL=/bin/bash

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x5b71a7], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x5b71a7], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x48c8fc], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x48c8fc], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x48c8fc], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x48c8fc], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x48c41a], sa_mask[0]=00000000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x48a9a5], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x48a9a5], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x48a9a5], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x48a9a5], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 23.4.0 Darwin Kernel Version 23.4.0: Wed Feb 21 21:51:37 PST 2024; root:xnu-10063.101.15~2/RELEASE_ARM64_T8112 x86_64
rlimit: STACK 8176k, CORE 0k, NPROC 2666, NOFILE 10240, AS infinity
load average:6.35 3.74 3.35

CPU:total 8 (initial active 8) (1 cores per cpu, 1 threads per core) family 6 model 44 stepping 0, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, aes, clmul, tsc, tscinvbit, tscinv

Memory: 4k page, physical 16777216k(21132k free)

/proc/meminfo:


vm_info: Java HotSpot(TM) 64-Bit Server VM (25.221-b11) for bsd-amd64 JRE (1.8.0_221-b11), built on Jul  4 2019 04:36:22 by "java_re" with gcc 4.2.1 (Based on Apple Inc. build 5658) (LLVM build 2336.11.00)

time: Fri May 16 11:42:14 2025
timezone: CST
elapsed time: 12 seconds (0d 0h 0m 12s)

