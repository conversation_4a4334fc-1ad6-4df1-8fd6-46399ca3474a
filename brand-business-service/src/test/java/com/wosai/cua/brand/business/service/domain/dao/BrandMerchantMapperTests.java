package com.wosai.cua.brand.business.service.domain.dao;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class BrandMerchantMapperTests {

    @Autowired
    private BrandMerchantMapper brandMerchantMapper;

    @Test
    public void testPageQueryFunctions(){
        QueryMerchantConditionsDO conditions = new QueryMerchantConditionsDO();
        conditions.setBrandIds(Lists.newArrayList("123456789"));
        conditions.setMerchantName("公司");
        int count = brandMerchantMapper.countBrandMerchantsByConditions(conditions);
        List<BrandMerchantDO> brandMerchantDOList = brandMerchantMapper.pageBrandMerchantsByConditions(conditions, 0, 1);
        System.out.println(count);
        System.out.println(JSON.toJSONString(brandMerchantDOList));
    }
}
