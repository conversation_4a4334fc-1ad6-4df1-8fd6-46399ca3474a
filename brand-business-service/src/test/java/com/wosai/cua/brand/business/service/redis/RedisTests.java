package com.wosai.cua.brand.business.service.redis;

import com.alibaba.fastjson2.JSON;
import com.lark.oapi.core.utils.Lists;
import com.wosai.cua.brand.business.service.enums.RedisKeyEnum;
import com.wosai.cua.brand.business.service.helper.RedisHelper;
import com.wosai.cua.brand.business.service.module.tripartite.fuiou.FuiouToBeActiveRecordModule;
import org.apache.commons.collections4.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@SpringBootTest
@RunWith(SpringRunner.class)
public class RedisTests {

    @Autowired
    private RedisHelper redisHelper;

    @Test
    public void testRedis()
    {
        List<FuiouToBeActiveRecordModule> list = Lists.newArrayList();
        list.add(FuiouToBeActiveRecordModule.builder().brandId("123").merchantId("456").tradeNo("789").build());
        list.add(FuiouToBeActiveRecordModule.builder().brandId("abc").merchantId("def").tradeNo("ghi").build());
        redisHelper.saveAllToSet(RedisKeyEnum.FUIOU_OPEN_ACCOUNT_RECORDS.getKey(), list);
        List<FuiouToBeActiveRecordModule> result = redisHelper.getAllFromSet(RedisKeyEnum.FUIOU_OPEN_ACCOUNT_RECORDS.getKey(), FuiouToBeActiveRecordModule.class);
        System.out.println(result);
    }

    @Test
    public void testRedis2()
    {
        Map<Object, Object> mapByKey = redisHelper.getMapByKey(String.format(RedisKeyEnum.IMPORT_MERCHANT_TASK_TRANSACTION.getKey(), "1362465503058264064"));
        if (MapUtils.isEmpty(mapByKey)) {
            return;
        }
        mapByKey.forEach((k, v) -> {
            if (Objects.nonNull(v)) {
                List<String> merchantIds = JSON.parseArray((String) v,String.class);
                System.out.println(merchantIds);
            }
        });
    }
}
