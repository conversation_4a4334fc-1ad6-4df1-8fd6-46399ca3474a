package com.wosai.cua.brand.business.service.domain.dao;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantCreationRecordDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class BrandMapperTests {

    @Autowired
    private BrandMapper brandMapper;

    @Autowired
    private BrandMerchantCreationRecordMapper brandMerchantCreationRecordMapper;

    @Test
    public void testQueryBrandByBrandId(){
        BrandDO brandDO = brandMapper.selectBrandByBrandId("123456789");
        System.out.println(
                JSON.toJSONString(brandDO)
        );
        List<BrandDO> brandDOS = brandMapper.selectList(null);
        System.out.println(
                JSON.toJSONString(brandDOS)
        );
        BrandMerchantCreationRecordDO recordDo = new BrandMerchantCreationRecordDO();
        recordDo.setBrandId("22222222");
        recordDo.setMerchantId("mercha222222ntId");
        recordDo.setNeedRetry(1);
        brandMerchantCreationRecordMapper.insertCreationRecord(recordDo);
        System.out.println(recordDo.getId());
    }
}
