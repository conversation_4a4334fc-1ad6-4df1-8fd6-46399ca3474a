package com.wosai.cua.brand.business.service;

import com.wosai.cua.brand.business.service.job.BrandCallbackRecordsJobHandler;
import com.wosai.cua.brand.business.service.job.TopUpAccountNameJobHandler;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class BrandBusinessServiceApplicationTests {

	@Autowired
	private BrandCallbackRecordsJobHandler brandCallbackRecordsJobHandler;

	@Autowired
	private TopUpAccountNameJobHandler topUpAccountNameJobHandler;
	@Test
	void contextLoads() {
//		brandCallbackRecordsJobHandler.invokeBrandCallbackRecords();

		topUpAccountNameJobHandler.topUpAccountNameCompensate();
	}

}
