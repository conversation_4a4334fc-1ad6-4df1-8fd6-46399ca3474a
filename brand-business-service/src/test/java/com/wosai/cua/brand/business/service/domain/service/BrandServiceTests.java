package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class BrandServiceTests {

    @Autowired
    private BrandTaskLogDomainService brandTaskLogDomainService;

    @Test
    public void testEncryptionAspect(){
//        BrandModule brandModule = new BrandModule();
//        brandModule.setBrandId("123123123").setCity("测试").setAlias("测试切面").setContactPhone("***********");
//        brandDomainService.createBrand(brandModule);
////        brandBusiness.createBrand(new CreateBrandRequestDTO());
////        BrandModule brandModuleByBrandId = brandDomainService.getBrandModuleByBrandId("c3886211-80ac-4f55-be21-09587a366e28");
////        System.out.println(JSON.toJSONString(brandModuleByBrandId));
////        List<BrandModule> brandModuleByBrandIds = brandDomainService.getBrandModuleByBrandIds(Lists.newArrayList("c3886211-80ac-4f55-be21-09587a366e28"));
////        System.out.println(JSON.toJSONString(brandModuleByBrandIds));
        BrandTaskLogModule module = new BrandTaskLogModule();
        module.setBrandId("123");
        module.setTaskStatus(1);
        module.setTaskId(1232134L);
        module.setTaskName("测试");
        module.setTaskType(0);
        Long l = brandTaskLogDomainService.insertBrandTaskLog(module);
        System.out.println(l);
    }
}
