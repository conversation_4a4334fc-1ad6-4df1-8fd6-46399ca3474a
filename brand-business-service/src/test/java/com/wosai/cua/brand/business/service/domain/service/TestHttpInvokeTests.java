package com.wosai.cua.brand.business.service.domain.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.BrandBusinessServiceApplication;
import com.wosai.cua.brand.business.service.config.feign.FeignConfig;
import com.wosai.cua.brand.business.service.domain.https.enums.FeiShuMegTypeEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.BindBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.RegisteredUserRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.BaseFeiShuCardMessageRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.BaseFeiShuMessageRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.CardMessage;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.TextMessage;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.ActiveAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.ActiveAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryConcentrateRelationRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryConcentrateRelationRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantAppletPreRegisterRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantAppletPreRegisterRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.RegisterBehaviorRecordInfoRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.BindBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.RegisteredResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.feishu.BaseFeiShuMessageResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.ActiveAllocateAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.QueryAllocateAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.QueryConcentrateRelationResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantRegisterResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.VfinanceBaseResponse;
import com.wosai.cua.brand.business.service.helper.SignHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountRes;
import com.wosai.upay.common.util.JacksonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {BrandBusinessServiceApplication.class, CiticBankConfigModule.class, FuiouConfigModule.class, MyBankConfigModule.class, FeignConfig.class})
public class TestHttpInvokeTests {

    @Autowired
    private VfinanceInterfaceService vfinanceInterfaceService;

    @Autowired
    private List<TripartiteSystemCallService> tripartiteSystemCallServices;

    @Autowired
    private FeiShuMessageRobotInvokeService feiShuMessageRobotInvokeService;

    @Value("${open.feishu.message_robot_secret}")
    private String secret;

    @Test
    public void test() throws NoSuchAlgorithmException, InvalidKeyException {
        // 获取当前时间的Instant对象
        Instant now = Instant.now();

        // 转换为秒级时间戳
        long timestampInSeconds = now.getEpochSecond();
        String genSign = SignHelper.genSign(secret, timestampInSeconds);
        BaseFeiShuMessageRequest<TextMessage> request = new BaseFeiShuMessageRequest<>();
        request.setSign(genSign);
        request.setTimestamp(String.valueOf(timestampInSeconds));
        request.setMsgType(FeiShuMegTypeEnum.TEXT.getType());
        TextMessage textMessage = new TextMessage();
        textMessage.setText("品牌测试");
        request.setContent(textMessage);
        BaseFeiShuMessageResponse<Object> objectBaseFeiShuMessageResponse = feiShuMessageRobotInvokeService.sendTextMessage(request);
        System.out.println(JacksonUtil.toJsonString(objectBaseFeiShuMessageResponse));
    }

    @Test
    public void testFeishuCardMessage() throws NoSuchAlgorithmException, InvalidKeyException {
        // 获取当前时间的Instant对象
        Instant now = Instant.now();

        // 转换为秒级时间戳
        long timestampInSeconds = now.getEpochSecond();
        String genSign = SignHelper.genSign(secret, timestampInSeconds);
        BaseFeiShuCardMessageRequest<CardMessage> request = new BaseFeiShuCardMessageRequest<>();
        request.setSign(genSign);
        request.setTimestamp(String.valueOf(timestampInSeconds));
        request.setMsgType(FeiShuMegTypeEnum.INTERACTIVE.getType());
        CardMessage cardMessage = new CardMessage();
        cardMessage.setType("template");
        CardMessage.DataBean dataBean = new CardMessage.DataBean();
        dataBean.setTemplateId("AAqCSbCzZ7Te2");
        dataBean.setTemplateVersionName("1.0.1");
        Map<String,Object> templateVariable = Maps.newHashMap();
        List<Map<String,Object>> tableRawValues = Lists.newArrayList();
        Map<String,Object> tableRawValue = Maps.newHashMap();
        tableRawValue.put("brand_sn","123213213");
        tableRawValue.put("merchant_sn","4543535345");
        tableRawValue.put("failure_reason","测试失败了");
        tableRawValues.add(tableRawValue);
        templateVariable.put("table_raw_array",tableRawValues);
        dataBean.setTemplateVariable(templateVariable);
        cardMessage.setData(dataBean);
        request.setCard(cardMessage);
        BaseFeiShuMessageResponse<Object> objectBaseFeiShuMessageResponse = feiShuMessageRobotInvokeService.sendCardMessage(request);
        System.out.println(JacksonUtil.toJsonString(objectBaseFeiShuMessageResponse));
    }

    @Test
    public void testMyBank(){
        tripartiteSystemCallServices.forEach(tripartiteSystemCallService -> {
            if (tripartiteSystemCallService.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.MY_BANK)){
                MyBankConfigModule configModule = new MyBankConfigModule();
                configModule.setAppId("****************");
                configModule.setIsvOrgId("202211000000000002648");
                configModule.setIsvPrivateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC+DjjKuBYNScrhp7/+yf/vqsZbyWAMuvLZ76rv9wLA3XTCY1Cb3xxm7xvux/OzhzDlQdURRrEhjjsVdP9Bx0Ig5yfGyDec/4Z50dcRgqsu17QU/m2WXHEwFd4yBAGomWXzy/q7+8JJ9YoTraSDfKhfV3qWx7/vBzqk0QNzZ3AgFwSMiP5Azz8K2GhkHWxbVLlji9+/oRL5yj13a8P9bbtIrdUB/PZ+Fk3klxhBL9Vxii0YmX96nwVuJgkmgyiRCwmLhIQBFdL7H63cNgUi9UoCsFC6lV4ErEcHCUFsLqggukKPmTlr4KSnFvf9bZoIYC9I7XBWcM+KDGTSdxeGpXbvAgMBAAECggEABIJr4It7oncUxEPpr071rqcbq8PcbpDlADzKjoUK4K6gfZhDql8h2mNkA0dlReY4R8hHGPDXdRdd2YV8JQBoVkWF0RahEy2Q8EUFWFoEW8ksca8TxJSO7vgl3IPx0iFJpP47BcjUdFLKIutk0uXbTN/Tfc5hhHdkcdKvxUY4B9rZYSNI5+YdIGjJj/nBWNGBndIqKnLfs2AhpCqPkADC3xo73fxiM/77TPB7RiaFeK9jk80kLAwaZeOKArCIY7YSpnyhqvPi4kO9vxTeGGHONhN+mfAsMG94AY83vZ6qhUiZZuofslbUt7beOw2PTmzGi8jXDI8k6WVHKqNnwYt/+QKBgQD/fT8sG3oJyRpvfhc/gPM+ovYGL3MH+6B3Gtq32K+ZyAmtoHnfLFkaa1bOLUEklvr2xViFTU4NlUyEWNE1jGW1JFC9AhU3JUnVKmiG/BSURH8neYsNUQ/yKOmjRyTvQmELp2Qb7h46ZWM6kWOzrJtiXMvkoAjWwgO/4YOxEazJdQKBgQC+b3zZW/yh/cOTCAzfN8AR/luSq9TJ7D8CZxHVOpXRL3YP950SUWEfuyOby4ddhFDwU3b6+BhtOtClMrBKO2Q9wbPVjVD5plGlhhMaLiNJmbFL63hEazrMOE3UVbOY8EYBJyyeUguqiYyRJkUbiVubRsdbFLUbGai8qWg5BvTOUwKBgEFOUoeDvn4h2ZAGOwsQexzXquuJ1W2E9E99ncrAqKI2b8Lh8kUJoP0P0vCAwNYJgbzyVN4+FGWEdDqgOVnmuVjEH58wmRuvfF/wpydZ6Ci+GYKNnu2Yeur7aj1CQj6mSQghkYVSKIfkwqiF4WZcCJvr/HJENf4vOaYijvcD/ZbBAoGBAKoZFyFnIq7m5cvtAuJW/76SveSyiuyZkmZo/erB25PvmrsEZ043VlNrapD8KLsFNu6S/tGIzPiz8i28qu6DQjRPUnxLL6ruPjtlGKbn0ykomM7BUrl6Nhi3qf0hV7wh0cWx4g7AJh97oQz9a/j+pc56WBMo2eOM9cUeZDOb3Qp1AoGAQwB8ndcxkt4UCfC+87Rpy5yrxVuaVKDZfTIy4qpuLrHRqCIHvAl41EnJTE8MPIemXhdTmPeLmGWe7KjGDo0F0GPRqBMVKrLrCnM52FUx/Wp5P2h8ZVXvm7TR5/b3jhCb7qC0gjey+HunyEn78H9Vxty4XzcSlF0zu8uVUSgwIJ8=");
                configModule.setIsvPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvg44yrgWDUnK4ae//sn/76rGW8lgDLry2e+q7/cCwN10wmNQm98cZu8b7sfzs4cw5UHVEUaxIY47FXT/QcdCIOcnxsg3nP+GedHXEYKrLte0FP5tllxxMBXeMgQBqJll88v6u/vCSfWKE62kg3yoX1d6lse/7wc6pNEDc2dwIBcEjIj+QM8/CthoZB1sW1S5Y4vfv6ES+co9d2vD/W27SK3VAfz2fhZN5JcYQS/VcYotGJl/ep8FbiYJJoMokQsJi4SEARXS+x+t3DYFIvVKArBQupVeBKxHBwlBbC6oILpCj5k5a+Ckpxb3/W2aCGAvSO1wVnDPigxk0ncXhqV27wIDAQAB");
                configModule.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDOb4B1dnwONcW0RoJMa0IOq3O6jiqnTGLUpxEw2xJg+c7wsb6DBy5CAoR0w2ZjZ/BjKxGIQ+DoDg3NsHJeyuEjNF0/Ro/R5xVpFC5z4cBVSC2/gddz4a1EoGDJewML/Iv0yIw7ylB86++h23nRd079c5S9RZXurBfnLW2Srhqk2QIDAQAB");
                //实例化具体API对应的request类,当前调用API接口：小程序商户预入驻接口<ant.mybank.bkmerchantprod.merch.applet.pre.register>
                MerchantAppletPreRegisterRequest merchAppletPreRegisterRequest = new MerchantAppletPreRegisterRequest(configModule.getAppId());

                //以下方法为demo的model入参方式
                MerchantAppletPreRegisterRequestModel merchAppletPreRegisterRequestModel = new MerchantAppletPreRegisterRequestModel();
                merchAppletPreRegisterRequestModel.setIsvOrgId(configModule.getIsvOrgId());
                merchAppletPreRegisterRequestModel.setOutTradeNo("");
                merchAppletPreRegisterRequestModel.setOutMerchantId("");
                merchAppletPreRegisterRequestModel.setMerchantType("");
                merchAppletPreRegisterRequestModel.setMerchantName("");
                merchAppletPreRegisterRequestModel.setDealType("");
                merchAppletPreRegisterRequestModel.setCertPhotoA("");
                merchAppletPreRegisterRequestModel.setCertPhotoB("");
                merchAppletPreRegisterRequestModel.setLicensePhoto("");
                merchAppletPreRegisterRequestModel.setMerchantDetail("");

                merchAppletPreRegisterRequest.setRequestBody(merchAppletPreRegisterRequestModel);
                tripartiteSystemCallService.call(merchAppletPreRegisterRequest, MerchantRegisterResponse.class, configModule);
            }
        });
    }

    @Test
    public void testCitic(){
        tripartiteSystemCallServices.forEach(tripartiteSystemCallService -> {
            if (tripartiteSystemCallService.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.CITIC)){
                CiticBankConfigModule configModule = new CiticBankConfigModule();
                configModule.setPrivateKey("A1JTQVDjl+Hb5rsllFq0FolON+DKQPzHYOB8ojFqB57ACXpFbWWvl2IwYivxTqCkI3aOn53GdZ7vjjNHWoObVHWvWHpcUazlqpsjjSp2q86Z3my9Dqu4WbKCSF1XKCE9JydLZ/i/In3klzrs7QR88k2CKRVwRDdcUG2gu1godkB/L6drqJVbfaAaNBzS+wJghmSFgstsR2wm4vxY4ZQlQdE5iMNpxKQjaX36sXhwpZECMOG1cx/8CpTl2nAoWeWMeUIBum5KO2l3OEJV5SL9NCAdbL7Ded0CmnZN9q9056t8L+fepndE4gNAbWOvAgeoemJEz5mH/thLDcXA4/PPgw0F/7f++4Z6kKiMEGZoyQaUdeIKzf8UTB6relbNZK4DLcZxd3WTC0i2fBa/T+mUuBeBCWFhHBPPRK+nunIPSno5Aw5wHCGvaUtbWS2od792vEXUwWWdPm4ZEBbubrlLnqLV3EBIREM9qCDBpxi7xVhu/4W4E62Relavn15v5EJqF2g+3f58nLOjnZaJecPiGLAahkGQzHUQ7AzA9ZY6wkRQq0UF759MRcr5fIRKAnTLpjYBOWweepcnVBoiAaBqLOOV8e5WN8fqRILvTRfYQBXvT4i3jdQZA9iFH3aN3uNAtRh/Wnc8Jb/aTMpTBAQGdMHLPIZQMCF7pA4lEpyRjlDJ7FsWU7BQtA/dPs+mx8VTr7GpSVyfT0UFBIjd/oJoupCVkK7Ls4x9bytVR9LpTOsmvwfTpnSOFksaWyyRuRJUNnAK32Vyr5iUEYWZ5tNMyi253oLmpZKpNiq5Z4oZOFcpG5HJmNsaX6X4Vxd6VSUKyVSggqlgHRMnb0l2EZLY0PfRFeQL6+WY91DkcRHSqMoQJ0IzmcG96raSOki33qy4vy2D7++q8ppUtgkwzH7l0B+EN4QgtEV0dK9OhizPW/Xjh0ytF76+vdsMhZ+MIZymyQk8hkl75tZia+GJgBTF9WtenCruDORQUcbdC4GKbsWKkHvn72uWXOvZSmptOuXmbS2RbD+NmJNA0vr41BF+UFapFp+EZU5VWorpqizB6neXhdL8aPyZA7EWVPX6DIU7iGtBgTi5qCmptk/SqOXiC1BonkLd64it6E7BZv30z8RxUY2Pxou2ZuqtkCSzrqRv3iQiYY+EKDw0Fek5gX8onVMwPRJs2sNnCa1u8Hp2oJ9ATl+OZh3kklRHoOsEQdccvhqb+RrIkqVz+Ml1xc3udunUuS48NXEArDEX8eIcMJ3wwjU17asHr59pVfC4HP3fagRy4Xnn1u00FQ7PyFd1hiSwxl8s5K7xtb0ZYCtI7PlI+CFB/GChTZ22wZ3JOBpqpbAB2i0npYmoCKv83bPxdFNu8BpGkXUgVSXboqlXLbbKshBlvixO2LiSEBbY5+vuWOZfJrCe/VQUh4nqjsUuF0TgGiPdG13BBVxpBDLgItIuwSMj4/pOkE0u7HcB/VtDIPD+j+Hx2X865O8a4wdwbCfhiu8QjDNll8Qths0ZPBIlZxnAXO0sWAm/1yYrA6dBH3vZztg8Rkab9n8Z+sxkTQAJeGm7SEsNDf3Hq+lzBHJK4qEWvCATCBIRFpsXzZWJVjVTe17TLoN49z+y1+COJQLByCHKIoXRtjawhr0xMOCBYPGKo5b3C5IAkG8=");
                configModule.setPrivateKeyPassword("Mima001");
                configModule.setPublicKey("MIIDITCCAgmgAwIBAgIBMDANBgkqhkiG9w0BAQUFADArMQswCQYDVQQGEwJDTjENMAsGA1UECwwEUFROUjENMAsGA1UEAwwEdGVzdDAeFw0xNzA2MDEwMjMzNDZaFw0yNzA1MzAwMjMzNDZaMCsxCzAJBgNVBAYTAkNOMQ0wCwYDVQQLDARQVE5SMQ0wCwYDVQQDDAR0ZXN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAto3/T7lVxrlHOo1XcpwniHmCLxnnMU+9TMglpiy+Z6JeDzgHj1XX1dyf5fOXT/lV3F8A226rXD6WQS7M9vv4mcBlf0nbCCz/m+2wJPMhvpDetewaKn0tGnXudqM/vEWKzk4ePs+12elePsDr58I+ffXOTzZALrutY0rn4tsHkqBP7jWYY7UpERR+aFptbIMzTR3iMJY4LDbNu13DVMpbKKWLMheB2i4FJr8kZhC0Fnk3sltkN0XNOi4tRhzqZqIttXWWhTEnj3JKd+ZA3H2BovujpkNayPiyFFl+Jhd6wfnPLgBSCq4CjNm3OYIuIFb6zMEyWRRFbYSBa789hWSCPQIDAQABo1AwTjAdBgNVHQ4EFgQU9FIGuf/328a525OrV+7B4owIRPcwHwYDVR0jBBgwFoAU9FIGuf/328a525OrV+7B4owIRPcwDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAs+iRoOBndGaSdH8s3KZyy8uzNQKRF0wazQkKdFDuvzxrylbjZUzSS6cpkwuUgBAulLxpNrWzqyejRQ2yz6iiArtixBFiGhv9B380g1szKnCoVExFumdudCuQPMaUScyAr8Z9d17/EhQLTn8IAy1Sa1iLzOTONnMessZpf/PaguGZLwpkaU55syL4P3PAhLveHp/t7q6bTY/iAQrcc0Oqbngpk4vX8+9BqUrjIRs4cIjhU8K8+yHYPqogNC9FD2NL8JkMzpa/ekKR0knbdCaAB1rcuenFbGQAnS/JscndJv/xqW2z/psqb5XS9ivTRD5f67G0Bluo5ZdUrzrgkrjSrQ==");
                configModule.setMerchantId("J04033900000000");
                RegisteredUserRequest registeredUserRequest = new RegisteredUserRequest();
                registeredUserRequest.setTransCode("21000001");
                registeredUserRequest.setReqSsn("J04033900000000201912171513100001s300223");
                registeredUserRequest.setLaasSsn("LAASJ100364201912171513100001s300223");
                registeredUserRequest.setMerchantId(configModule.getMerchantId());
                registeredUserRequest.setMerchantSn("test1");
                registeredUserRequest.setUserType("1");
                registeredUserRequest.setUserName("联调测试");
                registeredUserRequest.setUserRole("001001");
                registeredUserRequest.setSignType("00");//签约类型
                registeredUserRequest.setUserIdType("01");
                registeredUserRequest.setUserIdNo("000013477");
                registeredUserRequest.setUserPhone("15562632654");
                registeredUserRequest.setLegalPersonName("法人姓名");
                registeredUserRequest.setLegalPersonId("123456");
                registeredUserRequest.setUserAddress("用户地址");
                registeredUserRequest.setReqReserved("123456");//发起方保留域
                registeredUserRequest.setLegalPersonIdType("");
                RegisteredResponse call = tripartiteSystemCallService.call(registeredUserRequest, RegisteredResponse.class, configModule);
                System.out.println(call);
            }
        });
    }

    public void testCiticBindBankCard(){
        tripartiteSystemCallServices.forEach(tripartiteSystemCallService -> {
            if (tripartiteSystemCallService.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.CITIC)){
                CiticBankConfigModule configModule = new CiticBankConfigModule();
                configModule.setPrivateKey("A1JTQVDjl+Hb5rsllFq0FolON+DKQPzHYOB8ojFqB57ACXpFbWWvl2IwYivxTqCkI3aOn53GdZ7vjjNHWoObVHWvWHpcUazlqpsjjSp2q86Z3my9Dqu4WbKCSF1XKCE9JydLZ/i/In3klzrs7QR88k2CKRVwRDdcUG2gu1godkB/L6drqJVbfaAaNBzS+wJghmSFgstsR2wm4vxY4ZQlQdE5iMNpxKQjaX36sXhwpZECMOG1cx/8CpTl2nAoWeWMeUIBum5KO2l3OEJV5SL9NCAdbL7Ded0CmnZN9q9056t8L+fepndE4gNAbWOvAgeoemJEz5mH/thLDcXA4/PPgw0F/7f++4Z6kKiMEGZoyQaUdeIKzf8UTB6relbNZK4DLcZxd3WTC0i2fBa/T+mUuBeBCWFhHBPPRK+nunIPSno5Aw5wHCGvaUtbWS2od792vEXUwWWdPm4ZEBbubrlLnqLV3EBIREM9qCDBpxi7xVhu/4W4E62Relavn15v5EJqF2g+3f58nLOjnZaJecPiGLAahkGQzHUQ7AzA9ZY6wkRQq0UF759MRcr5fIRKAnTLpjYBOWweepcnVBoiAaBqLOOV8e5WN8fqRILvTRfYQBXvT4i3jdQZA9iFH3aN3uNAtRh/Wnc8Jb/aTMpTBAQGdMHLPIZQMCF7pA4lEpyRjlDJ7FsWU7BQtA/dPs+mx8VTr7GpSVyfT0UFBIjd/oJoupCVkK7Ls4x9bytVR9LpTOsmvwfTpnSOFksaWyyRuRJUNnAK32Vyr5iUEYWZ5tNMyi253oLmpZKpNiq5Z4oZOFcpG5HJmNsaX6X4Vxd6VSUKyVSggqlgHRMnb0l2EZLY0PfRFeQL6+WY91DkcRHSqMoQJ0IzmcG96raSOki33qy4vy2D7++q8ppUtgkwzH7l0B+EN4QgtEV0dK9OhizPW/Xjh0ytF76+vdsMhZ+MIZymyQk8hkl75tZia+GJgBTF9WtenCruDORQUcbdC4GKbsWKkHvn72uWXOvZSmptOuXmbS2RbD+NmJNA0vr41BF+UFapFp+EZU5VWorpqizB6neXhdL8aPyZA7EWVPX6DIU7iGtBgTi5qCmptk/SqOXiC1BonkLd64it6E7BZv30z8RxUY2Pxou2ZuqtkCSzrqRv3iQiYY+EKDw0Fek5gX8onVMwPRJs2sNnCa1u8Hp2oJ9ATl+OZh3kklRHoOsEQdccvhqb+RrIkqVz+Ml1xc3udunUuS48NXEArDEX8eIcMJ3wwjU17asHr59pVfC4HP3fagRy4Xnn1u00FQ7PyFd1hiSwxl8s5K7xtb0ZYCtI7PlI+CFB/GChTZ22wZ3JOBpqpbAB2i0npYmoCKv83bPxdFNu8BpGkXUgVSXboqlXLbbKshBlvixO2LiSEBbY5+vuWOZfJrCe/VQUh4nqjsUuF0TgGiPdG13BBVxpBDLgItIuwSMj4/pOkE0u7HcB/VtDIPD+j+Hx2X865O8a4wdwbCfhiu8QjDNll8Qths0ZPBIlZxnAXO0sWAm/1yYrA6dBH3vZztg8Rkab9n8Z+sxkTQAJeGm7SEsNDf3Hq+lzBHJK4qEWvCATCBIRFpsXzZWJVjVTe17TLoN49z+y1+COJQLByCHKIoXRtjawhr0xMOCBYPGKo5b3C5IAkG8=");
                configModule.setPrivateKeyPassword("Mima001");
                configModule.setPublicKey("MIIDITCCAgmgAwIBAgIBMDANBgkqhkiG9w0BAQUFADArMQswCQYDVQQGEwJDTjENMAsGA1UECwwEUFROUjENMAsGA1UEAwwEdGVzdDAeFw0xNzA2MDEwMjMzNDZaFw0yNzA1MzAwMjMzNDZaMCsxCzAJBgNVBAYTAkNOMQ0wCwYDVQQLDARQVE5SMQ0wCwYDVQQDDAR0ZXN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAto3/T7lVxrlHOo1XcpwniHmCLxnnMU+9TMglpiy+Z6JeDzgHj1XX1dyf5fOXT/lV3F8A226rXD6WQS7M9vv4mcBlf0nbCCz/m+2wJPMhvpDetewaKn0tGnXudqM/vEWKzk4ePs+12elePsDr58I+ffXOTzZALrutY0rn4tsHkqBP7jWYY7UpERR+aFptbIMzTR3iMJY4LDbNu13DVMpbKKWLMheB2i4FJr8kZhC0Fnk3sltkN0XNOi4tRhzqZqIttXWWhTEnj3JKd+ZA3H2BovujpkNayPiyFFl+Jhd6wfnPLgBSCq4CjNm3OYIuIFb6zMEyWRRFbYSBa789hWSCPQIDAQABo1AwTjAdBgNVHQ4EFgQU9FIGuf/328a525OrV+7B4owIRPcwHwYDVR0jBBgwFoAU9FIGuf/328a525OrV+7B4owIRPcwDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAs+iRoOBndGaSdH8s3KZyy8uzNQKRF0wazQkKdFDuvzxrylbjZUzSS6cpkwuUgBAulLxpNrWzqyejRQ2yz6iiArtixBFiGhv9B380g1szKnCoVExFumdudCuQPMaUScyAr8Z9d17/EhQLTn8IAy1Sa1iLzOTONnMessZpf/PaguGZLwpkaU55syL4P3PAhLveHp/t7q6bTY/iAQrcc0Oqbngpk4vX8+9BqUrjIRs4cIjhU8K8+yHYPqogNC9FD2NL8JkMzpa/ekKR0knbdCaAB1rcuenFbGQAnS/JscndJv/xqW2z/psqb5XS9ivTRD5f67G0Bluo5ZdUrzrgkrjSrQ==");
                configModule.setMerchantId("J04033900000000");
                BindBankCardRequest bindBankCardRequest = new BindBankCardRequest();
//                bindBankCardRequest.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + merchant.getSn().substring(merchant.getSn().length() - 8));
            }
        });
    }

    @Test
    public void testCiticUnbindBankCard(){
        tripartiteSystemCallServices.forEach(tripartiteSystemCallService -> {
            if (tripartiteSystemCallService.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.CITIC)){
                CiticBankConfigModule configModule = new CiticBankConfigModule();
                configModule.setPrivateKey("A1JTQeRof6UjL+AuIYJmpQlc1PFZkp5uNaE6mZCKP9OFJzs/YVrKTdLrQoW0GJeFVlMD1bSXnYWiaexdetoInfTUkI7Qo+uow5WtEETzHsV3Nm6xkneF5xDKBY3iSu5+pP/N3BQWPY6SW6w8cvcvYIBgn9C/YVQKn7IDdzXp6TGyreTASV1pcn7iLcXrjADplvNNrtFPTZZpWnMKj/HN0RGdv0d5XKCaJMib3EWEBoPj/r4oltiSF/m9MpkOVQidBmhc3w6uUtm5KhxL6+byUD3zVGHcRP34UF7CNxhmkLqN/5VFYSELbdMkFyg4/NB00Tb2m15c5NX1hmJ9FaLWSCgRri9vCXxLnhr2gT9jEmwiotIhZ99LqpWL/3UP0yynUgZdm0E5lI4oiPnTAdgJd2zUInHtZcZJjLpCHSZw2OFpA+karEXyrm+jvogQPPMQByDilkxerpRAhsBbtY040t2IxNTXoJHqPsybhVfv0ujLKc97YEuBwisr1SO52cysuCMCn4Rp2HjvBKKSnlB19mLF7Jea8cELvwsPgEfV/YFVeNARzUTQne3fIXxGQhO7VZLBFaQ17tjaZUsViKmzyx4X+uIegzUHYTeKxQkksWRjfIgU5QRxm6DsghkPIYfdSszp4RcQPDDtLSBrTg8KqRw8DMw1T2LhP5pAJ2yqJUTa4NQfhEF7zM/RC6h5IvBxBSEKLvR0HVnYVd2nMZcnqmsaVNu0eDBMzRsc4zc9sy8+10EaBG3IHfNl5QB/w4NU7LTAPLKDn+oMEMoV7r5daBjkDe+AHJbWNQ/ESmzgl5NzJpXGMj0OdthOK0e5Z4hvuOJEisTf5P4m5jj6k2nvUEHRwswSZysDKmief3UgFN3dpCfJAyySOLgnZGsOAw5Pr0atdCN4fZ1IKkTGhwmjfOsUjsFlOjDTzA0qvQEgQ0e16wQYjUBymlWypBoobHo52GWgVYEGSiT/DAwBH2yqinKeXMGg7rlP55Yj4g75RS7vQ0+GVHyhHRyN4c/x9BtO60rbIzGZpMAiQhpNTCY9wnlrtFGU8cKi8j+TA7FkM2jzcq7mOBDGzEJPNlb1G2u9JvUROBmXxvQFAPSAzJtnuXfbT8OlWgAMdOEt5RodwcFKy5pHtIezGeOnf4IBev0JsPBFG1z32gUsxOHrrAvrqb+vIf/xohiYwTCTL/30loGptCqk0oDokVrArIgvB/5e/nTwUWEH8RKtErjoFjk1VmEFaYNLnBfI37Z+cnC4lG95xfTD7Dv5h1/65aD613mWljGYzp+D/sKOCWXCv9gzuIXAM2yIHVP2el2znMUeg99K6+LQ2vzAQCYVlx9dugwYK9a3WA/Qy73fS4c7ncdHPHaBYzqTcQw1ZUoTTMRNmD6hiJ7W42bmWuRpoYcTvgAt80MtSzBaaY6LswdwcN1wLuZ2lwYAGbC2fLmXCPQdUIIl1PBVMxa2dIEkSBBivQM18oo8hj2nx72zcHAMzU0BIIVCJRua/K0EtnvkeBgICeY5DqAW0Mt35yQXpR8NW4Jv3IDoUwqOLfotiuYCOAcSppzBs3lfmCZLyMgucGUiMvI+tSVejiRw46xRjmGOPHs7glsHkHVCxJ6OGxic7YELgvsHxjO+9LCKBBZKWH197+O8duHtzEK2hhmV6v4=");
                configModule.setPrivateKeyPassword("Mima001");
                configModule.setPublicKey("MIIDITCCAgmgAwIBAgIBMDANBgkqhkiG9w0BAQUFADArMQswCQYDVQQGEwJDTjENMAsGA1UECwwEUFROUjENMAsGA1UEAwwEdGVzdDAeFw0xODA1MTEwODEzMTZaFw0yODA1MDgwODEzMTZaMCsxCzAJBgNVBAYTAkNOMQ0wCwYDVQQLDARQVE5SMQ0wCwYDVQQDDAR0ZXN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhy9EEDpORGwqSrxoo5+EjGnkWmlgFyP6ilczKfz0/w2041Z7s4o0ggxVd2yy/VYDYWe98L48i8QMmItxxa0c2PLPxzbf0iUuNQ7viropAHXC/RpP5uonSiXslGb00o/1PevtzXjdZUQuhIqaOaIPBsOcXq3rm/w75V/krPBja5G+PqPyc9jmnAN5GY841mqtdextp7EJoiv/+ijnKuiwZxfdbDE8ocTdNv/tIAMLCSb1U+74H0dy0RqxcQKar8hAlEC1WLRaJEzylSWHIh+JTbWDscDHU4/JCvOsATMUw5KjemhNrkIpTimp0B4O2FEgWDEzb2/Yi2EhCZ/PdlA7+QIDAQABo1AwTjAdBgNVHQ4EFgQUlEsCMxIblsXzHP4KJ/H7PU86ToMwHwYDVR0jBBgwFoAUlEsCMxIblsXzHP4KJ/H7PU86ToMwDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAbjxpk2wC19yl0KVxUZFsacRXjpU16BgrtiWR2EuqUUnolXBEPciP33QTa6UbXU1o46ksi5YFDjCDEtGpa4BOB20Kfzn4EwHhvYTBpXqwpi+V5l2Wa8ljuTVlMzHvEx9hoZD4WqkFADk+OtSIJH2qsM2tywoDhSwpA5uj+rJKXYn/qetIaHMAb4V3ayKjA5IqI0JKjNZ0gsD09fmMD0TtL6jq/zzeMmbAil1nrWiPZLxNUM5MMeRMUFL2udsUB/h+G0slhI7bn69SrOjdp8k4g2pZwa9PnGCn0JktrXd+XhIkiuQYxpoNVeG4FbImvc5HV4XNjmn1VVvgaJqP7lV4fg==");
                configModule.setMerchantId("J01043100000000");
                BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
                brandMerchantModule.setMemberId("J01043100002051");
                brandMerchantModule.setMerchantSn("*************");
                BizBankAccountRes bankAccount = new BizBankAccountRes();
                bankAccount.setHolder("周彤");
                bankAccount.setNumber("6217002720008958899");
                BindBankCardRequest bindBankCardRequest = BindBankCardRequest.buildUnbindRequest(configModule,brandMerchantModule,bankAccount);
                BindBankCardResponse call = tripartiteSystemCallService.call(bindBankCardRequest, BindBankCardResponse.class, configModule);
                System.out.println(JSON.toJSONString(call));
                if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(call.getResultCode())){
                }
            }
        });
    }

    @Test
    public void testFuiou(){
        System.out.println(UUID.randomUUID());
        tripartiteSystemCallServices.forEach(tripartiteSystemCallService -> {
            if (tripartiteSystemCallService.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.FUIOU)){
                FuiouConfigModule configModule = new FuiouConfigModule();
                configModule.setPrivateKey("MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJMr8NnRV7ve7Y5FEBium/TsU0fK5NvzvFpsYxPAQhBXY+EN0Bi2JEg790C1njk9Q3U36u2JBDHAiDIomlgh6wWkJsFn7dghV/fCWSX1VVJ+dRINZy1432fRaJ8GqspvMneBpeLjBe94IwlWKpN+AOR+BNX8QL/uHmfCPlVQXos9AgMBAAECgYAzqbMs434m50UBMmFKKNF6kxNRGnpodBFktLO7FTybu/HF6TFp21a1PMe5IYhfk5AAsBZ6OCUOygWFhhdYZN+5W+dweF3kp1rLE4y5CjwqNlk/g22TAndf9znh/ltHFLvITToqu/eh/34tE1gyNxRbsi1olw/1wv8ZRjM3vtM9QQJBANvNwFq+CJHUyFzkXQB7+ycQFnY8wDq8Uw2Hv9ZMjgIntH7FSlJtdu5mAYPPo6f74slO5tFUMNP7EVppqsjYaNkCQQCraD6iKHo+OIlvvYIKiMXatJGD7N1GNhq5CrhUNPWLHwv/Ih2D3JJdF8IUZOPIJfUxTfM2fZYI+EVdsv6s4RcFAkAGjNYbnighOGcUJZYD6q3sVxVkRqEv3ubWs2HrH/Lna4l8caKqXCq8JfwLkod8/QugFiLYwBqIZqX4vMdjHtfZAkBsAl9dbWZCaPvpxp/4JWGPxDLhz9NLV/KU4bVvkoObq++yUHwKyGYOdVcd5MlIKOsNq5Hzp0Vw14lWVuF2bMxFAkBuNrZksvUULNIaWDKd4rQ6GVzUxXuIZW0ZE6atHYDiXPB4jVAjKRtLxZAV1qH9cr1zNJlcg+RbGYUdF9t4A9n5" );
                configModule.setMerchantNo("0002900F0370542");
                configModule.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCTK/DZ0Ve73u2ORRAYrpv07FNHyuTb87xabGMTwEIQV2PhDdAYtiRIO/dAtZ45PUN1N+rtiQQxwIgyKJpYIesFpCbBZ+3YIVf3wlkl9VVSfnUSDWcteN9n0WifBqrKbzJ3gaXi4wXveCMJViqTfgDkfgTV/EC/7h5nwj5VUF6LPQIDAQAB");
                configModule.setFyPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGg8ORm9u3s8o60tcgD7ZsL/0WR5vbR0krgmOkpm3yt1hgBoYABoUhiLTTt6V6N4RJg9YK2ku9J3hTNPmiGbfaUobcYs/9RLM1TCw9xyJESoQSRXKvKdIFh/pdAaZdMBsZX+Ltpk5H+PdGBIj/lvxMhJ5LELKCHR6bJ/v62dtApwIDAQAB");
                configModule.setFyPrivateKey("MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIaDw5Gb27ezyjrS1yAPtmwv/RZHm9tHSSuCY6SmbfK3WGAGhgAGhSGItNO3pXo3hEmD1graS70neFM0+aIZt9pShtxiz/1EszVMLD3HIkRKhBJFcq8p0gWH+l0Bpl0wGxlf4u2mTkf490YEiP+W/EyEnksQsoIdHpsn+/rZ20CnAgMBAAECgYB1r65ZJJ10+Y3DLVgdquGVgd7RsVEA5jt0H54CHcIwCoz9ZneyagHsNujOGuxiI1RP5VJNKHP/SBsT4VNOqWWaDoVn97thBr+JYCZX0SMdNznSEPMuZu6GKNSeuDl5alKZMB6CumIJmjl6JlDUeUturqbv5XwW1FaiNtrWE7x4sQJBAMEkNvj1hyKtopoLsOca5ydpR/vLOfQgPXGlKHRcOJfj45T3bSYCZ6fHc1t6IpvFUVqYElY1pLA8JYPc9PQaePMCQQCySv+mZfkAz3f2V0U8aw9nfbks4vnUi9XS3e+V593NaJ6IVX3M6sSXAHWJFgezEt7LdB+d54taMpKVeyUDpYZ9AkB/BjBZYDF2LzhHk/TOqbTpCKbdBPWihymh+ns2vAhEbQ6aRHg2jVJa2CQYP6VPSWCN8oHszO75MTWDGejIOjjdAkAKJb6bJ96eLzCyspDcOXOs/jjV1y1E7ZiD4eHK9GFpWXT8aXE5gnsh5QLLhJd3l7Fafwd1o0IJJiu1mkanCHq5AkEAwMfKw/GCAj8i+c7hHTccO0d69fC+fGDakb5zzTpzwNi/6VVIKYG6idOmnHqknGrj9h0UR3sKdE0iXvSBuHHBdg==");
                QueryAllocateAccountRequest request = new QueryAllocateAccountRequest();
                request.setBody(new QueryAllocateAccountRequestBody(configModule.getMerchantNo()));
                request.getBody().setTraceNo(String.valueOf(System.currentTimeMillis()));
                request.getBody().setAccountIn("037054220240918500002108209");
                QueryAllocateAccountResponse call = tripartiteSystemCallService.call(request, QueryAllocateAccountResponse.class, configModule);
                System.out.println(JSON.toJSONString(call));

                ActiveAllocateAccountRequest activeAllocateAccountRequest = new ActiveAllocateAccountRequest();
                activeAllocateAccountRequest.setBody(new ActiveAllocateAccountRequestBody(configModule.getMerchantNo()));
                activeAllocateAccountRequest.getBody().setTraceNo(String.valueOf(System.currentTimeMillis()));
                activeAllocateAccountRequest.getBody().setAccountIn("037054220240918500002107798");
                activeAllocateAccountRequest.getBody().setCheckType("2");
                ActiveAllocateAccountResponse activeAllocateAccountResponse = tripartiteSystemCallService.call(activeAllocateAccountRequest, ActiveAllocateAccountResponse.class, configModule);
                System.out.println(JSON.toJSONString(activeAllocateAccountResponse));

                QueryConcentrateRelationRequest queryConcentrateRelationRequest = new QueryConcentrateRelationRequest();
                queryConcentrateRelationRequest.setBody(new QueryConcentrateRelationRequestBody(configModule.getMerchantNo()));
                queryConcentrateRelationRequest.getBody().setTraceNo(String.valueOf(System.currentTimeMillis()));
                queryConcentrateRelationRequest.getBody().setMchntCdConcentrate("0002900F0096235");
                queryConcentrateRelationRequest.getBody().setType("02");
                QueryConcentrateRelationResponse queryConcentrateRelationResponse = tripartiteSystemCallService.call(queryConcentrateRelationRequest, QueryConcentrateRelationResponse.class, configModule);
                System.out.println(JSON.toJSONString(queryConcentrateRelationResponse));
            }
        });
    }


    @Test
    public void test2()
    {
        // 先查查到了不登记，查不到再登记
        RegisterBehaviorRecordInfoRequest registerBehaviorRecordInfoRequest = new RegisterBehaviorRecordInfoRequest("2330000000121");
        registerBehaviorRecordInfoRequest.setMemberId("200000620017");
        registerBehaviorRecordInfoRequest.setFunctionFlag("2");
        String s = vfinanceInterfaceService.invokeService(registerBehaviorRecordInfoRequest);
        VfinanceBaseResponse response = JSON.parseObject(s, VfinanceBaseResponse.class);
        System.out.println(JSON.toJSONString(response));
    }
}
