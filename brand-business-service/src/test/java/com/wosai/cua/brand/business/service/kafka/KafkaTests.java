package com.wosai.cua.brand.business.service.kafka;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.kafka.BrandMerchantEnrollMsg;
import com.wosai.cua.brand.business.api.enums.EnrollChannelEnum;
import com.wosai.cua.brand.business.api.enums.MessageTypeEnum;
import com.wosai.cua.brand.business.service.ApplicationTests;
import com.wosai.cua.brand.business.service.kafka.helper.AvroBeanHelper;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


public class KafkaTests extends ApplicationTests {

    @Autowired
    private BrandBaseKafkaProducer brandBaseKafkaProducer;

    @Test
    public void testSendMsg(){
        brandBaseKafkaProducer.sendMessage(brandBaseKafkaProducer.getTopic(), AvroBeanHelper.getBaseBrandMsg(JSON.toJSONString(BrandMerchantEnrollMsg.builder().brandId("12312313").merchantSn("123213131").enrollChannelEnum(EnrollChannelEnum.PING_AN_WEI_JIN)), MessageTypeEnum.ENROLL));
    }
}
