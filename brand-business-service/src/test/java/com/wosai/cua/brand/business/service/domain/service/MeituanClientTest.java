package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.domain.tripartite.meituan.client.MeituanClient;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.request.BillListRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.response.BaseResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.response.BillListResponse;
import com.wosai.upay.common.util.JacksonUtil;
import org.junit.Test;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

public class MeituanClientTest {

    @Test
    public void testQueryBillList() throws Exception {
        long readTimeout = 30000;
        long connectTimeout = 30000;
        String appId = "7967";
        String signKey = "f9af4ad73aa90dc3035522f081d714b8";
        MeituanClient meituanClient = new MeituanClient(readTimeout, connectTimeout, appId, signKey);
        LocalDate start = LocalDate.of(2024, 7, 31);
        LocalDate end = LocalDate.of(2024, 7, 31);
        Long platformChargeFee2 = 0L;
        do {
            LocalDate currentEnd = start.plusDays(1);
            BillListRequest request = new BillListRequest();
            BaseResponse response;
            int offset = 0;
            request.setAppPoiCode("13971387");
            request.setStartDate(start.atStartOfDay().toEpochSecond(ZoneOffset.ofHours(8)));
            request.setEndDate(currentEnd.atStartOfDay().toEpochSecond(ZoneOffset.ofHours(8)) - 1);
            request.setOffset(offset);
            response = meituanClient.queryAuthorizationStatus(request);
            System.out.println(JacksonUtil.toJsonString(response));
            start = currentEnd;
        } while (start.isBefore(end) || start.isEqual(end));
        System.out.println(platformChargeFee2);
    }

    @Test
    public void testQueryTotalAmountByDay() throws Exception {
        long readTimeout = 30000;
        long connectTimeout = 30000;
        String appId = "7967";
        String signKey = "f9af4ad73aa90dc3035522f081d714b8";
        MeituanClient meituanClient = new MeituanClient(readTimeout, connectTimeout, appId, signKey);
        LocalDate start = LocalDate.of(2024, 7, 30);
        LocalDate end = LocalDate.of(2024, 8, 2);
        List<String> sns = Arrays.asList(
                "12646461",
                "12108050",
                "3832183",
                "13424832",
                "15390583",
                "12206603",
                "11186936",
                "15421465",
                "18088349",
                "17669563",
                "3764174",
                "3764484",
                "11358926",
                "16744443",
                "18267305",
                "17738136",
                "10852164",
                "10530710",
                "10531225",
                "10531227",
                "11130571",
                "12890584",
                "20493463",
                "10686647",
                "11846159",
                "10531223",
                "11330145",
                "11825828",
                "11953544",
                "20582888",
                "19287328",
                "10686162",
                "3764475",
                "11475970",
                "12921006",
                "19266220",
                "13583553",
                "10566645",
                "11932620",
                "10515211",
                "12406102",
                "11833811",
                "12913111",
                "14674349",
                "17641736",
                "18783466",
                "3832170",
                "13973838",
                "11634816",
                "12920882",
                "13972055",
                "12656693",
                "11102092",
                "11809183",
                "12979797",
                "11509631",
                "12159271",
                "12935814",
                "11393580",
                "12572720",
                "10565830",
                "13036426",
                "19139480",
                "21176705",
                "19857235",
                "12880989",
                "10417096",
                "3764152",
                "11846677",
                "19330911",
                "11925718",
                "3764494",
                "12411834",
                "11048256",
                "11962148",
                "12304471",
                "3764146",
                "10546396",
                "3764192",
                "10515216",
                "12647962",
                "12396654",
                "12703760",
                "11395011",
                "11768495",
                "15587164",
                "15588552",
                "15745331",
                "11498212",
                "10546395",
                "11913352",
                "11666938",
                "3764154",
                "13712831",
                "11938482",
                "15744372",
                "11933711",
                "13712803",
                "14232179",
                "11950846",
                "12066404",
                "18126846",
                "13073427",
                "13115406",
                "12392112",
                "17755497",
                "3840406",
                "12979704",
                "3764180",
                "18789650",
                "10517227",
                "13216565",
                "11786270",
                "11937458",
                "12192494",
                "12885807",
                "13864633",
                "15310213",
                "13408705",
                "11787230",
                "3764477",
                "20089671",
                "21030980",
                "12458317",
                "3832171",
                "3764495",
                "18924750",
                "15275350",
                "14616755",
                "16740193",
                "12449631",
                "20693433",
                "12654106",
                "17401471",
                "10545871",
                "12672975",
                "17610065",
                "3764169",
                "11923672",
                "10550770",
                "16843882",
                "11973621",
                "21034652",
                "12159166",
                "11919489",
                "13204071",
                "11797422",
                "16629060",
                "11492607",
                "10575462",
                "12446067",
                "12177078",
                "11771300",
                "3764181",
                "13331865",
                "18643234",
                "10566590",
                "3764155",
                "17608118",
                "13581433",
                "10515212",
                "13204139",
                "12921301",
                "12979498",
                "11243918",
                "11974536",
                "3764186",
                "11968599",
                "12927774",
                "12163366",
                "11810636",
                "15959088",
                "20813340",
                "15209826",
                "17974836",
                "13204485",
                "11596882",
                "10687044",
                "18975306",
                "12999924",
                "3764193",
                "11714285",
                "11391244",
                "13410370",
                "12884275",
                "3764148",
                "10550324",
                "3764175",
                "11992838",
                "19724984",
                "12937130",
                "12718805",
                "11384813",
                "18783153",
                "16868347",
                "18662847",
                "14758232",
                "16805290",
                "20586026",
                "19227108",
                "17759548",
                "20025305",
                "19745620",
                "18783545",
                "15292908",
                "17094950",
                "17328009",
                "15534870",
                "16285950",
                "20620324",
                "17929114",
                "17148720",
                "17400168",
                "17148432",
                "18957966",
                "17769221",
                "18785855",
                "17653944",
                "20410050",
                "10531226",
                "11932531",
                "10531222",
                "15310671",
                "12534635",
                "12497363",
                "11861939",
                "11708760",
                "11542564",
                "12675873",
                "11215625",
                "20585118",
                "10550861",
                "20827602",
                "12709711",
                "20553532",
                "20613275",
                "11434065",
                "19779734",
                "18919571",
                "11922567",
                "3764491",
                "19336351",
                "11394827",
                "11872058",
                "12190641",
                "10939741",
                "12691187",
                "13216865",
                "10514174",
                "12429779",
                "16676828",
                "20560224",
                "20634084",
                "12927975",
                "3764190",
                "11932589",
                "20617157",
                "20585763",
                "19099626",
                "22108984",
                "22074800",
                "20634241",
                "18156160",
                "20585847",
                "20634243",
                "20585761",
                "20585851",
                "16859472");
        long total = 0L;
        do {
            LocalDate currentEnd = start.plusDays(1);
            for (String sn : sns) {
                try {
                    BillListRequest request = new BillListRequest();
                    BillListResponse response = null;
                    int offset = 0;
                    do {
                        request.setAppPoiCode(sn);
                        request.setStartDate(start.atStartOfDay().toEpochSecond(ZoneOffset.ofHours(8)));
                        request.setEndDate(currentEnd.atStartOfDay().toEpochSecond(ZoneOffset.ofHours(8)) - 1);
                        request.setOffset(offset);
                        response = meituanClient.queryBillList(request);
                        if (response.getData() == null || response.getData().isEmpty()) {
                            continue;
                        }
                        if (!response.getData().get(0).getSettleBillDesc().endsWith("2024-08-02")) {
                            continue;
                        }
                        String amount = response.getExtraInfo().getSettleSum();
                        amount = amount.replace(".00", "");
                        System.out.println(String.format("%s-%s: %s", currentEnd.minusDays(1).format(DateTimeFormatter.ISO_DATE), sn, amount));
                        total += Long.valueOf(amount);
                    } while (response.getData().size() == request.getLimit());
                } catch (Exception e) {
                    System.out.printf("%s-%s: %s%n", currentEnd.minusDays(1).format(DateTimeFormatter.ISO_DATE), sn, "error");
                }
            }
            start = currentEnd;
        } while (start.isBefore(end) || start.isEqual(end));
        System.out.println(total);
    }
}
