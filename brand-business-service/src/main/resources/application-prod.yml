spring:
  redis-cluster:
    host: r-bp1f11c0dda79d14.redis.rds.aliyuncs.com
    port: 6379
    password: tyY3mcXZ(zJl!9o6
    database: 3
    pool:
      max-active: 16
      max-idle: 16
      min-idle: 0
      max-wait: 1500
  datasource:
    master:
      url: pk-brand-business-brand_business-8960?useTimezone=true&serverTimezone=GMT%2B8
      initialSize: 3
      maxActive: 15
      maxIdle: 15
      minIdle: 3
      test-while-idle: true
      validation-query: select 1 from dual
      validation-interval: 60000
      time-between-eviction-runs-millis: 60000
      driver-class-name: com.mysql.cj.jdbc.Driver
    slave:
      url: pk-brand-business-brand_business-8023?useTimezone=true&serverTimezone=GMT%2B8
      initialSize: 3
      maxActive: 15
      maxIdle: 15
      minIdle: 3
      test-while-idle: true
      validation-query: select 1 from dual
      validation-interval: 60000
      time-between-eviction-runs-millis: 60000
      driver-class-name: com.mysql.cj.jdbc.Driver

# redisson
redisson:
  redis:
    host: r-bp1f11c0dda79d14.redis.rds.aliyuncs.com
    port: 6379
    password: tyY3mcXZ(zJl!9o6
    database: 3

xxl:
  job:
    admin-addresses: http://ft-xxl-job-admin
    log-retention-days: 30
    app-name: ${spring.application.name}
    port: 9999
    access-token: ${ACCESS_TOKEN}
    log-path: /var/logs/ft-xxl-job

core-business: http://app-core-business/
merchant-center: http://merchant-center/
merchant-bank-service: http://merchant-bank-service/
merchant-user-service: http://merchant-user-service/
uc-user-service: http://uc-user-service/
core-crypto-service: http://core-crypto/
bank-info: http://bank-info-service/
crow-api: http://crow-server.internal.shouqianba.com/
upay-transaction: http://upay-transaction-query.internal.shouqianba.com/
sp-workflow-service: http://sp-workflow-service/
merchant-business-open: http://merchant-business-open/
trade-manage-service: http://trade-manage-service/
sales-system-poi: http://sales-system-poi/
merchant-contract-job: http://merchant-contract-job/
pay-business-open: http://pay-business-open/
merchant-contract-access: http://merchant-contract-access/
sales-system-service: http://sales-system-service/
short-url-service: http://short-url/
marketing-prepaid-card: http://marketing-saas-prepaid-card/
profit-sharing-proxy: http://profit-sharing-proxy/
shouqianba-tools-service: http://shouqianba-tools-service/

appid:
  indirect: 728d74a2-103a-4e67-937f-0baa3216865c
  ## 这个待定
  paymentHub: xxx
  brandPaymentHub: ab6d6bf7-6990-49ab-9dbc-34d0e6b31d76
devCode:
  brandPaymentHub: NJADYTYQGBQZ

uc:
  secret: c4cba5957d4e41efb83be3f76b4b51a4

core:
  crypto:
    client:
      access_id: a80c8c7c-cd08-4426-b866-c0f2d1e2f9df
      access_secret: ********************************

tmp:
  folder: /brand/tmp/

#维金接口请求地址
vygin-service-address: http://vintra.vpc.shouqianba.com/mgs/service.do
#网商银行接口请求地址
#ant.mybank.merchantprod.merch.applet.register.query
#ant.mybank.bkcloudfunds.vostro.batchquery
#ant.mybank.bkcloudbatch.batch.create
#ant.mybank.bkcloudbatch.batch.query
#ant.mybank.bkmerchantprod.merch.applet.pre.register
#ant.mybank.merchantprod.merchant.arrangement.audit
#ant.mybank.merchantprod.merchant.arrangement.info.query
#ant.mybank.bkcloudfunds.order.withhold.apply
#ant.mybank.bkcloudfunds.merchant.account.unfreeze.apply
#ant.mybank.bkcloudfunds.merchant.balance.unfreeze.query
#ant.mybank.bkcloudfunds.protocol.withhold.query
#ant.mybank.bkcloudfunds.protocol.withhold.refund.apply
#ant.mybank.bkcloudfunds.protocol.withhold.refund.query
#以上 head头 AppId  I   大写，请求mybank-service-address
mybank-service-address: https://bkgw.mybank.cn/open/lite/api/common/request.htm
#  ant.mybank.bkcloudfunds.withdraw.apply
#  ant.mybank.bkcloudfunds.withdraw.applyconfirm
#  ant.mybank.bkcloudfunds.withdraw.query
#  ant.mybank.bkcloudfunds.bill.pay
#  ant.mybank.merchantprod.merch.register.query
#  ant.mybank.bkcloudfunds.merchant.scene.balance.query
#  ant.mybank.bkcloudfunds.account.balance.query
#  ant.mybank.bkcloudfunds.recon.query
#  ant.mybank.bkcloudfunds.account.open
#  ant.mybank.bkcloudfunds.account.query
#  以上 head头 Appid  i   小写，请求mybank-fcsupergw-address
mybank-fcsupergw-address: https://fcopen.mybank.cn/open/api/common/request2.htm
# 中信银行接口请求地址
citic-service-address: https://laas.citicbank.cn/api/public
# 富友接口请求地址
fuiou-service-address: https://richfront.fuioupay.com

feign:
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: full

mybatis-plus:
  global-config:
    db-config:
      id-type: auto

brand:
  kafka:
    bootstrap-servers: aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
    schema:
      registry:
        url: http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
    base:
      topic: events_CUA_base-brand-business
    data-center:
      topic: analytics_data_volcengine_push
    old:
      bootstrap-servers: conflunt-kafka-01.shouqianba.com:9092,conflunt-kafka-02.shouqianba.com:9092,conflunt-kafka-03.shouqianba.com:9092 ,conflunt-kafka-04.shouqianba.com:9092,conflunt-kafka-05.shouqianba.com:9092
      schema:
        registry:
          url: http://conflunt-schema-01.shouqianba.com:8081,http://conflunt-schema-02.shouqianba.com:8081,http://conflunt-schema-03.shouqianba.com:8081,http://conflunt-schema-04.shouqianba.com:8081,http://conflunt-schema-05.shouqianba.com:8081

#飞书相关配置
open:
  feishu:
    appid: cli_a637d2a389f3d013
    appSecret: 0F6c0EwdFGQgObOdUMHCQfACJWvjQjFJ
    message_robot_secret: KDbIvIMPyjwlKGBa1yrSEc
    message_robot_url: https://open.feishu.cn/open-apis/bot/v2/hook/8dc18ef8-e91c-4e21-9abc-369859da63e4

fuiou:
  open:
    account:
      check_type: 1

app-id:
  merchant: ********

aggregation:
  open:
    notify-url: http://brand-business/internal/callback/aggregationOpenNotify
databus:
  consumer:
    topic: databus.event.merchant.basic.allin
consumer:
  init: true