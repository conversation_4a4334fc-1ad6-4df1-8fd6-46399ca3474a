<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandSubTaskMapper">
  <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandSubTaskDO">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.cua.brand.business.service.domain.entity.BrandSubTaskDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sub_task_context" jdbcType="LONGVARCHAR" property="subTaskContext" />
    <result column="task_result" jdbcType="LONGVARCHAR" property="taskResult" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, task_id, task_status, ctime, mtime
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    sub_task_context, task_result
  </sql>
  <insert id="batchInsertSubTasks">
    insert into brand_sub_task
    (task_id, sub_task_context)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId}, #{item.subTaskContext})
    </foreach>
  </insert>
  <select id="selectByTaskId" resultType="com.wosai.cua.brand.business.service.domain.entity.BrandSubTaskDO">
    select * from brand_sub_task where task_id = #{taskId}
  </select>
</mapper>