<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandSmsTemplateConfigDOMapper">

    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandSmsTemplateConfigDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
        <result property="method" column="method" jdbcType="VARCHAR"/>
        <result property="templateCode" column="template_code" jdbcType="VARCHAR"/>
        <result property="terminalCode" column="terminal_code" jdbcType="VARCHAR"/>
        <result property="textContent" column="text_content" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
        <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,brand_id,method,
        template_code,terminal_code,text_content,deleted,
        ctime,mtime
    </sql>
</mapper>
