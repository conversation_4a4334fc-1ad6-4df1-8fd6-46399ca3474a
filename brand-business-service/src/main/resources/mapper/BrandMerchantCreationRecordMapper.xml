<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandMerchantCreationRecordMapper">
    <resultMap id="BaseResultMap"
               type="com.wosai.cua.brand.business.service.domain.entity.BrandMerchantCreationRecordDO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="record_id" jdbcType="VARCHAR" property="recordId"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="merchant_id" jdbcType="VARCHAR" property="merchantId"/>
        <result column="merchant_sn" jdbcType="VARCHAR" property="merchantSn"/>
        <result column="result" jdbcType="VARCHAR" property="result"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="need_retry" jdbcType="TINYINT" property="needRetry"/>
        <result column="retry_num" jdbcType="TINYINT" property="retryNum"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id,record_id, brand_id, merchant_id,merchant_sn, `result`, `status`, need_retry, retry_num, created_time,
        updated_time
    </sql>
    <insert id="batchCreateBrandMerchantCreationRecord">
        INSERT INTO brand_business.brand_merchant_creation_record
        (brand_id, merchant_id, merchant_sn, `result`, status, need_retry)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.brandId},
            #{item.merchantId},
            #{item.merchantSn},
            <choose>
                <when test="item.result == null">
                    '',
                </when>
                <otherwise>
                    #{item.result},
                </otherwise>
            </choose>
            <choose>
                <when test="item.status == null">
                    0,
                </when>
                <otherwise>
                    #{item.status},
                </otherwise>
            </choose>
            <choose>
                <when test="item.needRetry == null">
                    0
                </when>
                <otherwise>
                    #{item.needRetry}
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>
    <insert id="insertCreationRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO brand_merchant_creation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">
                record_id,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="merchantId != null">
                merchant_id,
            </if>
            <if test="merchantSn != null">
                merchant_sn,
            </if>
            <if test="result != null">
                `result`,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="needRetry != null">
                need_retry,
            </if>
            <if test="retryNum != null">
                retry_num
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">
                #{recordId},
            </if>
            <if test="brandId != null">
                #{brandId},
            </if>
            <if test="merchantId != null">
                #{merchantId},
            </if>
            <if test="merchantSn != null">
                #{merchantSn},
            </if>
            <if test="result != null">
                #{result},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="needRetry != null">
                #{needRetry},
            </if>
            <if test="retryNum != null">
                #{retryNum}
            </if>
        </trim>
    </insert>
</mapper>