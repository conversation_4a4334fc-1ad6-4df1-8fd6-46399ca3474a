<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandWithdrawStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandWithdrawStrategyDO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="withdraw_type" jdbcType="VARCHAR" property="withdrawType"/>
        <result column="withdraw_cycle_type" jdbcType="VARCHAR" property="withdrawCycleType"/>
        <result column="withdraw_cycle_times" jdbcType="VARCHAR" property="withdrawCycleTimes"/>
        <result column="min_withdrawal_amount" jdbcType="INTEGER" property="minWithdrawalAmount"/>
        <result column="reserved_amount" jdbcType="INTEGER" property="reservedAmount"/>
        <result column="applicable_scene" jdbcType="VARCHAR" property="applicableScene"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, brand_id, strategy_id, withdraw_type, withdraw_cycle_type,withdraw_cycle_times, withdraw_cycle_time,
        min_withdrawal_amount,reserved_amount,
        applicable_scene,`remark`, deleted, created_time, updated_time
    </sql>
    <update id="deleteStrategyByStrategyIdList">
        UPDATE brand_withdraw_strategy
        <set>
            deleted = 1
        </set>
        <where>
            <if test="strategyIdList != null and strategyIdList.size() > 0">
                strategy_id IN
                <foreach collection="strategyIdList" item="strategyId" separator="," close=")" open="(">
                    #{strategyId}
                </foreach>
            </if>
        </where>
    </update>
    <update id="updateBrandWithdrawStrategyById">
        UPDATE brand_withdraw_strategy
        <set>
            <choose>
                <when test="withdrawCycleType == null">
                    withdraw_cycle_type = '',
                </when>
                <otherwise>
                    withdraw_cycle_type = #{withdrawCycleType},
                </otherwise>
            </choose>
            <choose>
                <when test="minWithdrawalAmount == null">
                    min_withdrawal_amount = 0,
                </when>
                <otherwise>
                    min_withdrawal_amount = #{minWithdrawalAmount},
                </otherwise>
            </choose>
            <choose>
                <when test="reservedAmount == null">
                    reserved_amount = 0,
                </when>
                <otherwise>
                    reserved_amount = #{reservedAmount},
                </otherwise>
            </choose>
            <choose>
                <when test="applicableScene == null">
                    applicable_scene = '',
                </when>
                <otherwise>
                    applicable_scene = #{applicableScene},
                </otherwise>
            </choose>
            <choose>
                <when test="withdrawType == null">
                    withdraw_type = '',
                </when>
                <otherwise>
                    withdraw_type = #{withdrawType},
                </otherwise>
            </choose>
            <choose>
                <when test="withdrawCycleTimes == null">
                    withdraw_cycle_times = '',
                </when>
                <otherwise>
                    withdraw_cycle_times = #{withdrawCycleTimes},
                </otherwise>
            </choose>
            <choose>
                <when test="withdrawCycleTime == null">
                    withdraw_cycle_time = 99,
                </when>
                <otherwise>
                    withdraw_cycle_time = #{withdrawCycleTime},
                </otherwise>
            </choose>
            <choose>
                <when test="remark == null">
                    remark = '',
                </when>
                <otherwise>
                    remark = #{remark},
                </otherwise>
            </choose>
        </set>
        <where>
            id = #{id}
        </where>
    </update>
    <select id="getBrandWithdrawStrategyByStrategyId"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_withdraw_strategy
        <where>
            strategy_id = #{strategyId} AND deleted = 0
        </where>
    </select>
    <select id="getBrandWithdrawStrategyListByBrandId"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_withdraw_strategy
        <where>
            brand_id = #{brandId} AND deleted = 0
        </where>
    </select>
    <select id="getBrandWithdrawStrategyByStrategyIdList"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_withdraw_strategy
        <where>
            <if test="strategyIdList != null and strategyIdList.size() > 0">
                strategy_id IN
                <foreach collection="strategyIdList" item="strategyId" open="(" separator="," close=")">
                    #{strategyId}
                </foreach>
            </if>
            AND deleted = 0
        </where>
    </select>

    <select id="countStrategyByBrandId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM brand_withdraw_strategy WHERE brand_id = #{brandId} AND deleted = 0
    </select>

    <select id="pageStrategyByBrandId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM brand_withdraw_strategy WHERE brand_id = #{brandId} AND deleted = 0 ORDER BY updated_time LIMIT #{offset},#{pageSize}
    </select>
</mapper>