<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandTaskLogMapper">
    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandTaskLogDO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="task_type" jdbcType="TINYINT" property="taskType"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="task_context" jdbcType="VARCHAR" property="taskContext"/>
        <result column="task_status" jdbcType="TINYINT" property="taskStatus"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="task_result" jdbcType="VARCHAR" property="taskResult"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, task_id, brand_id,task_name, task_type, platform, user_id, task_context, task_status,task_result, created_time, updated_time,task_result
    </sql>
    <select id="countBrandTaskLog" resultType="java.lang.Long">
        SELECT
        COUNT(id)
        FROM brand_task_log
        <where>
            <if test="brandId != null">
                brand_id = #{brandId}
            </if>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
            <if test="platform != null">
                AND platform = #{platform}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
        </where>
    </select>
    <select id="pageBrandTaskLogList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_task_log
        <where>
            <if test="conditions.brandId != null">
                brand_id = #{conditions.brandId}
            </if>
            <if test="conditions.taskType != null">
                AND task_type = #{conditions.taskType}
            </if>
            <if test="conditions.platform != null">
                AND platform = #{conditions.platform}
            </if>
            <if test="conditions.userId != null">
                AND user_id = #{conditions.userId}
            </if>
        </where>
        ORDER BY created_time DESC
        LIMIT #{offset},#{pageSize}
    </select>
    <select id="getAuditImportTaskLog"
            resultType="com.wosai.cua.brand.business.service.domain.entity.BrandTaskLogDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_task_log
        where task_status = 0
          and task_type in
        <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
            #{taskType}
        </foreach>
          and created_time > #{start,jdbcType=TIMESTAMP}
        order by created_time desc limit #{size}
    </select>
</mapper>