<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandMerchantControlRecordsMapper">

    <resultMap id="BaseResultMap"
               type="com.wosai.cua.brand.business.service.domain.entity.BrandMerchantControlRecordsDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
        <result property="outTradeNo" column="out_trade_no" jdbcType="VARCHAR"/>
        <result property="subAccountNo" column="sub_account_no" jdbcType="VARCHAR"/>
        <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
        <result property="controlOrderNo" column="control_order_no" jdbcType="VARCHAR"/>
        <result property="controlNotifyType" column="control_notify_type" jdbcType="VARCHAR"/>
        <result property="punishDate" column="punish_date" jdbcType="BIGINT"/>
        <result property="controlCategory" column="control_category" jdbcType="VARCHAR"/>
        <result property="controlStrategy" column="control_strategy" jdbcType="VARCHAR"/>
        <result property="changeReason" column="change_reason" jdbcType="VARCHAR"/>
        <result property="canUpgrade" column="can_upgrade" jdbcType="TINYINT"/>
        <result property="upgradeUrl" column="upgrade_url" jdbcType="VARCHAR"/>
        <result property="effectiveTime" column="effective_time" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
        <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,out_trade_no,sub_account_no,
        merchant_sn,control_order_no,control_notify_type,
        punish_date,control_category,control_strategy,
        change_reason,can_upgrade,upgrade_url,
        effective_time,deleted,ctime,
        mtime
    </sql>
</mapper>
