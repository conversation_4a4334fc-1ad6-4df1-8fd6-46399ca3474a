<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper">

<!--    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO">-->
<!--            <id property="id" column="id" jdbcType="BIGINT"/>-->
<!--            <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>-->
<!--            <result property="channelId" column="channel_id" jdbcType="VARCHAR"/>-->
<!--            <result property="channelType" column="channel_type" jdbcType="VARCHAR"/>-->
<!--            <result property="config" column="config" jdbcType="VARCHAR"/>-->
<!--            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>-->
<!--            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        id,brand_id,channel_id,channel_type,config,-->
<!--        created_time,updated_time-->
<!--    </sql>-->
</mapper>
