<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandMerchantLicensingAgreementMapper">

    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandMerchantLicensingAgreementDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
            <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="context" column="context" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,brand_id,merchant_id,
        type,url,context,
        deleted,ctime,mtime
    </sql>
</mapper>
