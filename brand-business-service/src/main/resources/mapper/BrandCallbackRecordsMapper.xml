<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandCallbackRecordsMapper">

    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandCallbackRecordsDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="fundManagementCompanyCode" column="fund_management_company_code" jdbcType="VARCHAR"/>
        <result property="tradeNo" column="trade_no" jdbcType="VARCHAR"/>
        <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="retryTimes" column="retry_times" jdbcType="INTEGER"/>
        <result property="function" column="function" jdbcType="VARCHAR"/>
        <result property="result" column="result" jdbcType="TINYINT"/>
        <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
        <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fund_management_company_code,trade_no,merchant_sn,
        app_id,content,retry_times,`function`,
        result,ctime,mtime
    </sql>
    <select id="pageSelectBrandCallbackRecords"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_callback_records
        <where>
            id <![CDATA[ > ]]> #{id} AND result = 0 AND retry_times <![CDATA[ < ]]> 5
        </where>
        order by id
        limit #{pageSize}
    </select>
</mapper>
