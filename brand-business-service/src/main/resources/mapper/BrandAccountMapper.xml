<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandAccountDOMapper">

    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandAccountDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
            <result property="accounts" column="accounts" jdbcType="VARCHAR"/>
            <result property="createdTime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,brand_id,accounts,
        ctime,mtime
    </sql>
</mapper>
