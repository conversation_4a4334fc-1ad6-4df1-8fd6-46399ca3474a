<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandMerchantBankCardMapper">
    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandMerchantBankCardDO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="merchant_id" jdbcType="VARCHAR" property="merchantId"/>
        <result column="bank_card_id" jdbcType="VARCHAR" property="bankCardId"/>
        <result column="bank_of_deposit" jdbcType="VARCHAR" property="bankOfDeposit"/>
        <result column="account_type" jdbcType="TINYINT" property="accountType"/>
        <result column="reserved_mobile_number" jdbcType="VARCHAR" property="reservedMobileNumber"/>
        <result column="third_bank_card_id" jdbcType="VARCHAR" property="thirdBankCardId"/>
        <result column="member_id" jdbcType="VARCHAR" property="memberId"/>
        <result column="is_default" jdbcType="BIT" property="isDefault"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="activate_fail_reason" jdbcType="VARCHAR" property="activateFailReason"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="activation_time" jdbcType="TIMESTAMP" property="activationTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, brand_id, merchant_id,bank_card_id, bank_of_deposit,
        account_type,reserved_mobile_number, third_bank_card_id,member_id, is_default, deleted, created_time, updated_time,
        activate_fail_reason,`status`,activation_time
    </sql>
    <insert id="batchInsertBankCards" parameterType="java.util.List">
        INSERT INTO brand_merchant_bank_card (brand_id, merchant_id,bank_card_id, bank_of_deposit,
            account_type,reserved_mobile_number, third_bank_card_id,member_id, is_default)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.brandId},
             #{item.merchantId},
             #{item.bankCardId},
             <choose>
                 <when test="item.bankOfDeposit == null">
                     '',
                 </when>
                 <otherwise>
                     #{item.bankOfDeposit},
                 </otherwise>
             </choose>
             #{item.accountType},
             #{item.reservedMobileNumber},
            <choose>
                <when test="item.thirdBankCardId == null">
                    '',
                </when>
                <otherwise>
                    #{item.thirdBankCardId},
                </otherwise>
            </choose>
            <choose>
                <when test="item.memberId == null">
                    '',
                </when>
                <otherwise>
                    #{item.memberId},
                </otherwise>
            </choose>
             #{item.isDefault}
            )
        </foreach>
    </insert>
    <update id="updateAllBankCardIsNotDefault">
        UPDATE brand_merchant_bank_card
        SET is_default = 0
        WHERE brand_id = #{brandId}
          AND merchant_id = #{merchantId}
    </update>
    <select id="pageQueryBankCardsByBrandIdAndMerchantId"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant_bank_card
        <where>
            brand_id = #{brandId} AND merchant_id = #{merchantId} AND deleted = 0
        </where>
        <if test="offset != null and pageSize != null">
            LIMIT #{offset},#{pageSize}
        </if>
    </select>
    <select id="getBrandMerchantBankCardByCardId"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant_bank_card
        <where>
            bank_card_id = #{bankCardId} AND deleted = 0
        </where>
    </select>
    <select id="getDefaultCard"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant_bank_card
        WHERE brand_id = #{brandId} AND merchant_id = #{merchantId} AND is_default = 1 AND deleted = 0 limit 1
    </select>
    <select id="getBankCardModulesByCarIdList"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant_bank_card
        <where>
            brand_id = #{brandId}
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="cardIds != null and cardIds.size() > 0">
                AND bank_card_id IN
                <foreach collection="cardIds" open="(" separator="," close=")" item="cardId">
                    #{cardId}
                </foreach>
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="isDefault != null">
                AND is_default = #{isDefault}
            </if>
            AND deleted = 0
        </where>
    </select>
    <select id="getDefaultCardsByMerchantIds"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant_bank_card
        <where>
            brand_id = #{brandId}
            <if test="merchantIds != null and merchantIds.size() > 0">
                AND merchant_id IN
                <foreach collection="merchantIds" item="merchantId" open="(" separator="," close=")">
                    #{merchantId}
                </foreach>
            </if>
            AND is_default = 1
            AND deleted = 0
        </where>
    </select>
    <select id="getBankCardListByBrandId"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant_bank_card
        <where>
            brand_id = #{brandId}
            <if test="startId != null">
                AND id &gt; #{startId}
            </if>
            AND `status` = 0
            AND is_default = 1
            AND deleted = 0
        </where>
        ORDER BY id ASC
        LIMIT #{pageSize}
    </select>
</mapper>