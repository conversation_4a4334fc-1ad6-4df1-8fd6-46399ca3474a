<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper">
    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="parent_brand_id" jdbcType="VARCHAR" property="parentBrandId"/>
        <result column="merchant_id" jdbcType="VARCHAR" property="merchantId"/>
        <result column="merchant_name" jdbcType="VARCHAR" property="merchantName"/>
        <result column="merchant_sn" jdbcType="VARCHAR" property="merchantSn"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="merchant_type" jdbcType="VARCHAR" property="merchantType"/>
        <result column="merchant_docking_mode" jdbcType="VARCHAR" property="merchantDockingMode"/>
        <result column="payment_mode" jdbcType="VARCHAR" property="paymentMode"/>
        <result column="sub_account_no" jdbcType="VARCHAR" property="subAccountNo"/>
        <result column="top_up_account_name" jdbcType="VARCHAR" property="topUpAccountName"/>
        <result column="top_up_account_no" jdbcType="VARCHAR" property="topUpAccountNo"/>
        <result column="member_id" jdbcType="VARCHAR" property="memberId"/>
        <result column="associated_sqb_store_id" jdbcType="VARCHAR" property="associatedSqbStoreId"/>
        <result column="sqb_store_sn" jdbcType="VARCHAR" property="sqbStoreSn"/>
        <result column="associated_meituan_store_sn" jdbcType="VARCHAR" property="associatedMeituanStoreSn"/>
        <result column="associated_elm_store_sn" jdbcType="VARCHAR" property="associatedElmStoreSn"/>
        <result column="dy_store_sn" jdbcType="VARCHAR" property="dyStoreSn"/>
        <result column="out_merchant_no" jdbcType="VARCHAR" property="outMerchantNo"/>
        <result column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="account_open_status" jdbcType="VARCHAR" property="accountOpenStatus"/>
        <result column="account_open_failure_reason" jdbcType="VARCHAR" property="accountOpenFailureReason"/>
        <result column="extra" jdbcType="VARCHAR" property="extra"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="associated_time" jdbcType="TIMESTAMP" property="associatedTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="bank_card_activate_status" jdbcType="VARCHAR" property="bankCardActivateStatus"/>
        <result column="account_opened_time" jdbcType="TIMESTAMP" property="accountOpenedTime"/>
        <result column="three_party_merchant_sn" jdbcType="VARCHAR" property="threePartyMerchantSn"/>
    </resultMap>
    <resultMap id="countMerchantNumber" type="com.wosai.cua.brand.business.service.domain.entity.CountBrandMerchantNumberDO">
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="merchant_num" jdbcType="INTEGER" property="merchantNum"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, brand_id, parent_brand_id, merchant_id, merchant_name, merchant_sn,type, merchant_type,payment_mode, merchant_docking_mode,
        sub_account_no,top_up_account_name,top_up_account_no, member_id, associated_sqb_store_id,sqb_store_sn, associated_meituan_store_sn,
        associated_elm_store_sn,dy_store_sn,out_merchant_no,strategy_id,account_open_status,account_opened_time, account_open_failure_reason,bank_card_activate_status,
        extra,deleted,
        created_time, updated_time, associated_time, version
    </sql>
    <insert id="batchInsertBrandMerchant" parameterType="java.util.List">
        INSERT INTO brand_merchant
        (brand_id, parent_brand_id, merchant_id, merchant_name,merchant_sn,type,
        merchant_type,merchant_docking_mode,payment_mode,sub_account_no, member_id,top_up_account_no,
        associated_sqb_store_id,sqb_store_sn,
        associated_meituan_store_sn,
        associated_elm_store_sn,dy_store_sn,out_merchant_no,strategy_id,account_open_status,account_open_failure_reason,extra,version,
        three_party_merchant_sn)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.brandId},
            <choose>
                <when test="item.parentBrandId == null">
                    '',
                </when>
                <otherwise>
                    #{item.parentBrandId},
                </otherwise>
            </choose>
            #{item.merchantId},
            #{item.merchantName},
            #{item.merchantSn},
            <choose>
                <when test="item.type == null">
                    '',
                </when>
                <otherwise>
                    #{item.type},
                </otherwise>
            </choose>
            #{item.merchantType},
            <choose>
                <when test="item.merchantDockingMode == null">
                    'SEPARATE_ACCOUNT',
                </when>
                <otherwise>
                    #{item.merchantDockingMode},
                </otherwise>
            </choose>
            <choose>
                <when test="item.paymentMode == null">
                    1,
                </when>
                <otherwise>
                    #{item.paymentMode},
                </otherwise>
            </choose>
            <choose>
                <when test="item.subAccountNo == null">
                    '',
                </when>
                <otherwise>
                    #{item.subAccountNo},
                </otherwise>
            </choose>
            <choose>
                <when test="item.memberId == null">
                    '',
                </when>
                <otherwise>
                    #{item.memberId},
                </otherwise>
            </choose>
            <choose>
                <when test="item.topUpAccountNo == null">
                    '',
                </when>
                <otherwise>
                    #{item.topUpAccountNo},
                </otherwise>
            </choose>
            <choose>
                <when test="item.associatedSqbStoreId == null">
                    '',
                </when>
                <otherwise>
                    #{item.associatedSqbStoreId},
                </otherwise>
            </choose>
            <choose>
                <when test="item.sqbStoreSn == null">
                    '',
                </when>
                <otherwise>
                    #{item.sqbStoreSn},
                </otherwise>
            </choose>
            <choose>
                <when test="item.associatedMeituanStoreSn == null">
                    '',
                </when>
                <otherwise>
                    #{item.associatedMeituanStoreSn},
                </otherwise>
            </choose>
            <choose>
                <when test="item.associatedElmStoreSn == null">
                    '',
                </when>
                <otherwise>
                    #{item.associatedElmStoreSn},
                </otherwise>
            </choose>
            <choose>
                <when test="item.dyStoreSn == null">
                    '',
                </when>
                <otherwise>
                    #{item.dyStoreSn},
                </otherwise>
            </choose>
            <choose>
                <when test="item.outMerchantNo == null">
                    '',
                </when>
                <otherwise>
                    #{item.outMerchantNo},
                </otherwise>
            </choose>
            <choose>
                <when test="item.strategyId == null">
                    0,
                </when>
                <otherwise>
                    #{item.strategyId},
                </otherwise>
            </choose>
            <choose>
                <when test="item.accountOpenStatus == null">
                    'HAVE_NOT_OPENED',
                </when>
                <otherwise>
                    #{item.accountOpenStatus},
                </otherwise>
            </choose>
            <choose>
                <when test="item.accountOpenFailureReason == null">
                    '',
                </when>
                <otherwise>
                    #{item.accountOpenFailureReason},
                </otherwise>
            </choose>
            <choose>
                <when test="item.extra == null">
                    '',
                </when>
                <otherwise>
                    #{item.extra},
                </otherwise>
            </choose>
            0,
            <choose>
                <when test="item.threePartyMerchantSn == null">
                    ''
                </when>
                <otherwise>
                    #{item.threePartyMerchantSn}
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>

    <update id="deleteBrandMerchantByBrandIdAndMerchantId">
        UPDATE brand_merchant SET deleted = 1,version=version+1,associated_sqb_store_id = '',associated_elm_store_sn = '',associated_meituan_store_sn = ''
        <where>
            brand_id = #{brandId}
            <if test="merchantIds != null and merchantIds.size() > 0">
                AND merchant_id IN
                <foreach collection="merchantIds" item="merchantId" open="(" separator="," close=")">
                    #{merchantId}
                </foreach>
            </if>
        </where>
    </update>
    <update id="deleteBrandMerchantWithdrawStrategy">
        UPDATE brand_merchant SET strategy_id = 0
        <where>
            <if test="strategyIds == null or strategyIds.size() == 0">
                strategy_id = 0;
            </if>
            <if test="strategyIds != null and strategyIds.size() > 0">
                strategy_id IN
                <foreach collection="strategyIds" open="(" separator="," close=")" item="strategyId">
                    #{strategyId}
                </foreach>
            </if>
        </where>
    </update>

    <update id="relevanceBrandWithdrawStrategy">
        UPDATE brand_merchant SET strategy_id = #{strategyId}
        <where>
            brand_id = #{brandId} AND merchant_sn IN
            <foreach collection="merchantSnList" item="merchantSn"  open="(" separator="," close=")">
                #{merchantSn}
            </foreach>
        </where>

    </update>

    <select id="selectBrandMerchantByMerchantId"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant WHERE merchant_id = #{merchantId} AND deleted = 0;
    </select>

    <select id="selectBrandMerchantByConditions"
            parameterType="com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant
        <where>
            <if test="brandId != null">
                brand_id = #{brandId}
            </if>
            <if test="brandIds != null and brandIds.size() > 0">
                brand_id IN
                <foreach collection="brandIds" separator="," item="brandId" open="(" close=")">
                    #{brandId}
                </foreach>
            </if>
            <if test="merchantIds != null and merchantIds.size() > 0">
                AND merchant_id IN
                <foreach collection="merchantIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="merchantTypes != null">
                AND merchant_type IN
                <foreach collection="merchantTypes" item="merchantType" open="(" separator="," close=")">
                    #{merchantType}
                </foreach>
            </if>
            <if test="types != null">
                AND type IN
                <foreach collection="types" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="merchantName != null and merchantName.length() > 0">
                AND merchant_name LIKE CONCAT('%',#{merchantName},'%')
            </if>
            <if test="merchantSn != null and merchantSn.length() > 0">
                AND merchant_sn = #{merchantSn}
            </if>
            <if test="strategyIdList != null and strategyIdList.size() > 0">
                strategy_id IN
                <foreach collection="strategyIdList" item="strategyId" open="(" separator="," close=")">
                    #{strategyId}
                </foreach>
            </if>
            <if test="sqbStoreId != null and sqbStoreId.length() > 0">
                AND associated_sqb_store_id = #{sqbStoreId}
            </if>
            <if test="meiTuanStoreSn != null and meiTuanStoreSn.length() > 0">
                AND associated_meituan_store_sn = #{meiTuanStoreSn}
            </if>
            <if test="elmStoreSn != null and elmStoreSn.length() > 0">
                AND associated_elm_store_sn = #{elmStoreSn}
            </if>
            <if test="dyStoreSn != null and dyStoreSn.length() > 0">
                AND dy_store_sn = #{dyStoreSn}
            </if>
            <if test="outMerchantNo != null and outMerchantNo.length() > 0">
                AND out_merchant_no = #{outMerchantNo}
            </if>
            <if test="bankCardActivateStatus != null and bankCardActivateStatus.length() > 0">
                AND bank_card_activate_status = #{bankCardActivateStatus}
            </if>
            <if test="deleted != null">
                AND deleted = #{deleted}
            </if>
        </where>
    </select>

    <select id="selectBrandMerchantByBrandIdAndMerchantIds"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant
        <where>
            brand_id = #{brandId}
            <if test="merchantIds != null and merchantIds.size() > 0">
                AND merchant_id IN
                <foreach collection="merchantIds" item="merchantId" open="(" separator="," close=")">
                    #{merchantId}
                </foreach>
            </if>
            AND deleted = 0
        </where>
    </select>

    <select id="countBrandMerchantsByConditions" resultType="java.lang.Integer"
            parameterType="com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO">
        SELECT COUNT(*) FROM brand_merchant
        <where>
            <if test="brandIds != null and brandIds.size() > 0">
                brand_id IN
                <foreach collection="brandIds" separator="," item="brandId" open="(" close=")">
                    #{brandId}
                </foreach>
            </if>
            <if test="merchantTypes != null and merchantTypes.size() > 0">
                AND merchant_type IN
                <foreach collection="merchantTypes" separator="," item="merchantType" close=")" open="(">
                    #{merchantType}
                </foreach>
            </if>
            <if test="types != null and types.size() > 0">
                AND type IN
                <foreach collection="types" separator="," item="type" close=")" open="(">
                    #{type}
                </foreach>
            </if>
            <if test="merchantName != null and merchantName.length() > 0">
                AND merchant_name LIKE CONCAT('%',#{merchantName},'%')
            </if>
            <if test="merchantSn != null and merchantSn.length() > 0">
                AND merchant_sn = #{merchantSn}
            </if>
            <if test="strategyIdList != null and strategyIdList.size() > 0">
                AND strategy_id IN
                <foreach collection="strategyIdList" separator="," item="strategyId" close=")" open="(">
                    #{strategyId}
                </foreach>
            </if>
            <if test="strategyNotNull != null and strategyNotNull > 0">
                AND strategy_id <![CDATA[ > ]]> 0
            </if>
            <if test="merchantIds != null and merchantIds.size() > 0">
                AND merchant_id IN
                <foreach collection="merchantIds" item="merchantId" open="(" separator="," close=")">
                    #{merchantId}
                </foreach>
            </if>
            <if test="accountOpenStatus != null and accountOpenStatus.length() > 0">
                AND account_open_status = #{accountOpenStatus}
            </if>
            <if test="outMerchantNo != null and outMerchantNo.length() > 0">
                AND out_merchant_no = #{outMerchantNo}
            </if>
            <if test="meiTuanStoreSn != null and meiTuanStoreSn.length() > 0">
                AND associated_meituan_store_sn = #{meiTuanStoreSn}
            </if>
            <if test="elmStoreSn != null and elmStoreSn.length() > 0">
                AND associated_elm_store_sn = #{elmStoreSn}
            </if>
            <if test="dyStoreSn != null and dyStoreSn.length() > 0">
                AND dy_store_sn = #{dyStoreSn}
            </if>
            <if test="bankCardActivateStatus != null and bankCardActivateStatus.length() > 0">
                AND bank_card_activate_status = #{bankCardActivateStatus}
            </if>
            AND deleted = 0
        </where>
    </select>

    <select id="pageBrandMerchantsByConditions"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant
        <where>
            <if test="conditions.brandIds != null and conditions.brandIds.size() > 0">
                brand_id IN
                <foreach collection="conditions.brandIds" separator="," item="brandId" open="(" close=")">
                    #{brandId}
                </foreach>
            </if>
            <if test="conditions.merchantTypes != null and conditions.merchantTypes.size() > 0">
                AND merchant_type IN
                <foreach collection="conditions.merchantTypes" separator="," item="merchantType" close=")" open="(">
                    #{merchantType}
                </foreach>
            </if>
            <if test="conditions.types != null and conditions.types.size() > 0">
                AND type IN
                <foreach collection="conditions.types" separator="," item="type" close=")" open="(">
                    #{type}
                </foreach>
            </if>
            <if test="conditions.merchantName != null and conditions.merchantName.length() > 0">
                AND merchant_name LIKE CONCAT('%',#{conditions.merchantName},'%')
            </if>
            <if test="conditions.merchantSn != null and conditions.merchantSn.length() > 0">
                AND merchant_sn = #{conditions.merchantSn}
            </if>
            <if test="conditions.strategyIdList != null and conditions.strategyIdList.size() > 0">
                AND strategy_id IN
                <foreach collection="conditions.strategyIdList" separator="," item="strategyId" close=")" open="(">
                    #{strategyId}
                </foreach>
            </if>
            <if test="conditions.strategyNotNull != null and conditions.strategyNotNull > 0">
                AND strategy_id <![CDATA[ > ]]> 0
            </if>
            <if test="conditions.merchantIds != null and conditions.merchantIds.size() > 0">
                AND merchant_id IN
                <foreach collection="conditions.merchantIds" item="merchantId" open="(" separator="," close=")">
                    #{merchantId}
                </foreach>
            </if>
            <if test="conditions.accountOpenStatus != null and conditions.accountOpenStatus.length() > 0">
                AND account_open_status = #{conditions.accountOpenStatus}
            </if>
            <if test="conditions.outMerchantNo != null and conditions.outMerchantNo.length() > 0">
                AND out_merchant_no = #{conditions.outMerchantNo}
            </if>
            <if test="conditions.meiTuanStoreSn != null and conditions.meiTuanStoreSn.length() > 0">
                AND associated_meituan_store_sn = #{conditions.meiTuanStoreSn}
            </if>
            <if test="conditions.elmStoreSn != null and conditions.elmStoreSn.length() > 0">
                AND associated_elm_store_sn = #{conditions.elmStoreSn}
            </if>
            <if test="conditions.dyStoreSn != null and conditions.dyStoreSn.length() > 0">
                AND dy_store_sn = #{conditions.dyStoreSn}
            </if>
            <if test="conditions.bankCardActivateStatus != null and conditions.bankCardActivateStatus.length() > 0">
                AND bank_card_activate_status = #{conditions.bankCardActivateStatus}
            </if>
            AND deleted = 0
        </where>
        ORDER BY created_time DESC
        <if test="offset != null and pageSize != null">
            LIMIT #{offset},#{pageSize}
        </if>
    </select>

    <select id="selectBrandMerchantByBrandIdAndMerchantId"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM brand_merchant
        <where>
            brand_id = #{brandId} AND merchant_id = #{merchantId}
            <if test="deleted != null">
                AND deleted = #{deleted}
            </if>
        </where>
    </select>

    <select id="countMerchantNumber"
            resultMap="countMerchantNumber">
        SELECT brand_id, count(brand_id) AS merchant_num
        FROM brand_merchant
        <where>
            <if test="brandIds != null and brandIds.size() > 0">
                brand_id IN
                <foreach collection="brandIds" item="brandId" open="(" close=")" separator=",">
                    #{brandId}
                </foreach>
            </if>
            AND deleted = 0
        </where>
        GROUP BY brand_id;
    </select>

    <select id="getBrandMerchantInfoByMemberId"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM brand_merchant WHERE member_id = #{memberId} AND deleted = 0
    </select>
    <select id="getBrandMerchantInfoByMerchantId"
            resultType="com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO">
        SELECT <include refid="Base_Column_List"/> FROM brand_merchant WHERE merchant_id = #{merchantId} AND deleted = 0
    </select>

    <select id="pageGetBrandMerchantByBrandIdAndId"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM brand_merchant
        <where>
            id <![CDATA[ > ]]> #{id} AND brand_id = #{brandId} AND account_open_status = 'OPENED' AND deleted = 0
        </where>
        order by id
        limit #{pageSize}
    </select>
    <select id="pageBrandMerchantById"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM brand_merchant
        <where>
            id <![CDATA[ > ]]> #{id} AND deleted = 0
        </where>
        order by id
        limit #{pageSize}
    </select>
    <select id="pageBrandMerchantByIdAndConditions"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand_merchant
        <where>
            id <![CDATA[ > ]]> #{id}
            <if test="conditions.brandId != null and conditions.brandId.length() > 0">
                AND brand_id = #{conditions.brandId}
            </if>
            <if test="conditions.bankCardActivateStatus != null and conditions.bankCardActivateStatus.length() > 0">
                AND bank_card_activate_status = #{conditions.bankCardActivateStatus}
            </if>
            <if test="conditions.accountOpenStatus != null and conditions.accountOpenStatus.length() > 0">
                AND account_open_status = #{conditions.accountOpenStatus}
            </if>
            <if test="conditions.meiTuanStoreSn != null and conditions.meiTuanStoreSn.length() > 0">
                AND associated_meituan_store_sn = #{conditions.meiTuanStoreSn}
            </if>
            <if test="conditions.elmStoreSn != null and conditions.elmStoreSn.length() > 0">
                AND associated_elm_store_sn = #{conditions.elmStoreSn}
            </if>
            <if test="conditions.dyStoreSn != null and conditions.dyStoreSn.length() > 0">
                AND dy_store_sn = #{conditions.dyStoreSn}
            </if>
            <if test="conditions.outMerchantNo != null and conditions.outMerchantNo.length() > 0">
                AND out_merchant_no = #{conditions.outMerchantNo}
            </if>
            AND deleted = 0
        </where>
        order by id
        limit #{pageSize}
    </select>
</mapper>