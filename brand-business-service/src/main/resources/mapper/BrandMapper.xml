<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.BrandMapper">
    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.BrandDO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="group_id" jdbcType="VARCHAR" property="groupId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="alias" jdbcType="VARCHAR" property="alias"/>
        <result column="merchant_sn" jdbcType="VARCHAR" property="merchantSn"/>
        <result column="docking_mode" jdbcType="VARCHAR" property="dockingMode"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="industry" jdbcType="VARCHAR" property="industry"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="contact_email" jdbcType="VARCHAR" property="contactEmail"/>
        <result column="partner_id" jdbcType="VARCHAR" property="partnerId"/>
        <result column="fund_management_company_code" jdbcType="VARCHAR" property="fundManagementCompanyCode"/>
        <result column="fund_management_company" jdbcType="VARCHAR" property="fundManagementCompany"/>
        <result column="fund_summary_account_name" jdbcType="VARCHAR" property="fundSummaryAccountName"/>
        <result column="fund_summary_account" jdbcType="VARCHAR" property="fundSummaryAccount"/>
        <result column="fund_gather_account" jdbcType="VARCHAR" property="fundGatherAccount"/>
        <result column="fund_settlement_account_name" jdbcType="VARCHAR" property="fundSettlementAccountName"/>
        <result column="fund_settlement_account" jdbcType="VARCHAR" property="fundSettlementAccount"/>
        <result column="level" jdbcType="TINYINT" property="level"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="extra" jdbcType="VARCHAR" property="extra" />
        <result column="sft_tag" jdbcType="TINYINT" property="sftTag"/>
        <result column="enable_sft" jdbcType="TINYINT" property="enableSft"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, brand_id, sn, `group_id`, `name`, `alias`,merchant_sn,docking_mode, parent_id, province, city, district,
        address, industry, contact_name, contact_phone,contact_email,partner_id, fund_management_company_code,fund_management_company, fund_summary_account_name,
        fund_summary_account, fund_gather_account, fund_settlement_account_name,fund_settlement_account, `level`, remark,
        `status`, deleted, created_time, updated_time, version,extra,sft_tag,enable_sft
    </sql>

    <update id="deleteBrand">
        update brand
        set deleted = 1,version=version+1
        where brand_id = #{brandId}
    </update>

    <update id="updateBrand" parameterType="com.wosai.cua.brand.business.service.domain.entity.BrandDO">
        UPDATE brand
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="alias != null">
                alias = #{alias},
            </if>
            <if test="merchantSn != null">
                merchant_sn = #{merchantSn},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            district = #{district},
            <if test="address != null">
                address = #{address},
            </if>
            <if test="industry != null">
                industry = #{industry},
            </if>
            <if test="contactName != null">
                contact_name = #{contactName},
            </if>
            <if test="contactPhone != null">
                contact_phone = #{contactPhone},
            </if>
            <if test="contactEmail != null">
                contact_email = #{contactEmail},
            </if>
            <if test="partnerId != null">
                partner_id = #{partnerId},
            </if>
            <if test="fundManagementCompany != null">
                fund_management_company = #{fundManagementCompany},
            </if>
            <if test="fundSummaryAccountName != null">
                fund_summary_account_name = #{fundSummaryAccountName},
            </if>
            <if test="fundSummaryAccount != null">
                fund_summary_account = #{fundSummaryAccount},
            </if>
            <if test="fundGatherAccount != null">
                fund_gather_account = #{fundGatherAccount},
            </if>
            <if test="fundSettlementAccountName != null">
                fund_settlement_account_name = #{fundSettlementAccountName},
            </if>
            <if test="fundSettlementAccount != null">
                fund_settlement_account = #{fundSettlementAccount},
            </if>
            <if test="enableSft != null">
                enable_sft = #{enableSft},
            </if>
            version = version+1
        </set>
        <where>
            brand_id = #{brandId}
        </where>
    </update>

    <select id="selectBrandByBrandIdListOrBrandSnList"
            resultMap="BaseResultMap">
        SELECT<include refid="Base_Column_List"/>,
        <include refid="Base_Column_List"/>
        FROM brand
        <where>
            <choose>
                <when test="brandIdList != null and brandIdList.size() > 0">
                    brand_id IN
                    <foreach collection="brandIdList" item="brandId" close=")" open="(" separator=",">
                        #{brandId}
                    </foreach>
                </when>
                <when test="brandSnList != null and brandSnList.size() > 0">
                    sn IN
                    <foreach collection="brandSnList" item="sn" close=")" open="(" separator=",">
                        #{sn}
                    </foreach>
                </when>
            </choose>
            AND deleted = 0
        </where>
    </select>

    <select id="selectBrandByBrandId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand
        <where>
            brand_id = #{brandId} AND deleted = 0
        </where>
    </select>

    <select id="countBrandByConditions" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM brand
        <where>
            <if test="brandConditions.parentId != null and brandConditions.parentId.length() > 0">
                AND parent_id = #{brandConditions.parentId}
            </if>
            <if test="brandConditions.brandName != null and brandConditions.brandName.length() > 0">
                AND name LIKE CONCAT('%',#{brandConditions.brandName},'%')
            </if>
            <if test="brandConditions.brandSn != null and brandConditions.brandSn.length() > 0">
                AND sn = #{brandConditions.brandSn}
            </if>
            <if test="brandConditions.contactPhone != null and brandConditions.contactPhone.length() > 0">
                AND contact_phone LIKE CONCAT('%',#{brandConditions.contactPhone},'%')
            </if>
            <if test="brandConditions.brandIdList != null and brandConditions.brandIdList.size() >0">
                AND brand_id IN
                <foreach collection="brandConditions.brandIdList" separator="," open="(" close=")" item="brandId">
                    #{brandId}
                </foreach>
            </if>
            <if test="brandConditions.sftTag != null">
                AND sft_tag = #{brandConditions.sftTag}
            </if>
            <if test="brandConditions.enableSft != null">
                AND enable_sft = #{brandConditions.enableSft}
            </if>
            AND deleted = 0
        </where>
    </select>

    <select id="pageBrandsByConditions"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand
        <where>
            <if test="brandConditions.parentId != null and brandConditions.parentId.length() > 0">
                AND parent_id = #{brandConditions.parentId}
            </if>
            <if test="brandConditions.brandName != null and brandConditions.brandName.length() > 0">
                AND name LIKE CONCAT('%',#{brandConditions.brandName},'%')
            </if>
            <if test="brandConditions.brandSn != null and brandConditions.brandSn.length() > 0">
                AND sn = #{brandConditions.brandSn}
            </if>
            <if test="brandConditions.contactPhone != null and brandConditions.contactPhone.length() > 0">
                AND contact_phone LIKE CONCAT('%',#{brandConditions.contactPhone},'%')
            </if>
            <if test="brandConditions.brandIdList != null and brandConditions.brandIdList.size() >0">
                AND brand_id IN
                <foreach collection="brandConditions.brandIdList" separator="," open="(" close=")" item="brandId">
                    #{brandId}
                </foreach>
            </if>
            <if test="brandConditions.sftTag != null">
                AND sft_tag = #{brandConditions.sftTag}
            </if>
            <if test="brandConditions.enableSft != null">
                AND enable_sft = #{brandConditions.enableSft}
            </if>
            AND deleted = 0
        </where>
        ORDER BY updated_time DESC
        <if test="offset != null and pageSize != null">
            LIMIT #{offset},#{pageSize}
        </if>
    </select>

    <select id="selectBrandByBrandSn" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM brand
        <where>
            sn = #{brandSn} AND deleted = 0
        </where>
    </select>
    <select id="selectBrandByMerchantSn"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM brand WHERE merchant_sn = #{merchantSn} AND deleted = 0
    </select>

    <select id="selectBrandByName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM brand where name = #{name} AND deleted = 0
    </select>
    <select id="selectBrandByParentBrandId"
            resultType="com.wosai.cua.brand.business.service.domain.entity.BrandDO">
        SELECT <include refid="Base_Column_List"/> FROM brand WHERE parent_id = #{brandId} AND deleted = 0
    </select>


</mapper>