package com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

/**
 * 动态格式转换器
 */
public interface Converter {

    /**
     * 把响应XML字符串转换为响应对象
     *
     * @param responseXml 响应XML字符串
     * @param clazz 领域类型
     * @param <T> 领域泛型
     * @return 响应对象
     * @throws MybankApiException
     */
    public <T extends MybankResponse> T toResponse(String responseXml, Class<T> clazz) throws MybankApiException;
}