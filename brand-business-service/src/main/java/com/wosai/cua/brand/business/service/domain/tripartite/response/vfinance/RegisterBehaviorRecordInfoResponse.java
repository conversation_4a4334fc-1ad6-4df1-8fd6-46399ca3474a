package com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RegisterBehaviorRecordInfoResponse {

    /**
     * member_id : ************
     * account_type : 302
     * sub_acct_no : ****************
     * tran_net_member_code : ************3027340011
     * status : T
     * code : 0
     * message : 登记上送处理中
     */

    @JSONField(name = "member_id")
    private String memberId;
    @JSONField(name = "account_type")
    private String accountType;
    @JSONField(name = "sub_acct_no")
    private String subAcctNo;
    @JSONField(name = "tran_net_member_code")
    private String tranNetMemberCode;
    private String status;
    private String code;
    private String message;
}
