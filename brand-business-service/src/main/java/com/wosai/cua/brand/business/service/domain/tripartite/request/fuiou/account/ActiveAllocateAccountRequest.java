package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

/**
 * 用户激活、短信重发接口（CJ003）
 * 接口名称：/activeAllocateAccount.fuiou
 * 接口说明：创建用户时，无需单独调用本接口。此接口用于签约01失效状态后重新激活发起签约或在00状态下调用重新进行短信重发。
 */
@Data
public class ActiveAllocateAccountRequest  implements TripartiteSystemCallRequest {

    public static final String ACTIVE_ACCOUNT_URL = "/activeAllocateAccount.fuiou";

    private ActiveAllocateAccountRequestBody body;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_ACTIVE_ALLOCATE_ACCOUNT;
    }
}
