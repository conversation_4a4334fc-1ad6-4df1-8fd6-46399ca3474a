package com.wosai.cua.brand.business.service.mybank.manage;

import com.wosai.cua.brand.business.service.domain.service.MyBankNotifyService;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.enums.MybankApiExceptionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 策略模式的工厂类
 */
@Component
public class StrategyFactory {

    /**
     * 此处在spring上下文加载时会把所有的策略注册到map中
     * 存放所有策略类Bean的map, Spring 会自动将实现 MybankStrategy 接口的类注入到这个Map中;
     * 这个Map的key值就是你的bean id, value值则为对应的策略实现类
     **/
    @Autowired
    Map<String, MyBankNotifyService> strategyMap = new ConcurrentHashMap<>();

    /**
     * @param type
     * @return com.mybank.api.service.MybankStrategy
     * @Description 根据对应通知的策略bean id获取对应策略实现类对象
     **/
    public MyBankNotifyService getStrategy(String type) throws Exception {
        MyBankNotifyService strategy = strategyMap.get(type);
        if (strategy == null) {
            throw new MybankApiException(MybankApiExceptionEnum.STRATEGY_NOT_DEFINED);
        }
        return strategy;
    }
}
