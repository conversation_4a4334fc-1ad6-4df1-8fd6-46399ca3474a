package com.wosai.cua.brand.business.service.helper;

import com.wosai.uc.auth.AuthSignBuild;
import com.wosai.uc.v2.dto.AppIdHolder;
import com.wosai.uc.v2.dto.IdentifierReq;
import com.wosai.uc.v2.dto.IdentityNoReq;
import com.wosai.uc.v2.dto.UcNaturalPersonIdHolder;
import com.wosai.uc.v2.dto.UcNaturalPersonReq;
import com.wosai.uc.v2.dto.UcUserIdHolder;
import com.wosai.uc.v2.dto.UcUserIdsHolder;
import com.wosai.uc.v2.dto.UcUserReq;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Component
public class UcUserV2Helper implements ApplicationContextAware {

    private static final String APP = "trade";

    private static String appSecret;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appSecret = applicationContext.getEnvironment().getProperty("uc.secret");
    }

    public static <T> UcUserReq<T> build(T param) {
        return new UcUserReq<>(APP, AuthSignBuild.buildSign(APP, appSecret), param);
    }

    public static UcUserReq<UcUserIdHolder> ucUserIdHolder(String ucUserId) {
        return build(new UcUserIdHolder().setUcUserId(ucUserId));
    }

    public static UcUserReq<UcUserIdsHolder> ucUserIdsHolder(List<String> ucUserIds) {
        return build(new UcUserIdsHolder().setUcUserIds(ucUserIds));
    }

    public static UcUserReq<IdentifierReq> identifierReq(String identifier) {
        return build(new IdentifierReq().setIdentifier(identifier));
    }

    public static UcUserReq<UcNaturalPersonIdHolder> ucNaturalPersonIdHolder(String naturalPersonId) {
        return build(new UcNaturalPersonIdHolder().setNaturalPersonId(naturalPersonId));
    }

    public static UcUserReq<UcNaturalPersonReq> ucNaturalPersonReq(String naturalPersonId, String ucUserId) {
        return build(new UcNaturalPersonReq().setNaturalPersonId(naturalPersonId).setUcUserId(ucUserId));
    }

    public static UcUserReq<AppIdHolder> appIdHolder(String appId) {
        return build(new AppIdHolder().setAppId(appId));
    }

    public static UcUserReq<IdentityNoReq> identityNoReq(String identityNo) {
        return build(new IdentityNoReq().setIdentityNo(identityNo));
    }

    public static void main(String[] args) {
        System.out.println(UUID.randomUUID().toString());
    }
}
