package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.ResponseBody;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
@Data
public class MerchantControlNotifyModel extends ResponseBody {

    private static final long serialVersionUID = 4234904376175417446L;

    /**
     * 外部交易号。网商系统生成的外部交易号，同一交易号被视为同一笔通知。
     */
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;

    /**
     * 商户号列表，英文逗号分隔。同主体在当前平台下开通的商户号数量，超过50个会被截断。
     */
    @XmlElement(name = "MerchantIds")
    private String merchantIds;

    /**
     * 外部商户号列表。合作方对商户的自定义编码，要求在合作方下保持唯一。同主体在当前平台下开通的商户号数量，超过50个会被截断。
     */
    @XmlElement(name = "OutMerchantIds")
    private String outMerchantIds;

    /**
     * 消息通知时间：格式yyyyMMddHHmmss，通知触发的时间，可能为管控、解管控。当有多条通知时，可通过时间来确认通知乱序的情况。
     */
    @XmlElement(name = "PunishDate")
    private String punishDate;

    /**
     * 管控通知类型：
     * ● 1-管控：对商户进行管控，即时生效。
     * ● 2-解除管控：对商户解除管控（解除单条管控单号）。
     * ● 3-弱管控（弱限权）：客户身份证超期未超过180天，不会阻断交易。不会进行管控。
     * ● 4-延迟管控（T+N管控）：客户已达到管控条件，为避免业务影响T+N天后进行管控，管控时不再通知。
     */
    @XmlElement(name = "ControlNotifyType")
    private String controlNotifyType;

    /**
     * 管控单号：通知管控时，会传管控单号，解除管控时会传原管控单号。
     * ● 身份类管控只有一条管控单据，可能多次通知（出现新的身份异常情况）、但单号一样，
     * ● 风险类、司法类大部分管控是每次不同的管控单号。
     */
    @XmlElement(name = "ControlOrderNo")
    private String controlOrderNo;

    /**
     * 管控类型
     * 1-身份类
     * 2-司法类
     * 3-风险类
     * ● 身份类：因客户身份异常，导致被管控，如身份证缺失、身份信息变更、反洗钱黑名单等。
     * ● 风险类：因客户命中安全防控规则，导致被管控，如不动户、安全防控等。
     * ● 司法类：因客户被司法冻结，导致被管控。
     */
    @XmlElement(name = "ControlCategory")
    private String controlCategory;

    /**
     *  管控策略
     * ● 1-不能出金
     * ● 2-不能入金
     * ● 3-不能出入金
     * ● 4-不限制：管控类型为弱管控、解除管控时为不限制，管控、延迟管控时
     */
    @XmlElement(name = "ControlStrategy")
    private String controlStrategy;

    /**
     *  管控原因（非对客）
     * 通知类型为解除管控时无该字段，管控、弱管控、延迟管控时有，展示客户的管控原因。
     * 多条管控原因用“||”进行分隔。
     * ● PERSON_ONLINE_AUTH：个人联网核查
     * ● PERSON_AML：个人反洗钱检查
     * ● IDENTITY_CARD_AUTH：身份证影像件认证
     * ● IDENTITY_CARD_EXPIRE：身份证过期
     */
    @XmlElement(name = "ChangeReason")
    private String changeReason;

    /**
     * 是否可升级
     * true-可引导升级解除
     * false 不允许引导升级解除
     * 该条管控是否可被引导解除管控。
     * 通常身份类管控下，可引导客户更新资料解除管控。司法类、安全类不可引导（极特殊情况身份类-洗钱黑名单不可解除）
     */
    @XmlElement(name = "CanUpgrade")
    private String canUpgrade;

    /**
     * 中心化升级链接(需要支付宝登录态才能打开，会引导进入有网商心智透出的解限页面)
     * 身份证类管控时，可跳转链接更新客户资料。解限链接中有客户ID，只能用于本人解限。
     */
    @XmlElement(name = "UpgradeUrl")
    private String upgradeUrl;

    /**
     * 延迟管控的生效时间
     * 当账户通知类型是4-延迟管控时，代表延迟到该时间管控才会生效
     */
    @XmlElement(name = "EffectiveTime")
    private String effectiveTime;

    @Getter
    public enum ControlStrategyEnum {
        /**
         * 不能出金
         */
        CANNOT_OUT("1"),
        /**
         * 不能入金
         */
        CANNOT_IN("2"),
        /**
         * 不能出入金
         */
        CANNOT_IN_OUT("3"),
        /**
         * 不限制
         */
        UNLIMITED("4");

        private final String code;

        ControlStrategyEnum(String code) {
            this.code = code;
        }

        public static String getName(String code) {
            for (ControlStrategyEnum controlStrategyEnum : ControlStrategyEnum.values()) {
                if (controlStrategyEnum.getCode().equals(code)) {
                    return controlStrategyEnum.name();
                }
            }
            return "";
        }
    }

    @Getter
    public enum ChangeReasonEnum {
        /**
         * 个人联网核查
         */
        PERSON_ONLINE_AUTH("PERSON_ONLINE_AUTH", "个人联网核查"),
        /**
         * 个人反洗钱检查
         */
        PERSON_AML("PERSON_AML","个人反洗钱检查"),
        /**
         * 身份证影像件认证
         */
        IDENTITY_CARD_AUTH("IDENTITY_CARD_AUTH","身份证影像件认证" ),
        /**
         * 身份证过期
         */
        IDENTITY_CARD_EXPIRE("IDENTITY_CARD_EXPIRE", "身份证过期");

        private final String code;

        private final String desc;

        ChangeReasonEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDesc(String code) {
            for (ChangeReasonEnum changeReasonEnum : ChangeReasonEnum.values()) {
                if (changeReasonEnum.getCode().equals(code)) {
                    return changeReasonEnum.getDesc();
                }
            }
            return null;
        }
    }

    @Getter
    public enum ControlCategoryEnum {
        /**
         * 身份类
         */
        IDENTITY_CATEGORY("1"),
        /**
         * 司法类
         */
        JUDICIAL_CATEGORY("2"),
        /**
         * 风险类
         */
        RISK_CATEGORY("3");

        private final String code;

        ControlCategoryEnum(String code) {
            this.code = code;
        }

        public static String getName(String code) {
            for (ControlCategoryEnum controlCategoryEnum : ControlCategoryEnum.values()) {
                if (controlCategoryEnum.getCode().equals(code)) {
                    return controlCategoryEnum.name();
                }
            }
            return "";
        }
    }

    @Getter
    public enum CanUpgradeEnum {
        /**
         * 可引导升级解除
         */
        TRUE("true",1),
        /**
         * 不允许引导升级解除
         */
        FALSE("false",0);

        private final String code;

        private final int ordinal;

        CanUpgradeEnum(String code, int ordinal) {
            this.code = code;
            this.ordinal = ordinal;
        }

        public static int getOrdinal(String code) {
            for (CanUpgradeEnum canUpgradeEnum : CanUpgradeEnum.values()) {
                if (canUpgradeEnum.getCode().equals(code)) {
                    return canUpgradeEnum.getOrdinal();
                }
            }
            return -1;
        }
    }
}
