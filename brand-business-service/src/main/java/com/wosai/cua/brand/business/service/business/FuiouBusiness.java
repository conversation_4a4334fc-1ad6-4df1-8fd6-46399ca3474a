package com.wosai.cua.brand.business.service.business;

import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.ActiveAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.ActiveAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.ActiveAllocateAccountResponse;
import com.wosai.cua.brand.business.service.enums.RedisKeyEnum;
import com.wosai.cua.brand.business.service.helper.RedisHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.tripartite.fuiou.FuiouToBeActiveRecordModule;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FuiouBusiness {

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private final BrandDomainService brandDomainService;

    private final MerchantService merchantService;

    private final BrandConfigDomainService brandConfigDomainService;

    @Value("${fuiou.open.account.check_type}")
    private String fuiouCheckType;

    private final ApolloConfig apolloConfig;

    private final RedisHelper redisHelper;

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = new EnumMap<>(FundManagementCompanyEnum.class);

    @Autowired
    public FuiouBusiness(List<TripartiteSystemCallService> tripartiteSystemCallServices, BrandDomainService brandDomainService, MerchantService merchantService, BrandConfigDomainService brandConfigDomainService, ApolloConfig apolloConfig, RedisHelper redisHelper) {
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.brandDomainService = brandDomainService;
        this.merchantService = merchantService;
        this.brandConfigDomainService = brandConfigDomainService;
        this.apolloConfig = apolloConfig;
        this.redisHelper = redisHelper;
    }

    @PostConstruct
    public void init() {
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }

    public String resend(String brandId, String merchantId,String merchantSn) {
        if (StringUtils.isBlank(merchantSn) && StringUtils.isBlank(merchantId)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_SN_OR_ID_NOT_BE_NULL_IN_SAME_TIME);
        }

        MerchantInfo merchant = null;
        if (StringUtils.isNotBlank(merchantSn)) {
            merchant = merchantService.getMerchantBySn(merchantSn, null);
        }
        if (StringUtils.isNotBlank(merchantId)){
            merchant = merchantService.getMerchantById(merchantId, null);
        }
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (Objects.isNull(brandMerchantModule) || StringUtils.isBlank(brandMerchantModule.getMemberId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandId, FuiouConfigModule.class);
        if (Objects.isNull(configModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NO_FIND_BRAND_CONFIG);
        }
        ActiveAllocateAccountRequest request = new ActiveAllocateAccountRequest();
        ActiveAllocateAccountRequestBody body = new ActiveAllocateAccountRequestBody(configModule.getMerchantNo());
        body.setAccountIn(brandMerchantModule.getMemberId());
        body.setTraceNo(brandMerchantModule.getMerchantSn() + System.currentTimeMillis());
        body.setCheckType(fuiouCheckType);
        request.setBody(body);
        ActiveAllocateAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, ActiveAllocateAccountResponse.class, configModule);
        if (Objects.isNull(response)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, "富友接口未响应");
        }
        if (!response.isSuccess()) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, response.getResultMsg());
        }
        brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus());
        String url = StringUtils.isNotBlank(response.getBody().getKsCheckUrl()) ? response.getBody().getKsCheckUrl() : "";
        brandMerchantModule.setAccountOpenFailureReason(url);
        brandDomainService.updateBrandMerchant(brandMerchantModule);
        redisHelper.saveAllToSet(RedisKeyEnum.FUIOU_OPEN_ACCOUNT_RECORDS.getKey(), Lists.newArrayList(
                        FuiouToBeActiveRecordModule.builder()
                                .brandId(brandMerchantModule.getBrandId())
                                .merchantId(brandMerchantModule.getMerchantId())
                                .tradeNo(body.getTraceNo())
                                .build()
                )
        );
        return url;
    }

}
