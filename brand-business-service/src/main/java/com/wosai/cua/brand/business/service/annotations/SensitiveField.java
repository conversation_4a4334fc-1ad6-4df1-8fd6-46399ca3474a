package com.wosai.cua.brand.business.service.annotations;

import com.wosai.cua.brand.business.service.enums.SensitiveFieldEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.FIELD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface SensitiveField {
    SensitiveFieldEnum fieldType() default SensitiveFieldEnum.DEFAULT;
}
