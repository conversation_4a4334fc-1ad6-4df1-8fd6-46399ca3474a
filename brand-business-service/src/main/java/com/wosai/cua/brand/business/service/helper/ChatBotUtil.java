package com.wosai.cua.brand.business.service.helper;

import cn.hutool.core.util.PhoneUtil;
import com.shouqianba.cua.chatbot.ChatBot;
import com.shouqianba.cua.chatbot.client.ChatBotClient;
import com.shouqianba.cua.chatbot.client.FeishuChatBotClient;
import com.shouqianba.cua.chatbot.message.TextMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/8/8
 */
@Slf4j
public class ChatBotUtil {

    private static ChatBotClient chatBotClient = new FeishuChatBotClient();

    /**
     * 创建品牌机器人
     */
    private static final ChatBot BRAND_CREATE_BOT = new ChatBot("https://open.feishu.cn/open-apis/bot/v2/hook/d63f4191-86bb-447e-860f-65876e7d0b46", "dKAQvhwTRrq0eego0A6Rpc", chatBotClient);

    public static void sendMessageToBrandCreateChatBot(String message) {
        try {
            BRAND_CREATE_BOT.sendMessageImmediately(new TextMessage(hideSensitiveInfo(message)));
        } catch (Exception e) {
            log.warn("发送消息失败 {}", message, e);
        }
    }


    /**
     * 脱敏
     *
     * @return
     */
    private static String hideSensitiveInfo(String text) {
        return hidePhone(text);
    }

    private static final Pattern PHONE_PATTERN = Pattern.compile("\\b(1[3-9]\\d{9})\\b");

    /**
     * 手机号脱敏
     *
     * @param text
     * @return
     */
    private static String hidePhone(String text) {
        Matcher matcher = PHONE_PATTERN.matcher(text);
        if (matcher.find()) {
            String phone = matcher.group();
            String obscured = PhoneUtil.hideBetween(phone).toString();
            return text.replace(phone, obscured);
        }
        return text;
    }
}
