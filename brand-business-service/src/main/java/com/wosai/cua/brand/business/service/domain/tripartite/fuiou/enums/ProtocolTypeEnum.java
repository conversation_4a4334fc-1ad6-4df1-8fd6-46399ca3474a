package com.wosai.cua.brand.business.service.domain.tripartite.fuiou.enums;

import lombok.Getter;

@Getter
public enum ProtocolTypeEnum {
    CHANNEL_COOPERATION_AGREEMENT("01", "渠道合作协议"),
    SALES_COOPERATION_AGREEMENT("02", "销售合作协议"),
    ACCOUNT_SPECIFIC_AGREEMENT("03", "账户专用协议")
    ;

    private final String protocolType;

    private final String desc;

    ProtocolTypeEnum(String protocolType, String desc)
    {
        this.protocolType = protocolType;
        this.desc = desc;
    }



}
