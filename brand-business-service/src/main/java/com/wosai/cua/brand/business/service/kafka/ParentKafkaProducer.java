package com.wosai.cua.brand.business.service.kafka;

import com.wosai.cua.brand.business.service.helper.ThreadPoolHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.InitializingBean;

import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Slf4j
public class ParentKafkaProducer implements InitializingBean {

    protected Properties properties;

    protected KafkaProducer<String, Object> kafkaProducer;

    protected KafkaProducer<String, Object> getKafkaProducer() {
        return kafkaProducer;
    }

    @Override
    public void afterPropertiesSet() {
        // 具体实现在继承的类中
    }

    /**
     * 发送消息 错误信息不报错 发送不太重要消息 允许丢失
     *
     * @param topic
     * @param object
     */
    public void sendMessage(String topic, Object object) {
        if (object == null) {
            return;
        }
        try {
            ThreadPoolHelper.execute(() -> syncSendMessage(topic,object));
        } catch (Exception e) {
            log.error("send Topic:{},kafka fail: {}", topic, object, e);
        }
    }

    public void syncSendMessage(String topic, Object object) {
        if (Objects.isNull(object)){
            return;
        }
        try {
            this.getKafkaProducer().send(new ProducerRecord<>(topic, object), (metadata, exception) -> {
                if (exception != null) {
                    log.error("send Topic:{},kafka fail:{} ", topic, object, exception);
                }else {
                    log.info("send Topic:{}, kafka success: {}", topic, object);
                }
            });
        } catch (Exception e) {
            log.error("send Topic:{},kafka fail: {}", topic, object, e);
        }
    }
}
