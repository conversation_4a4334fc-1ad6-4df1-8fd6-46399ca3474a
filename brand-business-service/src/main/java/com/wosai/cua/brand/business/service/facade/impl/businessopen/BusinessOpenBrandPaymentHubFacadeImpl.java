package com.wosai.cua.brand.business.service.facade.impl.businessopen;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.ExistBrandOpenPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.CreateBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.BrandPaymentHubDevParamDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.CheckOpenPaymentHubResponseDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenBrandPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateBrandResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.businessopen.BusinessOpenBrandPaymentHubFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.enums.OpenPaymentHubFailCodeEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.MerchantBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppInfoOpenResult;
import com.wosai.cua.brand.business.service.helper.ChatBotUtil;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/19
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusinessOpenBrandPaymentHubFacadeImpl implements BusinessOpenBrandPaymentHubFacade {

    private final BrandDomainService brandDomainService;
    private final MerchantService merchantService;
    private final BrandBusiness brandBusiness;
    private final MerchantBusinessOpenClient merchantBusinessOpenClient;
    private final Environment environment;
    @Value("${appid.brandPaymentHub}")
    private String brandPaymentHubAppId;

    private boolean isProd;

    @PostConstruct
    public void init() {
        isProd = Arrays.asList(environment.getActiveProfiles()).contains("prod");
    }

    @Override
    public CheckOpenPaymentHubResponseDTO checkBrandOpenPaymentHub(OpenBrandPaymentHubRequestDTO openBrandPaymentHubRequestDTO) {
        // 查询商户有没有已经关联的品牌
        BrandPaymentHubDevParamDTO devParam = openBrandPaymentHubRequestDTO.getDevParam();
        String merchantId = openBrandPaymentHubRequestDTO.getMerchantId();
        try {
            MerchantInfo merchantInfo = merchantService.getLatestMerchantById(merchantId);
            // 新增商户开通品牌
            if (Objects.isNull(merchantInfo)) {
                checkParams(devParam);
            } else {
                // 存量商户开通品牌, 品牌信息已经存在
                if (WosaiStringUtils.isNotEmpty(devParam.getBrandSn())) {
                    BrandModule brandModule = brandDomainService.getBrandModuleByBrandSn(devParam.getBrandSn());
                    if (Objects.isNull(brandModule)) {
                        throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
                    }
                    if (Objects.nonNull(brandModule.getFundManagementCompanyCode())) {
                        throw new BrandBusinessException(BrandBusinessExceptionEnum.PAYMENT_HUB_INFO_ALREADY_EXIST);
                    }
                } else {
                    // 新增品牌开通收付通
                    checkParams(devParam);
                    BrandMerchantModule brandMerchantInfo = brandDomainService.getBrandMerchantInfoByMerchantId(merchantId);
                    if (Objects.nonNull(brandMerchantInfo)) {
                        throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_A_BRAND);
                    }
                }
            }
            // 校验父品牌
            checkParentBrandId(devParam);
            return CheckOpenPaymentHubResponseDTO.success();
        } catch (BrandBusinessException e) {
            return CheckOpenPaymentHubResponseDTO.fail(String.valueOf(e.getCode()), e.getMessage());
        } catch (Exception e) {
            log.error("校验开通收付通系统异常 {}", openBrandPaymentHubRequestDTO.getMerchantId(), e);
            return CheckOpenPaymentHubResponseDTO.fail(String.valueOf(BrandBusinessExceptionEnum.SYSTEM_ERROR.getCode()), BrandBusinessExceptionEnum.SYSTEM_ERROR.getMessage());
        }
    }

    @Override
    public OpenPaymentHubResponseDTO openBrandPaymentHub(OpenBrandPaymentHubRequestDTO openBrandPaymentHubRequestDTO) {
        CheckOpenPaymentHubResponseDTO checkOpenPaymentHubResponseDTO = checkBrandOpenPaymentHub(openBrandPaymentHubRequestDTO);
        if (!checkOpenPaymentHubResponseDTO.isCheckSuccess()) {
            return new OpenPaymentHubResponseDTO()
                    .setStatus(OpenPaymentHubResponseDTO.FAIL_OPEN)
                    .setFailCode(checkOpenPaymentHubResponseDTO.getSubmitCheckCode())
                    .setFailMsg(checkOpenPaymentHubResponseDTO.getSubmitCheckMsg());
        }
        try {
            // 新增->创建品牌等信息
            BrandPaymentHubDevParamDTO devParam = openBrandPaymentHubRequestDTO.getDevParam();
            String brandSn = devParam.getBrandSn();
            if (WosaiStringUtils.isEmpty(devParam.getBrandSn())) {
                CreateBrandRequestDTO createBrandRequestDTO = JSON.parseObject(JSON.toJSONString(devParam), CreateBrandRequestDTO.class);
                createBrandRequestDTO.setMerchantSn(merchantService.getLatestMerchantById(openBrandPaymentHubRequestDTO.getMerchantId()).getSn());
                CreateBrandResponseDTO brand = brandBusiness.createBrand(createBrandRequestDTO);
                brandSn = brand.getSn();
            } else {
                // 存量->更新品牌等信息
                ExistBrandOpenPaymentHubRequestDTO existBrandOpenPaymentHubRequestDTO = new ExistBrandOpenPaymentHubRequestDTO();
                existBrandOpenPaymentHubRequestDTO.setBrandSn(devParam.getBrandSn());
                existBrandOpenPaymentHubRequestDTO.setFundManagementCompanyCode(devParam.getFundManagementCompanyCode());
                existBrandOpenPaymentHubRequestDTO.setConfig(devParam.getConfig());
                existBrandOpenPaymentHubRequestDTO.setAccounts(devParam.getAccounts());
                brandBusiness.existBrandOpenPaymentHub(existBrandOpenPaymentHubRequestDTO);
            }
            ChatBotUtil.sendMessageToBrandCreateChatBot(String.format("环境：%s，提交开通收付通品牌：%s", isProd ? "生产" : "测试", brandSn));
            return OpenPaymentHubResponseDTO.process();
        } catch (Exception e) {
            log.error("开通收付通品牌异常: {}", JSON.toJSONString(openBrandPaymentHubRequestDTO), e);
            throw e;
        }
    }

    @Override
    public OpenPaymentHubResponseDTO getBrandPaymentHubOpenStatus(OpenBrandPaymentHubRequestDTO openBrandPaymentHubRequestDTO) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(openBrandPaymentHubRequestDTO.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            return new OpenPaymentHubResponseDTO()
                    .setStatus(OpenPaymentHubResponseDTO.FAIL_OPEN)
                    .setFailCode(OpenPaymentHubFailCodeEnum.COMMON_FAIL.getCode())
                    .setFailMsg("开通失败，品牌商户信息不存在")
                    .setAppSubStatusText(OpenPaymentHubFailCodeEnum.COMMON_FAIL.getDesc());
        }
        BrandDetailInfoDTO brandDetailInfo = brandBusiness.getBrandDetailInfoByBrandId(brandMerchantModule.getBrandId(), true);
        if (Objects.isNull(brandDetailInfo)) {
            return new OpenPaymentHubResponseDTO()
                    .setStatus(OpenPaymentHubResponseDTO.FAIL_OPEN)
                    .setFailCode(OpenPaymentHubFailCodeEnum.COMMON_FAIL.getCode())
                    .setFailMsg("开通失败，品牌信息不存在")
                    .setAppSubStatusText(OpenPaymentHubFailCodeEnum.COMMON_FAIL.getDesc());
        }
        if (!checkConfig(brandDetailInfo.getFundManagementCompanyCode(), brandDetailInfo.getConfig())) {
            return new OpenPaymentHubResponseDTO()
                    .setStatus(OpenPaymentHubResponseDTO.PROCESS_OPEN)
                    .setFailMsg("开通中，配置信息待补充")
                    .setAppSubStatusText("配置信息待补充");
        }
        return new OpenPaymentHubResponseDTO().setStatus(OpenPaymentHubResponseDTO.SUCCESS_OPEN);
    }

    private void checkParentBrandId(BrandPaymentHubDevParamDTO devParam) {
        if (WosaiStringUtils.isNotEmpty(devParam.getParentBrandId())) {
            BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(devParam.getParentBrandId());
            if (Objects.isNull(brandModule)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARENT_BRAND_NOT_FIND);
            }
            if (WosaiStringUtils.isNotEmpty(brandModule.getParentId())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_IS_NOT_PARENT);
            }
            Optional<AppInfoOpenResult> brandPaymentHubAppInfo = merchantBusinessOpenClient.queryAppInfoOpenResultByMerchantId(merchantService.getMerchantBySn(brandModule.getMerchantSn(), null).getId()).stream().filter(r -> brandPaymentHubAppId.equals(r.getAppId())).findFirst();
            if (!brandPaymentHubAppInfo.isPresent() || brandPaymentHubAppInfo.get().getStatus() != AppInfoStatusEnum.SUCCESS) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_OPEN_PAYMENT_HUB);
            }
            if (!brandModule.getFundManagementCompanyCode().equals(devParam.getFundManagementCompanyCode())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_SUB_BRAND_FUND_MANAGE_CODE_NOT_EQUAL);
            }
        }
    }

    private boolean checkConfig(FundManagementCompanyEnum fundManagementCompanyEnum, BrandConfigDTO brandConfigDTO) {
        switch (fundManagementCompanyEnum) {
            case MY_BANK:
                if (WosaiStringUtils.isEmptyAny(brandConfigDTO.getMyBankConfig().getAppId(), brandConfigDTO.getMyBankConfig().getIsvOrgId(), brandConfigDTO.getMyBankConfig().getIsvPrivateKey(), brandConfigDTO.getMyBankConfig().getPublicKey(), brandConfigDTO.getMyBankConfig().getIsvPublicKey())) {
                    return false;
                }
                break;
            case PAB:
                if (WosaiStringUtils.isEmptyAny(brandConfigDTO.getPabConfig().getPartnerId())) {
                    return false;
                }
                break;
            case CITIC:
                if (WosaiStringUtils.isEmptyAny(brandConfigDTO.getCiticConfig().getMerchantId(), brandConfigDTO.getCiticConfig().getPublicKey(), brandConfigDTO.getCiticConfig().getPrivateKey(), brandConfigDTO.getCiticConfig().getPrivateKeyPassword())) {
                    return false;
                }
                break;
            case FUIOU:
                if (WosaiStringUtils.isEmptyAny(brandConfigDTO.getFuiouConfig().getMerchantNo(), brandConfigDTO.getFuiouConfig().getPublicKey(), brandConfigDTO.getFuiouConfig().getPrivateKey(), brandConfigDTO.getFuiouConfig().getFyPrivateKey(), brandConfigDTO.getFuiouConfig().getFyPublicKey())) {
                    return false;
                }
                break;
            default:
        }
        return true;
    }

    private void checkParams(BrandPaymentHubDevParamDTO devParam) {
        if (WosaiStringUtils.isNotEmpty(devParam.getBrandSn())) {
            return;
        }
        ParamsCheckHelper.checkFundManagementCompany(devParam.getFundManagementCompanyCode(), devParam.getConfig(), devParam.getAccounts());
        if (WosaiStringUtils.isEmpty(devParam.getName())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "品牌名称不能为空");
        }
        if (WosaiStringUtils.isEmpty(devParam.getProvince())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "所在省不能为空");
        }
        if (WosaiStringUtils.isEmpty(devParam.getCity())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "所在市不能为空");
        }
        if (WosaiStringUtils.isEmpty(devParam.getDistrict())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "所在区不能为空");
        }
        if (WosaiStringUtils.isEmpty(devParam.getAddress())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "详细地址不能为空");
        }
        if (WosaiStringUtils.isEmpty(devParam.getIndustry())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "行业编号不能为空");
        }
        if (WosaiStringUtils.isEmpty(devParam.getContactName())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "联系人姓名不能为空");
        }
        if (WosaiStringUtils.isEmpty(devParam.getContactPhone())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "联系人手机号不能为空");
        }
        if (Objects.isNull(devParam.getFundManagementCompanyCode())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "资管机构编号不能为空");
        }
    }
}
