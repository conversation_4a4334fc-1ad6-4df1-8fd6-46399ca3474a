package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wosai.cua.brand.business.api.enums.MerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "brand_merchant")
public class BrandMerchantDO {
    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id
     */
    @TableField(value = "brand_id")
    private String brandId;

    /**
     * 父品牌id
     */
    @TableField(value = "parent_brand_id")
    private String parentBrandId;

    /**
     * 商户id
     */
    @TableField(value = "merchant_id")
    private String merchantId;

    /**
     * 商户名称
     */
    @TableField(value = "merchant_name")
    private String merchantName;

    /**
     * 商户sn
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;

    /**
     * 商户类型：
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum
     */
    private String type;

    /**
     * 商户角色：FRANCHISEE-加盟商、SUPPLIER-供应商、BRAND_OWNER-品牌商、BRAND_OPERATED_STORES-品牌自营门店、SERVICE_PROVIDER_SQB-服务商（收钱吧）
     * @see MerchantTypeEnum
     */
    @TableField(value = "merchant_type")
    private String merchantType;

    /**
     * 品牌商户对接模式
     * @see MerchantDockingModeEnum
     */
    @TableField(value = "merchant_docking_mode")
    private String merchantDockingMode;

    /**
     * 支付模式：1收付通 2商家模式 3微信品牌模式 4支付宝品牌模式 5支付宝微信品牌模式
     */
    @TableField(value = "payment_mode")
    private Integer paymentMode;

    /**
     * 子账号
     */
    @TableField(value = "sub_account_no")
    private String subAccountNo;

    /**
     * 系统会员id
     */
    @TableField(value = "member_id")
    private String memberId;

    /**
     *
     */
    @TableField(value = "top_up_account_no")
    private String topUpAccountNo;

    @TableField(value = "top_up_account_name")
    private String topUpAccountName;

    /**
     * 关联品牌商户门店编号
     */
    @TableField(value = "associated_sqb_store_id")
    private String associatedSqbStoreId;

    /**
     * 关联品牌商户门店编号
     */
    @TableField(value = "sqb_store_sn")
    private String sqbStoreSn;

    /**
     * 关联美团门店号
     */
    @TableField(value = "associated_meituan_store_sn")
    private String associatedMeituanStoreSn;

    /**
     * 关联饿了么门店号
     */
    @TableField(value = "associated_elm_store_sn")
    private String associatedElmStoreSn;

    /**
     * 抖音门店号
     */
    @TableField(value = "dy_store_sn")
    private String dyStoreSn;

    /**
     * 外部商户号
     */
    @TableField(value = "out_merchant_no")
    private String outMerchantNo;

   /**
    * 第三方商户编号
    */
   @TableField(value = "three_party_merchant_sn")
   private String threePartyMerchantSn;

    @TableField(value = "strategy_id")
    private Long strategyId;

    @TableField(value = "account_open_status")
    private String accountOpenStatus;

    @TableField(value = "account_open_failure_reason")
    private String accountOpenFailureReason;

    /**
     * 额外信息
     */
    private String extra;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private Date updatedTime;

    /**
     * 关联时间
     */
    @TableField(value = "associated_time")
    private Date associatedTime;

    /**
     * 版本
     */
    private Integer version;

    @TableField(value = "bank_card_activate_status")
    private String bankCardActivateStatus;

    @TableField(value = "account_opened_time")
    private Date accountOpenedTime;

}