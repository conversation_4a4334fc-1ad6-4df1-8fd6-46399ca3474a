package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@Data
public class CreateMemberRequest extends VfinanceBaseRequest{

    /**
     * 外部系统用户ID（统一使用营业执照上的【统一社会信用代码】）
     */
    private String uid;
    /**
     * 会员名称（门店使用店铺名称，内部公司使用公司名称，专户按业务命名。）
     */
    @JSONField(name = "member_name")
    private String memberName;

    /**
     * 平台用户手机号
     */
    private String mobile;
    /**
     * 邮箱号
     */
    private String email;
    /**
     * 用户真实姓名（企业（直营店、部分加盟店、内部公司）：公司名称。个体工商户：法定代表人名称）
     */
    @JSONField(name = "real_name")
    private String realName;
    /**
     * 会员证件类型（企业：73 个体工商户：1）
     *
     */
    @JSONField(name = "member_globle_type")
    private String memberGlobalType;
    /**
     * 会员证件号码（企业：统一社会信用代码 个体工商户：身份证号）
     */
    @JSONField(name = "member_globle_id")
    private String memberGlobalId;
    /**
     * IP地址
     */
    @JSONField(name = "ip_address")
    private String ipAddress;
    /**
     * MAC地址
     */
    @JSONField(name = "mac_address")
    private String macAddress;
    /**
     * 签约渠道
     */
    @JSONField(name = "signing_channel")
    private String signingChannel;

    @JSONField(name = "indiv_business_flag")
    private String indivBusinessFlag;

    /**
     * 公司名称（个体工商户、企业必输）
     */
    @JSONField(name = "company_name")
    private String companyName;

    /**
     * 公司证件类型（73:统一社会信用代码）
     */
    @JSONField(name = "company_global_type")
    private String companyGlobalType;

    /**
     * 公司证件号码（统一社会信用代码）
     */
    @JSONField(name = "company_global_id")
    private String companyGlobalId;

    /**
     * 店铺id（个体工商户、企业必输）
     */
    @JSONField(name = "shop_id")
    private String shopId;

    /**
     * 店铺名称（个体工商户、企业必输）
     */
    @JSONField(name = "shop_name")
    private String shopName;

    /**
     * 法人标志（个体工商户、企业必输）
     */
    @JSONField(name = "rep_flag")
    private String repFlag;

    /**
     * 法人名称（个体工商户、企业必输）
     */
    @JSONField(name = "repr_client_name")
    private String reprClientName;

    /**
     * 法人证件类型（个体工商户、企业必输）
     */
    @JSONField(name = "repr_global_type")
    private String reprGlobalType;

    /**
     * 法人证件号码（个体工商户、企业必输）
     */
    @JSONField(name = "repr_global_id")
    private String reprGlobalId;

    public CreateMemberRequest(String partnerId) {
        super();
        this.service = CREATE_MEMBER;
        super.partnerId = partnerId;
    }
}
