package com.wosai.cua.brand.business.service.module.bank;

import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.enums.SensitiveFieldEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 银行卡模型
 * <AUTHOR>
 */
@SensitiveClass
@Accessors(chain = true)
@Data
public class BankCardModule {

    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 商户id
     */
    private String merchantId;
    /**
     * 收钱吧银行卡id
     */
    private String bankCardId;

    /**
     * 开户银行：PAB-平安银行，CMBC-中国民生银行，CMB-招商银行，SPDB-浦发银行，CITIC-中信银行，CIB-兴业银行，CGB-广发银行，HXB-华夏银行，CEB-中国光大银行，ICBC-中国工商银行，CCB-中国建设银行，ABC-中国农业银行，BOC-中国银行，BOCOM-交通银行
     */
    @Deprecated
    private String bankOfDeposit;

    /**
     * 账户类型：1-个人，2-企业
     */
    private Integer accountType;

    /**
     * 持卡人姓名
     */
    private String holder;

    /**
     * 银行预留手机号
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.MOBILE)
    private String reservedMobileNumber;


    private String thirdBankCardId;

    /**
     * 三方系统会员id
     */
    private String memberId;

    /**
     * 是否是默认卡：0-否，1-是
     */
    private Boolean isDefault;

    /**
     * 是否删除，0-否、1-是
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 激活失败原因
     */
    private String activateFailReason;

    /**
     * 银行卡状态：0-未激活，1-已激活
     */
    private Integer status;

    private String bankCardNo;
    private String openingBankNumber;
    private String mobile;

    private Date activationTime;
}
