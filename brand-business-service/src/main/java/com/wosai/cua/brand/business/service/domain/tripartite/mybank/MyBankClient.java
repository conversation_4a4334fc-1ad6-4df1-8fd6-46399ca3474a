package com.wosai.cua.brand.business.service.domain.tripartite.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MyBankRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankUploadRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

public interface MyBankClient {

    /**
     * @param <T>
     * @param request
     * @return
     * @throws MybankApiException
     */
    <T extends MybankResponse> T execute(MyBankRequest<T> request) throws MybankApiException;

    /**
     * @param <T>
     * @param request
     * @return
     * @throws MybankApiException
     */
    <T extends MybankResponse> T execute(MybankUploadRequest<T> request) throws MybankApiException;

}