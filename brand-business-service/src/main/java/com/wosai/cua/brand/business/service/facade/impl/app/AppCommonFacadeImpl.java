package com.wosai.cua.brand.business.service.facade.impl.app;

import com.alibaba.fastjson2.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.CommonOperationDTO;
import com.wosai.cua.brand.business.api.dto.request.app.DictionaryRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.DictionaryResponseDTO;
import com.wosai.cua.brand.business.api.facade.app.AppCommonFacade;
import com.wosai.cua.brand.business.service.business.CommonBusiness;
import com.wosai.cua.brand.business.service.module.common.dictionary.DictionaryModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class AppCommonFacadeImpl implements AppCommonFacade {

    private final CommonBusiness commonBusiness;

    @Autowired
    public AppCommonFacadeImpl(CommonBusiness commonBusiness) {
        this.commonBusiness = commonBusiness;
    }

    @Override
    public List<DictionaryResponseDTO> getDictionaryList(DictionaryRequestDTO dto) {
        List<DictionaryModule> dictionaryModules = commonBusiness.getDictionaryModules(dto.getDictionary().name());
        return JSON.parseArray(JSON.toJSONString(dictionaryModules), DictionaryResponseDTO.class);
    }

    @Override
    public Boolean commonOperation(CommonOperationDTO commonOperation) {
        return commonBusiness.commonOperation(commonOperation.getBrandId(), commonOperation.getOperationMerchantId(), commonOperation.getOperation(), commonOperation.getScene());
    }
}
