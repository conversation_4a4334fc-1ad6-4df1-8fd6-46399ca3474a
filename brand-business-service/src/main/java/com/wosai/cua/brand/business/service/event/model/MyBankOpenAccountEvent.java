package com.wosai.cua.brand.business.service.event.model;

import com.wosai.cua.brand.business.service.event.Event;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.type.MyBankOpenAccountEventType;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MyBankOpenAccountEvent implements Event {

    /**
     * 品牌ID
     */
    private String brandId;

    /**
     * 配置模块
     */
    private MyBankConfigModule myBankConfigModule;

    /**
     * 账户类型
     */
    private String myBankAccountType;

    /**
     * 网商银行商户ID
     */
    private String myBankMerchantId;

    /**
     * 商户ID
     */
    private String merchantSn;

    @Override
    public EventType getEventType() {
        return MyBankOpenAccountEventType.SUCCESS;
    }
}
