package com.wosai.cua.brand.business.service.domain.tripartite.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

/**
 * 响应解释器接口,响应格式是XML
 */
public interface MybankParser<T extends MybankResponse> {

    /**
     * 把响应XML字符串解释成相应的领域对象
     *
     * @param responseXml 响应字符串
     * @return 领域对象
     */
    public T parse(String responseXml) throws MybankApiException;

    /**
     * 获取响应类类型
     */
    public Class<T> getResponseClass() throws MybankApiException;

}