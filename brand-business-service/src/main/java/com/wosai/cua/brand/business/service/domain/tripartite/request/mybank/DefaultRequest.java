package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

public interface DefaultRequest<T extends MybankResponse> extends TripartiteSystemCallRequest {

    /**
     * 获取API接口代码
     *
     * @return API代码
     */
    String getApiFunction();

    /**
     * 获取当前API接口版本
     *
     * @return API版本
     */
    String getApiVersion();

    /**
     * 设置当前API接口版本
     *
     * @param apiVersion API版本
     */
    void setApiVersion(String apiVersion);

    /**
     * 获取当前API接口响应结果类型
     *
     * @return 响应类型
     */
    Class<T> getResponseClass();
}