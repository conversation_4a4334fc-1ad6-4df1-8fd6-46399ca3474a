package com.wosai.cua.brand.business.service.domain.service.impl.merchant;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.request.merchant.BaseCreateMerchantDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.MerchantCenterDomainService;
import com.wosai.mc.model.req.AccountReq;
import com.wosai.mc.model.req.CreateMerchantAndStoreReq;
import com.wosai.mc.model.req.CreateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.CreateStoreReq;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
public class PersonalMerchantDomainServiceImpl implements MerchantCenterDomainService {

    @Override
    public BrandMerchantTypeEnum getBrandMerchantType() {
        return BrandMerchantTypeEnum.PERSONAL;
    }

    @Override
    public CreateMerchantAndStoreReq getCreateMerchantAndStoreReq(BaseCreateMerchantDTO createMerchantDto, String ucUserId) {
        BaseCreateMerchantDTO.PersonalMerchantDTO personalMerchant = JSON.parseObject(JSON.toJSONString(createMerchantDto), BaseCreateMerchantDTO.PersonalMerchantDTO.class);
        return this.getMerchantComplete(personalMerchant, ucUserId);
    }

    private CreateMerchantAndStoreReq getMerchantComplete(BaseCreateMerchantDTO.PersonalMerchantDTO personalMerchant, String ucUserId) {
        CreateMerchantAndStoreReq merchantComplete = new CreateMerchantAndStoreReq();
        // 创建商户
        CreateMerchantReq merchantReq = new CreateMerchantReq();
        merchantReq.setId(UUID.randomUUID().toString());
        merchantReq.setName(personalMerchant.getMerchantName());
        merchantReq.setBusinessName(personalMerchant.getMerchantName());
        merchantReq.setContactName(personalMerchant.getName());
        merchantReq.setContactCellphone(personalMerchant.getCellphone());
        merchantReq.setContactEmail(personalMerchant.getEmail());
        merchantReq.setStreetAddress(personalMerchant.getAddress());
        merchantReq.setProvince(personalMerchant.getProvince());
        merchantReq.setCity(personalMerchant.getCity());
        merchantReq.setDistrict(personalMerchant.getDistrict());
        merchantComplete.setMerchant(merchantReq);
        CreateMerchantBusinessLicenseReq license = this.getCreateMerchantBusinessLicenseReq(personalMerchant, merchantReq);
        merchantComplete.setLicense(license);
        AccountReq account = this.getAccountReq(personalMerchant, ucUserId);
        merchantComplete.setAccount(account);
        CreateStoreReq store = new CreateStoreReq();
        store.setMerchantId(merchantReq.getId());
        store.setName(personalMerchant.getStoreName());
        merchantComplete.setStore(store);
        return merchantComplete;
    }

    private CreateMerchantBusinessLicenseReq getCreateMerchantBusinessLicenseReq(BaseCreateMerchantDTO.PersonalMerchantDTO personalMerchant, CreateMerchantReq merchantReq) {
        CreateMerchantBusinessLicenseReq license = new CreateMerchantBusinessLicenseReq();
        license.setMerchantId(merchantReq.getId());
        license.setType(0);
        license.setLegalPersonName(personalMerchant.getName());
        license.setLegalPersonIdType(personalMerchant.getIdType());
        license.setLegalPersonIdNumber(personalMerchant.getIdNumber());
        license.setLegalPersonIdCardFrontPhoto("https://images.wosaimg.com/c9/8fb1c032d51d7ed1539d6d54d014400ebb601c.png");
        license.setIdValidity("********-********");
        return license;
    }

    private AccountReq getAccountReq(BaseCreateMerchantDTO.PersonalMerchantDTO personalMerchant, String ucUserId) {
        AccountReq account = new AccountReq();
        account.setIdentifier(personalMerchant.getCellPhoneNumber());
        account.setIdentityType(1);
        account.setUcUserId(ucUserId);
        account.setName(personalMerchant.getName());
        account.setIdType(personalMerchant.getIdType());
        account.setIdNumber(personalMerchant.getIdNumber());
        account.setIdCardFrontPhoto("https://images.wosaimg.com/c9/8fb1c032d51d7ed1539d6d54d014400ebb601c.png");
        account.setIdCardValidity("********-********");
        return account;
    }
}
