package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class RequestDocument extends MybankObject {
    private static final long serialVersionUID = -6416239359269887194L;

    @XmlElementRef
    private Request request;

    public Request getRequest() {
        return request;
    }

    public void setRequest(Request request) {
        this.request = request;
    }

    public RequestDocument() {
    }

    public RequestDocument(Request request) {
        this.request = request;
    }
}