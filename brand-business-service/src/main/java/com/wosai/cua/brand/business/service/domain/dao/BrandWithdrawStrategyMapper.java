package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandWithdrawStrategyDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandWithdrawStrategyMapper extends BaseMapper<BrandWithdrawStrategyDO> {
    /**
     * 根据策略id获取提现策略
     * @param strategyId 策略id
     * @return 提现策略对象
     */
    BrandWithdrawStrategyDO getBrandWithdrawStrategyByStrategyId(@Param("strategyId")Long strategyId);

    /**
     * 根据策略id获取提现策略
     * @param strategyIdList 策略id集合
     * @return 提现策略对象
     */
    List<BrandWithdrawStrategyDO> getBrandWithdrawStrategyByStrategyIdList(@Param("strategyIdList")List<Long> strategyIdList);
    /**
     * 根据品牌id获取提现策略集合
     * @param brandId 品牌id
     * @return 提现策略集合
     */
    List<BrandWithdrawStrategyDO> getBrandWithdrawStrategyListByBrandId(@Param("brandId")String brandId);

    /**
     * 批量删除品牌提现策略（逻辑删除）
     * @param strategyIdList 策略id集合
     * @return 删除条数
     */
    int deleteStrategyByStrategyIdList(@Param("strategyIdList")List<Long> strategyIdList);

    /**
     * 统计策略总数
     * @param brandId 品牌下
     * @return 策略数量
     */
    int countStrategyByBrandId(@Param("brandId")String brandId);

    /**
     * 分页查询品牌策略
     * @param brandId 品牌id
     * @param offset 偏移量
     * @param pageSize 每页查询数量
     * @return 策略集合
     */
    List<BrandWithdrawStrategyDO> pageStrategyByBrandId(@Param("brandId")String brandId,@Param("offset")int offset,@Param("pageSize")int pageSize);

    /**
     * 根据id更新提现策略
     * @param brandWithdrawStrategy 提现策略数据
     * @return 更新条数
     */
    int updateBrandWithdrawStrategyById(BrandWithdrawStrategyDO brandWithdrawStrategy);
}