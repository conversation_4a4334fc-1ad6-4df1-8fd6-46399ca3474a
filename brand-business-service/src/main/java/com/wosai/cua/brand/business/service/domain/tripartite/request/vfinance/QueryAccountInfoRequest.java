package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * <AUTHOR>
 */
public class QueryAccountInfoRequest extends VfinanceBaseRequest{
    /**
     * 会员唯一标识号	String(32)	会员id，若query_flag为1，2或4，则该字段必输	可空
     */
    @JSONField(name = "member_id")
    private String memberId;
    /**
     * 查询标志	String(1)	1:普通子账号2：商户子账号 3：功能子账号 4：资方子账号	不可空	2
     */
    @JSONField(name = "query_flag")
    private String queryFlag;
    /**
     * 页码	String(6)	起始值为1，每次最多返回20条记录，第二页返回的记录数为第21至40条记录，第三页为41至60条记录，顺序均按照建立时间的先后	必输	1
     */
    @JSONField(name = "page_num")
    private String pageNum;

    public QueryAccountInfoRequest() {
        super();
        super.service = QUERY_ACCOUNT_INFO;
    }

    public QueryAccountInfoRequest(String partnerId) {
        super();
        super.service = QUERY_ACCOUNT_INFO;
        super.partnerId = partnerId;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getQueryFlag() {
        return queryFlag;
    }

    public void setQueryFlag(String queryFlag) {
        this.queryFlag = queryFlag;
    }

    public String getPageNum() {
        return pageNum;
    }

    public void setPageNum(String pageNum) {
        this.pageNum = pageNum;
    }
}
