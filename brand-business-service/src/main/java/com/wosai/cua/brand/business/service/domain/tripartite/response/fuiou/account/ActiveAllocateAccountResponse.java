package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ActiveAllocateAccountResponse extends TripartiteSystemCallResponse {

    private ActiveAllocateAccountResponseBody body;

}
