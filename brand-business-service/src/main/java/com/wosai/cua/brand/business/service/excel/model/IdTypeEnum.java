package com.wosai.cua.brand.business.service.excel.model;

import com.shouqianba.cua.annotation.ITextValueEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
public enum IdTypeEnum implements ITextValueEnum<Integer> {

    ID_CARD(1, "身份证"),
    H_M_PASS_CARD(2, "港澳居民往来内地通行证"),
    TW_PASS_CARD(3, "台湾居民往来内地通行证"),
    PASSPORT(4, "非中华人民共和国护照"),
    PRC_PASSPORT(5, "中华人民共和国护照"),
    HK_MACAO_RESIDENCE_PERMIT(6, "港澳居民居住证"),
    TAIWAN_RESIDENCE_PERMIT(7, "台湾居民居住证"),
    ;


    @Getter
    private final Integer value;
    private final String text;

    IdTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public Integer getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static IdTypeEnum getByText(String text) {
        for (IdTypeEnum idTypeEnum : values()) {
            if (idTypeEnum.getText().equals(text)) {
                return idTypeEnum;
            }
        }
        return null;
    }


}
