package com.wosai.cua.brand.business.service.domain.tripartite.mybank.parse.xml;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankParser;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

/**
 * XML对象解析器
 */
public class ObjectXmlParser<T extends MybankResponse> implements MybankParser<T> {

    private Class<T> clazz;

    public ObjectXmlParser(Class<T> clazz) { this.clazz = clazz; }

    @Override
    public T parse(String responseXml) throws MybankApiException {
        XmlConverter converter = XmlConverter.getInstance();
        return converter.toResponse(responseXml, clazz);
    }

    @Override
    public Class<T> getResponseClass() {
        return clazz;
    }
}