package com.wosai.cua.brand.business.service.helper;

import com.google.common.collect.Maps;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.CreateBrandRequestDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.WithdrawCycleTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class ParamsCheckHelper {

    private ParamsCheckHelper() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 提现策略时间检查方法
     */
    private static final Map<WithdrawCycleTypeEnum, Consumer<Integer>> CHECK_WITHDRAW_CYCLE_METHOD = Maps.newHashMap();

    /**
     * 校验品牌账户相关参数
     */
    private static final Map<FundManagementCompanyEnum, BiConsumer<ConfigDTO, AccountsDTO>> CHECK_FUND_MANAGEMENT_COMPANY_PARAMS_METHOD = Maps.newHashMap();
    /**
     * 校验品牌商户相关参数
     */
    private static final Map<FundManagementCompanyEnum, Consumer<CreateBrandMerchantRequestDTO>> CHECK_FUND_MANAGEMENT_COMPANY_MERCHANT_PARAMS_METHOD = Maps.newHashMap();
    /**
     * 天的最小时间（时）
     */
    private static final int MIN_DAY_TIME = 1;
    /**
     * 天的最大时间（时）
     */
    private static final int MAX_DAY_TIME = 23;
    /**
     * 月的最小时间（日期）
     */
    private static final int MIN_MONTH_TIME = 1;
    /**
     * 月的最大时间（日期）
     */
    private static final int MAX_MONTH_TIME = 31;
    private static final int MIN_WEEK_TIME = 1;
    private static final int MAX_WEEK_TIME = 7;

    static {
        CHECK_WITHDRAW_CYCLE_METHOD.put(WithdrawCycleTypeEnum.DAY, (time -> {
            if (time < MIN_DAY_TIME || time > MAX_DAY_TIME) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.WITHDRAW_STRATEGY_DAY_CHECK_FAIL);
            }
        }));
        CHECK_WITHDRAW_CYCLE_METHOD.put(WithdrawCycleTypeEnum.MONTH, (time -> {
            if (time < MIN_MONTH_TIME || time > MAX_MONTH_TIME) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.WITHDRAW_STRATEGY_MONTH_CHECK_FAIL);
            }
        }));
        CHECK_WITHDRAW_CYCLE_METHOD.put(WithdrawCycleTypeEnum.WEEK, (time -> {
            if (time < MIN_WEEK_TIME || time > MAX_WEEK_TIME) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.WITHDRAW_STRATEGY_WEEK_CHECK_FAIL);
            }
        }));
        CHECK_FUND_MANAGEMENT_COMPANY_PARAMS_METHOD.put(FundManagementCompanyEnum.PAB, (configDTO, accountsDTO) -> {
            if (Objects.isNull(configDTO) || Objects.isNull(configDTO.getPabConfig())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PAB_CONFIG_CAN_NOT_NULL);
            }
            if (StringUtils.isBlank(configDTO.getPabConfig().getPartnerId())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "合作方id不能为空");
            }
            if (Objects.isNull(accountsDTO) || Objects.isNull(accountsDTO.getPabAccounts())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PAB_ACCOUNTS_CAN_NOT_NULL);
            }
            if (StringUtils.isBlank(accountsDTO.getPabAccounts().getFundSettlementAccount())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "资金结算账户不能为空");
            }
            if (StringUtils.isBlank(accountsDTO.getPabAccounts().getFundSettlementAccountName())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "资金结算账户名称不能为空");
            }
            if (StringUtils.isBlank(accountsDTO.getPabAccounts().getFundSummaryAccount())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "资金汇总专用账户不能为空！");
            }
            if (StringUtils.isBlank(accountsDTO.getPabAccounts().getFundSummaryAccountName())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "资金汇总专用账户名不能为空！");
            }
        });

        CHECK_FUND_MANAGEMENT_COMPANY_PARAMS_METHOD.put(FundManagementCompanyEnum.MY_BANK, (configDTO, accountsDTO) -> {
            if (Objects.isNull(accountsDTO) || Objects.isNull(accountsDTO.getMyBankAccounts())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.MY_BANK_ACCOUNTS_CAN_NOT_NULL);
            }
            if (StringUtils.isBlank(accountsDTO.getMyBankAccounts().getOrderManagementAccount())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "订单账款管理专户不能为空！");
            }
        });

        CHECK_FUND_MANAGEMENT_COMPANY_PARAMS_METHOD.put(FundManagementCompanyEnum.CITIC, (configDTO, accountsDTO) -> {
            if (Objects.isNull(configDTO) || Objects.isNull(configDTO.getCiticConfig())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.CITIC_CONFIG_CAN_NOT_NULL);
            }
            if (StringUtils.isBlank(configDTO.getCiticConfig().getMerchantId())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "中信银行平台商户号不能为空");
            }
        });

        CHECK_FUND_MANAGEMENT_COMPANY_PARAMS_METHOD.put(FundManagementCompanyEnum.FUIOU, (configDTO, accountsDTO) -> {
            if (Objects.isNull(configDTO) || Objects.isNull(configDTO.getFuiouConfig())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.FUIOU_CONFIG_CAN_NOT_NULL);
            }
            if (Objects.isNull(configDTO.getFuiouConfig().getMerchantDockingMode())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "富友支付品牌对接模式不能为空");
            }
            if (StringUtils.isBlank(configDTO.getFuiouConfig().getMerchantNo())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "富管家商户号不能为空");
            }
        });

        CHECK_FUND_MANAGEMENT_COMPANY_MERCHANT_PARAMS_METHOD.put(FundManagementCompanyEnum.FUIOU, (requestDto -> {
            if (Objects.isNull(requestDto.getBankCard())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR,"银行卡信息不能为空");
            }
            if (StringUtils.isBlank(requestDto.getBankCard().getBankCardNo())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR,"银行卡号不能为空");
            }
            if (StringUtils.isBlank(requestDto.getBankCard().getHolder())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR,"账户持有人名称holder不能为空");
            }
            if (StringUtils.isBlank(requestDto.getBankCard().getOpeningNumber())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR,"开户行号openingNumber，不能为空");
            }
            if (StringUtils.isBlank(requestDto.getBankCard().getReservedMobileNumber())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR,"银行预留手机号不能为空");
            }
            if (Objects.isNull(requestDto.getBankCard().getType())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR,"银行卡类型不能为空");
            }
        }));
    }

    /**
     * 校验提现策略时间
     */
    public static void checkWithdrawCycleTime(WithdrawCycleTypeEnum withdrawCycleTypeEnum, Integer time) {
        if (Objects.isNull(withdrawCycleTypeEnum)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.WITHDRAW_CYCLE_TYPE_CAN_NOT_FIND);
        }
        CHECK_WITHDRAW_CYCLE_METHOD.get(withdrawCycleTypeEnum).accept(time);
    }

    /**
     * 校验品牌账户相关参数
     */
    public static void checkFundManagementCompanyParams(CreateBrandRequestDTO requestDto) {
        if (Objects.isNull(requestDto)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR);
        }
        BiConsumer<ConfigDTO, AccountsDTO> consumer = CHECK_FUND_MANAGEMENT_COMPANY_PARAMS_METHOD.get(requestDto.getFundManagementCompanyCode());
        if (Objects.nonNull(consumer)){
            consumer.accept(requestDto.getConfig(), requestDto.getAccounts());
        }
    }

    public static void checkFundManagementCompany(FundManagementCompanyEnum fundManagementCompanyEnum, ConfigDTO configDTO, AccountsDTO accountsDTO) {
        if (Objects.isNull(configDTO) || Objects.isNull(accountsDTO)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "配置信息或账户信息不能为空");
        }
        BiConsumer<ConfigDTO, AccountsDTO> consumer = CHECK_FUND_MANAGEMENT_COMPANY_PARAMS_METHOD.get(fundManagementCompanyEnum);
        if (Objects.nonNull(consumer)){
            consumer.accept(configDTO, accountsDTO);
       }
    }

    public static void checkFundManagementCompanyMerchantParams(FundManagementCompanyEnum fundManagementCompanyCode,CreateBrandMerchantRequestDTO requestDto) {
        if (Objects.isNull(requestDto)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR);
        }
        Consumer<CreateBrandMerchantRequestDTO> consumer = CHECK_FUND_MANAGEMENT_COMPANY_MERCHANT_PARAMS_METHOD.get(fundManagementCompanyCode);
        if (Objects.nonNull(consumer)){
            consumer.accept(requestDto);
        }
    }

    private static final int[] WEIGHT = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    private static final char[] VALID_CHECK_SUM = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

    public static boolean isValidChineseID(String id) {
        if (id == null || id.length() != 18) {
            return false;
        }

        // 检查格式
        if (!Pattern.matches("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$", id)) {
            return false;
        }

        // 验证出生日期
        try {
            LocalDate.parse(id.substring(6, 14), DateTimeFormatter.BASIC_ISO_DATE);
        } catch (DateTimeParseException e) {
            return false;
        }

        // 验证校验码
        char[] chars = id.toUpperCase().toCharArray();
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += (chars[i] - '0') * WEIGHT[i];
        }
        return chars[17] == VALID_CHECK_SUM[sum % 11];
    }

    /**
     * 校验证件有效期开始时间
     * @param idValidityStart 校验证件有效期开始时间
     * @return true - 合法，false - 非法
     */
    public static boolean checkIdValidityStart(String idValidityStart) {
        try {
            // 严格校验有效期
            Date date = DateUtils.parseDateStrictly(idValidityStart, "yyyyMMdd");
            if (date.after(new Date())) {
                return false;
            }
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 校验证件有效期截止时间
     * @param idValidityEnd 校验证件有效期截止时间
     * @return true - 合法，false - 非法
     */
    public static boolean checkIdValidityEnd(String idValidityEnd) {
        if ("长期有效".equals(idValidityEnd)) {
            return true;
        }
        try {
            // 严格校验有效期
            Date date = DateUtils.parseDateStrictly(idValidityEnd, "yyyyMMdd");
            if (date.before(new Date())) {
                return false;
            }
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 证件有效期校验
     *   1. 格式必须为yyyyMMdd-yyyyMMdd
     *   2. 日期必须有效 不能是20209999这种
     *   3. 证件截止时间需大于的系统当前时间60天
     *   4. 校验有效期是否为整数年等
     * @param identityValidity  证件有效期
     */
    public static boolean checkIdValidity(String identityValidity) {
        if (identityValidity.length() != 17 || identityValidity.charAt(8) != '-') {
            return false;
        }
        String[] idValidityArray = WosaiStringUtils.split(identityValidity, "\\-");

        try {
            // 严格校验有效期
            DateUtils.parseDateStrictly(idValidityArray[0], "yyyyMMdd");
            DateUtils.parseDateStrictly(idValidityArray[1], "yyyyMMdd");
        } catch (ParseException e) {
            return false;
        }
        String startYearStr = idValidityArray[0].substring(0, 4);
        String startDateStr = idValidityArray[0].substring(4);
        String endYearStr = idValidityArray[1].substring(0, 4);
        String endDateStr = idValidityArray[1].substring(4);

        int result = Integer.parseInt(endYearStr) - Integer.parseInt(startYearStr);

        if (!"9999".equals(endYearStr)) {
            if (!checkYears(result) || !checkDate(startDateStr, endDateStr)) {
                return false;
            }
        }
        return true;
    }

    private static boolean checkYears(int result) {
        return result == 5 || result == 10 || result == 20;
    }

    private static boolean checkDate(String startDateStr, String endDateStr) {
        if ("0229".equals(startDateStr)) {
            return "0229".equals(endDateStr) || "0301".equals(endDateStr);
        } else {
            return startDateStr.equals(endDateStr);
        }
    }

    /**
     * 校验银行卡有效期
     * @param cardValidity
     * @return
     */
    public static boolean checkCardValidity(String cardValidity) {
        if ("长期有效".equals(cardValidity)) {
            return true;
        }
        try {
            // 严格校验有效期
            DateUtils.parseDateStrictly(cardValidity, "yyyy年MM月");
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    public static void checkFundManagementCompanyParams(FundManagementCompanyEnum fundManagementCompanyEnum, ConfigDTO configDTO, AccountsDTO accountsDTO) {
        CHECK_FUND_MANAGEMENT_COMPANY_PARAMS_METHOD.get(fundManagementCompanyEnum).accept(configDTO, accountsDTO);
    }
}
