package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantprodMerchNotifyModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 商户入驻结果通知接口请求
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class MerchantNotifyRequest extends MybankResponse {

    private static final long serialVersionUID = 623234331891696246L;
    @XmlElementRef
    private MerchantprodMerchNotify merchantprodMerchNotify;

    public MerchantprodMerchNotify getMerchantprodMerchNotify() {
        return merchantprodMerchNotify;
    }

    public void setMerchantprodMerchNotify(MerchantprodMerchNotify merchantprodMerchNotify) {
        this.merchantprodMerchNotify = merchantprodMerchNotify;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "request")
    public static class MerchantprodMerchNotify extends MybankObject {

        private static final long serialVersionUID = 352484418652976682L;
        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private RequestHead requestHead;

        /**
         * 业务应答参数（body）
         */
        @XmlElementRef
        private MerchantprodMerchNotifyModel merchantprodMerchNotifyModel;

        public RequestHead getRequestHead() {
            return requestHead;
        }
        public void setRequestHead(RequestHead requestHead) {
            this.requestHead = requestHead;
        }

        public MerchantprodMerchNotifyModel getMerchantprodMerchNotifyModel() {
            return merchantprodMerchNotifyModel;
        }
        public void setMerchantprodMerchNotifyModel(MerchantprodMerchNotifyModel merchantprodMerchNotifyModel) {
            this.merchantprodMerchNotifyModel = merchantprodMerchNotifyModel;
        }
    }
}