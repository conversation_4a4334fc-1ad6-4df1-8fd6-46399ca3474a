package com.wosai.cua.brand.business.service.excel.model;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
public enum PayTypeEnum {

    LEGAL_SETTLEMENT(1, "经营者/法人对私"),
    PUBLIC_SETTLEMENT(2, "对公账户"),
    NO_LEGAL_SETTLEMENT(3, "非法人对私"),
    ;


    @Getter
    private final Integer value;
    private final String text;

    PayTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public Integer getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static PayTypeEnum getByText(String text) {
        for (PayTypeEnum idTypeEnum : values()) {
            if (idTypeEnum.getText().equals(text)) {
                return idTypeEnum;
            }
        }
        return null;
    }


}
