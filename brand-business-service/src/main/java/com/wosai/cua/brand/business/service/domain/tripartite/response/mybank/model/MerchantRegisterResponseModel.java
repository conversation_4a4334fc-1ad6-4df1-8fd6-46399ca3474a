package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 商户入驻申请<ant.mybank.merchantprod.merch.register>
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
@Data
public class MerchantRegisterResponseModel extends MybankObject {

    private static final long serialVersionUID = -870987562799451097L;
    /**
     * 返回码组件
     * <p>
     * 返回S不代表入驻成功，只代表入驻申请已受理。需要回查。
     */
    @XmlElementRef
    private RespInfo respInfo;

    /**
     * 外部商户号
     */
    @XmlElement(name = "OutMerchantId")
    private String outMerchantId;

    /**
     * 申请单号。合作方可通过此单号回查商户进件结果
     */
    @XmlElement(name = "OrderNo")
    private String orderNo;

    /**
     * 外部交易号
     */
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;
}