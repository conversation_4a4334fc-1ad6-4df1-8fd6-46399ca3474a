package com.wosai.cua.brand.business.service.domain.service.impl.fund;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.domain.dao.BrandAccountDOMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandAccountDO;
import com.wosai.cua.brand.business.service.domain.service.BrandFundManagementCompanyDomainService;
import com.wosai.cua.brand.business.service.module.account.AccountsModule;
import com.wosai.cua.brand.business.service.module.account.pab.PabAccountModule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class PabManagementServiceImpl implements BrandFundManagementCompanyDomainService {

    private final BrandAccountDOMapper brandAccountMapper;

    public PabManagementServiceImpl(BrandAccountDOMapper brandAccountMapper) {
        this.brandAccountMapper = brandAccountMapper;
    }
    @Override
    public FundManagementCompanyEnum getFundManagementCompany() {
        return FundManagementCompanyEnum.PAB;
    }

    @Override
    public AccountsModule getBrandAccountModuleByBrandId(String brandId) {
        LambdaQueryWrapper<BrandAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandAccountDO::getBrandId, brandId);
        BrandAccountDO brandAccount = brandAccountMapper.selectOne(queryWrapper);
        if (brandAccount != null && StringUtils.isNotBlank(brandAccount.getAccounts())) {
            return JSON.parseObject(brandAccount.getAccounts(), PabAccountModule.class);
        }
        return null;
    }
}
