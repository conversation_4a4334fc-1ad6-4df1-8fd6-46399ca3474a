package com.wosai.cua.brand.business.service.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;


@Getter
public enum BankCardExportEnum {
    MERCHANT_SN(0, "merchantSn", "商户编号"),
    MERCHANT_NAME(1, "merchantName", "商户名称"),
    ACCOUNT_NAME(2, "accountName", "账户名"),
    BANK_NAME(3, "bankName", "开户银行"),
    BANK_CARD_NO(4, "bankCardNumber", "银行卡号"),
    RESERVED_MOBILE_NUMBER(5, "reservedMobileNumber", "预留手机号"),
    ID_NUMBER(6, "idNumber", "证件号"),
    BANK_CARD_CAUSE_OF_ACTIVATION_FAILURE(7, "activateFailReason", "激活失败原因")
    ;

    private final int col;
    private final String fieldName;
    private final String title;


    BankCardExportEnum(int col, String fieldName, String title) {
        this.col = col;
        this.fieldName = fieldName;
        this.title = title;
    }

    public static List<BankCardExportEnum> getAllOrderByCol() {
        ArrayList<BankCardExportEnum> bankCardExportEnums = Lists.newArrayList(BankCardExportEnum.values());
        bankCardExportEnums.sort(Comparator.comparingInt(BankCardExportEnum::getCol));
        return bankCardExportEnums;
    }

    public static Map<Integer, String> getColumnMap() {
        Map<Integer, String> map = Maps.newHashMap();
        ArrayList<BankCardExportEnum> bankCardExportEnums = Lists.newArrayList(BankCardExportEnum.values());
        bankCardExportEnums.forEach(b -> map.put(b.getCol(), b.getTitle()));
        return map;
    }
}
