package com.wosai.cua.brand.business.service.domain.service.impl.merchant;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.request.merchant.BaseCreateMerchantDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.MerchantCenterDomainService;
import com.wosai.mc.model.req.AccountReq;
import com.wosai.mc.model.req.CreateMerchantAndStoreReq;
import com.wosai.mc.model.req.CreateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.CreateStoreReq;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
public class CompanyMerchantDomainServiceImpl implements MerchantCenterDomainService {

    @Override
    public BrandMerchantTypeEnum getBrandMerchantType() {
        return BrandMerchantTypeEnum.COMPANY;
    }

    @Override
    public CreateMerchantAndStoreReq getCreateMerchantAndStoreReq(BaseCreateMerchantDTO createMerchantDto, String ucUserId) {
        BaseCreateMerchantDTO.CompanyMerchantDTO companyMerchant = JSON.parseObject(JSON.toJSONString(createMerchantDto), BaseCreateMerchantDTO.CompanyMerchantDTO.class);;
        return this.getMerchantComplete(companyMerchant, ucUserId);
    }

    private CreateMerchantAndStoreReq getMerchantComplete(BaseCreateMerchantDTO.CompanyMerchantDTO companyMerchant, String ucUserId) {
        CreateMerchantAndStoreReq merchantComplete = new CreateMerchantAndStoreReq();
        // 创建商户
        CreateMerchantReq merchantReq = new CreateMerchantReq();
        merchantReq.setId(UUID.randomUUID().toString());
        merchantReq.setName(companyMerchant.getMerchantName());
        merchantReq.setBusinessName(companyMerchant.getMerchantName());
        merchantReq.setProvince(companyMerchant.getProvince());
        merchantReq.setCity(companyMerchant.getCity());
        merchantReq.setDistrict(companyMerchant.getDistrict());
        merchantReq.setContactName(companyMerchant.getContactName());
        merchantReq.setContactCellphone(companyMerchant.getContactPhone());
        merchantReq.setContactEmail(companyMerchant.getContactEmail());
        merchantReq.setStreetAddress(companyMerchant.getAddress());
        merchantComplete.setMerchant(merchantReq);
        CreateMerchantBusinessLicenseReq license = this.getCreateMerchantBusinessLicenseReq(companyMerchant, merchantReq);
        merchantComplete.setLicense(license);
        AccountReq account = this.getAccountReq(companyMerchant, ucUserId);
        merchantComplete.setAccount(account);
        CreateStoreReq store = new CreateStoreReq();
        store.setMerchantId(merchantReq.getId());
        store.setName(companyMerchant.getStoreName());
        merchantComplete.setStore(store);
        return merchantComplete;
    }

    private AccountReq getAccountReq(BaseCreateMerchantDTO.CompanyMerchantDTO companyMerchant, String ucUserId) {
        AccountReq account = new AccountReq();
        account.setIdentifier(companyMerchant.getCellPhoneNumber());
        account.setIdentityType(1);
        account.setUcUserId(ucUserId);
        account.setName(companyMerchant.getContactName());
        account.setIdType(1);
        account.setIdNumber(companyMerchant.getPersonalId());
        account.setIdCardFrontPhoto("https://images.wosaimg.com/c9/8fb1c032d51d7ed1539d6d54d014400ebb601c.png");
        account.setIdCardValidity("********-********");
        return account;
    }

    private CreateMerchantBusinessLicenseReq getCreateMerchantBusinessLicenseReq(BaseCreateMerchantDTO.CompanyMerchantDTO companyMerchant, CreateMerchantReq merchantReq) {
        CreateMerchantBusinessLicenseReq license = new CreateMerchantBusinessLicenseReq();
        license.setMerchantId(merchantReq.getId());
        license.setType(companyMerchant.getLicenseType());
        license.setNumber(companyMerchant.getLicenseNumber());
        license.setLegalPersonName(companyMerchant.getLegalPersonName());
        license.setLegalPersonIdType(companyMerchant.getLegalPersonLicenseType());
        license.setLegalPersonIdNumber(companyMerchant.getLegalPersonLicenseNumber());
        return license;
    }
}
