package com.wosai.cua.brand.business.service.module.bank;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BankInfoBeanModule {

    private String id;
    /**
     * 联行号
     */
    @JSONField(name = "opening_number")
    private String openingNumber;
    /**
     * 清算账号
     */
    @JSONField(name = "clearing_number")
    private String clearingNumber;
    /**
     * 银行名称
     */
    @JSONField(name = "bank_name")
    private String bankName;
    /**
     * 支行名称
     */
    @JSONField(name = "branch_name")
    private String branchName;
    private long ctime;
    private long mtime;
    private boolean deleted;
    private Integer version;
    private Integer status;
    @JSONField(name = "bank_city_code")
    private String bankCityCode;
}
