package com.wosai.cua.brand.business.service.module.config.convert;

import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class BrandConfigConverter {
    private BrandConfigConverter() {
        throw new IllegalStateException("Utility class");
    }
    public static BrandConfigModule convertModule(BrandConfigDO brandConfig) {
        if (brandConfig == null) {
            return null;
        }
        BrandConfigModule brandConfigModule = new BrandConfigModule();
        brandConfigModule.setId(brandConfig.getId());
        brandConfigModule.setBrandId(brandConfig.getBrandId());
        brandConfigModule.setChannelId(brandConfig.getChannelId());
        brandConfigModule.setChannelType(brandConfig.getChannelType());
        brandConfigModule.setConfig(brandConfig.getConfig());
        brandConfigModule.setCreatedTime(brandConfig.getCreatedTime());
        brandConfigModule.setUpdatedTime(brandConfig.getUpdatedTime());
        return brandConfigModule;
    }

    public static BrandConfigDO convertDO(BrandConfigModule brandConfig) {
        if (brandConfig == null) {
            return null;
        }
        BrandConfigDO brandConfigDO = new BrandConfigDO();
        brandConfigDO.setId(brandConfig.getId());
        brandConfigDO.setBrandId(brandConfig.getBrandId());
        brandConfigDO.setChannelId(brandConfig.getChannelId());
        brandConfigDO.setChannelType(brandConfig.getChannelType());
        brandConfigDO.setConfig(brandConfig.getConfig());
        brandConfigDO.setCreatedTime(brandConfig.getCreatedTime());
        brandConfigDO.setUpdatedTime(brandConfig.getUpdatedTime());
        return brandConfigDO;
    }

    public static List<BrandConfigModule> convertModuleList(List<BrandConfigDO> brandConfigList) {
        if (CollectionUtils.isEmpty(brandConfigList)) {
            return Collections.emptyList();
        }
        return brandConfigList.stream().map(BrandConfigConverter::convertModule).collect(Collectors.toList());
    }
}
