package com.wosai.cua.brand.business.service.domain.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantsAnalyzeService;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import com.wosai.cua.brand.business.service.module.config.convert.BrandConfigConverter;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【brand_config(品牌配置表)】的数据库操作Service实现
 * @createDate 2024-03-27 14:20:39
 */
@Service
public class BrandConfigDomainServiceImpl implements BrandConfigDomainService {

    private final BrandConfigDOMapper brandConfigMapper;

    private final BrandMapper brandMapper;

    @Autowired
    public BrandConfigDomainServiceImpl(BrandConfigDOMapper brandConfigMapper, BrandMapper brandMapper) {
        this.brandConfigMapper = brandConfigMapper;
        this.brandMapper = brandMapper;
    }


    @Override
    public void insertBrandConfig(BrandConfigModule configModule) {
        BrandConfigDO brandConfig = JSON.parseObject(JSON.toJSONString(configModule), BrandConfigDO.class);
        brandConfigMapper.insert(brandConfig);
    }

    @Override
    public void updateBrandConfig(BrandConfigModule configModule) {
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandConfigDO::getBrandId, configModule.getBrandId());
        BrandConfigDO brandConfig = brandConfigMapper.selectOne(queryWrapper);
        if (Objects.isNull(brandConfig)) {
            brandConfig = new BrandConfigDO();
            brandConfig.setBrandId(configModule.getBrandId());
            brandConfig.setChannelId(configModule.getChannelId());
            brandConfig.setChannelType(configModule.getChannelType());
            brandConfig.setConfig(configModule.getConfig());
            brandConfigMapper.insert(brandConfig);
            return;
        }
        brandConfig.setConfig(configModule.getConfig());
        brandConfig.setChannelId(configModule.getChannelId());
        brandConfig.setChannelType(configModule.getChannelType());
        brandConfig.setUpdatedTime(null);
        brandConfigMapper.updateById(brandConfig);
    }

    @Override
    public BrandConfigModule getBrandConfigByBrandId(String brandId) {
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = BrandMerchantsAnalyzeService.getBrandIdQueryWrapper(brandId);
        BrandConfigDO brandConfig = brandConfigMapper.selectOne(queryWrapper);
        if (Objects.isNull(brandConfig)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(brandConfig), BrandConfigModule.class);
    }

    @Override
    public <R> R getConfigByBrandId(String brandId, Class<R> clazz) {
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = BrandMerchantsAnalyzeService.getBrandIdQueryWrapper(brandId);
        BrandConfigDO brandConfig = brandConfigMapper.selectOne(queryWrapper);
        if (Objects.isNull(brandConfig) || StringUtils.isBlank(brandConfig.getConfig())) {
            return null;
        }
        return JSON.parseObject(brandConfig.getConfig(), clazz);
    }

    @Override
    public List<BrandConfigModule> getBrandConfigByBrandIds(List<String> brandIds) {
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(brandIds)) {
            queryWrapper.in(BrandConfigDO::getBrandId, brandIds);
            List<BrandConfigDO> brandConfigs = brandConfigMapper.selectList(queryWrapper);
            return BrandConfigConverter.convertModuleList(brandConfigs);
        }
        return Collections.emptyList();
    }

    @Override
    public BrandConfigModule getBrandConfigByChannelIdAndChannelType(String channelId, String channelType) {
        if (StringUtils.isNotBlank(channelId) && StringUtils.isNotBlank(channelType)) {
            LambdaQueryWrapper<BrandConfigDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BrandConfigDO::getChannelId, channelId);
            queryWrapper.eq(BrandConfigDO::getChannelType, channelType);
            List<BrandConfigDO> brandConfigs = brandConfigMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(brandConfigs)) {
                return null;
            }
            brandConfigs = brandConfigs.stream().filter(brandConfig -> StringUtils.isNotBlank(brandConfig.getConfig()) && StringUtils.isNotBlank(brandConfig.getChannelId()) && StringUtils.isNotBlank(brandConfig.getChannelType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(brandConfigs)) {
                return null;
            }
            return BrandConfigConverter.convertModule(brandConfigs.get(0));
        }
        return null;
    }

    @Override
    public void dealPabConfig() {
        LambdaQueryWrapper<BrandDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandDO::getFundManagementCompanyCode, "PAB");
        queryWrapper.eq(BrandDO::getDeleted, 0);
        List<BrandDO> brandDOList = brandMapper.selectList(queryWrapper);
        for (BrandDO brandDO : brandDOList) {
            LambdaQueryWrapper<BrandConfigDO> queryConfigWrapper = new LambdaQueryWrapper<>();
            queryConfigWrapper.eq(BrandConfigDO::getBrandId, brandDO.getBrandId());
            BrandConfigDO brandConfig = brandConfigMapper.selectOne(queryConfigWrapper);
            if (Objects.isNull(brandConfig)) {
                brandConfig = new BrandConfigDO();
                brandConfig.setBrandId(brandDO.getBrandId());
                brandConfig.setChannelId(brandDO.getPartnerId());
                brandConfig.setChannelType("PAB");
                PabConfigModule pabConfigModule = new PabConfigModule();
                pabConfigModule.setPartnerId(brandDO.getPartnerId());
                pabConfigModule.setAllowLogin(true);
                brandConfig.setConfig(JSON.toJSONString(pabConfigModule));
                brandConfigMapper.insert(brandConfig);
            }else{
                PabConfigModule pabConfigModule = JSON.parseObject(brandConfig.getConfig(), PabConfigModule.class);
                pabConfigModule.setPartnerId(brandDO.getPartnerId());
                brandConfig.setConfig(JSON.toJSONString(pabConfigModule));
                brandConfigMapper.updateById(brandConfig);
            }
        }
    }


}




