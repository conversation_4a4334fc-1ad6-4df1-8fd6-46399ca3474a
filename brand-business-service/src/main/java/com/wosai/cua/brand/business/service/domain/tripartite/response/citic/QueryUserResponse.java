package com.wosai.cua.brand.business.service.domain.tripartite.response.citic;

import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryUserResponse extends TripartiteSystemCallResponse {
    /**
     * 流水号
     */
    private String serialNumber;
    /**
     * 用户编号,银行给用户分配的编号，银行保证唯一性； 后续交易均使用该编号唯一定位用户。
     */
    private String userId;
    /**
     * 认证失败原因
     */
    private String checkFailReason;
    /**
     * 认证状态
     */
    private String userCheckStatus;
    /**
     * 商户编号
     */
    private String merchantId;
    /**
     * 账号名称
     */
    private String userName;
    /**
     * 账号状态
     */
    private String userStatus;
    /**
     * 认证级别
     */
    private String userReignLevel;
}
