package com.wosai.cua.brand.business.service.module.merchant.analyze;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.annotations.AnalyzeFieldCheck;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CompanyMerchantAnalyzeModule extends CreateMerchantAnalyzeModule {

    /**
     * 企业名称（商户名称）
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写公司名称！")
    private String companyName;
    /**
     * 企业证照类型
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写企业证照类型！")
    private Integer licenseType;
    /**
     * 证照编号
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写企业证照编号！")
    private String licenseNumber;
    /**
     * 联系人
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写联系人！")
    private String contactName;
    /**
     * 联系人电话
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写联系人手机号！",
            needFormatCheck = true,
            pattern = "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$",
            formatCheckMsg = "联系人手机号格式不正确！"
    )
    private String contactPhone;
    /**
     * 联系人邮箱
     */
    @AnalyzeFieldCheck(
            needRequired = false,
            message = "",
            needFormatCheck = true,
            pattern = "^[0-9A-Za-z_]+([-+.][0-9A-Za-z_]+)*@[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*\\.[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*$",
            formatCheckMsg = "联系人邮箱格式不正确！"
    )
    private String contactEmail;
    /**
     * 联系人证件类型
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写联系人证件类型！")
    private Integer personalLicenseType;
    /**
     * 联系人证件号
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写联系人证件号！")
    private String personalId;
    /**
     * 法人姓名
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写法人姓名！")
    private String legalPersonName;
    /**
     * 法人证件类型
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写法人证件类型！")
    private Integer legalPersonLicenseType;
    /**
     * 法人证件号
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写法人证件号！",
            needFormatCheck = true,
            pattern = "[+-]?\\d+(\\.\\d+)?[eE][+-]?\\d+",
            satisfyPattern = false,
            formatCheckMsg = "法人证件号格式不正确！"
    )
    private String legalPersonLicenseNumber;
    /**
     * 联行号
     */
    private String correspondentNumber;
    /**
     * 对公账户名称
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写对公账户名称！",groups = {FundManagementCompanyEnum.PAB,FundManagementCompanyEnum.CITIC,FundManagementCompanyEnum.FUIOU})
    private String bankAccountName;

}
