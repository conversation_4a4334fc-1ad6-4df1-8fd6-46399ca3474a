package com.wosai.cua.brand.business.service.module.config.citic;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.brand.citic.CiticConfigDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.PurposeOfTransferEnum;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
public class CiticBankConfigModule extends ConfigModule {
    /**
     * 对应中信的商户id
     */
    private String merchantId;

    /**
     * 对应中信提供的公钥
     */
    private String publicKey;

    /**
     * 对应收钱吧生成的私钥
     */
    private String privateKey;

    /**
     * 对应收钱吧与中信交换生成的公钥
     */
    private String sqbPublicKey;

    /**
     * 私钥的密码
     */
    private String privateKeyPassword;

    public static CiticBankConfigModule convert(CiticConfigDTO config) {
        if (config == null) {
            return null;
        }
        CiticBankConfigModule module = new CiticBankConfigModule();
        module.setOpenTransfer(config.getOpenTransfer());
        module.setMerchantId(config.getMerchantId());
        module.setPublicKey(config.getPublicKey());
        module.setPrivateKey(config.getPrivateKey());
        module.setPrivateKeyPassword(config.getPrivateKeyPassword());
        module.setPurposeList(PurposeOfTransferEnum.getCodeListByEnumList(config.getPurposeList()));
        module.setNeedSpecialTreatment(config.getNeedSpecialTreatment());
        return module;
    }

    public static BrandConfigDTO convert(CiticBankConfigModule configModule, String brandId, Boolean needGetConfig) {
        if (configModule == null) {
            return null;
        }
        BrandConfigDTO brandConfigDTO = new BrandConfigDTO();
        if (Objects.nonNull(needGetConfig) && Boolean.TRUE.equals(needGetConfig)) {
            CiticConfigDTO dto = CiticConfigDTO.builder()
                    .merchantId(configModule.getMerchantId())
                    .privateKey(configModule.getPrivateKey())
                    .publicKey(configModule.getPublicKey())
                    .privateKeyPassword(configModule.getPrivateKeyPassword())
                    .build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setCiticConfig(dto);
        } else {
            CiticConfigDTO dto = CiticConfigDTO.builder()
                    .merchantId(configModule.getMerchantId())
                    .build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setCiticConfig(dto);
        }
        brandConfigDTO.setFundManagementCompanyCode(FundManagementCompanyEnum.CITIC.getFundManagementCompanyCode());
        brandConfigDTO.setBrandId(brandId);
        return brandConfigDTO;
    }

    public static BrandConfigDTO convert(String s, String brandId, Boolean needGetConfig) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        CiticBankConfigModule configModule = JSON.parseObject(s, CiticBankConfigModule.class);
        BrandConfigDTO brandConfigDTO = new BrandConfigDTO();
        if (Objects.nonNull(needGetConfig) && Boolean.TRUE.equals(needGetConfig)) {
            CiticConfigDTO dto = CiticConfigDTO.builder()
                    .merchantId(configModule.getMerchantId())
                    .privateKey(configModule.getPrivateKey())
                    .publicKey(configModule.getPublicKey())
                    .privateKeyPassword(configModule.getPrivateKeyPassword())
                    .build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setCiticConfig(dto);
        } else {
            CiticConfigDTO dto = CiticConfigDTO.builder()
                    .build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setCiticConfig(dto);
        }
        brandConfigDTO.setFundManagementCompanyCode(FundManagementCompanyEnum.CITIC.getFundManagementCompanyCode());
        brandConfigDTO.setBrandId(brandId);
        return brandConfigDTO;
    }
}
