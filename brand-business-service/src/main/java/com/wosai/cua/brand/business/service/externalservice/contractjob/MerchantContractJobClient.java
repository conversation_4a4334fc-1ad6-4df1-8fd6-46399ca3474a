package com.wosai.cua.brand.business.service.externalservice.contractjob;

import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeRequest;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeResult;
import com.wosai.cua.brand.business.service.helper.JsonRpcClientHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/24
 */
@Component
public class MerchantContractJobClient {

    @Value("${merchant-contract-job}")
    private String merchantContractJobUrl;
    @Value("${x-env-flag}")
    private String xEnvFlag;

    /**
     * 切换支付模式
     *
     * @param request 请求参数
     * @return 切换结果
     */
    public PaymentModeChangeResult changePaymentMode(PaymentModeChangeRequest request) {
        return JsonRpcClientHelper.doRpcCall(merchantContractJobUrl + "rpc/merchantProviderParams", "changePaymentMode", request, PaymentModeChangeResult.class, xEnvFlag);
    }

    public String queryMerchantAcquirer(String merchantSn) {
        return JsonRpcClientHelper.doRpcCall(merchantContractJobUrl + "rpc/acquirer", "getMerchantAcquirer", merchantSn, String.class, xEnvFlag);
    }

}
