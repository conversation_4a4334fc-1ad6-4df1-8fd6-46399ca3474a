package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.BaseResponseBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
public class QueryAllocateAccountResponseBody extends BaseResponseBody {

    private List<Account> accountInlist;

    @Data
    public static class Account {
        /**
         * 开户请求流水号
         */
        private String mchntTraceNo;
        /**
         * 用户编号
         */
        private String accountIn;
        /**
         * 开户行行号
         */
        private String interBankNo;
        /**
         * 户名
         */
        private String outAcntNm;
        /**
         * 状态
         * 枚举值：
         * 00 协议未签署
         * 01 失效（24小时失效或者3次签约失效）
         * 02 生效
         * 03 禁用
         * 05 冻结
         * 06 待银行开户
         * 07注销
         * 08银行开户失败
         */
        private String status;
        /**
         * 手机号
         */
        private String mobile;
        /**
         * 证件号
         */
        private String licNo;
        /**
         * 开户银行账号
         */
        private String outAcntNo;
        /**
         * 分账比例
         */
        private Integer allocateScale;
        /**
         * 银行收款账户号
         */
        private String bankAcctNo;
        /**
         * 银行账户联行号
         */
        private String bankInterNo;
        /**
         * 开户行名称
         */
        private String bankNm;
        /**
         * 富友收款记账户账号
         */
        private String depositAccount;
        /**
         * 开户行名称
         */
        private String issInsName;
        /**
         * 开户省市区
         */
        private String issCityName;
        /**
         * 分支行名称
         */
        private String subBranchName;
        /**
         * 富友银行行号
         */
        private String fyInterBankNo;

        private List<Card> cards;

        @Data
        public static class Card {
            private String interBankNo;
            private String outAcntNo;
            private String outAcntNm;
            /**
             * 绑卡状态
             * 枚举值：
             * 00 协议未签署
             * 01 失效（24小时失效或者3次签约失效）
             * 02 生效
             * 03 禁用
             * 05冻结
             * 07注销
             */
            private String status;
            private String mobile;
            private String issInsName;
            /**
             * 枚举值：
             * 01 是
             * 其他值代表非默认卡
             */
            private String isDefault;
        }
    }
}
