package com.wosai.cua.brand.business.service.config.rpc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.upay.common.helper.MyObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2018-10-26
 */
@Configuration
public class ObjectMapperConfig {

    /**
     * 不使用模板项目自动设置的 ObjectMapper
     *
     * @return
     */
    @Bean
    public ObjectMapper objectMapper() {
        return new MyObjectMapper();
    }
}
