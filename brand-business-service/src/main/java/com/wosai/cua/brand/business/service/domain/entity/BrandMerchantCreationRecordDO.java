package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName(value = "brand_merchant_creation_record")
@Data
public class BrandMerchantCreationRecordDO {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建记录id
     */
    @TableField(value = "record_id")
    private String recordId;

    /**
     * 品牌id
     */
    @TableField(value = "brand_id")
    private String brandId;

    /**
     * 商户id
     */
    @TableField(value = "merchant_id")
    private String merchantId;

    @TableField(value = "merchant_sn")
    private String merchantSn;

    /**
     * 创建结果
     */
    private String result;

    /**
     * 创建结果状态值：1-创建成功，0-创建失败
     */
    private Integer status;

    /**
     * 是否需要重试，0-不需要，1-需要
     */
    @TableField(value = "need_retry")
    private Integer needRetry;

    /**
     * 重试次数
     */
    @TableField(value = "retry_num")
    private Integer retryNum;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private Date updatedTime;
}