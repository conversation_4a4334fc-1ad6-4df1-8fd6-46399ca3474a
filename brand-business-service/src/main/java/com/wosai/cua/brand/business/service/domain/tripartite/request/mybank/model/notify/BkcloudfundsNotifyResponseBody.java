package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.ResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 通知类接口统一响应body
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class BkcloudfundsNotifyResponseBody extends ResponseBody {

    private static final long serialVersionUID = 901058399373671796L;

    @XmlElementRef
    private RespInfo respInfo;

    public RespInfo getRespInfo() {
        return respInfo;
    }

    public void setRespInfo(RespInfo respInfo) {
        this.respInfo = respInfo;
    }

    public BkcloudfundsNotifyResponseBody() {
    }

    public BkcloudfundsNotifyResponseBody(RespInfo respInfo) {
        this.respInfo = respInfo;
    }
}