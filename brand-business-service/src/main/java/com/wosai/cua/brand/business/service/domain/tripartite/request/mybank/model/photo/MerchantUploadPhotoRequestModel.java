package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.photo;


import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;

import java.io.File;

/**
 * 图片上传接口<ant.mybank.merchantprod.merchant.uploadphoto>
 * <p>
 * 该接口为合作方提供上传商户入驻过程中所需要的图片的能力,注意：该接口以POST方式提交表单，格式为multipart/form-data，并非以报文的形式提交。
 * 提交成功后，网商银行将返回图片文件路径。合作方须在商户入驻接口上将图片文件路径一并上送。
 * 图片大小限制在5M以内
 * 图片上传请求有并发数量限制，须大量请求请提前报备
 */
public class MerchantUploadPhotoRequestModel extends MybankObject {
    private static final long serialVersionUID = -2022783689198621312L;

    /**
     * 合作方机构号
     * <p>
     * 网商银行分配
     */
    private String isvOrgId;

    /**
     * 图片类型
     *
     * 可选值：
     *
     * 01 身份证正面
     *
     * 02 身份证反面
     *
     * 03 营业执照
     *
     * 04组织机构代码证
     *
     * 05 开户许可证
     *
     * 06 门头照
     *
     * 07 其他
     *
     * 08 收银台照片
     *
     * 09 门店内景照片
     *
     * 10 各大餐饮平台入驻照片
     *
     * 11 手持身份证合照
     *
     * 12 租赁协议
     *
     * 30 资金授权归集管理关系图片
     */
    private String photoType;

    /**
     * 外部交易号
     * <p>
     * 合作方系统生成的外部交易号，同一交易号被视为同一笔交易。
     */
    private String outTradeNo;

    /**
     * 图片文件
     * <p>
     * 上传照片的文件名以及图片的内容（在发送请求时，图片内容以二进制数据流的形式发送）
     * 注意照片名称不能超过30个字符
     */
    private File picture;

    /**
     * 接口名称
     */
    private String function;

    /**
     * 接口版本
     */
    private String version;

    /**
     * 外部服务机构应用id
     * <p>
     * 由浙江网商银行统一分配,用于识别合作伙伴应用系统，即对端系统编号。
     * 注意此字段的大小写要求
     */
    private String appId;

    /**
     * 报文发起时间
     * <p>
     * 格式：yyyyMMddHHmmss，请求发起时间
     */
    private String reqTime;

    /**
     * 签名值
     * <p>
     * 对部分字段做签名，签名字段包括：IsvOrgId,PhotoType,OutTradeNo,Function,Version,AppId,ReqTime
     * 签名算法：SHA256withRSA
     * 签名字段值以key=value形式根据字段组成字母进行字典升序排列，排列后的key=value键值对以“&”字符连接，最后对连接字符串进行URLEncode，获取待签名串
     */
    private String signature;

    public String getIsvOrgId() { return isvOrgId; }
    public void setIsvOrgId(String isvOrgId) { this.isvOrgId = isvOrgId; }

    public String getPhotoType() { return photoType; }
    public void setPhotoType(String photoType) { this.photoType = photoType; }

    public String getOutTradeNo() { return outTradeNo; }
    public void setOutTradeNo(String outTradeNo) { this.outTradeNo = outTradeNo; }

    public File getPicture() { return picture; }
    public void setPicture(File picture) { this.picture = picture; }

    public String getFunction() { return function; }
    public void setFunction(String function) { this.function = function; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }

    public String getAppId() { return appId; }
    public void setAppId(String appId) { this.appId = appId; }

    public String getReqTime() { return reqTime; }
    public void setReqTime(String reqTime) { this.reqTime = reqTime; }

    public String getSignature() { return signature; }
    public void setSignature(String signature) { this.signature = signature; }
}