package com.wosai.cua.brand.business.service.externalservice.trademanage;

import com.wosai.cua.brand.business.service.externalservice.trademanage.model.MchFeeRateBO;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Component
public class TradeManageClient {

    @Autowired
    private FeeRateService feeRateService;

    public List<MchFeeRateBO> getMchEffectFeeRates(String merchantSn) {
        List<ListMchFeeRateResult> listMchFeeRateResults = feeRateService.listMchEffectFeeRates(merchantSn);
        return listMchFeeRateResults.stream().map(listMchFeeRateResult ->
                        new MchFeeRateBO()
                                .setTradeComboId(listMchFeeRateResult.getTradeComboId())
                                .setPayway(listMchFeeRateResult.getPayWay()))
                .collect(Collectors.toList());
    }
}
