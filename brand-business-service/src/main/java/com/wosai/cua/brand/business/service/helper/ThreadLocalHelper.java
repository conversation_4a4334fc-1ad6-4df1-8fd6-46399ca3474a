package com.wosai.cua.brand.business.service.helper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 线程变量存储工具类
 * <p>
 * Created by j5land on 17/12/12.
 */
public class ThreadLocalHelper {

    private static final ThreadLocal<Map<String, Object>> threadLocal = ThreadLocal.withInitial(HashMap::new);

    public static Map<String, Object> get() {
        return threadLocal.get();
    }

    public static Object get(String key) {
        Map<String, Object> map = threadLocal.get();
        return map.get(key);
    }

    public static Object get(String key, Object defaultValue) {
        Map<String, Object> map = threadLocal.get();
        return map.get(key) == null ? defaultValue : map.get(key);
    }

    public static void set(String key, Object value) {
        Map<String, Object> map = threadLocal.get();
        map.put(key, value);
    }

    public static void set(Map<String, Object> keyValueMap) {
        Map<String, Object> map = threadLocal.get();
        map.putAll(keyValueMap);
    }

    public static void removeThreadLocal() {
        threadLocal.remove();
    }

    public static Object removeKey(String key) {
        Map<String, Object> map = threadLocal.get();
        return map.remove(key);
    }

    public static void clear(String prefix) {
        if (prefix == null) {
            return;
        }
        Map<String, Object> map = threadLocal.get();
        List<String> removeKeys = new ArrayList<>();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            if (key.startsWith(prefix)) {
                removeKeys.add(key);
            }
        }
        for (String key : removeKeys) {
            map.remove(key);
        }
    }
}
