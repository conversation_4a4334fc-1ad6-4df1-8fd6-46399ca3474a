package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;

/**
* <AUTHOR>
* @description 针对表【brand_config(品牌配置表)】的数据库操作Mapper
* @createDate 2024-03-27 14:20:39
* @Entity generator.domain.BrandConfig
*/
public interface BrandConfigDOMapper extends BaseMapper<BrandConfigDO> {

}




