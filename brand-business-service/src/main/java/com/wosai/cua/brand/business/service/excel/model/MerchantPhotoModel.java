package com.wosai.cua.brand.business.service.excel.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 商户照片信息
 * <AUTHOR>
 * @date 2024/10/9
 */
@Data
@Accessors(chain = true)
public class MerchantPhotoModel {

    // 以下是小微的照片
    public static final String BANK_CARD_NAME = "银行卡";
    public static final String MICRO_ID_FRONT_PHOTO = "身份证正面";
    public static final String MICRO_ID_BACK_PHOTO = "身份证反面";

    // 以下是营业执照类型的照片
    public static final String LICENSE_PHOTO = "营业证照";
    public static final String LEGAL_PERSON_FRONT_PHOTO = "法人证件正面";
    public static final String LEGAL_PERSON_BACK_PHOTO = "法人证件反面";
    public static final String TRADE_LICENSE = "许可证";
    public static final String HOLDER_ID_FRONT_PHOTO = "结算人证件正面";
    public static final String HOLDER_ID_BACK_PHOTO = "结算人证件反面";
    public static final String LETTER_OF_AUTHORIZATION = "授权委托书";
    public static final String HAND_LETTER_OF_AUTHORIZATION = "辅助证明材料";
    public static final String PUBLIC_BANK_CARD_IMAGE = "对公账户凭证";
    public static final String DEFAULT_IMAGE = "https://images.wosaimg.com/14/b48cdcfdfe38416ca11e4b2bc57d5829.jpg";
    public static final MerchantPhotoModel DEFAULT_IMAGE_MODEL = new MerchantPhotoModel()
            .setBankCardPhoto(DEFAULT_IMAGE)
            .setMicroIdFrontPhoto(DEFAULT_IMAGE)
            .setMicroIdBackPhoto(DEFAULT_IMAGE)
            .setHolderIdFrontPhoto(DEFAULT_IMAGE)
            .setHolderIdBackPhoto(DEFAULT_IMAGE)
            .setLicensePhoto(DEFAULT_IMAGE)
            .setLegalPersonFrontPhoto(DEFAULT_IMAGE)
            .setLegalPersonBackPhoto(DEFAULT_IMAGE)
            .setLetterOfAuthorization(DEFAULT_IMAGE)
            .setTradeLicensePhoto(DEFAULT_IMAGE)
            .setHandLetterOfAuthorizationPhoto(new ArrayList<>())
            .setPublicBankCardPhoto(DEFAULT_IMAGE);
    /**
     * 银行卡照片
     */
    private String bankCardPhoto;
    /**
     * 小微身份证正面
     */
    private String microIdFrontPhoto;
    /**
     * 小微身份证反面
     */
    private String microIdBackPhoto;
    /**
     * 银行卡持有人正面照片
     */
    private String holderIdFrontPhoto;
    /**
     * 银行卡持有人反面照片
     */
    private String holderIdBackPhoto;
    /**
     * 营业执照照片
     */
    private String licensePhoto;
    /**
     * 法人身份证正面照
     */
    private String legalPersonFrontPhoto;
    /**
     * 法人身份证反面照
     */
    private String legalPersonBackPhoto;
    /**
     * 授权函
     */
    private String letterOfAuthorization;
    /**
     * 许可证照片
     */
    private String tradeLicensePhoto;
    /**
     * 辅助证明材料
     */
    private List<String> handLetterOfAuthorizationPhoto;
    /**
     * 对公账户凭证
     */
    private String publicBankCardPhoto;
}
