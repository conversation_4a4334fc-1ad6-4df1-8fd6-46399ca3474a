package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.ResponseBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 商户入驻结果通知（异步通知接口）< ant.mybank.merchantprod.merch.notify>
 * <p>
 * 开户结果异步通知
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
@Data
public class MerchantprodMerchNotifyModel extends ResponseBody {

    public static final String REGISTER_STATUS_SUCCESS = "1";

    public static final String REGISTER_STATUS_FAIL = "2";

    private static final long serialVersionUID = -3264596667211388155L;
    /**
     * 外部商户号
     */
    @XmlElement(name = "OutMerchantId")
    private String outMerchantId;

    /**
     * 申请单号
     */
    @XmlElement(name = "OrderNo")
    private String orderNo;

    /**
     * 商户号
     * <p>
     * 网商为商户分配的商户号，通过商户入驻结果查询接口获取。
     */
    @XmlElement(name = "MerchantId")
    private String merchantId;

    /**
     * 开户结果
     * 可选值：
     * 1：成功
     * 2：失败
     */
    @XmlElement(name = "RegisterStatus")
    private String registerStatus;

    /**
     * 外部交易号
     */
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;

    /**
     * 异步通知类型
     */
    @XmlElement(name = "AsyncNotifyType")
    private String asyncNotifyType;

    /**
     * 户失败原因返回。当商户开户结果为失败时返回。可能出现的失败原因描述见附录。
     */
    @XmlElement(name = "FailReason")
    private String failReason;
}