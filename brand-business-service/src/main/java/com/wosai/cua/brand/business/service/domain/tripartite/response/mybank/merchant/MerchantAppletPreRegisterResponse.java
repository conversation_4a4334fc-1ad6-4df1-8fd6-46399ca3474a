package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.MerchantAppletPreRegisterResponseModel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Mybank Api: ant.mybank.bkmerchantprod.merch.applet.pre.register
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class MerchantAppletPreRegisterResponse extends MybankResponse {

    private static final long serialVersionUID = -4735708539318970547L;
    @XmlElementRef
    private MerchantprodMerchAppletPreRegister merchantprodMerchAppletPreRegister;

    public MerchantprodMerchAppletPreRegister getMerchantprodMerchAppletPreRegister() {
        return merchantprodMerchAppletPreRegister;
    }

    public void setMerchantprodMerchAppletPreRegister(MerchantprodMerchAppletPreRegister merchantprodMerchAppletPreRegister) {
        this.merchantprodMerchAppletPreRegister = merchantprodMerchAppletPreRegister;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "response")
    public static class MerchantprodMerchAppletPreRegister extends MybankObject {

        private static final long serialVersionUID = 5052713063611608619L;
        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private ResponseHead responseHead;

        /**
         * 业务应答参数（body）
         */
        @XmlElementRef
        private MerchantAppletPreRegisterResponseModel merchantprodMerchAppletPreRegisterResponseModel;

        public ResponseHead getResponseHead() {
            return responseHead;
        }

        public void setResponseHead(ResponseHead responseHead) {
            this.responseHead = responseHead;
        }

        public MerchantAppletPreRegisterResponseModel getMerchantprodMerchAppletPreRegisterResponseModel() {
            return merchantprodMerchAppletPreRegisterResponseModel;
        }

        public void setMerchantprodMerchAppletPreRegisterResponseModel(MerchantAppletPreRegisterResponseModel merchantprodMerchAppletPreRegisterResponseModel) {
            this.merchantprodMerchAppletPreRegisterResponseModel = merchantprodMerchAppletPreRegisterResponseModel;
        }
    }
}