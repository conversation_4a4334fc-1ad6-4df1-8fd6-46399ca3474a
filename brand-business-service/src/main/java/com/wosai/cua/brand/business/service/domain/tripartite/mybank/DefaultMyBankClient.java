package com.wosai.cua.brand.business.service.domain.tripartite.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping.DefaultSignChecker;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping.DefaultSigner;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping.SignChecker;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping.Signer;

public class DefaultMyBankClient extends AbstractMybankClient {

    private String privateKey;
    private String mybankPublicKey;
    private Signer signer;
    private SignChecker signChecker;

    /**
     *
     * @param serverUrl 网关请求地址
     * @param privateKey ISV请求报文签名私钥
     * @param mybankPublicKey 网商银行网关公钥
     */
    public DefaultMyBankClient(String serverUrl, String privateKey, String mybankPublicKey) {
        super(serverUrl);
        this.privateKey = privateKey;
        this.signer = new DefaultSigner(privateKey);
        this.mybankPublicKey = mybankPublicKey;
        this.signChecker = new DefaultSignChecker(mybankPublicKey);
    }

    /**
     *
     * @param serverUrl 网关请求地址
     * @param charset 交互报文字符编码
     * @param privateKey ISV请求报文签名私钥
     * @param mybankPublicKey 网商银行网关公钥
     */
    public DefaultMyBankClient(String serverUrl, String charset, String privateKey, String mybankPublicKey) {
        super(serverUrl, charset);
        this.privateKey = privateKey;
        this.signer = new DefaultSigner(privateKey);
        this.mybankPublicKey = mybankPublicKey;
        this.signChecker = new DefaultSignChecker(mybankPublicKey);
    }

    /**
     *
     * @param serverUrl 网关请求地址
     * @param charset 交互报文字符编码
     * @param signType 交互报文签名方式
     * @param privateKey ISV请求报文签名私钥
     * @param mybankPublicKey 网商银行网关公钥
     */
    public DefaultMyBankClient(String serverUrl, String charset, String signType, String privateKey, String mybankPublicKey) {
        super(serverUrl, charset, signType);
        this.privateKey = privateKey;
        this.signer = new DefaultSigner(privateKey);
        this.mybankPublicKey = mybankPublicKey;
        this.signChecker = new DefaultSignChecker(mybankPublicKey);
    }

    /**
     *
     * @param serverUrl 网关请求地址
     * @param format 交互报文格式
     * @param charset 交互报文字符编码
     * @param signType 交互报文签名方式
     * @param privateKey ISV请求报文签名私钥
     * @param mybankPublicKey 网商银行网关公钥
     */
    public DefaultMyBankClient(String serverUrl, String format, String charset, String signType, String privateKey, String mybankPublicKey) {
        super(serverUrl, format, charset, signType);
        this.privateKey = privateKey;
        this.signer = new DefaultSigner(privateKey);
        this.mybankPublicKey = mybankPublicKey;
        this.signChecker = new DefaultSignChecker(mybankPublicKey);
    }

    @Override
    public Signer getSigner() { return signer; }

    @Override
    public SignChecker getSignChecker() { return signChecker; }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
        if (this.signer == null) {
            this.signer = new DefaultSigner(privateKey);
        }
    }

    public void setMybankPublicKey(String mybankPublicKey) {
        this.mybankPublicKey = mybankPublicKey;
        if (this.signChecker == null) {
            this.signChecker = new DefaultSignChecker(mybankPublicKey);
        }
    }

    public static Builder builder(String serverUrl, String privateKey, String mybankPublicKey) {
        return new Builder(serverUrl, privateKey, mybankPublicKey);
    }

    public static class Builder {
        private DefaultMyBankClient client;

        Builder(String serverUrl, String privateKey, String mybankPublicKey) {
            client = new DefaultMyBankClient(serverUrl, privateKey, mybankPublicKey);
        }

        public DefaultMyBankClient build() {
            return client;
        }

        public Builder format(String format) {
            client.setFormat(format);
            return this;
        }

        public Builder signType(String signType) {
            client.setSignType(signType);
            return this;
        }

        public Builder charset(String charset) {
            client.setCharset(charset);
            return this;
        }

        public Builder connectTimeout(int connectTimeout) {
            client.setConnectTimeout(connectTimeout);
            return this;
        }

        public Builder readTimeout(int readTimeout) {
            client.setReadTimeout(readTimeout);
            return this;
        }
    }
}