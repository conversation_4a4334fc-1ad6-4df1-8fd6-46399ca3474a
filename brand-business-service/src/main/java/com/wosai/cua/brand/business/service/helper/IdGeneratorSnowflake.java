package com.wosai.cua.brand.business.service.helper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.UUID;

/**
 *  IdGeneratorSnowflake
 *  雪花算法id生成器
 * <AUTHOR>
 * @date : 2023/01/27 08:55:30
 * @version : V1.0
 **/
@Slf4j
@Component
public class IdGeneratorSnowflake {

    private static final long DATACENTER_ID = 1;
    private SnowflakeIdUtils snowflake;

    @PostConstruct
    public void init() {
        long workerId;
        try {
            //获取本机的ip地址编码
            workerId = Math.abs(InetAddress.getLocalHost().getHostAddress().hashCode()) % 31;
            log.info("当前机器的workerId: " + workerId);
            snowflake = new SnowflakeIdUtils(workerId, DATACENTER_ID);
        } catch (UnknownHostException e) {
            log.warn("当前机器的workerId获取失败 ----> " + e);
            workerId = Math.abs(UUID.randomUUID().hashCode()) % 31;
            snowflake = new SnowflakeIdUtils(workerId, DATACENTER_ID);
        }
    }

    public long nextId() {
        return snowflake.nextId();
    }

    public synchronized long snowflakeId(long workerId, long datacenterId) {
        return new SnowflakeIdUtils(workerId, datacenterId).nextId();
    }

    public String nextSFTSn(){
        return "S" + snowflake.nextId();
    }
}