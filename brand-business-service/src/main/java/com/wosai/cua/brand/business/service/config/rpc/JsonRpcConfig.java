package com.wosai.cua.brand.business.service.config.rpc;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.shouqianba.service.MerchantProviderParamsService;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.app.service.BrandUserService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.core.crypto.service.CryptoService;
import com.wosai.data.crow.api.service.OnlineQueryService;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.marekting.prepaid.service.StoredBrandService;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreExtService;
import com.wosai.mc.service.StoreService;
import com.wosai.sales.core.service.UserService;
import com.wosai.sales.merchant.business.service.common.CommonAppConfigService;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.sales.merchant.business.service.common.TimeTaskService;
import com.wosai.sales.merchant.business.service.open.OpenBusinessOpenService;
import com.wosai.sales.service.goDe.GaoDeService;
import com.wosai.sharing.proxy.service.AggregationService;
import com.wosai.shorturl.api.ShortUrlService;
import com.wosai.tools.service.InfoQueryService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.uc.v2.service.UcUserServiceV2;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.bank.info.api.service.BusinessLicenseDicV2Service;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.core.service.SnGenerator;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.transaction.service.TaskLogService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * JsonRpc配置
 *
 * <AUTHOR>
 */
@Configuration
public class JsonRpcConfig {

    private static final String MERCHANT_CENTER_SERVICE_NAME = "merchant-center";

    private static final String CORE_BUSINESS_SERVICE_NAME = "core-business";

    @Value("${core-business}")
    private String coreBusiness;

    @Value("${merchant-center}")
    private String merchantCenter;

    @Value("${merchant-bank-service}")
    private String merchantBankService;

    @Value("${merchant-user-service}")
    private String merchantUserService;

    @Value("${uc-user-service}")
    private String ucUserService;

    @Value("${core-crypto-service}")
    private String cryptoService;

    @Value("${bank-info}")
    private String bankInfoServiceUrl;

    @Value("${crow-api}")
    private String crowApi;

    @Value("${upay-transaction}")
    private String upayTransactionUrl;

    @Value("${merchant-contract-access}")
    private String merchantContractAccessUrl;

    @Value("${sp-workflow-service}")
    private String spWorkflowServiceUrl;

    @Value("${merchant-business-open}")
    private String merchantBusinessOpenUrl;

    @Value("${trade-manage-service}")
    private String tradeManageServiceUrl;

    @Value("${sales-system-poi}")
    private String salesSystemPoiUrl;

    @Value("${sales-system-service}")
    private String salesSystemServiceUrl;

    @Value("${marketing-prepaid-card}")
    private String marketingPrepaidCardUrl;

    @Value("${short-url-service}")
    private String shortUrlServiceUrl;

    @Value("${profit-sharing-proxy}")
    private String profitSharingProxyUrl;

    @Value("${shouqianba-tools-service}")
    private String shouqianbaToolsServiceUrl;

    private JsonProxyFactoryBean getJsonProxyFactoryWareTracingBean(String serviceUrl, String serverName, Class<?> serviceInterface) {
        JsonProxyFactoryBean factoryBean = new JsonProxyFactoryBean();
        factoryBean.setServiceUrl(serviceUrl);
        factoryBean.setServiceInterface(serviceInterface);
        factoryBean.setReadTimeoutMillis(3000);
        factoryBean.setServerName(serverName);
        factoryBean.setConnectionTimeoutMillis(3000);
        return factoryBean;
    }

    private JsonProxyFactoryBean getJsonProxyFactoryWareTracingBean(String serviceUrl, String serverName, Class<?> serviceInterface, int readTimeoutMillis, int connectionTimeoutMillis) {
        JsonProxyFactoryBean factoryBean = new JsonProxyFactoryBean();
        factoryBean.setServiceUrl(serviceUrl);
        factoryBean.setServiceInterface(serviceInterface);
        factoryBean.setReadTimeoutMillis(readTimeoutMillis);
        factoryBean.setServerName(serverName);
        factoryBean.setConnectionTimeoutMillis(connectionTimeoutMillis);
        return factoryBean;
    }

    @Bean
    public JsonProxyFactoryBean snGenerator() {
        return getJsonProxyFactoryWareTracingBean(coreBusiness + "rpc/sn", CORE_BUSINESS_SERVICE_NAME, SnGenerator.class);
    }

    @Bean
    public JsonProxyFactoryBean coreMerchantService() {
        return getJsonProxyFactoryWareTracingBean(coreBusiness + "rpc/merchant", CORE_BUSINESS_SERVICE_NAME, com.wosai.upay.core.service.MerchantService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantService() {
        return getJsonProxyFactoryWareTracingBean(merchantCenter + "rpc/merchant", MERCHANT_CENTER_SERVICE_NAME, MerchantService.class);
    }

    @Bean
    public JsonProxyFactoryBean storeService() {
        return getJsonProxyFactoryWareTracingBean(merchantCenter + "rpc/store", MERCHANT_CENTER_SERVICE_NAME, StoreService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantBizBankAccountService() {
        return getJsonProxyFactoryWareTracingBean(merchantBankService + "rpc/merchantBizBankAccount", "merchant-bank-service", MerchantBizBankAccountService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantBankAccountService() {
        return getJsonProxyFactoryWareTracingBean(merchantBankService + "/rpc/merchantBank", "merchant-bank-service", BankService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantUserService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserService + "rpc/merchantuserV2", "merchant-user-service", MerchantUserServiceV2.class);
    }

    @Bean
    public JsonProxyFactoryBean brandUserService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserService + "rpc/brand", "merchant-user-service", BrandUserService.class);
    }

    @Bean
    public JsonProxyFactoryBean ucUserAccountService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserService + "rpc/ucUser", "merchant-user-service", UcUserAccountService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantBusinessLicenseService() {
        return getJsonProxyFactoryWareTracingBean(merchantCenter + "rpc/merchant_business_license", MERCHANT_CENTER_SERVICE_NAME, MerchantBusinessLicenseService.class);
    }

    @Bean
    public JsonProxyFactoryBean storeExtService() {
        return getJsonProxyFactoryWareTracingBean(merchantCenter + "rpc/storeExt", MERCHANT_CENTER_SERVICE_NAME, StoreExtService.class);
    }

    @Bean
    public JsonProxyFactoryBean ucUserServiceV2() {
        return getJsonProxyFactoryWareTracingBean(ucUserService + "rpc/v2/ucUser", "uc-user-service", UcUserServiceV2.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeConfigService() {
        return getJsonProxyFactoryWareTracingBean(coreBusiness + "rpc/tradeConfig", CORE_BUSINESS_SERVICE_NAME, TradeConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean cryptoService() {
        return getJsonProxyFactoryWareTracingBean(cryptoService + "rpc/crypto", "core-crypto", CryptoService.class);
    }

    @Bean
    public JsonProxyFactoryBean bankInfoService() {
        return getJsonProxyFactoryWareTracingBean(bankInfoServiceUrl + "rpc/bankinfo", "bank-info", BankInfoService.class);
    }

    @Bean
    public JsonProxyFactoryBean industryService() {
        return getJsonProxyFactoryWareTracingBean(bankInfoServiceUrl + "rpc/industry_v2", "bank-info", IndustryV2Service.class);
    }

    @Bean
    public JsonProxyFactoryBean districtsService() {
        return getJsonProxyFactoryWareTracingBean(bankInfoServiceUrl + "rpc/districtsv2", "bank-info", DistrictsServiceV2.class);
    }

    @Bean
    public JsonProxyFactoryBean businessLicenseDicV2Service() {
        return getJsonProxyFactoryWareTracingBean(bankInfoServiceUrl + "rpc/business_license_dic_v2", "bank-info", BusinessLicenseDicV2Service.class);
    }

    @Bean
    public JsonProxyFactoryBean tagIngestService() {
        return getJsonProxyFactoryWareTracingBean(crowApi + "rpc/tag_ingests", "crow-api", TagIngestService.class);
    }

    @Bean
    public JsonProxyFactoryBean onlineQueryService() {
        return getJsonProxyFactoryWareTracingBean(crowApi + "rpc/online_queries", "crow-api", OnlineQueryService.class);
    }

    @Bean
    public JsonProxyFactoryBean taskLogService() {
        return getJsonProxyFactoryWareTracingBean(upayTransactionUrl + "rpc/statementTask", "upay-transaction", TaskLogService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantContractAccessService() {
        return getJsonProxyFactoryWareTracingBean(merchantContractAccessUrl + "rpc/merchantProviderParams", "merchant-contract-access", MerchantProviderParamsService.class);
    }

    @Bean
    public JsonProxyFactoryBean callBackService() {
        return getJsonProxyFactoryWareTracingBean(spWorkflowServiceUrl + "/rpc/callback", "sp-workflow-service", CallBackService.class);
    }

    @Bean
    public JsonProxyFactoryBean businessOpenService() {
        return getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "rpc/open/businessOpenService", "merchant-business-open", OpenBusinessOpenService.class, 10000, 10000);
    }

    @Bean
    public JsonProxyFactoryBean timeTaskService() {
        return getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "rpc/timeTask", "merchant-business-open", TimeTaskService.class, 10000, 10000);
    }

    @Bean
    public JsonProxyFactoryBean commonAppInfoService() {
        return getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "rpc/common/appInfo", "merchant-business-open", CommonAppInfoService.class);
    }

    @Bean
    public JsonProxyFactoryBean commonAppConfigService() {
        return getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "rpc/common/appConfig", "merchant-business-open", CommonAppConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean feeRateService() {
        return getJsonProxyFactoryWareTracingBean(tradeManageServiceUrl + "rpc/mchFeeRate", "trade-manage-service", FeeRateService.class);
    }

    @Bean
    public JsonProxyFactoryBean gaodeService() {
        return getJsonProxyFactoryWareTracingBean(salesSystemPoiUrl + "rpc/gaoDe", "sales-system-poi", GaoDeService.class);
    }

    @Bean
    public JsonProxyFactoryBean userService() {
        return getJsonProxyFactoryWareTracingBean(salesSystemServiceUrl + "rpc/user", "sales-system-service", UserService.class);
    }

    @Bean
    public JsonProxyFactoryBean shortUrlService() {
        return getJsonProxyFactoryWareTracingBean(shortUrlServiceUrl + "rpc/shortUrl", "short-url-service", ShortUrlService.class);
    }

    @Bean
    public JsonProxyFactoryBean storedBrandService() {
        return getJsonProxyFactoryWareTracingBean(marketingPrepaidCardUrl + "rpc/brand", "marketing-saas-prepaid-card", StoredBrandService.class);
    }

    @Bean
    public JsonProxyFactoryBean aggregationServiceService() {
        return getJsonProxyFactoryWareTracingBean(profitSharingProxyUrl + "rpc/aggregation", "profit-sharing-proxy", AggregationService.class);
    }

    @Bean
    public JsonProxyFactoryBean infoQueryService() {
        return getJsonProxyFactoryWareTracingBean(shouqianbaToolsServiceUrl + "rpc/infoQuery", "shouqianba-tools-service", InfoQueryService.class);
    }
}

