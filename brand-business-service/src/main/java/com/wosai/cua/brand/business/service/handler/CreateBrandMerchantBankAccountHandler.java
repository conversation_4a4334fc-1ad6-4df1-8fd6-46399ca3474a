package com.wosai.cua.brand.business.service.handler;

import com.wosai.cua.brand.business.api.dto.request.CreateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.enums.BankAccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.BusinessLicenseEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/20
 */
@Component
public class CreateBrandMerchantBankAccountHandler implements OpenPaymentHubHandler {

    @Autowired
    private BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    @Override
    public void handle(PaymentHubOpenContext context) {
        OpenPaymentHubRequestDTO requestDTO = context.getOpenPaymentHubRequestDTO();
        BrandModule brandModule = context.getBrandModule();
        // 中信银行和平安银行 创建银行卡信息
        if (FundManagementCompanyEnum.PAB.equals(brandModule.getFundManagementCompanyCode()) || FundManagementCompanyEnum.CITIC.equals(brandModule.getFundManagementCompanyCode())) {
            CreateBankCardDTO createBankCardDTO = new CreateBankCardDTO()
                    .setSetDefault(false)
                    .setBrandId(brandModule.getBrandId())
                    .setMerchantId(requestDTO.getMerchantId())
                    .setBankCardNo(requestDTO.getDevParam().getNumber())
                    .setOpeningNumber(requestDTO.getDevParam().getOpeningNumber())
                    .setReservedMobileNumber(requestDTO.getDevParam().getPhoneNumber());
            if (BusinessLicenseEnum.MICRO_MERCHANT.getType().equals(context.getMerchantBusinessLicenseInfo().getType()) || BusinessLicenseEnum.INDIVIDUAL_BUSINESS_MERCHANT.getType().equals(context.getMerchantBusinessLicenseInfo().getType())) {
                createBankCardDTO.setType(BankAccountTypeEnum.PERSONAL.getAccountType());
            } else {
                createBankCardDTO.setType(BankAccountTypeEnum.COMPANY.getAccountType());
                createBankCardDTO.setHolder(requestDTO.getDevParam().getHolder());
            }
            brandMerchantBankAccountBusiness.createBankCard(createBankCardDTO);
        }
    }
}
