package com.wosai.cua.brand.business.service.module.brand;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.dto.brand.BrandMerchantExtraDTO;
import com.wosai.cua.brand.business.api.dto.brand.mybank.MyBankMerchantExtraDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.MerchantAccountDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.enums.SensitiveFieldEnum;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantExtraModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.bank.service.BankService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@SensitiveClass
public class BrandMerchantModule {

    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 父品牌id
     */
    private String parentBrandId;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户sn
     */
    private String merchantSn;

    /**
     * 商户类型
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum
     */
    private String type;

    /**
     * 商户类型描述
     */
    private String typeDesc;

    /**
     * 品牌商户类型：FRANCHISEE-加盟商、SUPPLIER-供应商、BRAND_OWNER-品牌商、BRAND_OPERATED_STORES-品牌自营门店、SERVICE_PROVIDER_SQB-服务商（收钱吧）
     * @see com.wosai.cua.brand.business.api.enums.MerchantTypeEnum
     */
    private String merchantType;

    /**
     * 对接模式
     */
    private String merchantDockingMode;

    /**
     * 对接模式描述
     */
    private String merchantDockingModeDesc;

    /**
     * 商户类型说明
     */
    private String merchantTypeDesc;

    private Integer paymentMode;
    /**
     * 商户联系人姓名
     */
    private String merchantContactName;
    /**
     * 商户联系人电话
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.MOBILE)
    private String merchantContactPhone;

    /**
     * 商户联系人邮箱
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.EMAIL)
    private String merchantContactEmail;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 街道地址
     */
    private String streetAddress;

    /**
     * 行业id
     */
    private String industry;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 子账号名称
     */
    private String subAccountName;

    /**
     * 子账号
     */
    private String subAccountNo;

    /**
     * 系统会员id
     */
    private String memberId;

    /**
     * 充值账户名称
     */
    private String topUpAccountName;
    /**
     * 充值账户
     */
    private String topUpAccountNo;

    /**
     * 关联品牌商户门店id
     */
    private String associatedSqbStoreId;

    /**
     * 关联品牌商户门店编号
     */
    private String sqbStoreSn;

    /**
     * 关联美团门店号
     */
    private String associatedMeituanStoreSn;

    /**
     * 关联饿了么门店号
     */
    private String associatedElmStoreSn;

    /**
     * 抖音门店号
     */
    private String dyStoreSn;

    /**
     * 外部商户号
     */
    private String outMerchantNo;

   /**
    * 第三方商户编号
    */
   private String threePartyMerchantSn;
   
   /**
    * 提现策略id
    */
   private Long strategyId;

    /**
     * 商户账户开通状态
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum
     */
    private String accountOpenStatus;

    /**
     * 商户账户开通状态描述
     */
    private String accountOpenStatusDesc;

    /**
     * 商户账户开通失败原因
     */
    private String accountOpenFailureReason;

    private String bankCardActivateStatus;

    private Date accountOpenedTime;

    /**
     * 额外信息
     */
    private BrandMerchantExtraModule extra;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 关联时间
     */
    private Date associatedTime;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 商户证书信息
     * @see MerchantBusinessLicenseInfo
     */
    private MerchantBusinessLicenseInfo merchantBusinessLicense;

    /**
     * 商户经营名称
     */
    private String merchantBusinessName;

    /**
     * 激活链接
     */
    private String activationUrl;

    private ContactInfo contactInfo;

    private BankCardInfo bankCardInfo;

    private String notifyMobile;

    /**
     * 是否已存在商户导入
     */
    private Boolean isAlreadyExistMerchantImport;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfo {

        private String contactName;
        /**
         * 联系人电话
         */
        private String contactPhone;
        /**
         * 联系人邮箱
         */
        private String contactEmail;
        /**
         * 联系人证件类型
         */
        private Integer contactIdType;
        /**
         * 联系人证件号
         */
        private String contactId;
    }

    @Data
    public static class BankCardInfo{
        private String bankCardNo;
        private String openingBankNumber;
        private String mobile;
        private String holder;
    }

    public static BrandMerchantInfoDTO convertToBrandMerchantInfo(BrandMerchantModule brandMerchantModule) {
        if (brandMerchantModule == null) {
            return null;
        }
        BrandMerchantInfoDTO brandMerchantInfoDTO = new BrandMerchantInfoDTO();
        brandMerchantInfoDTO.setBrandId(brandMerchantModule.getBrandId());
        brandMerchantInfoDTO.setMerchantId(brandMerchantModule.getMerchantId());
        brandMerchantInfoDTO.setMerchantName(brandMerchantModule.getMerchantName());
        brandMerchantInfoDTO.setMerchantSn(brandMerchantModule.getMerchantSn());
        brandMerchantInfoDTO.setType(brandMerchantModule.getType());
        brandMerchantInfoDTO.setTypeDesc(brandMerchantModule.getTypeDesc());
        brandMerchantInfoDTO.setMerchantType(brandMerchantModule.getMerchantType());
        brandMerchantInfoDTO.setMerchantTypeDesc(brandMerchantModule.getMerchantTypeDesc());
        brandMerchantInfoDTO.setPaymentMode(brandMerchantModule.getPaymentMode());
        brandMerchantInfoDTO.setMerchantContactName(brandMerchantModule.getMerchantContactName());
        brandMerchantInfoDTO.setMerchantContactPhone(brandMerchantModule.getMerchantContactPhone());
        brandMerchantInfoDTO.setMerchantContactEmail(brandMerchantModule.getMerchantContactEmail());
        brandMerchantInfoDTO.setCity(brandMerchantModule.getCity());
        brandMerchantInfoDTO.setStreetAddress(brandMerchantModule.getStreetAddress());
        brandMerchantInfoDTO.setIndustry(brandMerchantModule.getIndustry());
        brandMerchantInfoDTO.setIndustryName(brandMerchantModule.getIndustryName());
        brandMerchantInfoDTO.setSqbStoreId(brandMerchantModule.getAssociatedSqbStoreId());
        brandMerchantInfoDTO.setMeiTuanStoreSn(brandMerchantModule.getAssociatedMeituanStoreSn());
        brandMerchantInfoDTO.setElmStoreSn(brandMerchantModule.getAssociatedElmStoreSn());
        brandMerchantInfoDTO.setOutMerchantNo(brandMerchantModule.getOutMerchantNo());
        brandMerchantInfoDTO.setThreePartyMerchantSn(brandMerchantModule.getThreePartyMerchantSn());
        brandMerchantInfoDTO.setAccountOpenStatus(brandMerchantModule.getAccountOpenStatus());
        brandMerchantInfoDTO.setAccountOpenFailureReason(brandMerchantModule.getAccountOpenFailureReason());
        brandMerchantInfoDTO.setMerchantBusinessName(brandMerchantModule.getMerchantBusinessName());
        brandMerchantInfoDTO.setAccountOpenedTime(brandMerchantModule.getAccountOpenedTime());
        brandMerchantInfoDTO.setBankCardActivateStatus(brandMerchantModule.getBankCardActivateStatus());
        brandMerchantInfoDTO.setCreatedTime(brandMerchantModule.getCreatedTime());
        brandMerchantInfoDTO.setAssociatedTime(brandMerchantModule.getAssociatedTime());
        MerchantAccountDTO merchantAccount = new MerchantAccountDTO();
        merchantAccount.setMemberId(brandMerchantModule.getMemberId());
        merchantAccount.setSubAccountName(brandMerchantModule.getSubAccountName());
        merchantAccount.setSubAccount(brandMerchantModule.getSubAccountNo());
        merchantAccount.setTopUpAccountName(brandMerchantModule.getTopUpAccountName());
        merchantAccount.setTopUpAccountNo(brandMerchantModule.getTopUpAccountNo());
        brandMerchantInfoDTO.setMerchantAccount(merchantAccount);
        if (Objects.nonNull(brandMerchantModule.getExtra()) && Objects.nonNull(brandMerchantModule.getExtra().getMyBankMerchantExtraModule())) {
            BrandMerchantExtraDTO extra = new BrandMerchantExtraDTO();
            extra.setMyBankMerchantExtra(MyBankMerchantExtraDTO.builder()
                    .arrangementNo(brandMerchantModule.getExtra().getMyBankMerchantExtraModule().getArrangementNo())
                    .arrangementStatus(brandMerchantModule.getExtra().getMyBankMerchantExtraModule().getArrangementStatus())
                    .arrangementType(brandMerchantModule.getExtra().getMyBankMerchantExtraModule().getArrangementType())
                    .build());
        }
        brandMerchantInfoDTO.setActivationUrl(brandMerchantModule.getActivationUrl());
        return brandMerchantInfoDTO;
    }

    public static BrandMerchantModule convertByBrandMerchantDO(BrandMerchantDO brandMerchantDO) {
        if (brandMerchantDO == null) {
            return null;
        }
        BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
        brandMerchantModule.setId(brandMerchantDO.getId());
        brandMerchantModule.setBrandId(brandMerchantDO.getBrandId());
        brandMerchantModule.setParentBrandId(brandMerchantDO.getParentBrandId());
        brandMerchantModule.setMerchantId(brandMerchantDO.getMerchantId());
        brandMerchantModule.setMerchantName(brandMerchantDO.getMerchantName());
        brandMerchantModule.setMerchantSn(brandMerchantDO.getMerchantSn());
        brandMerchantModule.setType(brandMerchantDO.getType());
        brandMerchantModule.setMerchantType(brandMerchantDO.getMerchantType());
        brandMerchantModule.setPaymentMode(brandMerchantDO.getPaymentMode());
        brandMerchantModule.setSubAccountNo(brandMerchantDO.getSubAccountNo());
        brandMerchantModule.setMemberId(brandMerchantDO.getMemberId());
        brandMerchantModule.setTopUpAccountNo(brandMerchantDO.getTopUpAccountNo());
        brandMerchantModule.setAssociatedSqbStoreId(brandMerchantDO.getAssociatedSqbStoreId());
        brandMerchantModule.setAssociatedMeituanStoreSn(brandMerchantDO.getAssociatedMeituanStoreSn());
        brandMerchantModule.setAssociatedElmStoreSn(brandMerchantDO.getAssociatedElmStoreSn());
        brandMerchantModule.setDyStoreSn(brandMerchantDO.getDyStoreSn());
        brandMerchantModule.setOutMerchantNo(brandMerchantDO.getOutMerchantNo());
        brandMerchantModule.setThreePartyMerchantSn(brandMerchantDO.getThreePartyMerchantSn());
        brandMerchantModule.setSqbStoreSn(brandMerchantDO.getSqbStoreSn());
        brandMerchantModule.setStrategyId(brandMerchantDO.getStrategyId());
        brandMerchantModule.setAccountOpenStatus(brandMerchantDO.getAccountOpenStatus());
        brandMerchantModule.setAccountOpenStatusDesc(BrandMerchantAccountOpenStatusEnum.getStatusDescription(brandMerchantDO.getAccountOpenStatus()));
        brandMerchantModule.setAccountOpenFailureReason(brandMerchantDO.getAccountOpenFailureReason());
        brandMerchantModule.setExtra(JSON.parseObject(brandMerchantDO.getExtra(), BrandMerchantExtraModule.class));
        brandMerchantModule.setDeleted(brandMerchantDO.getDeleted());
        brandMerchantModule.setCreatedTime(brandMerchantDO.getCreatedTime());
        brandMerchantModule.setUpdatedTime(brandMerchantDO.getUpdatedTime());
        brandMerchantModule.setAssociatedTime(brandMerchantDO.getAssociatedTime());
        brandMerchantModule.setVersion(brandMerchantDO.getVersion());
        brandMerchantModule.setMerchantDockingMode(brandMerchantDO.getMerchantDockingMode());
        brandMerchantModule.setMerchantTypeDesc(MerchantTypeEnum.getDescByMerchantType(brandMerchantDO.getMerchantType()));
        brandMerchantModule.setAccountOpenedTime(brandMerchantDO.getAccountOpenedTime());
        brandMerchantModule.setBankCardActivateStatus(brandMerchantDO.getBankCardActivateStatus());
        return brandMerchantModule;
    }

    public static List<BrandMerchantModule> convertByBrandMerchantList(List<BrandMerchantDO> brandMerchantDOList) {
        if (CollectionUtils.isEmpty(brandMerchantDOList)) {
            return Lists.newArrayList();
        }
        return brandMerchantDOList.stream().map(BrandMerchantModule::convertByBrandMerchantDO).collect(Collectors.toList());
    }
}
