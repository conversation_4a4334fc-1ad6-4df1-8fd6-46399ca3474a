package com.wosai.cua.brand.business.service.domain.tripartite.request.feishu;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.Map;

@Data
public class CardMessage {
    /**
     * type : template
     * data : {"template_id":"AAqyBQVmUNxxx","template_version_name":"1.0.0"}
     */

    private String type;
    private DataBean data;

    @Data
    public static class DataBean {
        /**
         * template_id : AAqyBQVmUNxxx
         * template_version_name : 1.0.0
         */

        @J<PERSON>NField(name = "template_id")
        private String templateId;
        @JSONField(name = "template_version_name")
        private String templateVersionName;
        @JSONField(name = "template_variable")
        private Map<String,Object> templateVariable;
    }
}
