package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.api.dto.request.merchant.BaseCreateMerchantDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.mc.model.req.CreateMerchantAndStoreReq;

/**
 * <AUTHOR>
 */
public interface MerchantCenterDomainService {
    /**
     * 获取商户类型
     * @return BrandMerchantTypeEnum
     */
    BrandMerchantTypeEnum getBrandMerchantType();

    /**
     * 获取调用商户中心创建商户的请求对象
     * @param createMerchantDto 创建商户对象
     * @param ucUserId 用户id
     * @return 商户中心创建商户和门店的请求对象
     */
    CreateMerchantAndStoreReq getCreateMerchantAndStoreReq(BaseCreateMerchantDTO createMerchantDto, String ucUserId);
}
