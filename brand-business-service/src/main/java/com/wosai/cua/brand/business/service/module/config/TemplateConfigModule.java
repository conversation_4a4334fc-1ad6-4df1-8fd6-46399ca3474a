package com.wosai.cua.brand.business.service.module.config;

import com.wosai.cua.brand.business.api.dto.brand.TemplateConfigDTO;
import com.wosai.cua.brand.business.api.enums.SmsTemplateMethodEnum;
import com.wosai.cua.brand.business.service.domain.entity.BrandSmsTemplateConfigDO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class TemplateConfigModule {

    private String templateCode;

    private String terminalCode;

    private String templateName;

    private SmsTemplateMethodEnum method;

    private String textContent;

    public static List<TemplateConfigModule> getTemplateConfigList(List<TemplateConfigDTO> templateConfigDTOList, Map<String, TemplateConfigDTO> templateConfigMap) {
        return templateConfigDTOList.stream().map(r -> {
            TemplateConfigDTO defaultConfig = templateConfigMap.get(r.getTemplateCode());
            TemplateConfigModule templateConfigModule = new TemplateConfigModule();
            templateConfigModule.setTemplateCode(r.getTemplateCode());
            templateConfigModule.setTerminalCode(Objects.nonNull(defaultConfig) ? defaultConfig.getTerminalCode() : r.getTerminalCode());
            templateConfigModule.setTemplateName(Objects.nonNull(defaultConfig) ? defaultConfig.getTemplateName() : r.getTemplateName());
            templateConfigModule.setMethod(Objects.nonNull(defaultConfig) ? defaultConfig.getMethod() : r.getMethod());
            templateConfigModule.setTextContent(r.getTextContent());
            return templateConfigModule;
        }).collect(Collectors.toList());
    }

    public static List<BrandSmsTemplateConfigDO> getBrandSmsTemplateConfigDOList(String brandId, List<TemplateConfigModule> templateConfigModuleList) {
        return templateConfigModuleList.stream().map(r -> {
            BrandSmsTemplateConfigDO brandSmsTemplateConfigDO = new BrandSmsTemplateConfigDO();
            brandSmsTemplateConfigDO.setBrandId(brandId);
            brandSmsTemplateConfigDO.setTemplateCode(r.getTemplateCode());
            brandSmsTemplateConfigDO.setTerminalCode(r.getTerminalCode());
            brandSmsTemplateConfigDO.setTemplateName(r.getTemplateName());
            brandSmsTemplateConfigDO.setMethod(r.getMethod().getMethod());
            brandSmsTemplateConfigDO.setTextContent(r.getTextContent());
            return brandSmsTemplateConfigDO;
        }).collect(Collectors.toList());
    }

    public static List<TemplateConfigDTO> convertTemplateConfigDtoByModule(List<TemplateConfigModule> templateConfigModuleList) {
        return templateConfigModuleList.stream().map(r -> {
            TemplateConfigDTO templateConfigDTO = new TemplateConfigDTO();
            templateConfigDTO.setTemplateCode(r.getTemplateCode());
            templateConfigDTO.setTerminalCode(r.getTerminalCode());
            templateConfigDTO.setTemplateName(r.getTemplateName());
            templateConfigDTO.setMethod(r.getMethod());
            templateConfigDTO.setTextContent(r.getTextContent());
            return templateConfigDTO;
        }).collect(Collectors.toList());
    }

    public static List<TemplateConfigModule> convertTemplateModuleByDo(List<BrandSmsTemplateConfigDO> brandSmsTemplateConfigDOList) {
        return brandSmsTemplateConfigDOList.stream().filter(brandSmsTemplateConfigDO -> StringUtils.isNotBlank(brandSmsTemplateConfigDO.getTextContent())).map(r -> {
            TemplateConfigModule templateConfigModule = new TemplateConfigModule();
            templateConfigModule.setTemplateCode(r.getTemplateCode());
            templateConfigModule.setTerminalCode(r.getTerminalCode());
            templateConfigModule.setTemplateName(r.getTemplateName());
            templateConfigModule.setMethod(SmsTemplateMethodEnum.getByMethod(r.getMethod()));
            templateConfigModule.setTextContent(r.getTextContent());
            return templateConfigModule;
        }).collect(Collectors.toList());
    }
}