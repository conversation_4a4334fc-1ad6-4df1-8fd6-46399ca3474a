package com.wosai.cua.brand.business.service.excel.context;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.cua.brand.business.service.module.log.TaskContextModule;
import lombok.Getter;

import java.io.File;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Getter
public abstract class AbstractImportContext implements Context{

    private final String brandId;
    private final String operatorId;
    private final String platform;
    private final String auditSn;
    private final String auditId;
    private final String fileUrl;
    private final Map<String, Object> formData;
    private final ExcelImportTypeEnum auditType;

    protected AbstractImportContext(BrandTaskLogModule brandTaskLogModule) {
        this.brandId = brandTaskLogModule.getBrandId();
        this.operatorId = brandTaskLogModule.getUserId();
        this.platform = brandTaskLogModule.getPlatform();
        TaskContextModule taskContextModule = JSON.parseObject(brandTaskLogModule.getTaskContext(), TaskContextModule.class);
        this.auditSn = taskContextModule.getAuditSn();
        this.auditId = taskContextModule.getAuditId();
        this.fileUrl = taskContextModule.getFileUrl();
        this.formData = taskContextModule.getFormData();
        this.auditType = ExcelImportTypeEnum.getExcelImportTypeEnumByTaskType(brandTaskLogModule.getTaskType());
    }

    public String getFileName() {
        return this.getAuditType().getDesc() + "_" + this.getAuditSn() + ".xlsx";
    }

    public File getPhotoFile() {
        return null;
    }
}
