package com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.BaseNotifyBody;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class XmlConvertUtil {
	/**
	 * // 正则表达式匹配空白字符
	 */
	private static final String regex = "\\p{Cntrl}|\\s";

	private static final ObjectMapper xmlMapper = new XmlMapper();

	public static <T> T xml2Bean(String xmlText, Class<T> clazz) throws Exception {
		xmlText = xmlText.replaceAll(regex, "");
		return xmlMapper.readValue(xmlText, clazz);
	}

	public static <T extends BaseNotifyBody> T notifyXml2Bean(String xmlText, Class<T> clazz) throws Exception{
		xmlText = xmlText.replaceAll(regex, "");
		return xmlMapper.readValue(xmlText, clazz);
	}

	public static <T> String bean2Xml(T bean) throws Exception{
		return xmlMapper.writeValueAsString(bean);
	}

}
