package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.notify;

import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.BaseNotifyBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AddConcentrateRelationResult extends BaseNotifyBody<AddConcentrateRelationResult.Result> {

    @Data
    public static class Result {
        /**
         * 订单归集比例
         */
        private Integer orderConcentrateScale;
        /**
         * 余额归集比例
         */
        private Integer balanceConcentrateScale;
        /**
         * 商户号
         */
        private String mchntCd;
        /**
         * 被归集商户号
         */
        private String mchntCdConcentrate;
        /**
         * 资金用途
         * 枚举值：
         * 01 缴纳品牌费
         * 02 缴纳管理费
         * 03 缴纳服务费
         * 04 资金归集
         * 05 缴纳其他费用
         */
        private String useType;
        /**
         * 归集模式
         * 枚举值：
         * 01 余额归集
         * 02 按转结金额归集
         * 03 订单预归集
         * 04 订单事后归集
         */
        private List<String> concentrateTypes;
        /**
         * 授权状态
         * 枚举值：
         * 00 初始化
         * 01 成功
         * 02 已拒绝
         * 03 关系解除
         * 04 授权失败
         * 05 失效
         */
        private String status;
        /**
         * 协议文件url的地址
         */
        private String url;
        /**
         * 批次号
         */
        private String batchNo;
    }
}
