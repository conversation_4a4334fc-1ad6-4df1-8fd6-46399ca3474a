package com.wosai.cua.brand.business.service.helper;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.HibernateValidatorConfiguration;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class ValidationUtils {

    private static final ValidatorFactory FACTORY;
    private static final Validator VALIDATOR;

    static {
        HibernateValidatorConfiguration
                configuration = Validation.byProvider(HibernateValidator.class).configure();
        FACTORY = configuration.addProperty("hibernate.validator.fail_fast", "true")
                .buildValidatorFactory();
        VALIDATOR = FACTORY.getValidator();
    }


    /**
     * 参数校验
     *
     * @param target
     * @param groups
     * @param <T>
     * @return
     */
    public static <T> ValidationResult validate(T target, Class<?>... groups) {
        Set<ConstraintViolation<T>> constraints = VALIDATOR.validate(target, groups);
        boolean pass = CollectionUtils.isEmpty(constraints);
        String msg = null;
        if (!pass) {
            ConstraintViolation<T> constraint = constraints.iterator().next();
            msg = constraint.getMessage();
        }
        return ValidationResult.builder()
                .pass(pass).msg(msg)
                .build();

    }


    @Getter
    @Setter
    @Builder
    public static class ValidationResult {

        /**
         * 是否通过
         */
        private boolean pass;

        /**
         * 校验结果信息
         */
        private String msg;

        public boolean isInvalid() {
            return !pass;
        }

    }
}