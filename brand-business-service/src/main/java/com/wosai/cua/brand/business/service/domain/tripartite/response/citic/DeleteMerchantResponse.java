package com.wosai.cua.brand.business.service.domain.tripartite.response.citic;

import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteMerchantResponse extends TripartiteSystemCallResponse {
    /**
     * 流水号
     */
    private String serialNumber;
    /**
     * 操作提示信息
     */
    private String message;
}
