package com.wosai.cua.brand.business.service.event.listener;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.kafka.BrandMerchantEnrollMsg;
import com.wosai.cua.brand.business.api.enums.MessageTypeEnum;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.Listener;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantEnrollEvent;
import com.wosai.cua.brand.business.service.event.type.MerchantRegistrationEventType;
import com.wosai.cua.brand.business.service.kafka.BrandBaseKafkaProducer;
import com.wosai.cua.brand.business.service.kafka.helper.AvroBeanHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MerchantRegistrationListener implements Listener<BrandMerchantEnrollEvent> {


    private final BrandBaseKafkaProducer brandBaseKafkaProducer;

    @Autowired
    public MerchantRegistrationListener(BrandBaseKafkaProducer brandBaseKafkaProducer) {
        this.brandBaseKafkaProducer = brandBaseKafkaProducer;
    }

    @Override
    public EventType getEventType() {
        return MerchantRegistrationEventType.SUCCESS;
    }

    @Override
    public void handle(BrandMerchantEnrollEvent event) {
        log.info("MerchantRegistrationListener 监听到品牌商户注册成功的事件：{}",JSON.toJSONString(event));
        brandBaseKafkaProducer.sendMessage(
                brandBaseKafkaProducer.getTopic(),
                AvroBeanHelper.getBaseBrandMsg(
                        JSON.toJSONString(
                                BrandMerchantEnrollMsg.builder().brandId(event.getBrandId()).merchantSn(event.getMerchantSn()).enrollChannelEnum(event.getEnrollChannelEnum()).build()
                        ), MessageTypeEnum.ENROLL
                )
        );
    }
}
