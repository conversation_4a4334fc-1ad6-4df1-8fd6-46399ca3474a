package com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.MethodUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * 反射工具类
 */
@Slf4j
public class ReflectUtil {


    public static String generateGetMethod(String fieldName){
        char firstChar = fieldName.charAt(0);
        String firstStr = String.valueOf(firstChar);
        return "get" + firstStr.toUpperCase() + fieldName.substring(1, fieldName.length());
    }

    public static Object getFieldValue(String fieldName, Object obj) throws RuntimeException{
        String getMethodName = generateGetMethod(fieldName);
        Class<?> clazz = obj.getClass();
        try {
        	Method declaredMethod = MethodUtils.getAccessibleMethod(clazz, getMethodName, new Class[0])  ;
        	return declaredMethod.invoke(obj);
        } catch (IllegalAccessException e) {
            log.error("反射获取字段值失败，该字段的get方法无法访问", e);
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            log.error("反射获取字段值失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取类的私有字段，排除final或static字段
     * @param clazz
     * @return
     */
    public static List<String> getPrivateFields(Class<?> clazz){
        return getPrivateFields(clazz, null);
    }

    /**
     * 获取类的私有字段，排除final或static字段
     * @param clazz
     * @param excludeFieldList 指定需排除的字段
     * @return
     */
    public static List<String> getPrivateFields(Class<?> clazz, List<String> excludeFieldList){
        List<Field> declaredFields = getAllFields(clazz);
        List<String> fieldNameList = new ArrayList<>();
        declaredFields.forEach(field -> {
            int modifiers = field.getModifiers();
            if (Modifier.isFinal(modifiers) || Modifier.isStatic(modifiers)) {
                return;
            }
            String fieldName = field.getName();
            if (CollectionUtils.isNotEmpty(excludeFieldList) && excludeFieldList.contains(fieldName)) {
                return;
            }
            fieldNameList.add(fieldName);
        });
        // 去重
        HashSet<String> h = new HashSet<>(fieldNameList);
        fieldNameList.clear();   
        fieldNameList.addAll(h);   
        return fieldNameList;

    }

    public static List<Field> getAllFields(Class<?> clazz) {
        ArrayList<Field> fields = new ArrayList<>();

        // 循环直到clazz为null，即到达了Object类
        while (clazz != null) {
            // 添加当前类的所有声明字段
            Field[] currentFields = clazz.getDeclaredFields();
            Collections.addAll(fields, currentFields);

            // 转向父类
            clazz = clazz.getSuperclass();
        }

        return fields;
    }
    
}
