package com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util;

import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.EncryptParamOutResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.BasicHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Ricky on 2016/11/20.
 */
@Slf4j
public class HttpUtils {

    public static final int SOCKET_TIME_OUT = 60000;
    public static final int CONNECT_TIME_OUT = 60000;
    public static final int CONNECTION_REQUEST_TIME_OUT = 60000;

    /**
     * 发送 post请求访问本地应用并根据传递参数不同返回不同结果
     */
    public static String post(String url, Map<String, String> map) {
        SSLContext sslContext = null;
        try {
            sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                public boolean isTrusted(X509Certificate[] chain, String authType) {
                    return true;
                }
            }).useProtocol("TLSv1.2").build();
        } catch (KeyManagementException e2) {
            e2.printStackTrace();
        } catch (NoSuchAlgorithmException e2) {
            e2.printStackTrace();
        } catch (KeyStoreException e2) {
            e2.printStackTrace();
        }
        // 信任域名
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER))
                .build();
        BasicHttpClientConnectionManager connManager = new BasicHttpClientConnectionManager(socketFactoryRegistry);
        CloseableHttpClient httpclient = HttpClients.createMinimal(connManager);
        // 创建httppost
        HttpPost httppost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(SOCKET_TIME_OUT)
                .setConnectTimeout(CONNECT_TIME_OUT)
                .setConnectionRequestTimeout(CONNECTION_REQUEST_TIME_OUT)
                .build();
        httppost.setConfig(requestConfig);
        // 创建参数队列
        List<NameValuePair> formparams = new ArrayList<>();

        for (String s : map.keySet()) {
            String key;
            String value;
            key = s;
            value = map.get(key);

            formparams.add(new BasicNameValuePair(key, value));
        }

        UrlEncodedFormEntity uefEntity;
        try {
            uefEntity = new UrlEncodedFormEntity(formparams, Constants.CHARSET);
            httppost.setEntity(uefEntity);

            log.info("提交富友支付请求 {}", httppost.getURI());
            try (CloseableHttpResponse response = httpclient.execute(httppost)) {
                HttpEntity entity = response.getEntity();

                if (entity != null) {
                    return EntityUtils.toString(entity, Constants.CHARSET);
                }

                // 打印响应状态
                log.info("富友响应状态 {}", response.getStatusLine());
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e1) {
            e1.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭连接,释放资源
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static <T> T encipherPost(String url, Object obj, Class<T> clazz, String fyPublicKey, String privateKey,String merchantSn) throws Exception {
        Map<String, String> param = new HashMap<>();
        param.put("mchntCd", merchantSn);
        param.put("message", FuiouRsaCipher.encryptByRsaPub(XmlConvertUtil.bean2Xml(obj), fyPublicKey, "GBK"));
        log.info("富友加密接口请求 {}", param);
        String res = HttpUtils.post(url, param);
        log.info("富友加密接口响应 {}", res);
        EncryptParamOutResponse paramOut = XmlConvertUtil.xml2Bean(res, EncryptParamOutResponse.class);
        return XmlConvertUtil.xml2Bean(
                FuiouRsaCipher.decryptByRsaPri(paramOut.getMessage(), privateKey, "GBK"), clazz
        );
    }

    public static <T> T unencryptedPost(String url, Object obj, Class<T> clazz) throws Exception {
        String reqWithSign = XmlConvertUtil.bean2Xml(obj);
        Map<String, String> param = new HashMap<>();
        param.put("req", URLEncoder.encode(reqWithSign, "GBK"));
        log.info("富友非加密接口请求 {}", param);
        String res = HttpUtils.post(url, param);
        if (StringUtils.isBlank(res)){
            return null;
        }
        res = URLDecoder.decode(res, "GBK");
        log.info("富友非加密接口响应 {}", res);
        return XmlConvertUtil.xml2Bean(res, clazz);
    }
}
