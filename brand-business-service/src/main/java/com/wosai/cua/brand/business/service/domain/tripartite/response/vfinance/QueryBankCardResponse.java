package com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryBankCardResponse extends VfinanceBaseResponse {
    /**
     * 卡列表
     */
    @JSONField(name = "card_list")
    private List<BankCardBean> cardList;

    @Data
    public static class BankCardBean {
        /**
         * 银行卡Id
         */
        @JSONField(name = "bank_card_id")
        private String bankCardId;
        /**
         * 账户类型：详见账户类型附录
         */
        @JSONField(name = "account_type")
        private String accountType;
        /**
         * 卡号
         */
        @JSONField(name = "card_no")
        private String cardNo;

        /**
         * 绑定手机号
         */
        private String mobile;

        /**
         * 卡类型
         */
        @JSONField(name = "card_type")
        private String cardType;

        /**
         * 卡属性
         */
        @JSONField(name = "card_attribute")
        private String cardAttribute;

        /**
         * 银行名称
         */
        @JSONField(name = "bank_name")
        private String bankName;

        /**
         * 银行编码
         */
        @JSONField(name = "bank_code")
        private String bankCode;

        /**
         * 是否是默认卡:1-是 2-否
         */
        @JSONField(name = "is_default")
        private String isDefault;

        /**
         * 卡状态:状态(0失效 1正常,3未激活)
         */
        @JSONField(name = "card_status")
        private String cardStatus;
    }
}
