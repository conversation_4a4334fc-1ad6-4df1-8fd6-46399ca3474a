package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 图片上传接口<ant.mybank.merchantprod.merchant.uploadphoto>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class MerchantUploadPhotoResponseModel extends MybankObject {

    private static final long serialVersionUID = 5923432244095803223L;

    /**
     * 返回码组件
     */
    @XmlElementRef
    private RespInfo respInfo;

    /**
     * 文件唯一编号，非地址，公网不可访问
     */
    @XmlElement(name = "PhotoUrl")
    private String photoUrl;

    /**
     * 外部交易号
     */
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;

    public RespInfo getRespInfo() { return respInfo; }
    public void setRespInfo(RespInfo respInfo) { this.respInfo = respInfo; }

    public String getPhotoUrl() { return photoUrl; }
    public void setPhotoUrl(String photoUrl) { this.photoUrl = photoUrl; }

    public String getOutTradeNo() { return outTradeNo; }
    public void setOutTradeNo(String outTradeNo) { this.outTradeNo = outTradeNo; }
}