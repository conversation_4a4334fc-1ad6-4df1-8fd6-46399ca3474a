package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "xml")
public class CancelConcentrateRelationApplyRequestBody extends BaseRequestBody {
    /**
     * 被归集商户号
     */
    @Sign
    private String mchntCdConcentrate;

    /**
     * 原授权申请号
     */
    @Sign
    private String batchNo;

    public CancelConcentrateRelationApplyRequestBody(String mchntCd) {
        super(mchntCd);
    }
}
