package com.wosai.cua.brand.business.service.annotations;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.FIELD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface AnalyzeFieldCheck {
    /**
     * check false message
     * @return message
     */
    String message();

    /**
     * 是否必填
     * @return boolean
     */
    boolean needRequired();

    /**
     * 是否需要格式校验
     * @return boolean
     */
    boolean needFormatCheck() default false;

    /**
     * 格式校验的正则表达式
     * @return 正则表达式
     */
    String pattern() default "";

    /**
     * 是否需要判断满足正则表达式
     * @return boolean
     */
    boolean satisfyPattern() default true;
    /**
     * 格式校验失败时的提示信息
     * @return 提示信息
     */
    String formatCheckMsg() default "";

    /**
     * 是否需要校验值有效性
     * @return boolean
     */
    boolean needCheckValueValidity() default false;

    /**
     * 校验值有效性失败时的提示信息
     * @return 提示信息
     */
    String checkValueValidityMsg() default "";

    /**
     * 校验字段所属sheet
     * @return sheet
     */
    BrandImportSheetEnum[] sheetEnums() default {};

    /**
     * 银行查询方式
     * @return "BANK_NAME"
     */
    String bankSearch() default "";

    /**
     * 校验分组
     * @return FundManagementCompanyEnum
     */
    FundManagementCompanyEnum[] groups() default {};
}
