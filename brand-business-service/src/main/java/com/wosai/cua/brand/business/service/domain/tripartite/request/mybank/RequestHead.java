package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "head")
public class RequestHead extends MybankObject {
    private static final long serialVersionUID = 175413296500834252L;

    /**
     * API接口版本号
     * <p>
     * 匹配接口文档版本
     */
    @XmlElement(name = "Version")
    private String version;

    /**
     * 应用ID
     * <p>
     * 由浙江网商银行统一分配,用于识别合作伙伴应用系统；即对端系统编号，联调前线下提供，注意此字段此处大小写要求
     */
    @XmlElement(name = "Appid")
    private String appid;

    /**
     * 应用ID
     * <p>
     * 由浙江网商银行统一分配,用于识别合作伙伴应用系统；即对端系统编号，联调前线下提供，注意此字段此处大小写要求
     */
    @XmlElement(name = "AppId")
    private String appId;

    /**
     * 接口英文代码
     * <p>
     * 接口定义中的报文编号，明确接口功能，关联接口文档
     */
    @XmlElement(name = "Function")
    private String function;

    /**
     * 报文发起时间
     * <p>
     * 格式：yyyyMMddHHmmss，请求发起时间
     */
    @XmlElement(name = "ReqTime")
    private String reqTime;

    /**
     * 报文发起时区
     * <p>
     * 请求发起系统所在时区, UTC+8
     */
    @XmlElement(name = "ReqTimeZone")
    private String reqTimeZone;

    /**
     * 请求报文ID
     * <p>
     * 唯一定位一次报文请求，由发起方生成，UUID生成，全局唯一
     */
    @XmlElement(name = "ReqMsgId")
    private String reqMsgId;

    /**
     * 报文字符编码
     * <p>
     * 目前支持UTF-8编码
     */
    @XmlElement(name = "InputCharset")
    private String inputCharset;

    /**
     * 保留字段
     * <p>
     * 使用K=V方式表达, 默认空值
     */
    @XmlElement(name = "Reserve")
    private String reserve;

    /**
     * 签名方式
     * <p>
     * 固定送 RSA
     */
    @XmlElement(name = "SignType")
    private String signType;

    public RequestHead() {
    }

    public RequestHead(String version, String appid, String function) {
        this.version = version;
        this.appid = appid;
        this.function = function;
    }

    public RequestHead(String version, String appid, String appId, String function) {
        this.version = version;
        this.appid = appid;
        this.appId = appId;
        this.function = function;
    }

    public static Builder builder(String version, String appid, String function) {
        return new Builder(version, appid, function);
    }

    public static Builder builder(String version, String appid, String appId, String function) {
        return new Builder(version, appid, appId, function);
    }

    public static class Builder {
        private RequestHead requestHead;

        Builder(String version, String appid, String function) {
            requestHead = new RequestHead(version, appid, function);
        }

        Builder(String version, String appid, String appId, String function) {
            requestHead = new RequestHead(version, appid, appId, function);
        }

        public RequestHead build() {
            return requestHead;
        }

        public Builder reqTime(String reqTime) {
            requestHead.setReqTime(reqTime);
            return this;
        }

        public Builder reqTimeZone(String reqTimeZone) {
            requestHead.setReqTimeZone(reqTimeZone);
            return this;
        }

        public Builder reqMsgId(String reqMsgId) {
            requestHead.setReqMsgId(reqMsgId);
            return this;
        }

        public Builder inputCharset(String inputCharset) {
            requestHead.setInputCharset(inputCharset);
            return this;
        }

        public Builder reserve(String reserve) {
            requestHead.setReserve(reserve);
            return this;
        }

        public Builder signType(String signType) {
            requestHead.setSignType(signType);
            return this;
        }
    }

    public String getVersion() {
        return version;
    }
    public void setVersion(String version) {
        this.version = version;
    }

    public String getAppid() {
        return appid;
    }
    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppId() {
        return appId;
    }
    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getFunction() {
        return function;
    }
    public void setFunction(String function) {
        this.function = function;
    }

    public String getReqTime() {
        return reqTime;
    }
    public void setReqTime(String reqTime) {
        this.reqTime = reqTime;
    }

    public String getReqTimeZone() {
        return reqTimeZone;
    }
    public void setReqTimeZone(String reqTimeZone) {
        this.reqTimeZone = reqTimeZone;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }
    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }

    public String getInputCharset() {
        return inputCharset;
    }
    public void setInputCharset(String inputCharset) {
        this.inputCharset = inputCharset;
    }

    public String getReserve() {
        return reserve;
    }
    public void setReserve(String reserve) {
        this.reserve = reserve;
    }

    public String getSignType() {
        return signType;
    }
    public void setSignType(String signType) {
        this.signType = signType;
    }
}