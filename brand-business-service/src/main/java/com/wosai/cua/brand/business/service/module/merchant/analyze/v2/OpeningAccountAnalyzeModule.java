package com.wosai.cua.brand.business.service.module.merchant.analyze.v2;

import com.wosai.cua.brand.business.api.annotations.ExcelColumn;
import com.wosai.cua.brand.business.api.enums.ExcelConvertValueEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
public class OpeningAccountAnalyzeModule {
    // 新增字段注释映射
    private static final Map<String, String> FIELD_NAME_MAPPING = new HashMap<>();
    static {
        FIELD_NAME_MAPPING.put("merchantSn", "收钱吧商户号");
        FIELD_NAME_MAPPING.put("storeSn", "收钱吧门店号");
        FIELD_NAME_MAPPING.put("outMerchantNo", "外部商户号");
        FIELD_NAME_MAPPING.put("shouFuTongRole", "收付通角色");
        FIELD_NAME_MAPPING.put("openingType", "开户类型");
        FIELD_NAME_MAPPING.put("openingName", "开户名称");
        FIELD_NAME_MAPPING.put("idNumber", "证件号码");
        FIELD_NAME_MAPPING.put("legalPersonName", "法人姓名");
        FIELD_NAME_MAPPING.put("legalPersonIdType", "法人证件类型");
        FIELD_NAME_MAPPING.put("legalPersonIdNumber", "法人身份证号");
        FIELD_NAME_MAPPING.put("contactPhoneNumber", "联系电话");
        FIELD_NAME_MAPPING.put("contactName", "联系人姓名");
        FIELD_NAME_MAPPING.put("settlementBankAccount", "结算银行账号");
        FIELD_NAME_MAPPING.put("bankReservedPhoneNumber", "银行预留电话");
        FIELD_NAME_MAPPING.put("bankCode", "银行编码（联行号）");
        FIELD_NAME_MAPPING.put("mtStoreId", "美团门店ID");
        FIELD_NAME_MAPPING.put("elmStoreId", "饿了么门店ID");
        FIELD_NAME_MAPPING.put("dyStoreId", "抖音门店ID");
    }

    /**
     * 数据类型
     *
     */
    @ExcelColumn(col = 0,convertValue = true)
    private String dataType;
    /** 收钱吧商户号 */
    @ExcelColumn(col = 1)
    private String merchantSn;

    /** 收钱吧门店号 */
    @ExcelColumn(col = 2)
    private String storeSn;

    /** 外部商户号 */
    @ExcelColumn(col = 3)
    private String outMerchantNo;

    /** 收付通角色 */
    @ExcelColumn(col = 4,convertValue = true)
    private String shouFuTongRole;
    /**
     * 开户类型
     *
     */
    @ExcelColumn(col = 5,convertValue = true)
    private String openingType;

    /** 开户名称 */
    @ExcelColumn(col = 6)
    private String openingName;

    /** 证件号码 */
    @ExcelColumn(col = 7)
    private String idNumber;

    /** 法人姓名 */
    @ExcelColumn(col = 8)
    private String legalPersonName;

    @ExcelColumn(col = 9,convertValue = true)
    private String legalPersonIdType;

    /** 法人身份证号 */
    @ExcelColumn(col = 10)
    private String legalPersonIdNumber;

    /** 联系电话 */
    @ExcelColumn(col = 11)
    private String contactPhoneNumber;

    /** 联系人姓名 */
    @ExcelColumn(col = 12)
    private String contactName;

    /** 结算银行账号 */
    @ExcelColumn(col = 13,needTrim = true)
    private String settlementBankAccount;

    /** 银行预留电话 */
    @ExcelColumn(col = 14)
    private String bankReservedPhoneNumber;

    /** 银行编码（联行号） */
    @ExcelColumn(col = 15)
    private String bankCode;

    /** 美团门店ID */
    @ExcelColumn(col = 16)
    private String mtStoreId;

    /** 饿了么门店ID */
    @ExcelColumn(col = 17)
    private String elmStoreId;

    /** 抖音门店ID */
    @ExcelColumn(col = 18)
    private String dyStoreId;

    /**
     * 校验错误信息
     */
    private String validateErrorMsg;

    /**
     * 校验参数是否正确
     * @return 校验结果，true表示参数正确，false表示参数错误
     */
    public boolean validateParameters() {
        StringBuilder errorMsgBuilder = new StringBuilder();

        // 校验 dataType 是否为空
        if (dataType == null) {
            errorMsgBuilder.append("数据类型不能为空;");
            validateErrorMsg = errorMsgBuilder.toString();
            return false;
        }

        // 定义校验规则映射
        Map<String, List<String>> validationRules = new HashMap<>();
        validationRules.put(ExcelConvertValueEnum.OPEN_TYPE_MERCHANT.getConvertValue(),
                            Arrays.asList("merchantSn", "shouFuTongRole"));
        validationRules.put(ExcelConvertValueEnum.OPEN_TYPE_STORE.getConvertValue(), 
                            Arrays.asList("storeSn", "shouFuTongRole", "openingType", "openingName", 
                                          "idNumber", "legalPersonName", "legalPersonIdNumber","legalPersonIdType",
                                          "contactPhoneNumber", "contactName"));

        // 获取当前 dataType 对应的校验规则
        List<String> requiredFields = validationRules.get(dataType);
        if (requiredFields == null) {
            errorMsgBuilder.append("未知的数据类型;");
            validateErrorMsg = errorMsgBuilder.toString();
            return false;
        }

        // 遍历校验规则，检查字段是否为空
        for (String field : requiredFields) {
            try {
                String fieldValue = (String) this.getClass().getMethod("get" + capitalize(field)).invoke(this);
                if (fieldValue == null || fieldValue.isEmpty()) {
                    // 使用字段注释名称生成错误信息
                    String fieldName = FIELD_NAME_MAPPING.getOrDefault(field, field);
                    errorMsgBuilder.append(fieldName).append("不能为空;");
                }
            } catch (Exception e) {
                log.error("Failed to get field value for field: {}", field, e);
                errorMsgBuilder.append("字段 ").append(field).append(" 校验失败;");
            }
        }
        validateErrorMsg = errorMsgBuilder.toString();
        return StringUtils.isBlank(validateErrorMsg);
    }

    // 辅助方法：将字段名首字母大写
    private String capitalize(String field) {
        return field.substring(0, 1).toUpperCase() + field.substring(1);
    }

    // Getters and setters


}