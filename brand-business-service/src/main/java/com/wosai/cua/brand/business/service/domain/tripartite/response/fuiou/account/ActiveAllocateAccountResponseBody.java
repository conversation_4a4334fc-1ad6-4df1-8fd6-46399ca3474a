package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.BaseResponseBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
public class ActiveAllocateAccountResponseBody extends BaseResponseBody {
    /**
     * 用户编号
     */
    @Sign
    private String accountIn;
    /**
     * 旷世验证h5链接
     * checkType为2的时返回，两个链接选用一个即可
     */
    private String ksCheckUrl;
    /**
     * 阿里验证h5链接
     * checkType为2的时返回，两个链接选用一个即可，阿里验证链接在微信小程序中不可用
     */
    private String checkUrl;
}
