package com.wosai.cua.brand.business.service.externalservice.paybusinessopen;

import com.wosai.cua.brand.business.service.externalservice.paybusinessopen.model.PayComboModel;
import com.wosai.cua.brand.business.service.helper.JsonRpcClientHelper;
import com.wosai.data.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/21
 */
@Component
public class PayBusinessOpenClient {

    @Value("${pay-business-open}")
    private String payBusinessOpenUrl;
    @Value("${x-env-flag}")
    private String xEnvFlag;

    public List<PayComboModel> queryPayCombos(String organizationId, String industryId) {
        Map<String, Object> request = new HashMap<>();
        request.put("user", CollectionUtil.hashMap("organizationId", organizationId));
        request.put("bodyParams", CollectionUtil.hashMap("industry_id", industryId));
        return JsonRpcClientHelper.doRpcCallList(payBusinessOpenUrl + "rpc/crmEdgeService", "getPayCombos",
                request, PayComboModel.class, xEnvFlag);
    }

}


