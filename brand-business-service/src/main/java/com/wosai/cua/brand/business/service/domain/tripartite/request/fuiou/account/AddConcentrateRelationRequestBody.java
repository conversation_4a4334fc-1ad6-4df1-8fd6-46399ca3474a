package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 4.10归集授权申请接口（CJ007）
 * 接口地址：/addConcentrateRelation.fuiou
 * 接口说明: 调用此接口，支持对同一合作方机构号下，已设置总分关系的商户间，由总商户向分商户发起归集授权申请，授权结果将通过TZ004异步回调通知
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
public class AddConcentrateRelationRequestBody extends BaseRequestBody {
    /**
     * 被归集商户在富友的唯一代码
     */
    @Sign
    private String mchntCdConcentrate;

    @Sign
    private List<String> concentrateTypes;

    /**
     * 订单归集最大比例
     */
    @Sign
    private String orderConcentrateScale;

    /**
     * 余额归集最大比例
     */
    @Sign
    private String balanceConcentrateScale;

    /**
     * 授权模式
     * 枚举值：
     * 1 短信模式
     * 2 返回url
     * 不传默认1
     */
    @Sign
    private String checkType;

    /**
     * 授权人手机号
     */
    @Sign
    private String mobile;

    /**
     * 资金用途
     * 枚举值：
     * 01 缴纳品牌费
     * 02 缴纳管理费
     * 03 缴纳服务费
     * 04 资金归集
     * 05 缴纳其他费用
     */
    @Sign
    private String useType;

    @Data
    @Builder
    public static class Item {
        /**
         * 枚举值：
         * 01 余额归集
         * 02 按转结金额归集
         * 03 订单预归集
         * 04 订单事后归集
         */
        @Sign
        private String concentrateType;
    }

    public AddConcentrateRelationRequestBody(String mchntCd) {
        super(mchntCd);
    }
}
