package com.wosai.cua.brand.business.service.event.model;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.event.Event;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.type.OpenStatusChangeEventType;
import com.wosai.cua.brand.business.service.kafka.dto.OpenStatusEventParams;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantExtraModule;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
@Builder
public class MerchantOpenStatusEvent implements Event {

    private String merchantId;

    private OpenStatusEventParams params;

    @Override
    public EventType getEventType() {
        return OpenStatusChangeEventType.CHANGED;
    }

    public static OpenStatusEventParams getEventParams(BrandModule brandModule, BrandMerchantModule brandMerchantModule) {
        OpenStatusEventParams eventParams = new OpenStatusEventParams();
        if (Objects.isNull(brandModule) || Objects.isNull(brandMerchantModule)) {
            return null;
        }
        eventParams.setBrand(brandModule.getName());
        eventParams.setSftInstitution(brandModule.getFundManagementCompanyCode().getFundManagementCompanyName());
        eventParams.setMerchantName(brandMerchantModule.getMerchantName());
        eventParams.setMerchantSn(brandMerchantModule.getMerchantSn());
        eventParams.setSftRole(brandMerchantModule.getMerchantType());
        eventParams.setOutMerchantSn(brandMerchantModule.getOutMerchantNo());
        eventParams.setStoreSn(brandMerchantModule.getSqbStoreSn());
        eventParams.setMtStoreSn(brandMerchantModule.getAssociatedMeituanStoreSn());
        eventParams.setElmStoreSn(brandMerchantModule.getAssociatedElmStoreSn());
        eventParams.setOpenStatus(brandMerchantModule.getAccountOpenStatus());
        eventParams.setBankcardStatus(brandMerchantModule.getBankCardActivateStatus());
        eventParams.setFailureReason(brandMerchantModule.getAccountOpenFailureReason());
        if (Objects.nonNull(brandMerchantModule.getExtra()) && Objects.nonNull(brandMerchantModule.getExtra().getMyBankMerchantExtraModule())){
            eventParams.setAgreementStatus(brandMerchantModule.getExtra().getMyBankMerchantExtraModule().getArrangementStatus());
        }
        if (brandMerchantModule.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.TO_BE_CONTROLLED.getStatus())){
            eventParams.setControlReason(brandMerchantModule.getAccountOpenFailureReason());
        }
        return eventParams;
    }

    public static OpenStatusEventParams getEventParams(BrandDO brand, BrandMerchantDO brandMerchant) {
        OpenStatusEventParams eventParams = new OpenStatusEventParams();
        if (Objects.isNull(brand) || Objects.isNull(brandMerchant)) {
            return null;
        }
        eventParams.setBrand(brand.getName());
        eventParams.setSftInstitution(brand.getFundManagementCompanyCode());
        eventParams.setMerchantName(brandMerchant.getMerchantName());
        eventParams.setMerchantSn(brandMerchant.getMerchantSn());
        eventParams.setSftRole(brandMerchant.getMerchantType());
        eventParams.setOutMerchantSn(brandMerchant.getOutMerchantNo());
        eventParams.setStoreSn(brandMerchant.getSqbStoreSn());
        eventParams.setMtStoreSn(brandMerchant.getAssociatedMeituanStoreSn());
        eventParams.setElmStoreSn(brandMerchant.getAssociatedElmStoreSn());
        eventParams.setOpenStatus(brandMerchant.getAccountOpenStatus());
        eventParams.setBankcardStatus(brandMerchant.getBankCardActivateStatus());
        eventParams.setFailureReason(brandMerchant.getAccountOpenFailureReason());
        if (StringUtils.isNotEmpty(brandMerchant.getExtra())){
            BrandMerchantExtraModule brandMerchantExtraModule = JSON.parseObject(brandMerchant.getExtra(), BrandMerchantExtraModule.class);
            if (Objects.nonNull(brandMerchantExtraModule) && Objects.nonNull(brandMerchantExtraModule.getMyBankMerchantExtraModule())){
                eventParams.setAgreementStatus(brandMerchantExtraModule.getMyBankMerchantExtraModule().getArrangementStatus());
            }
        }
        if (brandMerchant.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.TO_BE_CONTROLLED.getStatus())){
            eventParams.setControlReason(brandMerchant.getAccountOpenFailureReason());
        }
        return eventParams;
    }
}
