package com.wosai.cua.brand.business.service.excel.converters;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.service.excel.model.IdTypeEnum;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
public class MerchantTypeEnumConverter implements Converter<MerchantTypeEnum> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return IdTypeEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public MerchantTypeEnum convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return MerchantTypeEnum.getEnumByDesc(cellData.getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(MerchantTypeEnum merchantTypeEnum, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(merchantTypeEnum.getDesc());
    }
}
