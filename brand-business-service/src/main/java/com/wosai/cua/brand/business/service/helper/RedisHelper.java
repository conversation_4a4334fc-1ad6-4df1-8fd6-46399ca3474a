package com.wosai.cua.brand.business.service.helper;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.lark.oapi.core.utils.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis相关方法
 */
@Component
@Slf4j
public class RedisHelper {

    private final StringRedisTemplate redisTemplate;


    public RedisHelper(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 将对象添加到指定队列中
     *
     * @param queueName 队列名称
     * @param item      对象
     * @param <T>       对象类型
     */
    public <T> void addToQueue(String queueName, T item) {
        try {
            String serializedItem = JSON.toJSONString(item);
            redisTemplate.opsForList().rightPush(queueName, serializedItem);
        } catch (Exception e) {
            log.warn("Failed to add item to queue: {}", queueName, e);
        }
    }

    /**
     * 从指定队列中取出对象
     *
     * @param queueName 队列名称
     * @param clz       对象类型引用
     * @param <T>       对象类型
     * @return 对象
     */
    public <T> T removeFromQueue(String queueName, Class<T> clz) {
        try {
            String serializedItem = redisTemplate.opsForList().leftPop(queueName, 10, TimeUnit.SECONDS);
            if (serializedItem != null) {
                return JSON.parseObject(serializedItem, clz);
            }
        } catch (Exception e) {
            log.warn("Failed to remove item from queue: {}", queueName, e);
        }
        return null;
    }

    /**
     * 获取指定队列中的所有对象
     *
     * @param queueName 队列名称
     * @param clz       对象类型引用
     * @param <T>       对象类型
     * @return 对象列表
     */
    public <T> List<T> getAllFromQueue(String queueName, Class<T> clz) {
        try {
            List<String> serializedItemsList = redisTemplate.opsForList().range(queueName, 0, -1);
            if (CollectionUtils.isEmpty(serializedItemsList)) {
                return Collections.emptyList();
            }
            return serializedItemsList.stream()
                    .map(serializedItem -> {
                        try {
                            return JSON.parseObject(serializedItem, clz);
                        } catch (Exception e) {
                            log.warn("Failed to parse item from queue: {}. Item: {}", queueName, serializedItem, e);
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("Failed to get all items from queue: {}", queueName, e);
        }
        return Collections.emptyList();
    }

    public <T> void saveAllToSet(String key, List<T> items) {
        if (StringUtils.isEmpty(key)) {
            log.warn("key为空");
            return;
        }
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        try {
            redisTemplate.opsForSet().add(key, items.stream()
                    .map(JSON::toJSONString).toArray(String[]::new));
        } catch (Exception e) {
            log.warn("Failed to save items to set: {}", key, e);
        }
    }

    /**
     * 覆盖指定键的 Set 中的集合
     *
     * @param key   Redis 键
     * @param items 对象列表
     * @param <T>   对象类型
     */
    public <T> void overwriteSet(String key, List<T> items) {
        if (StringUtils.isEmpty(key)) {
            return;
        }
        try {
            // 删除旧的集合
            redisTemplate.delete(key);

            if (items != null && !items.isEmpty()) {
                redisTemplate.opsForSet().add(key, items.stream()
                        .map(JSON::toJSONString).toArray(String[]::new));
            }
        } catch (Exception e) {
            log.warn("Failed to overwrite set: {}", key, e);
        }
    }

    public <T> List<T> getAllFromSet(String key, Class<T> clz) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("Key cannot be null or empty");
        }
        try {
            Set<String> serializedItemsList = redisTemplate.opsForSet().members(key);
            if (CollectionUtils.isEmpty(serializedItemsList)) {
                return Collections.emptyList();
            }
            return serializedItemsList.stream()
                    .map(serializedItem -> {
                        try {
                            return JSON.parseObject(serializedItem, clz);
                        } catch (Exception e) {
                            log.warn("Failed to parse item from set: {}. Item: {}", key, serializedItem, e);
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("Failed to get all items from set: {}", key, e);
        }
        return Collections.emptyList();
    }

    public <T> void saveAllToHash(String key, List<T> items) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("Key cannot be null or empty");
        }
        if (items == null || items.isEmpty()) {
            return;
        }
        try {
            Map<String, String> hashEntries = Maps.newHashMap();
            for (int i = 0; i < items.size(); i++) {
                T item = items.get(i);
                String serializedItem = JSON.toJSONString(item);
                hashEntries.put(String.valueOf(i), serializedItem);
            }
            redisTemplate.opsForHash().putAll(key, hashEntries);
        } catch (Exception e) {
            log.warn("Failed to save items to hash: {}", key, e);
        }
    }

    public <T> void saveHash(String key, String hashKey, T value) {
        redisTemplate.opsForHash().put(key, hashKey, JSON.toJSONString(value));
    }

    public <T> void saveListByHash(String key, String hashKey, List<T> items) {
        if (CollectionUtils.isEmpty(items)){
            return;
        }
        redisTemplate.opsForHash().put(key, hashKey, JSON.toJSONString(items));
    }

    public <T> List<T> getListByHash(String key, String hashKey, Class<T> clz) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
        if (MapUtils.isEmpty(entries)) {
            return Lists.newArrayList();
        }
        Object o = entries.get(hashKey);
        if (o == null) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(JSON.toJSONString(o), clz);
    }

    public Map<Object, Object> getMapByKey(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    public <T> T getHash(String key, String hashKey, Class<T> clz) {
        String s = (String) redisTemplate.opsForHash().get(key, hashKey);
        if (StringUtils.isBlank(s)) {
            return null;
        }
        try {
            return JSON.parseObject(s, clz);
        } catch (Exception e) {
            log.warn("Failed to parse list from hash: {}. Item: {}", key, s, e);
            return null;
        }
    }

    /**
     * 根据key获取对象列表
     *
     * @param key Redis 键
     * @param clz 对象类型引用
     * @param <T> 泛型
     * @return 返回对象列表
     */
    public <T> List<T> getListByKey(String key, Class<T> clz) {
        String s = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(s)) {
            return Lists.newArrayList();
        }
        try {
            return JSON.parseArray(s, clz);
        } catch (Exception e) {
            log.warn("Failed to parse list from hash: {}. Item: {}", key, s, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 根据key获取对象
     *
     * @param key Redis 键
     * @param clz 对象类型引用
     * @param <T> 泛型
     * @return 返回对象
     */
    public <T> T getObjByKey(String key, Class<T> clz) {
        String s = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(s)) {
            return null;
        }
        try {
            return JSON.parseObject(s, clz);
        } catch (Exception e) {
            log.warn("Failed to parse list from hash: {}. Item: {}", key, s, e);
            return null;
        }
    }

    /**
     * 设置缓存
     *
     * @param key           Redis 键
     * @param value         缓存的值
     * @param expireSeconds 过期时间（秒）
     */
    public void setCache(String key, Object value, int expireSeconds) {
        redisTemplate.opsForValue().set(key, JSON.toJSONString(value), expireSeconds, TimeUnit.SECONDS);
    }

    public void deleteKey(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 设置过期时间
     *
     * @param key           Redis 键
     * @param expireDays 过期时间（天）
     */
    public void setExpireTime(String key, int expireDays) {
        redisTemplate.expire(key, expireDays, TimeUnit.DAYS);
    }
}
