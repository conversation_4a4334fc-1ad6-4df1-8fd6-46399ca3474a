package com.wosai.cua.brand.business.service.module.config;

import lombok.Data;

import java.util.Date;

@Data
public class BrandConfigModule {
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 配置参数
     */
    private String config;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}
