package com.wosai.cua.brand.business.service.mybank.manage;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.enums.third.FunctionEnum;
import org.springframework.stereotype.Component;

import java.util.SortedMap;

@Component
public class CommonNotifyHandle {

    private final StrategyFactory strategyFactory;
    private final CommonRequestHandle commonRequestHandle;

    public CommonNotifyHandle(StrategyFactory strategyFactory, CommonRequestHandle commonRequestHandle) {
        this.strategyFactory = strategyFactory;
        this.commonRequestHandle = commonRequestHandle;
    }
    public String notifyRequestHandle(String xmlContext) throws MybankApiException {
        String functionName;
        String result;
        try {
            functionName = getNotifyFunction(xmlContext);
            result = strategyFactory.getStrategy(FunctionEnum.getStrategyValue(functionName)).notifyHandle(xmlContext, functionName);
        } catch (Exception e) {
            throw new MybankApiException(e);
        }
        return result;
    }

    /**
     * @param xmlContext 请求的xml报文
     * @return function 通知的接口名
     * @Description 获取请求头中的function信息
     **/
    public String getNotifyFunction(String xmlContext) throws MybankApiException {
        SortedMap<String, String> notifyHead = commonRequestHandle.getNotifyHead(xmlContext);
        return notifyHead.get(MybankConstants.FUNCTION);
    }
}
