package com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 开通业务请求参数
 * <AUTHOR>
 * @date 2024/9/10
 */
@Data
@Accessors(chain = true)
public class BusinessOpenRequest {

    private String merchantId;
    private String storeId;
    private Map merchantInfo;
    private Map accountInfo;
    private Map licenseInfo;
    private Map storeInfo;
    private Map<String, Object> appInfo;
    /**
     * 待开通的应用ID
     */
    private String appId;
    /**
     * BD的id
     */
    private String userId;
}
