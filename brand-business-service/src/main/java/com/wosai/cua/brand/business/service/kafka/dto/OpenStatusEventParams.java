package com.wosai.cua.brand.business.service.kafka.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class OpenStatusEventParams {

    private String brand;

    @JSONField(name = "sft_institution")
    private String sftInstitution;

    @JSONField(name = "merchant_name")
    private String merchantName;

    @JSONField(name = "merchant_sn")
    private String merchantSn;

    @JSONField(name = "sft_role")
    private String sftRole;

    @JSONField(name = "sft_exmerchant_sn")
    private String outMerchantSn;

    @JSONField(name = "store_sn")
    private String storeSn;

    @JSONField(name = "mt_store_sn")
    private String mtStoreSn;

    @JSONField(name = "elm_store_sn")
    private String elmStoreSn;

    /**
     * 开通状态
     */
    @JSONField(name = "open_status")
    private String openStatus;

    /**
     * 银行卡状态
     */
    @JSONField(name = "bankcard_status")
    private String bankcardStatus;

    /**
     * 协议代扣状态
     */
    @JSONField(name = "agreement_status")
    private String agreementStatus;

    /**
     * 失败原因
     */
    @JSONField(name = "failure_reason")
    private String failureReason;

    /**
     * 管控原因
     */
    @JSONField(name = "control_reason")
    private String controlReason;

}
