package com.wosai.cua.brand.business.service.facade.impl;

import com.alibaba.fastjson2.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.brand.TemplateConfigDTO;
import com.wosai.cua.brand.business.api.dto.request.QuerySmsTemplateConfigRequest;
import com.wosai.cua.brand.business.api.dto.request.app.DictionaryRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.DictionaryResponseDTO;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.CommonFacade;
import com.wosai.cua.brand.business.service.business.CommonBusiness;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.module.common.dictionary.DictionaryModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class CommonFacadeImpl implements CommonFacade {

    private final CommonBusiness commonBusiness;

    private final ApolloConfig apolloConfig;

    @Autowired
    public CommonFacadeImpl(CommonBusiness commonBusiness, ApolloConfig apolloConfig) {
        this.commonBusiness = commonBusiness;
        this.apolloConfig = apolloConfig;
    }

    @Override
    public List<DictionaryResponseDTO> getDictionaryList(DictionaryRequestDTO dto) {
        List<DictionaryModule> dictionaryModules = commonBusiness.getDictionaryModules(dto.getDictionary().name());
        return JSON.parseArray(JSON.toJSONString(dictionaryModules), DictionaryResponseDTO.class);
    }

    @Override
    public Map<String, Object> getApolloConfig() {
        return JSON.parseObject(JSON.toJSONString(apolloConfig), Map.class);
    }

    @Override
    public List<TemplateConfigDTO> getSmsTemplateConfig(QuerySmsTemplateConfigRequest request) {
        switch (request.getMethod()) {
            case SMS:
                return JSON.parseArray(apolloConfig.getSmsTemplateConfig(), TemplateConfigDTO.class);
            case APP:
            case MSP:
                return Collections.emptyList();
            default:
                throw new BrandBusinessException("不支持的方法");
        }
    }
}
