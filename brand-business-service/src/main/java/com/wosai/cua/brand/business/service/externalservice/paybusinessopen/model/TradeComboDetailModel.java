package com.wosai.cua.brand.business.service.externalservice.paybusinessopen.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 套餐payway详情
 * <AUTHOR>
 */
@Data
public class TradeComboDetailModel {
    private int payway;
    @JsonProperty("trade_combo_id")
    private int tradeComboId;
    @JsonProperty("fee_rate_min_value")
    private String feeRateMinValue;
    @JsonProperty("fee_rate_max_value")
    private String feeRateMaxValue;
    @JsonProperty("fee_rate_min")
    private String feeRateMin;
    @JsonProperty("fee_rate_max")
    private String feeRateMax;
    @JsonProperty("fee_rate_default_value")
    private String feeRateDefaultValue;
    @JsonProperty("c2b_status")
    private int c2bStatus;
    @JsonProperty("h5_status")
    private int h5Status;
    @JsonProperty("b2c_status")
    private int b2cStatus;
    @JsonProperty("app_status")
    private int appStatus;
    @JsonProperty("wap_status")
    private int wapStatus;
    @JsonProperty("mini_status")
    private int miniStatus;
    @JsonProperty("ladder_fee_rates")
    private List<LadderFeeRateModel> ladderFeeRates;
}