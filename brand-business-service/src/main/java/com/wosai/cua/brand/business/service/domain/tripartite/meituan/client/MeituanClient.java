package com.wosai.cua.brand.business.service.domain.tripartite.meituan.client;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.constant.MeituanConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.request.BillListRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.request.CommonRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.response.BaseResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.response.BillListResponse;
import com.wosai.cua.brand.business.service.helper.MapHelper;
import com.wosai.cua.brand.business.service.helper.SignHelper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class MeituanClient {
    private Duration readTimeout = Duration.ofSeconds(5);
    private Duration connectTimeout = Duration.ofSeconds(5);
    private final String appId;
    private final String signKey;

    public MeituanClient(String appId, String signKey) {
        this.appId = appId;
        this.signKey = signKey;
    }

    public MeituanClient(long readTimeout, long connectTimeout, String appId, String signKey) {

        this.readTimeout = Duration.ofMillis(readTimeout);
        this.connectTimeout = Duration.ofMillis(connectTimeout);
        this.appId = appId;
        this.signKey = signKey;
    }

    public void setReadTimeout(long readTimeout) {
        this.readTimeout = Duration.ofMillis(readTimeout);
    }

    public void setConnectTimeout(long connectTimeout) {
        this.connectTimeout = Duration.ofMillis(connectTimeout);
    }

    public BillListResponse queryBillList(BillListRequest request) throws Exception {
        return get(MeituanConstants.HOST + "/api/v1/bill/list", request, BillListResponse.class);
    }

    public BaseResponse queryAuthorizationStatus(BillListRequest request) throws Exception {
        return get(MeituanConstants.HOST + "/api/v1/bill/list", request, BaseResponse.class);
    }

    private <RES> RES get(String uri, CommonRequest req, Class<RES> cls) throws Exception {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.readTimeout(this.readTimeout);
        builder.connectTimeout(connectTimeout);
        OkHttpClient client = builder.build();
        Request request = new Request.Builder()
                .url(buildGet(uri, req))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                return JSON.parseObject(response.body().string(), cls);
            } else {
                log.warn("call url {} fail", uri);
                throw new IOException("Unexpected code " + response);
            }
        }
    }

    private String buildGet(String uri, CommonRequest req){
        req.setAppId(appId);
        Map<String, Object> request =  JSON.parseObject(JSON.toJSONString(req), Map.class);
        Map<String, Object> paramsMap = MapHelper.sortMapByKey(request);
        String sourceBody = getSignCheckContent(paramsMap);
        String sign = SignHelper.getMd5(uri + "?" + sourceBody + signKey, false);
        return uri + "?" + sourceBody + "&sig=" + sign;
    }

    private String getSignCheckContent(Map params) {
        if (params == null) {
            return null;
        }
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = params.get(key);
            if(value == null){
                continue;
            }
            content.append((i == 0 ? "" : "&") + key + "=" + value);
        }
        return content.toString();
    }
}
