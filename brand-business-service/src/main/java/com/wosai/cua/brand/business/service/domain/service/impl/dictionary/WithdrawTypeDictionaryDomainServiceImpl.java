package com.wosai.cua.brand.business.service.domain.service.impl.dictionary;

import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.DictionaryDomainService;
import com.wosai.cua.brand.business.service.module.common.dictionary.DictionaryModule;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class WithdrawTypeDictionaryDomainServiceImpl implements DictionaryDomainService {

    /**
     * 字典名称
     */
    private static final String DICTIONARY = "WITHDRAW_TYPE";
    @Override
    public String dictionary() {
        return DICTIONARY;
    }

    @Override
    public List<DictionaryModule> getDictionaryModules() {
        List<DictionaryModule> list = Lists.newArrayList();
        Iterator<WithdrawTypeEnum> iterator = Arrays.stream(WithdrawTypeEnum.values()).iterator();
        while (iterator.hasNext()) {
            WithdrawTypeEnum next = iterator.next();
            list.add(new DictionaryModule(next.getWithdrawType(),next.getDesc()));
        }
        return list;
    }
}
