package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.ResponseBody;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 打款结果通知接口响应body
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class BkmerchantSettleNotifyPayResultResponseBody extends ResponseBody {

    private static final long serialVersionUID = -3476111396980727517L;
    /**
     * 合作方机构号（网商银行分配）
     */
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;

    /**
     * 返回码，默认返回OK
     */
    @XmlElement(name = "ResponseCode")
    private String responseCode;

    public String getIsvOrgId() {
        return isvOrgId;
    }

    public void setIsvOrgId(String isvOrgId) {
        this.isvOrgId = isvOrgId;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

}