package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 小程序入驻结果查询接口<ant.mybank.merchantprod.merch.applet.register.query>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class MerchantAppletRegisterQueryResponseModel extends MybankObject {

    private static final long serialVersionUID = 319204307217706814L;
    /**
     * 返回码组件
     */
    @XmlElementRef
    private RespInfo respInfo;

    /**
     * 外部商户号
     */
    @XmlElement(name = "OutMerchantId")
    private String outMerchantId;

    /**
     * 商户号
     */
    @XmlElement(name = "MerchantId")
    private String merchantId;

    /**
     * 商注册状态
     * 0：注册中
     * 1：成功
     * 2：失败
     */
    @XmlElement(name = "RegisterStatus")
    private String registerStatus;

    public RespInfo getRespInfo() {
        return respInfo;
    }

    public void setRespInfo(RespInfo respInfo) {
        this.respInfo = respInfo;
    }

    public String getOutMerchantId() {
        return outMerchantId;
    }

    public void setOutMerchantId(String outMerchantId) {
        this.outMerchantId = outMerchantId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getRegisterStatus() {
        return registerStatus;
    }

    public void setRegisterStatus(String registerStatus) {
        this.registerStatus = registerStatus;
    }
}