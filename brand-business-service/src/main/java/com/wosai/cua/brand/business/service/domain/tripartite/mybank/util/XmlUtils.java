package com.wosai.cua.brand.business.service.domain.tripartite.mybank.util;


import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.enums.MybankApiExceptionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.Request;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestDocument;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

/**
 * Encapsulating XML common operations
 */
public final class XmlUtils {

    public static String requestXmlBuild(RequestHead requestHead, RequestBody requestBody) throws MybankApiException {
        if (null == requestHead || null == requestBody) {
            throw new MybankApiException(MybankApiExceptionEnum.BUILD_XML_EXCEPTION);
        }
        return JaxbUtil.convertToXml(new RequestDocument(new Request(requestHead, requestBody)), MybankConstants.CHARSET_UTF8);
    }

    /**
     * XML报文格式检查
     *
     * @param xmlContent
     * @return true/false
     */
    public static boolean isXmlDocument(String xmlContent) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            factory.newDocumentBuilder().parse(new InputSource(new StringReader(xmlContent)));
        } catch (Exception e) {
            return false;
        }
        return true;
    }
}
