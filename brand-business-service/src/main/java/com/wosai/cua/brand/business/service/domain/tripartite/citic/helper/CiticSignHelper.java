package com.wosai.cua.brand.business.service.domain.tripartite.citic.helper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaCertStore;
import org.bouncycastle.cms.CMSProcessableByteArray;
import org.bouncycastle.cms.CMSSignedData;
import org.bouncycastle.cms.CMSSignedDataGenerator;
import org.bouncycastle.cms.CMSSignedDataParser;
import org.bouncycastle.cms.CMSTypedData;
import org.bouncycastle.cms.CMSTypedStream;
import org.bouncycastle.cms.SignerInformation;
import org.bouncycastle.cms.SignerInformationStore;
import org.bouncycastle.cms.jcajce.JcaSignerInfoGeneratorBuilder;
import org.bouncycastle.cms.jcajce.JcaSimpleSignerInfoVerifierBuilder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.operator.ContentSigner;
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder;
import org.bouncycastle.operator.jcajce.JcaDigestCalculatorProviderBuilder;
import org.bouncycastle.util.Store;

import java.io.ByteArrayInputStream;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR> Date: 2024/08/08 Time: 3:53 PM
 * 中信银行签名工具类
 */
@Slf4j
public class CiticSignHelper {

    private CiticSignHelper(){
        throw new IllegalStateException("Utility class");
    }
    public static String sign(byte[] byteMsg, String privateKeyPassword,
                              String privateKey, String publicKey) {
        try {

            // 私钥文件解密密码
            char[] keyPassword = privateKeyPassword.toCharArray();

            // 根据私钥和盐，生成秘钥
            byte[] base64EncodedPrivateKey = privateKey.getBytes();

            // 解密私钥
            PrivateKey signerPrivatekey = CiticCryptoHelper.decryptPrivateKey(Base64.decodeBase64(base64EncodedPrivateKey), keyPassword);

            // 公钥
            byte[] base64EncodedCert = publicKey.getBytes();
            X509Certificate signerCertificate = CiticCryptoHelper.generateX509Certificate(Base64.decodeBase64(base64EncodedCert));
            byte[] signature = sign(byteMsg, signerPrivatekey, signerCertificate, null, false);

            return new String(Base64.encodeBase64(signature));
        } catch (Exception e) {
            log.error("sign error>>>", e);
        }
        return null;
    }

    public static Boolean verifySign(byte[] msg, String sign, String senderCert) {
        try {
            byte[] base64EncodedSenderCert = senderCert.getBytes();
            X509Certificate signerCertificate = CiticCryptoHelper.generateX509Certificate(Base64.decodeBase64(base64EncodedSenderCert));
            PublicKey senderPubKey = signerCertificate.getPublicKey();
            return verifyDetachedSignature(msg, Base64.decodeBase64(sign.getBytes()), senderPubKey);
        } catch (Exception e) {
            log.error("verifySign error>>>", e);
        }
        return false;
    }

    public static byte[] sign(byte[] message, PrivateKey signerPrivatekey, X509Certificate signerCertificate, X509Certificate[] chain, boolean attach) throws RuntimeException {
        byte[] signedMessage = null;
        if (signerCertificate == null) {
            throw new RuntimeException("F<sign> parameter signerCertificate is null");
        } else if (signerPrivatekey == null) {
            throw new RuntimeException("F<sign> parameter signerPrivatekey is null");
        } else {
            try {
                String sigAlgName = signerCertificate.getSigAlgName();
                if (StringUtils.isNotBlank(sigAlgName) && sigAlgName.indexOf("RSA") > 0) {
                    CMSSignedDataGenerator cms = new CMSSignedDataGenerator();
                    ContentSigner sha1Signer = (new JcaContentSignerBuilder("SHA1withRSA")).setProvider("BC").build(signerPrivatekey);
                    cms.addSignerInfoGenerator((new JcaSignerInfoGeneratorBuilder((new JcaDigestCalculatorProviderBuilder()).setProvider("BC").build())).build(sha1Signer, signerCertificate));
                    List certList = new ArrayList();
                    if (chain != null) {
                        certList.addAll(Arrays.asList(chain));
                    }

                    Store certs = new JcaCertStore(certList);
                    cms.addCertificates(certs);
                    CMSTypedData msg = new CMSProcessableByteArray(message);
                    CMSSignedData signedData = cms.generate(msg, attach);
                    signedMessage = signedData.getEncoded();
                }

                return signedMessage;
            } catch (Exception var13) {
                String errormsg = null;
                errormsg = errormsg + "消息签名失败\n";
                errormsg = errormsg + "message:" + Arrays.toString(message) + "\n";
                errormsg = errormsg + "signerPrivatekey:" + new String(Base64.encodeBase64(signerPrivatekey.getEncoded())) + "\n";
                errormsg = errormsg + "attach:" + attach + "\n";
                throw new RuntimeException(errormsg, var13);
            }
        }
    }

    public static boolean verifyDetachedSignature(byte[] message, byte[] signedMessage, PublicKey signerPublickey) throws RuntimeException {

        boolean result = false;
        if (message == null) {
            return result;
        }

        if (signedMessage == null) {
            return result;
        }

        try {
            CMSSignedDataParser parser = new CMSSignedDataParser((new JcaDigestCalculatorProviderBuilder()).setProvider("BC").build(), new CMSTypedStream(new ByteArrayInputStream(message)), signedMessage);
            parser.getSignedContent().drain();
            Store certStore = parser.getCertificates();
            SignerInformationStore signersStore = parser.getSignerInfos();

            for (SignerInformation signerInfo : signersStore.getSigners()) {
                String encryptAlg = signerInfo.getEncryptionAlgOID();
                if ("1.2.840.113549.1.1.1".equals(encryptAlg)) {
                    if (signerPublickey == null) {
                        Iterator certificateItertor = certStore.getMatches(signerInfo.getSID()).iterator();
                        if (!certificateItertor.hasNext()) {
                            throw new RuntimeException("使用消息签名中自带的证书链进行验签失败");
                        }

                        X509CertificateHolder signcert = (X509CertificateHolder) certificateItertor.next();
                        if (!signerInfo.verify((new JcaSimpleSignerInfoVerifierBuilder()).setProvider("BC").build(signcert))) {
                            throw new RuntimeException("使用消息签名中自带的证书链进行验签失败");
                        }
                    } else {
                        boolean verifyResult = signerInfo.verify((new JcaSimpleSignerInfoVerifierBuilder()).setProvider("BC").build(signerPublickey));
                        if (!verifyResult) {
                            throw new RuntimeException("使用传入的公钥验签失败");
                        }
                    }
                }
            }

            result = true;
            return result;
        } catch (Exception var12) {
            String errormsg = "验证带不带原文的消息签名失败:\n";
            errormsg = errormsg + "sourceMessage:" + new String(message) + "\n";
            errormsg = errormsg + "signedMessage:" + new String(Base64.encodeBase64(signedMessage)) + "\n";
            errormsg = errormsg + "signerCertificate:" + new String(Base64.encodeBase64(signerPublickey.getEncoded())) + "\n";
            throw new RuntimeException(errormsg, var12);
        }
    }

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

}
