package com.wosai.cua.brand.business.service.domain.tripartite.meituan.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CommonRequest {
    @JsonProperty("app_id")
    private String appId;

    @JsonProperty("timestamp")
    private Long timestamp;

    public CommonRequest() {
        this.timestamp = System.currentTimeMillis() / 1000L;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

}
