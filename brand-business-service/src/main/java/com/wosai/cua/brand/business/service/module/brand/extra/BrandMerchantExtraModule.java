package com.wosai.cua.brand.business.service.module.brand.extra;

import com.wosai.cua.brand.business.api.dto.brand.extra.MerchantDockingModeExtraDTO;
import com.wosai.cua.brand.business.api.dto.response.ExtraDTO;
import com.wosai.cua.brand.business.api.enums.AggregationModelEnum;
import com.wosai.cua.brand.business.api.enums.ArrangementStatusEnum;
import com.wosai.cua.brand.business.api.enums.UseOfFundsEnum;
import com.wosai.cua.brand.business.service.module.brand.extra.mybank.MyBankMerchantExtraModule;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandMerchantExtraModule {
    /**
     * 网商银行扩展字段
     */
    private MyBankMerchantExtraModule myBankMerchantExtraModule;

    /**
     * 归集模式扩展字段
     */
    private BrandMerchantDockingModeExtraModule dockingModeExtraModule;

    private String extra;

    public static ExtraDTO convertToExtra(BrandMerchantExtraModule brandMerchantExtraModule) {
        ExtraDTO extraDTO = new ExtraDTO();
        if (Objects.nonNull(brandMerchantExtraModule) && Objects.nonNull(brandMerchantExtraModule.getMyBankMerchantExtraModule())) {
            ExtraDTO.MyBankExtraDTO myBankExtraDTO = new ExtraDTO.MyBankExtraDTO();
            myBankExtraDTO.setArrangementNo(brandMerchantExtraModule.getMyBankMerchantExtraModule().getArrangementNo());
            myBankExtraDTO.setArrangementStatus(ArrangementStatusEnum.getByCode(brandMerchantExtraModule.getMyBankMerchantExtraModule().getArrangementStatus()));
            myBankExtraDTO.setArrangementStatusDesc(Objects.nonNull(myBankExtraDTO.getArrangementStatus()) ? myBankExtraDTO.getArrangementStatus().getDesc() : null);
            myBankExtraDTO.setArrangementType(brandMerchantExtraModule.getMyBankMerchantExtraModule().getArrangementType());
            myBankExtraDTO.setArrangementNo(brandMerchantExtraModule.getMyBankMerchantExtraModule().getArrangementNo());
            extraDTO.setMyBankExtra(myBankExtraDTO);
        }
        if (Objects.nonNull(brandMerchantExtraModule) && Objects.nonNull(brandMerchantExtraModule.getDockingModeExtraModule())) {
            MerchantDockingModeExtraDTO merchantDockingModeExtraDTO = getDockingModeExtraDTO(brandMerchantExtraModule);
            extraDTO.setMerchantDockingModeExtra(merchantDockingModeExtraDTO);
        }
        return extraDTO;
    }

    private static MerchantDockingModeExtraDTO getDockingModeExtraDTO(BrandMerchantExtraModule brandMerchantExtraModule) {
        MerchantDockingModeExtraDTO merchantDockingModeExtraDTO = new MerchantDockingModeExtraDTO();
        merchantDockingModeExtraDTO.setAggregationModel(AggregationModelEnum.getByCode(brandMerchantExtraModule.getDockingModeExtraModule().getAggregationModel()));
        merchantDockingModeExtraDTO.setAggregationModelDesc(Objects.nonNull(merchantDockingModeExtraDTO.getAggregationModel()) ? merchantDockingModeExtraDTO.getAggregationModel().getDesc() : "未知模式");
        merchantDockingModeExtraDTO.setConcentrateScale(brandMerchantExtraModule.getDockingModeExtraModule().getConcentrateScale());
        merchantDockingModeExtraDTO.setUseOfFunds(UseOfFundsEnum.getByCode(brandMerchantExtraModule.getDockingModeExtraModule().getUseOfFunds()));
        merchantDockingModeExtraDTO.setUseOfFundsDesc(Objects.nonNull(merchantDockingModeExtraDTO.getUseOfFunds()) ? merchantDockingModeExtraDTO.getUseOfFunds().getDesc() : "未知用途");
        return merchantDockingModeExtraDTO;
    }
}
