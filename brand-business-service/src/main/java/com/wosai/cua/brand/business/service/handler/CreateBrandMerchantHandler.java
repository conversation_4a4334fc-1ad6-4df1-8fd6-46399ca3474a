package com.wosai.cua.brand.business.service.handler;

import com.wosai.cua.brand.business.api.dto.request.CreateBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.PaymentHubDevParamDTO;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.mc.service.MerchantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/20
 */
@Component
public class CreateBrandMerchantHandler implements OpenPaymentHubHandler {

    @Autowired
    private BrandBusiness brandBusiness;
    @Autowired
    private MerchantService merchantService;

    @Override
    public void handle(PaymentHubOpenContext context) {
        OpenPaymentHubRequestDTO requestDTO = context.getOpenPaymentHubRequestDTO();
        PaymentHubDevParamDTO devParam = requestDTO.getDevParam();
        // 创建品牌商户关联关系
        BrandModule brandModule = context.getBrandModule();
        CreateBrandMerchantRequestDTO createBrandMerchantRequest = new CreateBrandMerchantRequestDTO()
                .setBrandId(brandModule.getBrandId())
                .setMerchantSn(merchantService.getMerchantById(requestDTO.getMerchantId(), null).getSn())
                .setMerchantType(devParam.getMerchantType())
                .setAssociatedSqbStoreId(context.getStoreInfo().getId())
                .setAssociatedMeituanStoreSn(devParam.getMeituanStoreId())
                .setAssociatedElmStoreSn(devParam.getElmStoreId())
                .setNeedCreateBrandStore(true);
        brandBusiness.createBrandMerchantForBusinessOpen(createBrandMerchantRequest);
    }
}
