package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.api.enums.SmsTemplateMethodEnum;
import com.wosai.cua.brand.business.service.module.config.TemplateConfigModule;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【brand_sms_template_config(品牌短信模板配置表)】的数据库操作Service
* @createDate 2025-01-14 09:55:09
*/
public interface BrandSmsTemplateConfigDOService {

    void saveBrandSmsTemplateConfig(String brandId, List<TemplateConfigModule> templateConfigModules);

    List<TemplateConfigModule> getBrandSmsTemplateConfig(String brandId, SmsTemplateMethodEnum method, List<String> templateCode);
}
