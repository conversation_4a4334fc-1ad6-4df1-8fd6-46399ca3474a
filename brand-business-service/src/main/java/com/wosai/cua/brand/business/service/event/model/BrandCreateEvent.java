package com.wosai.cua.brand.business.service.event.model;

import com.wosai.cua.brand.business.service.event.Event;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.type.BrandCreateEventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandCreateEvent implements Event {

    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 品牌 SN
     */
    private String brandSn;
    /**
     * 商户编号
     */
    private String merchantSn;
    /**
     * 商户 ID
     */
    private String merchantId;

    @Override
    public EventType getEventType() {
        return BrandCreateEventType.SUCCESS;
    }
}
