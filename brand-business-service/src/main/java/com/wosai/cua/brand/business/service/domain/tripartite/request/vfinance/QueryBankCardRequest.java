package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryBankCardRequest extends VfinanceBaseRequest{

    @JSONField(name = "member_id")
    private String memberId;

    public QueryBankCardRequest(String partnerId) {
        super();
        super.service = QUERY_BANK_CARD;
        super.partnerId = partnerId;
    }
}
