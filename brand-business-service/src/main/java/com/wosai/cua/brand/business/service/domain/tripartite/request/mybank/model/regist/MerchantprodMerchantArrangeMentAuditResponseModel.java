package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *ant.mybank.merchantprod.merchant.arrangement.audit
 * */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class MerchantprodMerchantArrangeMentAuditResponseModel extends MybankObject {

    private static final long serialVersionUID = -7758257922998716919L;

    /**
     * 返回码组件
     */
    @XmlElementRef
    private RespInfo respInfo;

    public RespInfo getRespInfo() {
        return respInfo;
    }

    public void setRespInfo(RespInfo respInfo) {
        this.respInfo = respInfo;
    }
}
