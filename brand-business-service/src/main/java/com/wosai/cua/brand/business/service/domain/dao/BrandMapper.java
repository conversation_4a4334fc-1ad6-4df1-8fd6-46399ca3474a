package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryBrandConditionsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandMapper extends BaseMapper<BrandDO> {

    /**
     * 根据品牌id集合或者品牌编号集合查询品牌信息(两个条件互斥)
     *
     * @param brandIdList 品牌id集合
     * @param brandSnList 品牌编号集合
     * @return 品牌集合
     */
    List<BrandDO> selectBrandByBrandIdListOrBrandSnList(@Param("brandIdList") List<String> brandIdList, @Param("brandSnList") List<String> brandSnList);

    /**
     * 根据品牌id查询子品牌信息
     * @param brandId 品牌id
     * @return 子品牌列表
     */
    List<BrandDO> selectBrandByParentBrandId(@Param("brandId") String brandId);

    /**
     * 根据品牌id查询品牌信息
     * @param brandId 品牌id
     * @return 品牌DO对象
     */
    BrandDO selectBrandByBrandId(@Param("brandId") String brandId);

    /**
     * 根据品牌编号查询品牌信息
     * @param brandSn 品牌编号
     * @return 品牌DO对象
     */
    BrandDO selectBrandByBrandSn(@Param("brandSn") String brandSn);

    /**
     * 根据条件统计品牌数量
     * @param brandConditions 品牌查询条件
     * @return 统计数量
     */
    Integer countBrandByConditions(@Param("brandConditions") QueryBrandConditionsDO brandConditions);

    /**
     * 根据条件分页查询品牌
     * @param brandConditions 品牌查询条件
     * @param pageSize 每页条数
     * @param offset 偏移量
     * @return 查询结果集
     */
    List<BrandDO> pageBrandsByConditions(@Param("brandConditions") QueryBrandConditionsDO brandConditions,@Param("pageSize")Integer pageSize, @Param("offset")Integer offset);

    /**
     * 根据品牌id删除品牌
     * @param brandId 品牌id
     * @return 删除数量
     */
    int deleteBrand(@Param("brandId")String brandId);

    /**
     * 更新品牌信息
     * @param brand 品牌do对象
     * @return 更新条数
     */
    int updateBrand(BrandDO brand);

    /**
     * 根据商户编号获取品牌信息
     * @param merchantSn
     * @return 查询结果
     */
    BrandDO selectBrandByMerchantSn(@Param("merchantSn")String merchantSn);

    /**
     * 根据品牌名称查询品牌信息
     * @param name 品牌名称
     * @return 查询结果
     */
    BrandDO selectBrandByName(@Param("name")String name);
}