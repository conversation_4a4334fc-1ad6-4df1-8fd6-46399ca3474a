package com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;

/**
 * 维金接口返回基类
 *
 * <AUTHOR>
 */
@Data
public class VfinanceBaseResponse extends TripartiteSystemCallResponse {
    @JSONField(name = "is_success")
    private String success;
    @JSONField(name = "partner_id")
    private String partnerId;
    @JSONField(name = "_input_charset")
    private String charset;
    @JSONField(name = "error_code")
    private String errorCode;
    @JSONField(name = "error_message")
    private String errorMessage;
    private String memo;
}
