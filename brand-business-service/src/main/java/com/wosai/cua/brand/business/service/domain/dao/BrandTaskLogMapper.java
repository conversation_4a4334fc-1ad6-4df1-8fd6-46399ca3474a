package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandTaskLogDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryBrandTaskConditionsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandTaskLogMapper extends BaseMapper<BrandTaskLogDO> {
    /**
     * 统计品牌任务数量
     * @param conditions 条件
     * @return 数量
     */
    Long countBrandTaskLog(QueryBrandTaskConditionsDO conditions);

    /**
     * 分页查询品牌任务
     * @param conditions 条件
     * @param offset 偏移量
     * @param pageSize 每页条数
     * @return 查询结果
     */
    List<BrandTaskLogDO> pageBrandTaskLogList(@Param("conditions") QueryBrandTaskConditionsDO conditions,@Param("offset") Integer offset,@Param("pageSize") Integer pageSize);

    /**
     * 查询品牌导入任务列表
     * @param start 开始时间
     * @param size 总条数
     * @return 列表
     */
    List<BrandTaskLogDO> getAuditImportTaskLog(@Param("taskTypes") List<Integer> taskTypes, @Param("start") String start, @Param("size") int size);
}