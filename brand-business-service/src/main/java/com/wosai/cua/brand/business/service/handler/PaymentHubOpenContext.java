package com.wosai.cua.brand.business.service.handler;

import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubRequestDTO;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.StoreInfo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 校验和开通的上下文信息
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PaymentHubOpenContext {
    private OpenPaymentHubRequestDTO openPaymentHubRequestDTO;
    private MerchantBusinessLicenseInfo merchantBusinessLicenseInfo;
    /**
     * 品牌信息
     */
    private BrandModule brandModule;
    /**
     * 门店信息
     */
    private StoreInfo storeInfo;
}