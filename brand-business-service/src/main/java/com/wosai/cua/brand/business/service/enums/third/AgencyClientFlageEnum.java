package com.wosai.cua.brand.business.service.enums.third;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum AgencyClientFlageEnum {
    /**
     * 是否存在经办人
     * 1：是 2：否（企业必输，个纯个人与个体工商户无需填写）
     * 可空	企业：1个体工商户：2
     */
    YES("1","存在经办人"),

    NO("2","不存在经办人")
    ;

    private final String agencyClientFlag;

    private final String desc;


    AgencyClientFlageEnum(String agencyClientFlag, String desc) {
        this.agencyClientFlag = agencyClientFlag;
        this.desc = desc;
    }
}
