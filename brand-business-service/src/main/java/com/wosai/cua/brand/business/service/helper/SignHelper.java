package com.wosai.cua.brand.business.service.helper;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class SignHelper {

    private static final int SIGNUM = 1;
    /**
     * hex值
     */
    private static final int HEX_FLAG = 16;
    /**
     * 签名的长度
     */
    private static final int SIGN_LENGTH = 32;
    /**
     * 填充值
     */
    private static final String FILL_CHAR = "0";

    public static String genSign(String secret, long timestamp) throws NoSuchAlgorithmException, InvalidKeyException {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        //使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }

    /**
     * md5 32位加密方法
     *
     * @param input
     * @return
     */
    public static String getMd5(String input, boolean upper) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            BigInteger number = new BigInteger(SIGNUM, messageDigest);
            StringBuilder hashtext = new StringBuilder(number.toString(HEX_FLAG));
            while (hashtext.length() < SIGN_LENGTH) {
                hashtext.insert(0, FILL_CHAR);
            }
            if (upper) {
                hashtext = new StringBuilder(hashtext.toString().toUpperCase());
            }
            return hashtext.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
