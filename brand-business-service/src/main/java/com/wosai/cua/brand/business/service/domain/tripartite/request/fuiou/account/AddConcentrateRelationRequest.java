package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AddConcentrateRelationRequest extends BaseRequest<AddConcentrateRelationRequestBody> {

    public static final String REQUEST_URI = "/addConcentrateRelation.fuiou";

    private AddConcentrateRelationRequestBody body;
    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_ADD_CONCENTRATE_RELATION;
    }

    public AddConcentrateRelationRequest(String mchntCd) {
        this.body = new AddConcentrateRelationRequestBody(mchntCd);
    }


}
