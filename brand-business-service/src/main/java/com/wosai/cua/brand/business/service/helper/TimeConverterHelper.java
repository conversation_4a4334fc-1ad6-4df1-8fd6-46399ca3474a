package com.wosai.cua.brand.business.service.helper;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class TimeConverterHelper {

    private TimeConverterHelper() {
    }
    public static final String STANDARD_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String UNSIGNED_ABBREVIATION_FORMAT = "yyyyMMddHHmmss";

    public static final String UNSIGNED_ABBREVIATION_FORMAT_2 = "yyyyMMddHHmmssSSS";
    /**
     * 时间戳转日期
     * @param timestamp
     * @param pattern
     * @return
     */
    public static String timestampToDate(long timestamp,String pattern) {
        // 时间戳转换为毫秒
        Date date = new Date(timestamp * 1000);
        return dateFormat(date, pattern);
    }

    /**
     * 日期转时间戳
     * @param dateStr
     * @param pattern
     * @return
     */
    public static long dateToTimestamp(String dateStr,String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            Date date = sdf.parse(dateStr);
            // 日期转换为毫秒后再除以1000得到时间戳
            return date.getTime() / 1000;
        } catch (Exception e) {

            return 0;
        }
    }

    public static String dateFormat(Date date,String pattern){
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }
}