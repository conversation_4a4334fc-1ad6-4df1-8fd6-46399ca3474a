package com.wosai.cua.brand.business.service.module.brand;


import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.wosai.cua.brand.business.api.enums.DockingModeEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.enums.SensitiveFieldEnum;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
@SensitiveClass
public class BrandModule {
    /**
     * 主键
     */
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 品牌编号
     */
    private String sn;

    /**
     * 集团ID
     */
    private String groupId;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌简称
     */
    private String alias;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 对接方式
     *
     * @see com.wosai.cua.brand.business.api.enums.DockingModeEnum
     */
    private String dockingMode;

    /**
     * 对接方式描述
     */
    private String dockingModeDesc;

    /**
     * 父品牌id
     */
    private String parentId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 行业
     */
    private String industry;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.MOBILE)
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.EMAIL)
    private String contactEmail;

    /**
     * 合作方id
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_config表中
     */
    @Deprecated
    private String partnerId;

    /**
     * 资管机构编号，PAB、MY_BANK
     */
    private FundManagementCompanyEnum fundManagementCompanyCode;

    /**
     * 资管机构:平安银行、网商银行
     */
    private String fundManagementCompany;

    /**
     * 资金汇总专用账名
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    private String fundSummaryAccountName;

    /**
     * 资金汇总专用账户
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    private String fundSummaryAccount;

    /**
     * 资金归集账户
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    private String fundGatherAccount;

    /**
     * 资金结算账户名称
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    private String fundSettlementAccountName;

    /**
     * 资金结算账户
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    private String fundSettlementAccount;

    /**
     * 所辖商户数量
     */
    private Integer merchantNumber;

    /**
     * 品牌层级
     */
    private Integer level;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 账号
     */
    private String identifier;

    /**
     * 配置
     */
    private ConfigModule configModule;

    /**
     * 品牌对接方式(针对富友)
     */
    private String merchantDockingMode;

    /**
     * 管理商户会员id(针对富友)
     */
    private String adminMerchantMemberId;

    /**
     * 是否开通收付通：0-否，1-是
     */
    private Integer sftTag;

    /**
     * 是否启用收付通：0-否，1-是
     */
    private Integer enableSft;

    public <R> R getConfigModule(Class<R> clazz) {
        return JSON.parseObject(JSON.toJSONString(configModule), clazz);
    }

    public static BrandModule convert(BrandDO brand) {
        if (brand == null) {
            return null;
        }
        BrandModule brandModule = new BrandModule();
        brandModule.setId(brand.getId());
        brandModule.setBrandId(brand.getBrandId());
        brandModule.setSn(brand.getSn());
        brandModule.setName(brand.getName());
        brandModule.setAlias(brand.getAlias());
        brandModule.setGroupId(brand.getGroupId());
        brandModule.setMerchantSn(brand.getMerchantSn());
        brandModule.setDockingMode(brand.getDockingMode());
        brandModule.setDockingModeDesc(DockingModeEnum.getDescByCode(brand.getDockingMode()));
        brandModule.setParentId(brand.getParentId());
        brandModule.setProvince(brand.getProvince());
        brandModule.setCity(brand.getCity());
        brandModule.setDistrict(brand.getDistrict());
        brandModule.setAddress(brand.getAddress());
        brandModule.setIndustry(brand.getIndustry());
        brandModule.setContactName(brand.getContactName());
        brandModule.setContactPhone(brand.getContactPhone());
        brandModule.setContactEmail(brand.getContactEmail());
        brandModule.setPartnerId(brand.getPartnerId());
        brandModule.setFundManagementCompanyCode(FundManagementCompanyEnum.getFundManagementCompanyEnum(brand.getFundManagementCompanyCode()));
        brandModule.setFundManagementCompany(brand.getFundManagementCompany());
        brandModule.setFundSummaryAccountName(brand.getFundSummaryAccountName());
        brandModule.setFundSummaryAccount(brand.getFundSummaryAccount());
        brandModule.setFundGatherAccount(brand.getFundGatherAccount());
        brandModule.setFundSettlementAccountName(brand.getFundSettlementAccountName());
        brandModule.setFundSettlementAccount(brand.getFundSettlementAccount());
        brandModule.setLevel(brand.getLevel());
        brandModule.setRemark(brand.getRemark());
        brandModule.setCreatedTime(brand.getCreatedTime());
        brandModule.setUpdatedTime(brand.getUpdatedTime());
        brandModule.setExtra(brand.getExtra());
        brandModule.setSftTag(brand.getSftTag());
        brandModule.setEnableSft(brand.getEnableSft());
        return brandModule;
    }
}
