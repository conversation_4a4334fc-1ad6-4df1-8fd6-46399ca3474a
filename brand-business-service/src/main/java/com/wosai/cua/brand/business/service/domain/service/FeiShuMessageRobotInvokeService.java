package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.config.feign.FeignConfig;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.BaseFeiShuCardMessageRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.BaseFeiShuMessageRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.CardMessage;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.ImageMessage;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.PostMessage;
import com.wosai.cua.brand.business.service.domain.tripartite.request.feishu.TextMessage;
import com.wosai.cua.brand.business.service.domain.tripartite.response.feishu.BaseFeiShuMessageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 飞书通知机器人接口调用
 */
@FeignClient(url = "${open.feishu.message_robot_url}", name = "feishu-message-robot-service",configuration = FeignConfig.class)
public interface FeiShuMessageRobotInvokeService {
    /**
     * 发送文本消息
     * @param request 请求体
     * @return 返回结果
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    <T> BaseFeiShuMessageResponse<T> sendTextMessage(BaseFeiShuMessageRequest<TextMessage> request);

    /**
     * 发送图片消息
     * @param request 请求体
     * @return 返回结果
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    <T> BaseFeiShuMessageResponse<T> sendImageMessage(BaseFeiShuMessageRequest<ImageMessage> request);

    /**
     * 发送富文本消息
     * @param request 请求体
     * @return 返回结果
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    <T> BaseFeiShuMessageResponse<T> sendPostMessage(BaseFeiShuMessageRequest<PostMessage> request);

    /**
     * 发送卡片消息
     * @param request 请求体
     * @return 返回结果
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    <T> BaseFeiShuMessageResponse<T> sendCardMessage(BaseFeiShuCardMessageRequest<CardMessage> request);
}
