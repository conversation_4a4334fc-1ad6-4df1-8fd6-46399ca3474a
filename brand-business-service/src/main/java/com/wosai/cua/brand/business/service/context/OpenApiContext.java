package com.wosai.cua.brand.business.service.context;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import lombok.Data;

public class OpenApiContext {

    private OpenApiContext(){
        throw new UnsupportedOperationException("can not be instantiated");
    }

    private static final ThreadLocal<Context> CONTEXT_THREAD_LOCAL = ThreadLocal.withInitial(Context::new);

    public static void set(Context context){
        OpenApiContext.CONTEXT_THREAD_LOCAL.set(context);
    }
    public static void remove(){
        CONTEXT_THREAD_LOCAL.remove();
    }

    public static String getBrandId(){
        return CONTEXT_THREAD_LOCAL.get().getBrandId();
    }

    public static String getBrandSn(){
        return CONTEXT_THREAD_LOCAL.get().getBrandSn();
    }

    public static String getMerchantId(){
        return CONTEXT_THREAD_LOCAL.get().getAppId();
    }

    public static String getClientSn(){
        return CONTEXT_THREAD_LOCAL.get().getClientSn();
    }

    public static FundManagementCompanyEnum getFundManagementCompanyEnum(){
        return CONTEXT_THREAD_LOCAL.get().getFundManagementCompanyEnum();
    }

    @Data
    public static class Context{
        private String appId;

        private String brandId;

        private String brandSn;

        private String clientSn;

        private FundManagementCompanyEnum fundManagementCompanyEnum;
    }
}
