package com.wosai.cua.brand.business.service.kafka.helper;

import com.wosai.cua.brand.business.api.enums.MessageTypeEnum;
import com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019-02-14
 */
@Slf4j
public class AvroBeanHelper {
    public static BaseBrandMsg getBaseBrandMsg(String message, MessageTypeEnum messageTypeEnum){
        BaseBrandMsg brandMerchantEnrollMsg = new BaseBrandMsg();
        brandMerchantEnrollMsg.setMessageId(UUID.randomUUID().toString());
        brandMerchantEnrollMsg.setMessage(message);
        brandMerchantEnrollMsg.setMessageType(messageTypeEnum.getMessageType());
        return brandMerchantEnrollMsg;
    }
}
