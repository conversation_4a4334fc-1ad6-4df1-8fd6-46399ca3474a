package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CheckAccountResponse extends BaseOpenApiResponse{

    /**
     * 结果码
     */
    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 结果信息
     */
    @JsonProperty("result_msg")
    private String resultMsg;
}
