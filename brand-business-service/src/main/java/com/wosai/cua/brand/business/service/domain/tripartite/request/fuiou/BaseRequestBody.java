package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou;

import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import lombok.Data;

@Data
public class BaseRequestBody {
    /**
     * 商户流水号
     */
    @Sign
    protected String traceNo;
    /**
     * 商户号
     * 必填
     * 接入商户在富友的唯一代码
     */
    @Sign
    protected String mchntCd;

    /**
     * 签名
     */
    protected String signature;

    public BaseRequestBody(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public BaseRequestBody() {
    }
}
