package com.wosai.cua.brand.business.service.module.merchant.analyze;

import com.wosai.cua.brand.business.service.annotations.AnalyzeFieldCheck;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class IndividualBusinessMerchantAnalyzeModule extends CreateMerchantAnalyzeModule {
    /**
     * 商户名称
     */
    @AnalyzeFieldCheck(message = "未填写商户名称！", needRequired = true)
    private String merchantName;
    /**
     * 商户证照类型
     */
    @AnalyzeFieldCheck(message = "未填写商户证照类型！", needRequired = true)
    private Integer licenseType;
    /**
     * 商户证照编号
     */
    @AnalyzeFieldCheck(message = "未填写商户证照编号！", needRequired = true)
    private String licenseNumber;
    /**
     * 联系人姓名
     */
    @AnalyzeFieldCheck(message = "未填写联系人姓名！", needRequired = true)
    private String contactName;
    /**
     * 联系人电话
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写联系人手机号！",
            needFormatCheck = true,
            pattern = "^(13[0-9]|14[579]|15[012356789]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$",
            formatCheckMsg = "联系人手机号格式不正确！"
    )
    private String contactPhone;
    /**
     * 联系人邮箱
     */
    @AnalyzeFieldCheck(
            needRequired = false,
            message = "",
            needFormatCheck = true,
            pattern = "^[0-9A-Za-z_]+([-+.][0-9A-Za-z_]+)*@[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*\\.[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*$",
            formatCheckMsg = "联系人邮箱格式不正确！"
    )
    private String contactEmail;
    /**
     * 法人姓名
     */
    @AnalyzeFieldCheck(needRequired = true, message = "未填写法人姓名！")
    private String legalPersonName;
    /**
     * 法人证件类型
     */
    @AnalyzeFieldCheck(needRequired = true, message = "未填写法人证件类型！")
    private Integer legalPersonLicenseType;
    /**
     * 法人证件号
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写法人证件号！",
            needFormatCheck = true,
            pattern = "[+-]?\\d+(\\.\\d+)?[eE][+-]?\\d+",
            satisfyPattern = false,
            formatCheckMsg = "法人证件号格式不正确！")
    private String legalPersonLicenseNumber;

}
