package com.wosai.cua.brand.business.service.domain.service.impl.tripartite;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.tripartite.citic.CiticBankClient;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.BaseCiticRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.BindBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.DeleteMerchantRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.QueryMerchantRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.RegisteredUserRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.SetDefaultBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.UpdateMerchantRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.UpdateUserInfoResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.BindBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.CiticBindBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.CiticDeleteMerchantResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.CiticQueryUserResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.CiticRegisteredUserResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.CiticSetDefaultBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.CiticUpdateUserResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.DeleteMerchantResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.QueryUserResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.RegisteredResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.SetDefaultBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data.BindBankCardResponseData;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data.RegisteredUserResponseData;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data.SetDefaultBankCardResponseData;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * 中信接口调用实现
 */
@Service
@Slf4j
public class CiticSystemCallServiceImpl implements TripartiteSystemCallService {

    @Value("${citic-service-address}")
    private String citicServiceAddress;

    private static final Map<String, String> TRANS_CODE_ENDPOINTS = new HashMap<>();

    private static final Map<FunctionEnum, Function<BaseCiticRequest, Function<CiticBankConfigModule, TripartiteSystemCallResponse>>> FUNCTION_ENUM_CONSUMER_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        TRANS_CODE_ENDPOINTS.put("********", citicServiceAddress);
        TRANS_CODE_ENDPOINTS.put("********", citicServiceAddress);
        TRANS_CODE_ENDPOINTS.put("********", citicServiceAddress);
        TRANS_CODE_ENDPOINTS.put("********", citicServiceAddress);
        TRANS_CODE_ENDPOINTS.put("********", citicServiceAddress);
        TRANS_CODE_ENDPOINTS.put("********", citicServiceAddress);
        FUNCTION_ENUM_CONSUMER_MAP.put(FunctionEnum.CITIC_REGISTERED_USER, request -> citicBankConfigModule -> this.invokeRegisteredUser((RegisteredUserRequest) request, citicBankConfigModule));
        FUNCTION_ENUM_CONSUMER_MAP.put(FunctionEnum.CITIC_CREATE_BANK_CARD, request -> citicBankConfigModule -> this.invokeBindBankCard((BindBankCardRequest) request, citicBankConfigModule));
        FUNCTION_ENUM_CONSUMER_MAP.put(FunctionEnum.CITIC_SET_DEFAULT_BANK_CARD, request -> citicBankConfigModule -> this.invokeSetDefaultBankCard((SetDefaultBankCardRequest) request, citicBankConfigModule));
        FUNCTION_ENUM_CONSUMER_MAP.put(FunctionEnum.CITIC_DELETE_MERCHANT, request -> citicBankConfigModule -> this.invokeDeleteMerchant((DeleteMerchantRequest) request, citicBankConfigModule));
        FUNCTION_ENUM_CONSUMER_MAP.put(FunctionEnum.CITIC_QUERY_MERCHANT, request -> citicBankConfigModule -> this.invokeQueryUser((QueryMerchantRequest) request, citicBankConfigModule));
        FUNCTION_ENUM_CONSUMER_MAP.put(FunctionEnum.CITIC_UPDATE_USER, request -> citicBankConfigModule -> this.invokeUpdateUser((UpdateMerchantRequest) request, citicBankConfigModule));
    }

    @Override
    public FundManagementCompanyEnum getFundManagementCompanyEnum() {
        return FundManagementCompanyEnum.CITIC;
    }

    @Override
    public <T extends TripartiteSystemCallRequest, R extends TripartiteSystemCallResponse, C extends ConfigModule> R call(T request, Class<R> clazz, C configModule) {
        BaseCiticRequest baseCiticRequest = null;
        if (request instanceof BaseCiticRequest) {
            baseCiticRequest = (BaseCiticRequest) request;
        }
        CiticBankConfigModule citicBankConfigModule = null;
        if (configModule instanceof CiticBankConfigModule) {
            citicBankConfigModule = (CiticBankConfigModule) configModule;
            log.info("中信配置: {}", JSON.toJSONString(citicBankConfigModule));
        }
        if (Objects.isNull(baseCiticRequest) || Objects.isNull(citicBankConfigModule)) {
            log.error("citic request is not BaseCiticRequest or config is not CiticBankConfigModule");
            return handleError("citic request is not BaseCiticRequest or config is not CiticBankConfigModule", clazz);
        }
        if (FUNCTION_ENUM_CONSUMER_MAP.containsKey(request.getFunctionEnum())) {
            TripartiteSystemCallResponse callResponse = FUNCTION_ENUM_CONSUMER_MAP.get(request.getFunctionEnum()).apply(baseCiticRequest).apply(citicBankConfigModule);
            if (Objects.nonNull(callResponse)) {
                return JSON.parseObject(JSON.toJSONString(callResponse), clazz);
            }
        }
        return handleError("未找到相应的执行方法", clazz);
    }

    private <R> R handleError(String msg, Class<R> clazz) {
        // 假设定义了一个ErrorResponse对象用于表示错误情况
        TripartiteSystemCallResponse tripartiteSystemCallResponse = new TripartiteSystemCallResponse();
        tripartiteSystemCallResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
        tripartiteSystemCallResponse.setResultMsg(msg);
        return JSON.parseObject(JSON.toJSONString(tripartiteSystemCallResponse), clazz);
    }

    /**
     * 注册用户
     *
     * @param request               注册用户请求
     * @param citicBankConfigModule 配置
     * @return 注册用户响应
     */
    private RegisteredResponse invokeRegisteredUser(RegisteredUserRequest request, CiticBankConfigModule citicBankConfigModule) {
        RegisteredResponse registeredResponse = new RegisteredResponse();
        CiticBankClient<CiticRegisteredUserResponse> citicBankClient = new CiticBankClient<>(citicBankConfigModule.getMerchantId(), citicBankConfigModule.getPrivateKeyPassword(), citicBankConfigModule.getPrivateKey(), citicBankConfigModule.getPublicKey(), TRANS_CODE_ENDPOINTS);
        CiticRegisteredUserResponse response = citicBankClient.invoke(request, CiticRegisteredUserResponse.class);
        if (Objects.nonNull(response) && response.isSuccess()) {
            RegisteredUserResponseData data = response.getData();
            registeredResponse.setUserId(data.getUserId());
            registeredResponse.setIsNeedCheck(data.getIsNeedCheck());
            registeredResponse.setPwdId(data.getPwdId());
            registeredResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
            registeredResponse.setResultMsg(data.getRspMsg());
            registeredResponse.setSerialNumber(data.getReqSsn());
            registeredResponse.setTransId(data.getTransId());
        }
        if (Objects.nonNull(response) && response.isNotSuccess()) {
            RegisteredUserResponseData data = response.getData();
            registeredResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
            registeredResponse.setResultMsg(Objects.nonNull(data) ? data.getRspMsg() : response.getMessage());
        }
        return registeredResponse;
    }

    /**
     * 绑定银行卡
     *
     * @param request               绑定银行卡请求
     * @param citicBankConfigModule 配置
     * @return 绑定银行卡响应
     */
    private BindBankCardResponse invokeBindBankCard(BindBankCardRequest request, CiticBankConfigModule citicBankConfigModule) {
        BindBankCardResponse bindBankCardResponse = new BindBankCardResponse();
        CiticBankClient<CiticBindBankCardResponse> citicBankClient = new CiticBankClient<>(citicBankConfigModule.getMerchantId(), citicBankConfigModule.getPrivateKeyPassword(), citicBankConfigModule.getPrivateKey(), citicBankConfigModule.getPublicKey(), TRANS_CODE_ENDPOINTS);
        CiticBindBankCardResponse response = citicBankClient.invoke(request, CiticBindBankCardResponse.class);
        if (Objects.nonNull(response) && response.isSuccess()) {
            BindBankCardResponseData data = response.getData();
            bindBankCardResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
            bindBankCardResponse.setResultMsg(data.getRspMsg());
            bindBankCardResponse.setSerialNumber(data.getReqSsn());
            bindBankCardResponse.setTransId(data.getTransId());
            bindBankCardResponse.setPwdId(data.getPwdId());
        }
        if (Objects.nonNull(response) && response.isNotSuccess()) {
            BindBankCardResponseData data = response.getData();
            bindBankCardResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
            bindBankCardResponse.setResultMsg(Objects.nonNull(data) ? data.getRspMsg() : response.getMessage());
        }
        // 判断是否已经绑定过银行卡，绑定过则返回SUCCESS
        if (Objects.nonNull(response) && Objects.nonNull(response.getData()) && response.getData().getRspCode().equals(CiticBindBankCardResponse.BANK_CARD_BIND)){
            BindBankCardResponseData data = response.getData();
            bindBankCardResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
            bindBankCardResponse.setResultMsg(data.getRspMsg());
        }
        return bindBankCardResponse;
    }

    /**
     * 设置默认银行卡
     *
     * @param request               设置默认银行卡请求
     * @param citicBankConfigModule 配置
     * @return 设置默认银行卡响应
     */
    private SetDefaultBankCardResponse invokeSetDefaultBankCard(SetDefaultBankCardRequest request, CiticBankConfigModule citicBankConfigModule) {
        SetDefaultBankCardResponse setDefaultBankCardResponse = new SetDefaultBankCardResponse();
        CiticBankClient<CiticSetDefaultBankCardResponse> citicBankClient = new CiticBankClient<>(citicBankConfigModule.getMerchantId(), citicBankConfigModule.getPrivateKeyPassword(), citicBankConfigModule.getPrivateKey(), citicBankConfigModule.getPublicKey(), TRANS_CODE_ENDPOINTS);
        CiticSetDefaultBankCardResponse response = citicBankClient.invoke(request, CiticSetDefaultBankCardResponse.class);
        if (Objects.nonNull(response) && response.isSuccess()) {
            SetDefaultBankCardResponseData data = response.getData();
            setDefaultBankCardResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
            setDefaultBankCardResponse.setResultMsg(data.getRspMsg());
            setDefaultBankCardResponse.setSerialNumber(data.getReqSsn());
        }
        if (Objects.nonNull(response) && response.isNotSuccess()) {
            SetDefaultBankCardResponseData data = response.getData();
            setDefaultBankCardResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
            setDefaultBankCardResponse.setResultMsg(Objects.nonNull(data) ? data.getRspMsg() : response.getMessage());
        }
        return setDefaultBankCardResponse;
    }

    private DeleteMerchantResponse invokeDeleteMerchant(DeleteMerchantRequest request, CiticBankConfigModule citicBankConfigModule) {
        DeleteMerchantResponse deleteMerchantResponse = new DeleteMerchantResponse();
        CiticBankClient<CiticDeleteMerchantResponse> citicBankClient = new CiticBankClient<>(citicBankConfigModule.getMerchantId(), citicBankConfigModule.getPrivateKeyPassword(), citicBankConfigModule.getPrivateKey(), citicBankConfigModule.getPublicKey(), TRANS_CODE_ENDPOINTS);
        CiticDeleteMerchantResponse response = citicBankClient.invoke(request, CiticDeleteMerchantResponse.class);
        if (Objects.nonNull(response) && response.isSuccess()) {
            deleteMerchantResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
            deleteMerchantResponse.setResultMsg(response.getData().getRspMsg());
            deleteMerchantResponse.setSerialNumber(response.getData().getReqSsn());
            deleteMerchantResponse.setMessage(response.getData().getMessage());
        }
        if (Objects.nonNull(response) && response.isNotSuccess()) {
            deleteMerchantResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
        }
        return deleteMerchantResponse;
    }

    private QueryUserResponse invokeQueryUser(QueryMerchantRequest request, CiticBankConfigModule citicBankConfigModule) {
        QueryUserResponse queryUserResponse = new QueryUserResponse();
        CiticBankClient<CiticQueryUserResponse> citicBankClient = new CiticBankClient<>(citicBankConfigModule.getMerchantId(), citicBankConfigModule.getPrivateKeyPassword(), citicBankConfigModule.getPrivateKey(), citicBankConfigModule.getPublicKey(), TRANS_CODE_ENDPOINTS);
        try {
            CiticQueryUserResponse response = citicBankClient.invoke(request, CiticQueryUserResponse.class);
            if (Objects.nonNull(response) && Objects.nonNull(response.getData()) && response.isSuccess()) {
                queryUserResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                queryUserResponse.setResultMsg(response.getData().getRspMsg());
                queryUserResponse.setSerialNumber(response.getData().getReqSsn());
                queryUserResponse.setUserCheckStatus(response.getData().getUserCheckStatus());
                queryUserResponse.setUserId(response.getData().getUserId());
                queryUserResponse.setUserName(response.getData().getUserName());
                queryUserResponse.setUserReignLevel(response.getData().getUserReignLevel());
                queryUserResponse.setUserStatus(response.getData().getUserStatus());
                queryUserResponse.setCheckFailReason(response.getData().getCheckFailReason());
                queryUserResponse.setMerchantId(response.getData().getMerchantId());
            }
            if (Objects.nonNull(response) && response.isNotSuccess()) {
                queryUserResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                queryUserResponse.setResultMsg(response.getMessage());
            }
        } catch (Exception e) {
            log.error("query citic user error", e);
            queryUserResponse.setResultMsg(handleError("query citic user error", QueryUserResponse.class).getResultMsg());
        }
        return queryUserResponse;
    }

    private UpdateUserInfoResponse invokeUpdateUser(UpdateMerchantRequest request, CiticBankConfigModule citicBankConfigModule) {
        CiticBankClient<CiticUpdateUserResponse> citicBankClient = new CiticBankClient<>(citicBankConfigModule.getMerchantId(), citicBankConfigModule.getPrivateKeyPassword(), citicBankConfigModule.getPrivateKey(), citicBankConfigModule.getPublicKey(), TRANS_CODE_ENDPOINTS);
        try {
            CiticUpdateUserResponse response = citicBankClient.invoke(request, CiticUpdateUserResponse.class);
            if (Objects.nonNull(response) && response.isSuccess()) {
                log.info("citic update user success");
                log.info("citic update user success, response:{}", response);
            }
            if (Objects.nonNull(response) && response.isNotSuccess()) {
                log.error("citic update user fail, response:{}", response);
            }
        } catch (Exception e) {
            log.error("citic update user error", e);
        }
        return null;
    }
}
