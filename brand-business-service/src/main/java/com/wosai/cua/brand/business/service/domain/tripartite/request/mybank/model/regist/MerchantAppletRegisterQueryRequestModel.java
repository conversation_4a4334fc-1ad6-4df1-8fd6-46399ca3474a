package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist;


import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.CDataAdapter;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * 小程序入驻结果查询接口<ant.mybank.merchantprod.merch.applet.register.query>
 * <p>
 * 此接口用于在商户获取到注册二维码以后，平台根据二维码对应的外部商户号向网商主动获取商户注册状态
 * 与被动注册通知接口功能一致
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class MerchantAppletRegisterQueryRequestModel extends RequestBody {

    private static final long serialVersionUID = 6293985247805008089L;
    /**
     * 合作方机构号
     * <p>
     * 网商银行分配
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;

    /**
     * 外部商户号
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "OutMerchantId")
    private String outMerchantId;

    public String getIsvOrgId() {
        return isvOrgId;
    }

    public void setIsvOrgId(String isvOrgId) {
        this.isvOrgId = isvOrgId;
    }

    public String getOutMerchantId() {
        return outMerchantId;
    }

    public void setOutMerchantId(String outMerchantId) {
        this.outMerchantId = outMerchantId;
    }
}