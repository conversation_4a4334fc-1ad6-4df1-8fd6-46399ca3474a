package com.wosai.cua.brand.business.service;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.wosai.cua.xxl.job.spring.boot.properties.XxlJobProperties;
import com.wosai.database.instrumentation.springboot.v2.EnableDataSourceTranslate;
import com.wosai.oss.configuration.OssConfiguration;
import com.wosai.web.rpc.EnableJsonRpc;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 启动类
 * <AUTHOR>
 */
@EnableJsonRpc
@EnableDataSourceTranslate
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, OssConfiguration.class})
@MapperScan(basePackages = "com.wosai.cua.brand.business.service.domain.dao")
@EnableApolloConfig
@EnableConfigurationProperties(XxlJobProperties.class)
@EnableFeignClients
@EnableAspectJAutoProxy(exposeProxy = true)
public class BrandBusinessServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(BrandBusinessServiceApplication.class, args);
	}

}
