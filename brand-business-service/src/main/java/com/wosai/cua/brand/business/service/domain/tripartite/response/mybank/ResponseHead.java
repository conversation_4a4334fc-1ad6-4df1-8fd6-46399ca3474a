package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "head")
public class ResponseHead extends MybankObject {
    private static final long serialVersionUID = 1055464677625380253L;

    /**
     * API接口版本号
     * <p>
     * 匹配接口文档版本
     */
    @XmlElement(name = "Version")
    private String version;

    /**
     * 应用ID
     * <p>
     * 由浙江网商银行统一分配,用于识别合作伙伴应用系统；即对端系统编号，联调前线下提供，注意此字段此处大小写要求
     */
    @XmlElement(name = "AppId")
    private String appId;

    /**
     * 应用ID
     * <p>
     * 由浙江网商银行统一分配,用于识别合作伙伴应用系统；即对端系统编号，联调前线下提供，注意此字段此处大小写要求
     */
    @XmlElement(name = "Appid")
    private String appid;

    /**
     * 接口英文代码
     * <p>
     * 接口定义中的报文编号，明确接口功能，关联接口文档
     */
    @XmlElement(name = "Function")
    private String function;

    /**
     * 报文应答时间
     * <p>
     * 格式：yyyyMMddHHmmss，应答发起时间
     */
    @XmlElement(name = "RespTime")
    private String respTime;

    /**
     * 报文应答时区
     * <p>
     * 应答发起系统所在时区, UTC+8
     */
    @XmlElement(name = "RespTimeZone")
    private String respTimeZone;

    /**
     * 应答报文ID
     * <p>
     * 唯一定位一次报文请求，由发起方生成，应答方原样返回，UUID生成，全局唯一
     */
    @XmlElement(name = "ReqMsgId")
    private String reqMsgId;

    /**
     * 报文字符编码
     * <p>
     * 目前支持UTF-8编码
     */
    @XmlElement(name = "InputCharset")
    private String inputCharset;

    /**
     * 保留字段
     * <p>
     * 使用K=V方式表达, 默认空值
     */
    @XmlElement(name = "Reserve")
    private String reserve;

    /**
     * 签名方式
     * <p>
     * 固定送 RSA
     */
    @XmlElement(name = "SignType")
    private String signType;

    public ResponseHead() {
    }

    public ResponseHead(String version, String appId, String function, String reqMsgId) {
        this.version = version;
        this.appId = appId;
        this.function = function;
        this.reqMsgId = reqMsgId;
    }

    public ResponseHead(String version, String appId, String appid, String function, String reqMsgId) {
        this.version = version;
        this.appId = appId;
        this.appid = appid;
        this.function = function;
        this.reqMsgId = reqMsgId;
    }

    public static Builder builder(String version, String appId, String function, String reqMsgId) {
        return new Builder(version, appId, function, reqMsgId);
    }

    public static Builder builder(String version, String appId, String appid, String function, String reqMsgId) {
        return new Builder(version, appId, appid, function, reqMsgId);
    }

    public static class Builder {
        private ResponseHead responseHead;

        Builder(String version, String appId, String function, String reqMsgId) {
            responseHead = new ResponseHead(version, appId, function, reqMsgId);
        }

        Builder(String version, String appId, String appid, String function, String reqMsgId) {
            responseHead = new ResponseHead(version, appId, appid, function, reqMsgId);
        }

        public ResponseHead build() {
            return responseHead;
        }

        public Builder respTime(String respTime) {
            responseHead.setRespTime(respTime);
            return this;
        }

        public Builder respTimeZone(String respTimeZone) {
            responseHead.setRespTimeZone(respTimeZone);
            return this;
        }

        public Builder inputCharset(String inputCharset) {
            responseHead.setInputCharset(inputCharset);
            return this;
        }

        public Builder reserve(String reserve) {
            responseHead.setReserve(reserve);
            return this;
        }

        public Builder signType(String signType) {
            responseHead.setSignType(signType);
            return this;
        }
    }

    public String getVersion() {
        return version;
    }
    public void setVersion(String version) {
        this.version = version;
    }

    public String getAppId() {
        return appId;
    }
    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppid() {
        return appid;
    }
    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getFunction() {
        return function;
    }
    public void setFunction(String function) {
        this.function = function;
    }

    public String getRespTime() {
        return respTime;
    }
    public void setRespTime(String respTime) {
        this.respTime = respTime;
    }

    public String getRespTimeZone() {
        return respTimeZone;
    }
    public void setRespTimeZone(String respTimeZone) {
        this.respTimeZone = respTimeZone;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }
    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }

    public String getInputCharset() {
        return inputCharset;
    }
    public void setInputCharset(String inputCharset) {
        this.inputCharset = inputCharset;
    }

    public String getReserve() {
        return reserve;
    }
    public void setReserve(String reserve) {
        this.reserve = reserve;
    }

    public String getSignType() {
        return signType;
    }
    public void setSignType(String signType) {
        this.signType = signType;
    }
}