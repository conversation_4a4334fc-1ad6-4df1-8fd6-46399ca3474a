package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class ResponseDocument extends MybankObject {
    private static final long serialVersionUID = 6977872626560800648L;

    @XmlElementRef
    private Response response;

    public Response getResponse() { return response; }

    public void setResponse(Response response) { this.response = response; }

    public ResponseDocument() { }

    public ResponseDocument(Response response) { this.response = response; }
}