package com.wosai.cua.brand.business.service.facade.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.fuiou.ResendRequestDTO;
import com.wosai.cua.brand.business.api.facade.FuiouFacade;
import com.wosai.cua.brand.business.service.business.FuiouBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class FuiouFacadeImpl implements FuiouFacade {

    private final FuiouBusiness fuiouBusiness;

    public FuiouFacadeImpl(FuiouBusiness fuiouBusiness) {
        this.fuiouBusiness = fuiouBusiness;
    }

    @Override
    public String resend(ResendRequestDTO resendRequest) {
        return fuiouBusiness.resend(resendRequest.getBrandId(), resendRequest.getMerchantId(),resendRequest.getMerchantSn());
    }
}
