package com.wosai.cua.brand.business.service.aspect;

import com.wosai.cua.brand.business.api.annotations.MerchantFieldNotBank;
import com.wosai.cua.brand.business.api.annotations.Validation;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ValidationAspect {

    @Before("execution(* com.wosai.cua.brand.business.service.facade.impl.*.*(..))")
    public void valid(JoinPoint joinPoint) {
        // 在这里通过反射等方式获取目标字段的值，并进行校验逻辑
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        if (!method.isAnnotationPresent(Validation.class)) {
            return;
        }
        Object[] args = joinPoint.getArgs();
        // 可以根据实际情况进行进一步处理
        for (Object arg : args) {
            if (Objects.nonNull(arg)) {
                this.checkField(arg);
            }
        }
    }

    private void checkField(Object arg) {
        Field[] fields = arg.getClass().getDeclaredFields();
        List<Field> checkFields = Arrays.stream(fields).filter(field -> field.isAnnotationPresent(MerchantFieldNotBank.class)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(checkFields)) {
            checkFields.forEach(field -> {
                field.setAccessible(true);
                MerchantFieldNotBank annotation = field.getAnnotation(MerchantFieldNotBank.class);
                if (Objects.nonNull(annotation)) {
                    this.notBankCheck(arg, field, annotation, fields);
                }
            });
        }
    }

    private void notBankCheck(Object arg, Field field, MerchantFieldNotBank annotation, Field[] fields) {
        String targetFieldName = annotation.targetField();
        Optional<Field> any = Arrays.stream(fields).filter(targetField -> targetField.getName().equals(targetFieldName)).findAny();
        if (any.isPresent()) {
            try {
                List<BrandMerchantTypeEnum> brandMerchantTypeEnums = Arrays.stream(annotation.compareValues()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(brandMerchantTypeEnums)){
                    return;
                }
                any.get().setAccessible(true);
                Object targetFieldValue = any.get().get(arg);
                if (Objects.isNull(targetFieldValue)) {
                    log.error("目标字段不能为空！");
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR);
                }
                boolean isBank = brandMerchantTypeEnums.contains(targetFieldValue) && (Objects.isNull(field.get(arg)) || StringUtils.isEmpty(field.get(arg).toString().trim()));
                if (isBank) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), annotation.message());
                }
            } catch (IllegalAccessException e) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR);
            }
        }
    }
}
