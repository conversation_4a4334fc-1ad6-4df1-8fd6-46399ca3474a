package com.wosai.cua.brand.business.service.module.tripartite.fuiou;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FuiouToBeActiveRecordModule {
    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 商户id
     */
    private String merchantId;
    /**
     * 流水
     */
    private String tradeNo;
}
