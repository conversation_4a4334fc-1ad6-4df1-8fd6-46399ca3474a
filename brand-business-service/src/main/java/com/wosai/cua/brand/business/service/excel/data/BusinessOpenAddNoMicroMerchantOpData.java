package com.wosai.cua.brand.business.service.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.service.excel.converters.IdTypeEnumConverter;
import com.wosai.cua.brand.business.service.excel.converters.MerchantTypeEnumConverter;
import com.wosai.cua.brand.business.service.excel.converters.PayTypeEnumConverter;
import com.wosai.cua.brand.business.service.excel.groups.Default;
import com.wosai.cua.brand.business.service.excel.groups.Indirect;
import com.wosai.cua.brand.business.service.excel.model.IdTypeEnum;
import com.wosai.cua.brand.business.service.excel.model.PayTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Data
public class BusinessOpenAddNoMicroMerchantOpData implements ErrorAware {

    @ExcelProperty("序号")
    @NotEmpty(message = "序号为空", groups = Default.class)
    private String sequenceNo;
    @ExcelProperty(value = "合作关系", converter = MerchantTypeEnumConverter.class)
    @NotNull(message = "合作关系", groups = Default.class)
    private MerchantTypeEnum cooperation;
    @ExcelProperty("商户经营名称")
    @NotEmpty(message = "商户经营名称为空", groups = Default.class)
    private String businessName;
    @ExcelProperty("行业代码")
    @NotEmpty(message = "行业代码为空", groups = Default.class)
    private String industryCode;
    @ExcelProperty("所在地区代码")
    @NotEmpty(message = "所在地区代码为空", groups = Default.class)
    private String districtCode;
    @ExcelProperty("详细地址")
    @NotEmpty(message = "详细地址为空", groups = Default.class)
    private String streetAddress;
    @ExcelProperty("联系人")
    @NotEmpty(message = "联系人为空", groups = Default.class)
    private String contactName;
    @ExcelProperty("联系电话")
    @NotEmpty(message = "联系电话为空", groups = Default.class)
    private String contactPhone;
    @ExcelProperty("超级管理员登录账号")
    @NotEmpty(message = "超级管理员登录账号为空", groups = Default.class)
    private String loginPhone;
    @ExcelProperty("统一社会信用代码")
    @NotEmpty(message = "统一社会信用代码为空", groups = Default.class)
    private String licenseNumber;
    @ExcelProperty("营业证照名称")
    @NotEmpty(message = "营业证照名称为空", groups = Default.class)
    private String licenseName;
    @ExcelProperty("营业证照注册地址")
    @NotEmpty(message = "营业证照注册地址为空", groups = Default.class)
    private String licenseAddress;
    @ExcelProperty("营业证照有效开始日期")
    @NotEmpty(message = "营业证照有效开始日期为空", groups = Default.class)
    private String licenseValidityStart;
    @ExcelProperty("营业证照有效截止日期")
    @NotEmpty(message = "营业证照有效截止日期为空", groups = Default.class)
    private String licenseValidityEnd;
    @ExcelProperty("法人代表姓名")
    @NotEmpty(message = "法人代表姓名为空", groups = Default.class)
    private String legalPersonName;
    @ExcelProperty(value = "法人证件类型", converter = IdTypeEnumConverter.class)
    @NotNull(message = "法人证件类型为空", groups = Default.class)
    private IdTypeEnum legalPersonIdType;
    @ExcelProperty("法人代表证件号")
    @NotEmpty(message = "法人代表证件号为空", groups = Default.class)
    private String legalPersonIdNumber;
    @ExcelProperty("证件有效期开始日期")
    @NotEmpty(message = "证件有效期开始日期为空", groups = Default.class)
    private String idValidityStart;
    @ExcelProperty("证件有效期截止日期")
    @NotEmpty(message = "证件有效期截止日期为空", groups = Default.class)
    private String idValidityEnd;
    @ExcelProperty(value = "结算账户类型", converter = PayTypeEnumConverter.class)
    @NotNull(message = "结算账户类型为空", groups = Default.class)
    private PayTypeEnum payType;
    @ExcelProperty(value = "结算人证件类型", converter = IdTypeEnumConverter.class)
    private IdTypeEnum holderIdType;
    @ExcelProperty("结算人姓名")
    private String holder;
    @ExcelProperty("结算人证件号")
    private String identity;
    @ExcelProperty("结算人证件有效开始日期")
    private String holderIdValidityStart;
    @ExcelProperty("结算人证件有效截止日期")
    private String holderIdValidityEnd;
    @ExcelProperty("对公账户名称")
    private String publicHolder;
    @ExcelProperty("银行账号/卡号")
    @NotEmpty(message = "银行账号/卡号为空", groups = Indirect.class)
    private String number;
    @ExcelProperty("开户行行号")
    @NotEmpty(message = "开户行行号为空", groups = Indirect.class)
    private String openingNumber;
    @ExcelProperty("银行卡有效期")
    private String cardValidity;

    // ---以下是导入结果
    @ExcelProperty("商户号")
    private String merchantSn;
    @ExcelProperty("商户ID")
    private String merchantId;
    @ExcelProperty("间连扫码结果")
    private String indirectResult;


    public boolean isNoLegalSettlement() {
        return PayTypeEnum.NO_LEGAL_SETTLEMENT.equals(payType);
    }

    public boolean isPublicSettlement() {
        return PayTypeEnum.PUBLIC_SETTLEMENT.equals(payType);
    }

    // 实现 ErrorAware 接口
    @Override
    public void setErrorMessage(String errorMessage) {
        this.indirectResult = errorMessage;
    }
}
