package com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;

public interface SignChecker {

    /**
     * @param xmlContent 待验签XML字符串
     * @param charset 字符编码
     * @param signType 签名类型，RSA/RSA2
     * @return 验签结果 true/false
     */
    boolean check(String xmlContent, String charset, String signType) throws MybankApiException;

    /**
     * @param content  待验签参数字符串
     * @param sign     签名值
     * @param charset  字符编码
     * @param signType 签名类型，RSA/RSA2
     * @return 验签结果 true/false
     * @throws MybankApiException
     */
    boolean webCheck(String content, String sign, String charset, String signType) throws MybankApiException;
}