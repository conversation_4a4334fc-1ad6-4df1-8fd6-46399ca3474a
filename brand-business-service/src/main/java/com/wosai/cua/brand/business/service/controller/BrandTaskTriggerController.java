package com.wosai.cua.brand.business.service.controller;

import com.wosai.cua.brand.business.service.job.BrandTaskSchedule;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/10/8
 */
@RestController
@RequestMapping("/brand/task")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BrandTaskTriggerController {

    private final BrandTaskSchedule brandTaskSchedule;

    private static final ResponseEntity<String> SUCCESS_RESULT = ResponseEntity.ok("success");

    @GetMapping("/importTask")
    public ResponseEntity<String> importTask(Long timeLimit, int size) {
        brandTaskSchedule.brandMerchantImportTask(timeLimit, size);
        return SUCCESS_RESULT;
    }
}
