package com.wosai.cua.brand.business.service.domain.service.impl.tripartite;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.VfinanceBaseRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 平安-维金财务系统接口实现
 */
@Service
@Slf4j
public class PabVfinanceSystemCallServiceImpl implements TripartiteSystemCallService {

    private final VfinanceInterfaceService vfinanceInterfaceService;

    public PabVfinanceSystemCallServiceImpl(VfinanceInterfaceService vfinanceInterfaceService) {
        this.vfinanceInterfaceService = vfinanceInterfaceService;
    }

    @Override
    public FundManagementCompanyEnum getFundManagementCompanyEnum() {
        return FundManagementCompanyEnum.PAB;
    }

    @Override
    public <T extends TripartiteSystemCallRequest, R extends TripartiteSystemCallResponse, C extends ConfigModule> R call(T request, Class<R> clazz, C configModule) {
        VfinanceBaseRequest vfinanceBaseRequest = (VfinanceBaseRequest) request;
        try {
            log.info("调用维金接口{}，入参为：{}", vfinanceBaseRequest.getService(), JSON.toJSONString(request));
            String responseStr = vfinanceInterfaceService.invokeService(vfinanceBaseRequest);
            if (StringUtils.isNotBlank(responseStr)) {
                log.info("调用维金接口{}，出参为：{}", vfinanceBaseRequest.getService(), responseStr);
                return JSON.parseObject(responseStr, clazz);
            }
        } catch (Exception e) {
            log.warn("调用维金接口异常。", e);
            throw new BrandBusinessException(e.getMessage());
        }
        return null;
    }


}
