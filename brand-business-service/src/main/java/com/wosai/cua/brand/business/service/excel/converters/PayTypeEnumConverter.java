package com.wosai.cua.brand.business.service.excel.converters;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.wosai.cua.brand.business.service.excel.model.PayTypeEnum;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
public class PayTypeEnumConverter implements Converter<PayTypeEnum> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return PayTypeEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public PayTypeEnum convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return PayTypeEnum.getByText(cellData.getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(PayTypeEnum payTypeEnum, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(payTypeEnum.getText());
    }
}
