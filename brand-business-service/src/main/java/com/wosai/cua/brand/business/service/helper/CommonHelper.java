package com.wosai.cua.brand.business.service.helper;

import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.service.enums.AlreadyExistMerchantExcelFieldEnum;
import com.wosai.cua.brand.business.service.module.merchant.analyze.AlreadyExistMerchantAnalyzeModule;
import com.wosai.shorturl.api.ShortUrlService;
import com.wosai.shorturl.api.request.ShortUrlGenRequest;
import com.wosai.shorturl.api.result.ShortUrlGenResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayDeque;
import java.util.Date;
import java.util.Deque;
import java.util.Enumeration;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommonHelper {


    public static final long SHORT_URL_DEFAULT_EXPIRE_TIME = 24 * 60 * 60 * 1000;

    private final ShortUrlService shortUrlService;

    @Autowired
    public CommonHelper(ShortUrlService shortUrlService) {
        this.shortUrlService = shortUrlService;
    }

    public static String getMacAddress(InetAddress inetAddress) {
        try {
            NetworkInterface network = NetworkInterface.getByInetAddress(inetAddress);
            byte[] mac = network.getHardwareAddress();
            if (Objects.nonNull(mac)) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < mac.length; i++) {
                    sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? ":" : ""));
                }
                return sb.toString();
            }

        } catch (SocketException e) {
            log.error("获取mac地址异常。", e);
        }
        return null;
    }

    /**
     * 获取ip与mac地址的map关系
     *
     * @return ip地址与mac地址的映射
     */
    public static Map<String, String> getIpMacAddressMap() {
        Map<String, String> ipMacMap = Maps.newHashMap();
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (!address.isLoopbackAddress() && !address.getHostAddress().contains(":")) {
                        ipMacMap.put(address.getHostAddress(), getMacAddress(address));
                    }
                }
            }
        } catch (SocketException e) {
            log.error("获取ip地址异常。", e);
        }
        return ipMacMap;
    }

    public static String getMyBankQrCodeUrl(String bankQrCodeUrl, String appId, String outMerchantId, String isvOrgId) {
        if (StringUtils.isBlank(bankQrCodeUrl) || StringUtils.isBlank(appId) || StringUtils.isBlank(outMerchantId) || StringUtils.isBlank(isvOrgId)) {
            return null;
        }
        // 使用临时占位符替换原始占位符，避免与URL中的其他大括号冲突
        String tempURL = bankQrCodeUrl
                .replace("{appid}", "__APPID__")
                .replace("{outMchId}", "__OUTMCHID__")
                .replace("{ISV_ORG_ID}", "__ISVORGID__");

        // 使用实际的值替换临时占位符
        return tempURL
                .replace("__APPID__", appId)
                .replace("__OUTMCHID__", outMerchantId)
                .replace("__ISVORGID__", isvOrgId);
    }

    /**
     * 获取对象中指定字段的值
     *
     * @param t          目标对象
     * @param fieldNames 字段名称数组，由0到n-1的索引组成，分别表示某一层级的字段名称
     * @return 字段值
     */
    public static String getFieldValue(Object t, String... fieldNames) {
        Map<String, Field> fieldMap = Maps.newHashMap();
        Class<?> clazz = t.getClass();
        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                String fieldName = field.getName();
                fieldMap.put(fieldName, field);
            }
            clazz = clazz.getSuperclass();
        }
        Deque<Object> deque = new ArrayDeque<>();
        deque.push(t);
        while (!deque.isEmpty()) {
            for (String fieldName : fieldNames) {
                if (deque.isEmpty()) {
                    continue;
                }
                Object currentObj = deque.pop();
                try {
                    Field field = fieldMap.get(fieldName);
                    if (Objects.isNull(field)) {
                        continue;
                    }
                    field.setAccessible(true);
                    Object fieldValue = field.get(currentObj);
                    if (Objects.isNull(fieldValue)) {
                        return "";
                    }
                    if (fieldValue instanceof String) {
                        return fieldValue.toString();
                    }
                    if (fieldValue instanceof Date) {
                        return TimeConverterHelper.dateFormat((Date) fieldValue, TimeConverterHelper.STANDARD_TIME_FORMAT);
                    }
                    deque.push(fieldValue);
                } catch (IllegalAccessException e) {
                    log.error("获取字段{}的值失败", fieldName);
                }
            }
        }
        return null;
    }

    public String getShortUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        log.info("原始链接为：{}", url);
        return getShortUrl(url, SHORT_URL_DEFAULT_EXPIRE_TIME);
    }

    /**
     * 获取短链接
     *
     * @param url        原始链接
     * @param expireTime 短链接有效时间，单位：毫秒
     * @return 短链接
     */
    public String getShortUrl(String url, long expireTime) {
        ShortUrlGenRequest shortUrlGenRequest = new ShortUrlGenRequest();
        shortUrlGenRequest.setOriUrl(url);
        shortUrlGenRequest.setValidityPeriod(expireTime);
        ShortUrlGenResult shortUrlGenResult = shortUrlService.genShortUrl(shortUrlGenRequest);
        if (Objects.isNull(shortUrlGenResult)) {
            return null;
        }
        return shortUrlGenResult.getShortUrl();
    }
}
