package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.account;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.CDataAdapter;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * 商户通用开户接口<ant.mybank.bkcloudfunds.account.open>
 * 请求模型
 * <p>
 * 该接口为各个平台isv提供为商家基于场景增量开通子户的接口
 * BASIC， 开通可用和冻结子户
 * PREPAY，开通合并支付子户
 * SETTLING，开通待结算子户 ，用于预下单场景
 * TRADE_DEPOSIT 开通保证金子户， 目前处于管控状态， 需要配置才能使用
 * 只有BASIC SETTLING TRADE_DEPOSIT 三种场景才会返回卡号
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
@Data
public class BkcloudfundsAccountOpenRequestModel extends RequestBody {

    private static final long serialVersionUID = -4847466764671689357L;
    /**
     * 合作方机构号
     * <p>
     * 网商银行分配
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;

    /**
     * 网商商户号
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MerchantId")
    private String merchantId;

    /**
     * 开户场景
     * <p>
     * 目前支持 BASIC， PREPAY， SETTLING，TRADE_DEPOSIT 四种场景的子户
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "AcctType")
    private String acctType;

    /**
     * 外部流水号（用于幂等控制）
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;

    /**
     * 外部流水号（用于幂等控制）
     * <p>
     * Map<String,String>, JSON BASE64Encode,
     * AcctType=SETTLING 必填key=ACQUIRE_ID,alue=机构编号
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "ExtInfo")
    private String extInfo;
}