package com.wosai.cua.brand.business.service.config.feign;

import feign.Feign;
import feign.Retryer;
import feign.form.FormEncoder;
import feign.querymap.BeanQueryMapEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

// 全局配置： 使用@Configuration会全局配置到所有服务提供方(被调用方)
// 局部配置： 如果只针对某个服务，就不要使用@Configuration，在@FeignClient(name = "stock-service", path = "/stock", configuration = FeignClient.class)中加入configuration = FeignClient.class

/**
 * <AUTHOR>
 */
@Configuration
public class FeignConfig {

    @Bean
    public FormEncoder formEncoder(ObjectFactory<HttpMessageConverters> converters) {
        return new FastJsonFormEncoder(new SpringEncoder(converters));
    }

    /**
     * @Description 替换解析queryMap的类，实现父类中变量的映射
     * @date 2019/5/21 16:59
     * @version V1.0.0
     */
    @Bean
    public Feign.Builder feignBuilder() {
        return Feign.builder()
                .queryMapEncoder(new BeanQueryMapEncoder())
                .retryer(Retryer.NEVER_RETRY);
    }

}
