package com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;

public interface Signer {

    /**
     * @param xmlContent 待签名XML字符串
     * @param charset 字符编码
     * @param signType 签名类型，RSA/RSA2
     * @return 已签名XML字符串
     */
    String sign(String xmlContent, String charset, String signType) throws MybankApiException;

    /**
     * @param xmlContent 待签名XML字符串
     * @param charset 字符编码
     * @param signType 签名类型，RSA/RSA2
     * @return 已签名XML字符串
     */
    String notifyResponseSign(String xmlContent, String charset, String signType) throws MybankApiException;

    /**
     * @param content  待签名参数字符串
     * @param charset  字符编码
     * @param signType 签名类型，RSA/RSA2
     * @return 签名值
     * @throws MybankApiException
     */
    String webSign(String content, String charset, String signType) throws MybankApiException;
}