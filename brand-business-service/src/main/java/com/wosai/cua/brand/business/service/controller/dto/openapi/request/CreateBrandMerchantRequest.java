package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.api.enums.AggregationModelEnum;
import com.wosai.cua.brand.business.api.enums.MerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.UseOfFundsEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class CreateBrandMerchantRequest extends OpenApiBaseRequest{

    /**
     * 商户编号
     */
    @NotBlank(message = "商户编号不能为空")
    @JsonProperty("merchant_sn")
    private String merchantSn;

    /**
     * 商户类型
     * @see MerchantTypeEnum
     */
    @NotBlank(message = "商户类型不能为空")
    @JsonProperty("merchant_type")
    private String merchantType;

    /**
     * 关联品牌商户门店编号
     */
    @NotBlank(message = "关联品牌商户的收钱吧门店编号不能为空")
    @JsonProperty("sqb_store_sn")
    private String sqbStoreSn;

    /**
     * 关联美团门店号
     */
    @JsonProperty("mei_tuan_store_sn")
    private String associatedMeituanStoreSn;

    /**
     * 关联饿了么门店号
     */
    @JsonProperty("elm_store_sn")
    private String associatedElmStoreSn;

    /**
     * 商户对接方式:SEPARATE_ACCOUNT-分账，COLLECTION-归集（两种）
     * 不传，默认：分账模式
     * @see MerchantDockingModeEnum
     */
    @JsonProperty("merchant_docking_mode")
    private MerchantDockingModeEnum merchantDockingMode = MerchantDockingModeEnum.SEPARATE_ACCOUNT;

    /**
     * 归集模式:BALANCE_POOLING-余额归集，ORDERS_COLLECTED_AFTERWARDS-订单事后归集，ORDER_PRE_COLLECTION-订单预归集（三种）
     */
    @JsonProperty("aggregation_model")
    private AggregationModelEnum aggregationModel;

    /**
     * 归集最大比例
     * 如：100% = 10000
     */
    @JsonProperty("concentrate_scale")
    private Integer concentrateScale;

    /**
     * 资金用途
     */
    @JsonProperty("use_of_funds")
    private UseOfFundsEnum useOfFunds;

    /**
     * 被归集商户号
     */
    @JsonProperty("concentrate_merchant_no")
    private String concentrateMerchantNo;
    /**
     * 银行卡信息：富友品牌商户入驻时必传
     */
    @JsonProperty("bank_card")
    private CreateBankCardDTO bankCard;

    /**
     * 外部商户号
     */
    @JsonProperty("out_merchant_no")
    private String outMerchantNo;

    @Data
    @Accessors(chain = true)
    public static class CreateBankCardDTO {

        /**
         * 品牌id
         */
        @NotBlank(message = "品牌id必传")
        @JsonProperty("brand_id")
        private String brandId;
        /**
         * 商户id
         */
        @JsonProperty("merchant_id")
        private String merchantId;

        /**
         * 商户编号
         */
        @JsonProperty("merchant_sn")
        private String merchantSn;
        /**
         * 账户类型 1 个人账户 2 企业账户
         */
        @NotNull(message = "type不能为空")
        private Integer type;
        /**
         * 银行卡号
         */
        @NotBlank(message = "银行卡号bankCardNo不能为空")
        @JsonProperty("bank_card_no")
        private String bankCardNo;

        /**
         * 开户行号
         */
        @NotBlank(message = "开户行号openingNumber，不能为空")
        @JsonProperty("opening_number")
        private String openingNumber;

        @NotBlank(message = "银行预留手机号不能为空")
        @JsonProperty("reserved_mobile_number")
        private String reservedMobileNumber;

        /**
         * 银行卡分支行所在城市
         */
        private String city;

        @NotBlank(message = "账户持有人名称holder不能为空")
        private String holder;

        /**
         * 账户持有人证件类型：1 身份证；2 港澳居民来往内地通行证； 3 台湾居民来往大陆通行证； 4 非中华人民共和国护照； 5 中国护照
         */
        private Integer idType;

        /**
         * 账户持有人证件编号
         */
        private String identity;

        /**
         * 是否设置为默认卡
         */
        @JsonProperty("set_default")
        private Boolean setDefault;
    }
}
