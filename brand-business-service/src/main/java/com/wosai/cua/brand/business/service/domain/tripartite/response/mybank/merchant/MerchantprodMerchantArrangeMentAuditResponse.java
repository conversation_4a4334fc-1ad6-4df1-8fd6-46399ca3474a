package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantprodMerchantArrangeMentAuditResponseModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;


/**
 * Mybank Api: ant.mybank.merchantprod.merchant.arrangement.audit
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class MerchantprodMerchantArrangeMentAuditResponse extends MybankResponse {

    private static final long serialVersionUID = 5163265861410256686L;
    @XmlElementRef
    private MerchantprodMerchantArrangeMentAudit merchantprodMerchantArrangeMentAudit;

    public MerchantprodMerchantArrangeMentAudit getMerchantprodMerchantArrangeMentAudit() {
        return merchantprodMerchantArrangeMentAudit;
    }

    public void setMerchantprodMerchantArrangeMentAudit(MerchantprodMerchantArrangeMentAudit merchantprodMerchantArrangeMentAudit) {
        this.merchantprodMerchantArrangeMentAudit = merchantprodMerchantArrangeMentAudit;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "response")
    public static class MerchantprodMerchantArrangeMentAudit extends MybankObject {


        private static final long serialVersionUID = -3953934772452303431L;

        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private ResponseHead responseHead;

        /**
         * 业务应答参数（body）
         */
        @XmlElementRef
        private MerchantprodMerchantArrangeMentAuditResponseModel merchantprodMerchantArrangeMentAuditResponseModel;

        public ResponseHead getResponseHead() {
            return responseHead;
        }

        public void setResponseHead(ResponseHead responseHead) {
            this.responseHead = responseHead;
        }

        public MerchantprodMerchantArrangeMentAuditResponseModel getMerchantprodMerchantArrangeMentAuditResponseModel() {
            return merchantprodMerchantArrangeMentAuditResponseModel;
        }

        public void setMerchantprodMerchantArrangeMentAuditResponseModel(MerchantprodMerchantArrangeMentAuditResponseModel merchantprodMerchantArrangeMentAuditResponseModel) {
            this.merchantprodMerchantArrangeMentAuditResponseModel = merchantprodMerchantArrangeMentAuditResponseModel;
        }
    }

}
