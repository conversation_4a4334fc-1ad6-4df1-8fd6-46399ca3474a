package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 返回码组件
 * <p>
 * 返回码用于明确业务处理结果，在应答报文(结果文件)中必须出现，用于描述本次请求的处理结果。
 * 1.对于报文接口，返回码组件对应应答报文中的respInfo节点，如果应答报文是一个结果集，那么返回码组件需要与每个结果一一对应
 * 2.对于文件接口，结果文件中每一条数据都需要包括对应的返回码参数信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RespInfo")
@XmlType(propOrder = {"resultStatus", "resultCode", "resultMsg"})
public class RespInfo extends MybankObject {
    private static final long serialVersionUID = -6866379852663479440L;

    /**
     * 处理状态
     * <p>
     * 本次业务处理的状态，默认以下3个状态： S：成功，F：失败，U：未知
     * 如不满足要求，可根据业务扩展 注意：状态为U不代表交易失败，需要通过回查去处理，不能直接反馈客户交易失败
     */
    @XmlElement(name = "ResultStatus")
    private String resultStatus;

    /**
     * 返回码
     * <p>
     * 当resultStatus为S时，该字段必定为0000
     * 当resultStatus为F或U时，该字段可以为全局返回码，也可以为业务返回码 如果为业务返回码，参见业务接口部分
     */
    @XmlElement(name = "ResultCode")
    private String resultCode;

    /**
     * 返回码信息
     * <p>
     * 当resultStatus为S时，该字段可为空 当resultStatus为F或U时，需要描述该错误的原因
     */
    @XmlElement(name = "ResultMsg")
    private String resultMsg;

    public RespInfo() {
    }

    public RespInfo(String resultStatus, String resultCode) {
        this.resultStatus = resultStatus;
        this.resultCode = resultCode;
    }

    private RespInfo(String resultStatus, String resultCode, String resultMsg) {
        this.resultStatus = resultStatus;
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
    }

    public static Builder builder(String resultStatus, String resultCode, String resultMsg) {
        return new Builder(resultStatus, resultCode, resultMsg);
    }

    public static class Builder {
        private RespInfo respInfo;

        Builder(String resultStatus, String resultCode, String resultMsg) {
            respInfo = new RespInfo(resultStatus, resultCode, resultMsg);
        }

        public RespInfo build() {
            return respInfo;
        }
    }

    public String getResultStatus() {
        return resultStatus;
    }

    public void setResultStatus(String resultStatus) {
        this.resultStatus = resultStatus;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }
}