package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.card;

import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ModifyAccountInCardResponse extends TripartiteSystemCallResponse {

    private ModifyAccountInCardResponseBody body;

}
