package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.BaseResponseBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
public class QueryConcentrateRelationResponseBody extends BaseResponseBody {

    private List<Item> items;

    @Data
    public static class Item {
        private String mchntCdConcentrate;
        private String batchNo;
        private List<String> concentrateTypes;
        private String useType;

        private String status;

        private Integer orderConcentrateScale;

        private Integer balanceConcentrateScale;

        private String createDate;

        private String startDate;

        private String endDate;
    }
}
