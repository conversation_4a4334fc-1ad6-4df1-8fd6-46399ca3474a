package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.service.controller.dto.openapi.enums.CommunicationResponseCodeEnum;
import com.wosai.cua.brand.business.service.controller.dto.openapi.enums.OpenApiResponseCodeEnum;
import com.wosai.cua.brand.business.service.controller.dto.openapi.enums.ResultErrorCodeEnum;
import lombok.Data;

@Data
public class BaseResponse<T> {
    /**
     * 通讯结果码，表示接口调用的业务逻辑是否成功
     */
    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 业务结果
     */
    @JsonProperty("biz_response")
    private BizResponse<T> bizResponse;

    /**
     * 通讯错误描述
     */
    @JsonProperty("error_message")
    private String errorMsg;

    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private String errorCode;

    @Data
    public static class BizResponse<T> {
        /**
         * 业务结果码，表示接口调用的业务逻辑是否成功
         */
        @JsonProperty("result_code")
        private String resultCode;
        /**
         *
         */
        private T data;
        /**
         * 业务错误描述
         */
        @JsonProperty("error_message")
        private String errorMsg;
        /**
         * 业务错误码
         */
        @JsonProperty("error_code")
        private String errorCode;
    }

    public static <T> BaseResponse<T> success(T data) {
        BaseResponse<T> baseResponse = new BaseResponse<>();
        baseResponse.setResultCode(CommunicationResponseCodeEnum.SUCCESS.getCode());
        BizResponse<T> bizResponse = new BizResponse<>();
        bizResponse.setData(data);
        bizResponse.setResultCode(OpenApiResponseCodeEnum.SUCCESS.getCode());
        baseResponse.setBizResponse(bizResponse);
        return baseResponse;
    }

    public static <T> BaseResponse<T> failParams(String code, String message) {
        BaseResponse<T> baseResponse = new BaseResponse<>();
        baseResponse.setResultCode(ResultErrorCodeEnum.INVALID_PARAMS.getCode());
        baseResponse.setErrorCode(code);
        baseResponse.setErrorMsg(message);
        return baseResponse;
    }

    public static <T> BaseResponse<T> businessFail(String code, String message) {
        BaseResponse<T> baseResponse = new BaseResponse<>();
        baseResponse.setResultCode(CommunicationResponseCodeEnum.SUCCESS.getCode());
        BizResponse<T> bizResponse = new BizResponse<>();
        bizResponse.setResultCode(code);
        bizResponse.setErrorMsg(message);
        baseResponse.setBizResponse(bizResponse);
        return baseResponse;
    }
}
