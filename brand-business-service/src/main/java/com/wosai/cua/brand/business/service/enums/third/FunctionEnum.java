package com.wosai.cua.brand.business.service.enums.third;

/**
 * 通知接口地址与对应通知策略实现的bean id
 **/
public enum FunctionEnum {
    PROTOCOL_WITHHOLD_REFUND_RESULT_NOTIFY("ant.mybank.bkcloudfunds.protocol.withhold.refund.result.notify", "ProtocolWithholdRefundResultNotify"),
    EXTERNAL_REDIRECT_RESULT_NOTIFY("ant.mybank.bkmerchanttrade.external.redirect.result.notify", "bkmerchanttradeExternalRedirectResultNotifyService"),
    KYB_NOTICE("ant.mybank.bkmerchantprod.kyb.notice", "bkmerchantprodKybNoticeService"),
    MERCHANTPROD_MERCH_NOTIFY("ant.mybank.merchantprod.merch.notify", "bkMerchantprodMerchNotifyService"),
    PREPAY_NOTICE("ant.mybank.bkmerchanttrade.prePayNotice", "bkmerchanttradePrePayNoticeService"),
    ONLINE_PAY_NOTICE("ant.mybank.bkmerchanttrade.onlinePayNotice", "bkmerchanttradeOnlinePayNoticeService"),
    VOSTRO_NOTIFY("ant.mybank.bkcloudfunds.vostro.notify", "bkcloudfundsVostroNotifyService"),
    PLATFORM_DEPOSIT_NOTIFY("ant.mybank.bkcloudfunds.billpay.platform.deposit.notify", "bkcloudfundsBillpayPlatformDepositNotifyService"),
    BATCH_FINISH_NOTIFY("ant.mybank.bkcloudfunds.billpay.batch.finish.notify", "bkcloudfundsBillpayBatchFinishNotifyService"),
    REFUND_NOTIFY("ant.mybank.bkcloudfunds.refund.notify", "bkcloudfundsRefundNotifyService"),
    DIRECT_REFUND_NOTIFY("ant.mybank.bkcloudfunds.direct.refund.notify", "bkcloudfundsDirectRefundNotifyService"),
    ORDER_SHARE_NOTIFY("ant.mybank.bkcloudfunds.ordershare.notify", "bkcloudfundsOrderShareNotifyService"),
    REFUND_SHARE_NOTIFY("ant.mybank.bkcloudfunds.refundshare.notify", "bkcloudfundsRefundShareNotifyService"),
    WITHDRAW_NOTIFY("ant.mybank.bkcloudfunds.withdraw.notify", "bkcloudfundsWithdrawNotifyService"),
    SINGLE_SUBSIDY_NOTIFY("ant.mybank.bkcloudfunds.single.subsidy.notify", "bkcloudfundsSingleSubsidyNotifyService"),
    AUTH_SETTLE_OPERATE_NOTIFY("ant.mybank.bkcloudfunds.authsettle.operatenotify", "bkcloudfundsAuthsettleOperateNotifyService"),
    PREPAY_PUSH_NOTIFY("ant.mybank.bkcloudfunds.prepay.push.notify", "bkcloudfundsPrepayPushNotifyService"),
    SINGLE_PAYMENT_NOTIFY("ant.mybank.bkcloudfunds.single.payment.notify", "bkcloudfundsSinglePaymentNotifyService"),
    ACCOUNT_CURRENT_CHANGE_NOTIFY("ant.mybank.bkcloudfunds.account.current.change.notify", "bkcloudfundsAccountCurrentChangeNotifyService"),
    ELECTRONIC_RECEIPT_NOTIFY("ant.mybank.bkcloudfunds.electronicreceipt.notify", "bkcloudfundsElectronicReceiptNotifyService"),
    ACCOUNT_CERTIFICATE_NOTIFY("ant.mybank.bkcloudfunds.accountcertificate.notify", "bkcloudfundsAccountCertificateNotifyService"),
    NOTIFY_PAY_RESULT("ant.mybank.bkmerchantsettle.notifyPayResult", "bkmerchantSettleNotifyPayResultService"),
    SUPPLYCHAIN_CLEARING_NOTIFY("ant.mybank.bkcloudfunds.supplychain.clearing.notify", "bkcloudfundsSupplyChainClearingNotifyService"),
    CREATEORDER_NOTIFY("ant.mybank.bkcloudfunds.createorder.notify", "bkcloudfundsCreateOrderNotifyService"),
    ORDER_PAYED_NOTIFY("ant.mybank.bkcloudfunds.order.payed.notify", "bkcloudfundsOrderPayedNotifyService"),
    MERCH_SIGN_NOTIFY("ant.mybank.merchantprod.merch.sign.notify", "bkMerchantprodMerchSignNotifyService"),
    UNIFIED_ORDER_NOTIFY("ant.mybank.bkcloudfunds.unifiedorder.notify", "bkcloudfundsUnifiedOrderNotifyService"),
    PAYROLL_PAYMENT_NOTIFY("ant.mybank.bkcloudfunds.payroll.payment.notify", "bkcloudfundsPayrollPaymentNotifyService"),
    BATCH_RESULT_NOTIFY("ant.mybank.industry.payroll.batch.result.notify", "industryPayrollBatchResultNotifyService"),
    BATCH_CONFIRM_NOTIFY("ant.mybank.industry.payroll.batch.confirm.notify", "industryPayrollBatchConfirmNotifyService"),
    ALIBABA_ORDER_FUND_TRANSFER_NOTIFY("ant.mybank.bkcloudfunds.alibaba.order.fund.transfer.notify", "bkcloudfundsAlibabaOrderFundTransferNotifyService"),
    BALANCE_RECEIPT_APPLY_NOTIFY("ant.mybank.bkcloudfunds.balance.receipt.apply.notify", "bkcloudfundsBalanceReceiptApplyNotifyService"),
    DIRECTIONAL_BALANCE_PAY_NOTIFY("ant.mybank.bkcloudfunds.directional.balance.pay.notify", "bkcloudfundsDirectionalBalancePayNotifyService"),
    HOUSE_SPECIAL_ACCOUNT_BALANCE_RECEIPT_APPLY_NOTIFY("ant.mybank.bkcloudfunds.account.asset.receipt.apply.notify", "bkcloudfundsHouseSpecialAccountBalanceReceiptApplyNotifyService"),
    TMALL_HOUSE_SETTLE_NOTIFY("ant.mybank.bkcloudfunds.tmall.house.settle.notify", "bkcloudfundsTmallHouseSettleNotifyService"),
    ORIGINCARD_REFUND_NOTIFY("ant.mybank.bkcloudfunds.origincard.refund.notify", "bkcloudfundsOrigincardRefundNotifyService"),
    TMALL_HOUSE_VOSTRO_NOTIFY("ant.mybank.bkcloudfunds.tmall.house.vostro.notify", "bkcloudfundsTmallHouseVostroNotifyService"),
    PAYMENT_SINGLE_NOTIFY("ant.mybank.industry.payroll.payment.single.notify", "industryPayrollPaymentSingleNotifyService"),
    CHANNEL_VOSTRO_NOTIFY("ant.mybank.bkcloudfunds.channel.vostro.notify", "bkcloudfundsChannelVostroNotifyService"),
    SALARY_ACCOUNT_OPEN_NOTIFY("ant.mybank.bkmbp.salary.account.open.notify", "bkmbpSalaryAccountOpenNotifyService"),
    BUSINESS_OPERATE_NOTIFY("ant.mybank.bktradeprod.business.operate.notify", "bktradeprodBusinessOperateNotifyService"),
    REEXCHANGE_RESULT_NOTIFY("ant.mybank.bkcloudfunds.reexchange.result.notify", "bkcloudfundsReexchangeResultNotifyService"),
    ACCOUNT_OPEN_NOTIFY("ant.mybank.bkwalletprod.account.open.notify", "bkwalletprodAccountOpenNotifyService"),
    UNIFIED_ORDER_TRADE_RESULT_NOTIFY("ant.mybank.bkcloudfunds.unifiedorder.traderesult.notify", "bkcloudfundsUnifiedOrderTradeResultNotifyService"),
    ACCOUNT_REFUND_REEXCHANGE_NOTIFY("ant.mybank.bkcloudfunds.account.refund.reexchange.notify", "bkcloudfundsAccountRefundReexchangeNotifyService"),
    PAYROLL_VOSTRO_NOTIFY("ant.mybank.industry.payroll.vostro.notify", "industryPayrollVostroNotifyService"),
    PAYROLL_REFUND_NOTIFY("ant.mybank.industry.payroll.refund.notify", "industryPayrollRefundNotifyService"),
    PAYROLL_REEXCHANGE_RESULT_NOTIFY("ant.mybank.industry.payroll.reexchange.result.notify", "industryPayrollReexchangeResultNotifyService"),
    PAYROLL_ELECTRONICRECEIPT_NOTIFY("ant.mybank.industry.payroll.electronicreceipt.notify", "industryPayrollElectronicreceiptNotifyService"),
    INDUSTRY_PAYROLL_PAYMENT_NOTIFY("ant.mybank.industry.payroll.payment.notify", "industryPayrollPaymentNotifyService"),
    ACCOUNT_UNFREEZE_NOTIFY("ant.mybank.bkcloudfunds.merchant.account.unfreeze.notify", "accountUnfreezeNotifyService"),
    ARRANGEMENT_INFO_NOTIFY("ant.mybank.merchantprod.merchant.arrangement.info.notify", "merchantprodMerchantArrangementInfoNotifyService"),
    INDUSTRY_PAYROLL_CURRENT_TRANSFER_NOTIFY("ant.mybank.industry.payroll.current.transfer.notify", "industryPayrollCurrentTransferNotifyService"),
    AUTHORIZE_SIGN_NOTIFY("ant.mybank.bkcloudfunds.authorize.sign.notify", "bkcloudfundsAuthorizeSignNotifyService"),
    AUTHORIZE_WITHHOLD_NOTIFY("ant.mybank.bkcloudfunds.authorize.withhold.notify", "bkcloudfundsAuthorizeWithholdNotifyService"),
    SUBACCOUNT_SETTLE_NOTIFY("ant.mybank.industry.subaccount.settle.notify", "industrySubaccountSettleNotifyService"),
    PROTOCOL_WITHHOLD_RESULT_NOTIFY("ant.mybank.bkcloudfunds.protocol.withhold.result.notify", "bkcloudfundsProtocolWithholdResultNotifyService"),
    BKCLOUDFUNDS_OPERATE_RESULT_NOTIFY("ant.mybank.bkcloudfunds.operate.result.notify", "bkcloudfundsOperateResultNotifyService"),
    VOSTRO_SCENE_ACCOUNT_NOTIFY("ant.mybank.bkcloudfunds.vostro.scene.account.notify", "bkcloudfundsVostroSceneAccountNotifyService"),
    SCENE_ACCOUNT_TRANSFER_NOTIFY("ant.mybank.bkcloudfunds.scene.account.transfer.notify", "bkcloudfundsSceneAccountTransferNotifyService"),
    TRADE_OPERATE_RESULT_NOTIFY("ant.mybank.bkcloudfunds.trade.operate.result.notify", "bkcloudfundsTradeOperateResultNotifyService"),
    PROTOCOL_SIGN_NOTIFY("ant.mybank.bkcloudfunds.protocol.sign.notify", "bkcloudfundsProtocolSignNotifyService"),
    FUNDTRADE_OPERATE_RESULT_NOTIFY("ant.mybank.fundtrade.operate.result.notify", "fundtradeOperateResultNotifyService"),
    MERCHANT_CONTROL_NOTIFY("ant.mybank.merchantprod.merch.control.notify", "merchantControlNotifyService");

    private String functionKey;
    private String strategyValue;

    FunctionEnum(String functionKey, String strategyValue) {
        this.functionKey = functionKey;
        this.strategyValue = strategyValue;
    }

    public static String getStrategyValue(String functionKey) {
        FunctionEnum[] functionEnums = values();
        for (FunctionEnum functionEnum : functionEnums) {
            if (functionEnum.functionKey().equals(functionKey)) {
                return functionEnum.strategyValue();
            }
        }
        return null;
    }

    public static String getFunctionKey(String strategyValue) {
        FunctionEnum[] functionEnums = values();
        for (FunctionEnum functionEnum : functionEnums) {
            if (functionEnum.strategyValue().equals(strategyValue)) {
                return functionEnum.functionKey();
            }
        }
        return null;
    }

    private String functionKey() {
        return this.functionKey;
    }

    private String strategyValue() {
        return this.strategyValue;
    }

}