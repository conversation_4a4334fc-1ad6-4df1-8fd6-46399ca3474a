package com.wosai.cua.brand.business.service.helper;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.module.bank.BankOfDepositModule;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class BankOfDepositHelper {

    public static final String BANK_OF_DEPOSIT_KEY = "brand-business:bank_of_deposit";
    private List<BankOfDepositModule> bankOfDepositModules;


    @Qualifier("redisClusterTemplate")
    private final StringRedisTemplate redisClusterTemplate;

    @Getter
    private final ApolloConfig apolloConfig;

    @Autowired
    public BankOfDepositHelper(StringRedisTemplate redisClusterTemplate, ApolloConfig apolloConfig) {
        this.redisClusterTemplate = redisClusterTemplate;
        this.apolloConfig = apolloConfig;
    }

    @PostConstruct
    public void initBankOfDepositModules() {
        redisClusterTemplate.opsForValue().set(BANK_OF_DEPOSIT_KEY, apolloConfig.getBankOfDepositString(), 5, TimeUnit.MINUTES);
        bankOfDepositModules = JSON.parseArray(apolloConfig.getBankOfDepositString(), BankOfDepositModule.class);
    }

    public String getBankOfDepositByName(String name) {
        String bankOfDepositString = redisClusterTemplate.opsForValue().get(BANK_OF_DEPOSIT_KEY);
        if (StringUtils.isBlank(bankOfDepositString)) {
            bankOfDepositString = apolloConfig.getBankOfDepositString();
            redisClusterTemplate.opsForValue().set(BANK_OF_DEPOSIT_KEY, bankOfDepositString, 5, TimeUnit.MINUTES);
        }
        bankOfDepositModules = JSON.parseArray(bankOfDepositString, BankOfDepositModule.class);
        if (CollectionUtils.isEmpty(bankOfDepositModules)){
            log.info("银行名称{}，对应的配置项",name);
            return "";
        }
        Optional<BankOfDepositModule> first = bankOfDepositModules.stream().filter(bankOfDepositModule -> bankOfDepositModule.getBankName().equals(name)).findFirst();
        if (!first.isPresent()){
            log.info("银行名称{}，未查到对应的银行编号",name);
            return "";
        }
        return first.get().getBankNo();
    }

}
