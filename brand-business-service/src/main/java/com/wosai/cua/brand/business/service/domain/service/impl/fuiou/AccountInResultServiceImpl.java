package com.wosai.cua.brand.business.service.domain.service.impl.fuiou;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.EnrollChannelEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.service.BankCardDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.FuiouEncryptedNotifyService;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util.FuiouRsaCipher;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util.SignUtils;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util.XmlConvertUtil;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.notify.AccountInOutModel;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantEnrollEvent;
import com.wosai.cua.brand.business.service.event.publisher.DefaultEventPublisher;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.common.bean.ListResult;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.Markers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccountInResultServiceImpl implements FuiouEncryptedNotifyService {

    private static final Map<String, Consumer<AccountInOutModel>> FUNCTION_MAP = Maps.newHashMap();

    private final BrandConfigDomainService brandConfigDomainService;

    private final BrandDomainService brandDomainService;

    private final BankCardDomainService bankCardDomainService;

    private final MerchantBizBankAccountService merchantBizBankAccountService;

    private final DefaultEventPublisher defaultEventPublisher;

    @Autowired
    public AccountInResultServiceImpl(BrandConfigDomainService brandConfigDomainService, BrandDomainService brandDomainService, BankCardDomainService bankCardDomainService, MerchantBizBankAccountService merchantBizBankAccountService, ApolloConfig apolloConfig, DefaultEventPublisher defaultEventPublisher) {
        this.brandConfigDomainService = brandConfigDomainService;
        this.brandDomainService = brandDomainService;
        this.bankCardDomainService = bankCardDomainService;
        this.merchantBizBankAccountService = merchantBizBankAccountService;
        this.defaultEventPublisher = defaultEventPublisher;
    }

    @PostConstruct
    public void initFunctionMap() {
        FUNCTION_MAP.put("1", this::createUser);
        FUNCTION_MAP.put("2", this::modifyAgreement);
        FUNCTION_MAP.put("3", this::bindCard);
    }

    @Override
    public String getNotifyType() {
        return ACCOUNT_IN_RESULT;
    }

    @Override
    public String notifyHandle(String xmlContext, String notifyType, String merchantId) throws Exception {
        this.log(xmlContext, notifyType, merchantId);
        BrandConfigModule config = brandConfigDomainService.getBrandConfigByChannelIdAndChannelType(merchantId, FundManagementCompanyEnum.FUIOU.getFundManagementCompanyCode());
        if (Objects.isNull(config) || StringUtils.isBlank(config.getConfig())) {
            throw new BrandBusinessException("未找到" + merchantId + "的配置信息");
        }
        FuiouConfigModule configModule = JSON.parseObject(config.getConfig(), FuiouConfigModule.class);
        if (StringUtils.isBlank(configModule.getPrivateKey())) {
            throw new BrandBusinessException("未找到" + merchantId + "的私钥");
        }
        AccountInOutModel out;
        try {
            out = XmlConvertUtil.xml2Bean(
                    FuiouRsaCipher.decryptByRsaPri(xmlContext, configModule.getPrivateKey(), "GBK"), AccountInOutModel.class
            );
        } catch (Exception e) {
            log.error("【AccountInResultServiceImpl】报文解析失败", e);
            throw new BrandBusinessException("报文解析失败");
        }
        log.info("fuiou notify result:{}", JSON.toJSONString(out));
        boolean verifySign = SignUtils.verify(out, AccountInOutModel.class, configModule.getFyPublicKey());
        if (!verifySign) {
            log.error("【AccountInResultServiceImpl】富友签名验证失败");
            throw new BrandBusinessException("签名验证失败");
        }
        FUNCTION_MAP.get(out.getType()).accept(out);
        return "";
    }

    private void log(String xmlContext, String notifyType, String merchantId) {
        Map<String, Object> toAppendEntriesMap = new HashMap<>(10);
        toAppendEntriesMap.put("method", "AccountInResultServiceImpl.notifyHandle");
        Map<String, Object> requestMap = Maps.newHashMap();
        requestMap.put("xmlContext", xmlContext);
        toAppendEntriesMap.put("notifyType", notifyType);
        requestMap.put("merchantId", merchantId);
        toAppendEntriesMap.put("request", requestMap);
        log.info(Markers.appendEntries(toAppendEntriesMap), "");
    }

    private void createUser(AccountInOutModel out) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMemberId(out.getAccountIn());
        if (Objects.isNull(brandMerchantModule)) {
            log.error("【AccountInResultServiceImpl】未找到商户信息");
            throw new BrandBusinessException("未找到商户信息");
        }
        if ("01".equals(out.getStatus())) {
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.INVALID.getStatus());
        }
        if ("02".equals(out.getStatus())) {
            BankCardModule defaultBankCardModule = bankCardDomainService.getDefaultBankCardModule(brandMerchantModule.getBrandId(), brandMerchantModule.getMerchantId());
            if (defaultBankCardModule != null) {
                defaultBankCardModule.setStatus(1);
                bankCardDomainService.updateBankCard(defaultBankCardModule);
            }
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
            brandMerchantModule.setAccountOpenedTime(new Date());
            // 事务提交后执行的业务代码
            defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandMerchantModule.getBrandId()).merchantSn(brandMerchantModule.getMerchantSn()).enrollChannelEnum(EnrollChannelEnum.FUIOU).build());
        }
        brandDomainService.updateBrandMerchant(brandMerchantModule);
    }

    private void modifyAgreement(AccountInOutModel out) {

    }

    private static final String BIZ_TYPE = "bbs";
    private static final String STATUS_ACTIVE = "02";
    private static final String STATUS_INACTIVE = "01";

    private void bindCard(AccountInOutModel out) {
        // 空值检查
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMemberId(out.getAccountIn());
        if (brandMerchantModule == null) {
            throw new BrandBusinessException("BrandMerchantModule is null for account: " + out.getAccountIn());
        }

        List<BankCardModule> modules = bankCardDomainService.getBankCardModulesByCarIdList(
                brandMerchantModule.getBrandId(),
                brandMerchantModule.getMerchantId(),
                Lists.newArrayList(),
                null, null
        );

        if (CollectionUtils.isEmpty(modules)) {
            return; // 如果没有模块直接返回
        }

        HashMap<String, Object> request = Maps.newHashMap();
        request.put("merchant_id", brandMerchantModule.getMerchantId());
        request.put("biz", BIZ_TYPE);

        try {
            ListResult bizBankAccount = merchantBizBankAccountService.findBizBankAccount(request);
            long total = bizBankAccount.getTotal();

            if (total <= 0) {
                return; // 如果没有结果直接返回
            }

            Map<String, BankCardModule> bankCardModuleMap = modules.stream()
                    .collect(Collectors.toMap(BankCardModule::getBankCardId, Function.identity()));

            bizBankAccount.getRecords().forEach(res -> {
                // 确保类型转换正确
                String bankCardNo = (String) res.get("number");
                String bankCardId = (String) res.get("id");

                if (out.getOutAcntNo().equals(bankCardNo)) {
                    BankCardModule bankCardModule = bankCardModuleMap.get(bankCardId);
                    if (Objects.nonNull(bankCardModule)) {
                        if (STATUS_ACTIVE.equals(out.getStatus())) {
                            bankCardModule.setStatus(1);
                            if (Boolean.TRUE.equals(bankCardModule.getIsDefault())) {
                                brandMerchantModule.setBankCardActivateStatus(BankCardActivateStatusEnum.ACTIVATED.getActivateStatus());
                            }
                        } else if (STATUS_INACTIVE.equals(out.getStatus())) {
                            bankCardModule.setStatus(0);
                            bankCardModule.setActivateFailReason(out.getMsg());
                        }
                        bankCardDomainService.updateBankCard(bankCardModule);
                    }
                }
            });
            brandDomainService.updateBrandMerchant(brandMerchantModule);
        } catch (NullPointerException e) {
            log.error("NullPointerException in bindCard method: ", e);
            throw new BrandBusinessException("NullPointerException in bindCard method: " + e.getMessage());
        } catch (Exception e) {
            log.error("Error in bindCard method: ", e);
            throw new BrandBusinessException("Error in bindCard method: " + e.getMessage());
        }
    }


}
