package com.wosai.cua.brand.business.service.domain.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandAccountDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.service.BrandAccountDomainService;
import com.wosai.cua.brand.business.service.module.account.AccountsModule;
import com.wosai.cua.brand.business.service.module.account.BrandAccountModule;
import com.wosai.cua.brand.business.service.module.account.convert.BrandAccountModuleConverter;
import com.wosai.cua.brand.business.service.module.account.pab.PabAccountModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【brand_account(品牌相关账户汇总表)】的数据库操作Service实现
 * @createDate 2024-07-17 17:02:29
 */
@Service
public class BrandAccountDomainServiceImpl implements BrandAccountDomainService {

    private final BrandAccountDOMapper brandAccountMapper;

    private final BrandMapper brandMapper;


    @Autowired
    public BrandAccountDomainServiceImpl(BrandAccountDOMapper brandAccountMapper, BrandMapper brandMapper) {
        this.brandAccountMapper = brandAccountMapper;
        this.brandMapper = brandMapper;
    }

    @Override
    public void insertBrandAccount(BrandAccountModule brandAccountModule) {
        brandAccountMapper.insert(BrandAccountModuleConverter.convertBrandAccountDO(brandAccountModule));
    }

    @Override
    public <T extends AccountsModule> T getAccountsByBrandId(String brandId, Class<T> clazz) {
        LambdaQueryWrapper<BrandAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandAccountDO::getBrandId, brandId);
        BrandAccountDO brandAccount = brandAccountMapper.selectOne(queryWrapper);
        if (brandAccount != null) {
            return JSON.parseObject(brandAccount.getAccounts(), clazz);
        }
        return null;
    }



    @Override
    public void dealPabAccount() {
        LambdaQueryWrapper<BrandDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandDO::getFundManagementCompanyCode, "PAB");
        queryWrapper.eq(BrandDO::getDeleted, 0);
        List<BrandDO> brandDOList = brandMapper.selectList(queryWrapper);
        for (BrandDO brandDO : brandDOList) {
            LambdaQueryWrapper<BrandAccountDO> queryAccountWrapper = new LambdaQueryWrapper<>();
            queryAccountWrapper.eq(BrandAccountDO::getBrandId, brandDO.getBrandId());
            BrandAccountDO accountDO = brandAccountMapper.selectOne(queryAccountWrapper);
            if (Objects.isNull(accountDO)) {
                accountDO = new BrandAccountDO();
                accountDO.setBrandId(brandDO.getBrandId());
                PabAccountModule payAccountModule = new PabAccountModule();
                payAccountModule.setFundGatherAccount(brandDO.getFundGatherAccount());
                payAccountModule.setFundSettlementAccount(brandDO.getFundSettlementAccount());
                payAccountModule.setFundSettlementAccountName(brandDO.getFundSettlementAccountName());
                payAccountModule.setFundSummaryAccount(brandDO.getFundSummaryAccount());
                payAccountModule.setFundSummaryAccountName(brandDO.getFundSummaryAccountName());
                accountDO.setAccounts(JSON.toJSONString(payAccountModule));
                brandAccountMapper.insert(accountDO);
            } else {
                PabAccountModule payAccountModule = JSON.parseObject(accountDO.getAccounts(), PabAccountModule.class);
                payAccountModule.setFundGatherAccount(brandDO.getFundGatherAccount());
                payAccountModule.setFundSettlementAccount(brandDO.getFundSettlementAccount());
                payAccountModule.setFundSettlementAccountName(brandDO.getFundSettlementAccountName());
                payAccountModule.setFundSummaryAccount(brandDO.getFundSummaryAccount());
                payAccountModule.setFundSummaryAccountName(brandDO.getFundSummaryAccountName());
                accountDO.setAccounts(JSON.toJSONString(payAccountModule));
                brandAccountMapper.updateById(accountDO);
            }
        }
    }

    @Override
    public BrandAccountModule getBrandAccountModuleByBrandId(String brandId) {
        LambdaQueryWrapper<BrandAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandAccountDO::getBrandId, brandId);
        return BrandAccountModuleConverter.convertBrandAccountModule(brandAccountMapper.selectOne(queryWrapper));
    }

    @Override
    public void updateBrandAccount(BrandAccountModule brandAccountModule) {
        BrandAccountDO brandAccountDO = BrandAccountModuleConverter.convertBrandAccountDO(brandAccountModule);
        brandAccountDO.setUpdatedTime(new Date());
        brandAccountMapper.updateById(brandAccountDO);
    }
}




