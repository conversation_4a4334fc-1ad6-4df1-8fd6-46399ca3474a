package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.MerchantArrangementInfoQueryResponseModel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;


/**
 * Mybank Api: ant.mybank.merchantprod.merchant.arrangement.info.query
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class MerchantArrangementInfoQueryResponse extends MybankResponse {


    private static final long serialVersionUID = -8665792874828440584L;

    @XmlElementRef
    private MerchantprodMerchantArrangementInfoQuery  merchantprodMerchantArrangementInfoQuery;

    public MerchantprodMerchantArrangementInfoQuery getMerchantprodMerchantArrangementInfoQuery() {
        return merchantprodMerchantArrangementInfoQuery;
    }

    public void setMerchantprodMerchantArrangementInfoQuery(MerchantprodMerchantArrangementInfoQuery merchantprodMerchantArrangementInfoQuery) {
        this.merchantprodMerchantArrangementInfoQuery = merchantprodMerchantArrangementInfoQuery;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "response")
    public static class MerchantprodMerchantArrangementInfoQuery extends MybankObject {


        private static final long serialVersionUID = -7129051916115587485L;

        @XmlElementRef
        private ResponseHead responseHead;

        @XmlElementRef
        private MerchantArrangementInfoQueryResponseModel merchantArrangementInfoQueryResponseModel;


        public ResponseHead getResponseHead() {
            return responseHead;
        }

        public void setResponseHead(ResponseHead responseHead) {
            this.responseHead = responseHead;
        }

        public MerchantArrangementInfoQueryResponseModel getMerchantArrangementInfoQueryResponseModel() {
            return merchantArrangementInfoQueryResponseModel;
        }

        public void setMerchantArrangementInfoQueryResponseModel(MerchantArrangementInfoQueryResponseModel merchantArrangementInfoQueryResponseModel) {
            this.merchantArrangementInfoQueryResponseModel = merchantArrangementInfoQueryResponseModel;
        }
    }
}




