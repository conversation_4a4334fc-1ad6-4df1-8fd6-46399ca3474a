package com.wosai.cua.brand.business.service.business;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.dto.request.CreateWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.PageBrandWithdrawStrategyRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageQueryMerchantsByWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantByWdStrategyResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandWithdrawStrategyResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandWithdrawStrategyResponseDTO;
import com.wosai.cua.brand.business.api.enums.ApplicableSceneEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandWithdrawStrategyDomainService;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantConditionModule;
import com.wosai.cua.brand.business.service.module.withdraw.BrandWithdrawStrategyModule;
import com.wosai.cua.brand.business.service.module.withdraw.PageBrandWithdrawStrategyModule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class BrandWithdrawStrategyBusiness {

    private final BrandWithdrawStrategyDomainService brandWithdrawStrategyDomainService;

    private final BrandDomainService brandDomainService;

    private final ApolloConfig apolloConfig;

    public BrandWithdrawStrategyBusiness(BrandWithdrawStrategyDomainService brandWithdrawStrategyDomainService, BrandDomainService brandDomainService, ApolloConfig apolloConfig) {
        this.brandWithdrawStrategyDomainService = brandWithdrawStrategyDomainService;
        this.brandDomainService = brandDomainService;
        this.apolloConfig = apolloConfig;
    }

    @Transactional(rollbackFor = Exception.class)
    public BrandWithdrawStrategyModule createBrandWithdrawStrategy(CreateWithdrawStrategyDTO createWithdrawStrategy) {
        BrandWithdrawStrategyModule brandWithdrawStrategyModule = JSON.parseObject(JSON.toJSONString(createWithdrawStrategy), BrandWithdrawStrategyModule.class);
        if (StringUtils.isBlank(createWithdrawStrategy.getApplicableScene()) || ApplicableSceneEnum.CONFIGURATION.name().equals(createWithdrawStrategy.getApplicableScene())){
            if (Objects.nonNull(createWithdrawStrategy.getReservedAmount())) {
                this.checkAmount(createWithdrawStrategy.getReservedAmount());
                brandWithdrawStrategyModule.setReservedAmount(createWithdrawStrategy.getReservedAmount().multiply(new BigDecimal("100")).intValue());
            }
            if (Objects.nonNull(createWithdrawStrategy.getMinWithdrawalAmount())) {
                this.checkAmount(createWithdrawStrategy.getMinWithdrawalAmount());
                brandWithdrawStrategyModule.setMinWithdrawalAmount(createWithdrawStrategy.getMinWithdrawalAmount().multiply(new BigDecimal("100")).intValue());
            }
        }
        brandWithdrawStrategyDomainService.createBrandWithdrawStrategy(brandWithdrawStrategyModule);
        return brandWithdrawStrategyModule;
    }

    private void checkAmount(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return;
        }
        if (amount.compareTo(new BigDecimal(apolloConfig.getMinAmount())) < 0) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.AMOUNT_LESS_THEN_MIN.getCode(),String.format(BrandBusinessExceptionEnum.AMOUNT_LESS_THEN_MIN.getMessage(),apolloConfig.getMinAmount(),apolloConfig.getMaxAmount()));
        }
        if (amount.compareTo(new BigDecimal(apolloConfig.getMaxAmount())) > 0){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.AMOUNT_BIG_THEN_MAX.getCode(),String.format(BrandBusinessExceptionEnum.AMOUNT_BIG_THEN_MAX.getMessage(),apolloConfig.getMinAmount(),apolloConfig.getMaxAmount()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public BrandWithdrawStrategyModule modifyBrandWithdrawStrategy(ModifyWithdrawStrategyDTO modifyWithdrawStrategy) {
        BrandWithdrawStrategyModule brandWithdrawStrategyModule = JSON.parseObject(JSON.toJSONString(modifyWithdrawStrategy), BrandWithdrawStrategyModule.class);
        BrandWithdrawStrategyModule brandWithdrawStrategyModuleByStrategyId = brandWithdrawStrategyDomainService.getBrandWithdrawStrategyModuleByStrategyId(brandWithdrawStrategyModule.getStrategyId());
        if (Objects.isNull(brandWithdrawStrategyModuleByStrategyId)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.STRATEGY_INFO_NOT_FIND);
        }
        brandWithdrawStrategyModule.setId(brandWithdrawStrategyModuleByStrategyId.getId());
        if (StringUtils.isBlank(modifyWithdrawStrategy.getApplicableScene()) || ApplicableSceneEnum.CONFIGURATION.name().equals(modifyWithdrawStrategy.getApplicableScene())){
            if (Objects.nonNull(modifyWithdrawStrategy.getReservedAmount())) {
                this.checkAmount(modifyWithdrawStrategy.getReservedAmount());
                brandWithdrawStrategyModule.setReservedAmount(modifyWithdrawStrategy.getReservedAmount().multiply(new BigDecimal("100")).intValue());
            }
            if (Objects.nonNull(modifyWithdrawStrategy.getMinWithdrawalAmount())) {
                this.checkAmount(modifyWithdrawStrategy.getMinWithdrawalAmount());
                brandWithdrawStrategyModule.setMinWithdrawalAmount(modifyWithdrawStrategy.getMinWithdrawalAmount().multiply(new BigDecimal("100")).intValue());
            }
        }
        brandWithdrawStrategyDomainService.modifyBrandWithdrawStrategy(brandWithdrawStrategyModule);
        return brandWithdrawStrategyModule;
    }

    public List<BrandWithdrawStrategyModule> getBrandWithdrawStrategyModulesByBrandId(String brandId) {
        List<BrandWithdrawStrategyModule> brandWithdrawStrategyModules = brandWithdrawStrategyDomainService.getBrandWithdrawStrategyModuleByBrandId(brandId);
        this.supplementBrandWithdrawStrategyModuleList(brandWithdrawStrategyModules);
        return brandWithdrawStrategyModules;
    }

    private void supplementBrandWithdrawStrategyModuleList(List<BrandWithdrawStrategyModule> brandWithdrawStrategyModules) {
        if (CollectionUtils.isEmpty(brandWithdrawStrategyModules)) {
            return;
        }
        List<Long> strategyIdList = brandWithdrawStrategyModules.stream().map(BrandWithdrawStrategyModule::getStrategyId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(strategyIdList)) {
            return;
        }
        MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
        merchantConditionModule.setStrategyIdList(strategyIdList);
        PageBrandMerchantModule pageBrandMerchantModule = brandDomainService.pageQueryBrandMerchantModule(merchantConditionModule);
        if (Objects.isNull(pageBrandMerchantModule.getBrandMerchantModuleList())) {
            pageBrandMerchantModule.setBrandMerchantModuleList(Lists.newArrayList());
        }
        Map<Long, List<BrandMerchantModule>> strategyMerchantMap = pageBrandMerchantModule.getBrandMerchantModuleList().stream().collect(Collectors.groupingBy(BrandMerchantModule::getStrategyId));
        brandWithdrawStrategyModules.forEach(brandWithdrawStrategyModule -> {
            List<BrandMerchantModule> brandMerchantModules = strategyMerchantMap.get(brandWithdrawStrategyModule.getStrategyId());
            if (CollectionUtils.isEmpty(brandMerchantModules)) {
                brandWithdrawStrategyModule.setMerchantNumber(0);
                return;
            }
            brandWithdrawStrategyModule.setMerchantNumber(brandMerchantModules.size());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteBrandWithdrawStrategyByStrategyIdList(List<Long> strategyIdList) {
        int i = brandWithdrawStrategyDomainService.deleteBrandWithdrawStrategyByStrategyIdList(strategyIdList);
        brandDomainService.deleteBrandMerchantWithdrawStrategy(strategyIdList);
        return i;
    }

    public List<BrandWithdrawStrategyModule> getWithdrawStrategyListByStrategyIdList(List<Long> strategyIdList) {
        return brandWithdrawStrategyDomainService.getWithdrawStrategyListByStrategyIdList(strategyIdList);
    }

    public BrandWithdrawStrategyModule getBrandWithdrawStrategyModuleByStrategyId(Long strategyId){
        return brandWithdrawStrategyDomainService.getBrandWithdrawStrategyModuleByStrategyId(strategyId);
    }

    public BrandMerchantByWdStrategyResponseDTO pageBrandMerchantWdStrategyList(String brandId, AppPageQueryMerchantsByWithdrawStrategyDTO queryMerchantsByWithdrawStrategyDto) {
        BrandMerchantByWdStrategyResponseDTO response = new BrandMerchantByWdStrategyResponseDTO();
        MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
        merchantConditionModule.setBrandIds(Lists.newArrayList(brandId));
        merchantConditionModule.setMerchantSn(queryMerchantsByWithdrawStrategyDto.getMerchantSn());
        merchantConditionModule.setOutMerchantNo(queryMerchantsByWithdrawStrategyDto.getOutMerchantNo());
        if (Objects.nonNull(queryMerchantsByWithdrawStrategyDto.getStrategyId())) {
            merchantConditionModule.setStrategyIdList(Lists.newArrayList(queryMerchantsByWithdrawStrategyDto.getStrategyId()));
        }
        merchantConditionModule.setMerchantName(queryMerchantsByWithdrawStrategyDto.getMerchantName());
        merchantConditionModule.setStrategyNotNull(1);
        if (Objects.isNull(queryMerchantsByWithdrawStrategyDto.getPage())) {
            queryMerchantsByWithdrawStrategyDto.setPage(apolloConfig.getDefaultPage());
        }
        if (Objects.isNull(queryMerchantsByWithdrawStrategyDto.getPageSize())) {
            queryMerchantsByWithdrawStrategyDto.setPageSize(apolloConfig.getDefaultPageSize());
        }
        merchantConditionModule.setPage(queryMerchantsByWithdrawStrategyDto.getPage());
        merchantConditionModule.setPageSize(queryMerchantsByWithdrawStrategyDto.getPageSize());
        PageBrandMerchantModule pageBrandMerchantModule = brandDomainService.pageQueryBrandMerchantModule(merchantConditionModule);
        List<BrandMerchantModule> brandMerchantModuleList = pageBrandMerchantModule.getBrandMerchantModuleList();
        if (pageBrandMerchantModule.getTotal() == 0) {
            response.setTotal(0);
            return response;
        }
        response.setTotal(pageBrandMerchantModule.getTotal());
        if (CollectionUtils.isEmpty(brandMerchantModuleList)) {
            response.setBrandMerchants(Lists.newArrayList());
            return response;
        }
        List<Long> strategyIds = brandMerchantModuleList.stream().map(BrandMerchantModule::getStrategyId).distinct().collect(Collectors.toList());
        List<BrandWithdrawStrategyModule> withdrawStrategyModules = brandWithdrawStrategyDomainService.getWithdrawStrategyListByStrategyIdList(strategyIds);
        if (CollectionUtils.isEmpty(withdrawStrategyModules)) {
            response.setTotal(0);
            return response;
        }
        Map<Long, BrandWithdrawStrategyModule> brandWithdrawStrategyModuleMap = withdrawStrategyModules.stream().collect(Collectors.toMap(BrandWithdrawStrategyModule::getStrategyId, Function.identity()));
        List<BrandMerchantByWdStrategyResponseDTO.BrandMerchantWdStrategyDTO> brandMerchants = Lists.newArrayList();
        brandMerchantModuleList.forEach(brandMerchantModule -> {
            BrandWithdrawStrategyModule brandWithdrawStrategyModule = brandWithdrawStrategyModuleMap.get(brandMerchantModule.getStrategyId());
            if (Objects.isNull(brandWithdrawStrategyModule)) {
                return;
            }
            BrandMerchantByWdStrategyResponseDTO.BrandMerchantWdStrategyDTO strategyDto = JSON.parseObject(JSON.toJSONString(brandWithdrawStrategyModule), BrandMerchantByWdStrategyResponseDTO.BrandMerchantWdStrategyDTO.class);
            strategyDto.setMerchantSn(brandMerchantModule.getMerchantSn());
            strategyDto.setMerchantName(brandMerchantModule.getMerchantName());
            strategyDto.setOutMerchantNo(brandMerchantModule.getOutMerchantNo());
            brandMerchants.add(strategyDto);
        });
        response.setBrandMerchants(brandMerchants);
        return response;
    }

    public PageBrandWithdrawStrategyResponseDTO pageGetBrandWithdrawStrategy(PageBrandWithdrawStrategyRequestDTO request) {
        PageBrandWithdrawStrategyResponseDTO response = new PageBrandWithdrawStrategyResponseDTO();
        if (Objects.isNull(request.getPage())) {
            request.setPage(apolloConfig.getDefaultPage());
        }
        if (Objects.isNull(request.getPageSize())) {
            request.setPageSize(apolloConfig.getDefaultPageSize());
        }
        PageBrandWithdrawStrategyModule pageBrandWithdrawStrategyModule = brandWithdrawStrategyDomainService.pageBrandWithdrawStrategy(
                request.getPage(),
                request.getPageSize(),
                request.getBrandId()
        );
        List<BrandWithdrawStrategyModule> modulesRecords = pageBrandWithdrawStrategyModule.getRecords();
        this.supplementBrandWithdrawStrategyModuleList(modulesRecords);
        if (CollectionUtils.isNotEmpty(modulesRecords)) {
            List<BrandWithdrawStrategyResponseDTO> records = JSON.parseArray(JSON.toJSONString(modulesRecords), BrandWithdrawStrategyResponseDTO.class);
            records.forEach(brandWithdrawStrategyResponse -> {
                if (Objects.nonNull(brandWithdrawStrategyResponse.getReservedAmount())) {
                    brandWithdrawStrategyResponse.setReservedAmount(brandWithdrawStrategyResponse.getReservedAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                }
                if (Objects.nonNull(brandWithdrawStrategyResponse.getMinWithdrawalAmount())) {
                    brandWithdrawStrategyResponse.setMinWithdrawalAmount(brandWithdrawStrategyResponse.getMinWithdrawalAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                }
            });
            response.setRecords(records);
            response.setTotal(pageBrandWithdrawStrategyModule.getTotal());
        }
        return response;
    }

    public BrandWithdrawStrategyModule getStrategyModuleByBrandIdAndMerchantId(String brandId, String merchantId) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        if (Objects.isNull(brandMerchantModule.getStrategyId()) || brandMerchantModule.getStrategyId() < 1) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_NOT_BIND_STRATEGY);
        }
        return brandWithdrawStrategyDomainService.getBrandWithdrawStrategyModuleByStrategyId(brandMerchantModule.getStrategyId());
    }
}
