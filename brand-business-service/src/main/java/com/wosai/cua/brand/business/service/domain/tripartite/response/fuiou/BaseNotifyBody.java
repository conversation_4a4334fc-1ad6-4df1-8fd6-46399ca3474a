package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou;

import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BaseNotifyBody<T> extends ResponseBodySignature{

    @Sign
    protected String notifyType;

    protected T data;

    protected String message;
}
