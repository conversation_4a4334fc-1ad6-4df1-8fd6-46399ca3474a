package com.wosai.cua.brand.business.service.aspect;

import com.wosai.cua.brand.business.service.context.OpenApiContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class OpenApiAfterReturningAspect {

    @AfterReturning(pointcut = "execution(* com.wosai.cua.brand.business.service.controller.rest.OpenApiController.*(..))", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        // 移除本地线程的内容避免出现内存溢出
        OpenApiContext.remove();
    }
}