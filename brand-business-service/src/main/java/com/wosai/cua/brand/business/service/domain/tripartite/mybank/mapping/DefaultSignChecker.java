package com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.MybankSignature;

public class DefaultSign<PERSON><PERSON><PERSON> implements SignChecker {

    private String mybankPublicKey;

    public String getMybankPublicKey() {
        return mybankPublicKey;
    }

    public void setMybankPublicKey(String mybankPublicKey) {
        this.mybankPublicKey = mybankPublicKey;
    }

    public DefaultSignChecker(String mybankPublicKey) {
        this.mybankPublicKey = mybankPublicKey;
    }

    @Override
    public boolean check(String xmlContent, String charset, String signType) throws MybankApiException {
        return MybankSignature.check(xmlContent, this.mybankPublicKey, charset, signType);
    }

    @Override
    public boolean webCheck(String content, String sign, String charset, String signType) throws MybankApiException {
        return MybankSignature.rsa256CheckContent(content, sign, this.mybankPublicKey, signType, charset);
    }
}