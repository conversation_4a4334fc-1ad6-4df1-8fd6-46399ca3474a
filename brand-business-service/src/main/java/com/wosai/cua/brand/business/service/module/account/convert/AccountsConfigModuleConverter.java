package com.wosai.cua.brand.business.service.module.account.convert;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.module.account.AccountsModule;
import com.wosai.cua.brand.business.service.module.account.citic.CiticAccountModule;
import com.wosai.cua.brand.business.service.module.account.mybank.MyBankAccountModule;
import com.wosai.cua.brand.business.service.module.account.pab.PabAccountModule;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class AccountsConfigModuleConverter {

    private AccountsConfigModuleConverter() {
        throw new IllegalStateException("Utility class");
    }

    // 配置模块与资管公司的映射关系
    // 使用Map将枚举与相应的类映射起来，以提高可维护性和扩展性
    private static final Map<FundManagementCompanyEnum, Class<? extends AccountsModule>> CONFIG_MODULE_MAP = Maps.newHashMap();

    static {
        CONFIG_MODULE_MAP.put(FundManagementCompanyEnum.MY_BANK, MyBankAccountModule.class);
        CONFIG_MODULE_MAP.put(FundManagementCompanyEnum.PAB, PabAccountModule.class);
    }

    public static AccountsModule convert(FundManagementCompanyEnum fundManagementCompanyEnum, String config) {
        // 检查输入参数的有效性
        if (fundManagementCompanyEnum == null || config == null || config.trim().isEmpty()) {
            log.warn("Invalid input parameters.");
            return null;
        }

        // 获取对应的ConfigModule类
        Class<? extends AccountsModule> configModuleClass = CONFIG_MODULE_MAP.get(fundManagementCompanyEnum);
        if (configModuleClass == null) {
            log.warn("Unsupported fund management company: {}", fundManagementCompanyEnum);
            return null;
        }

        try {
            // 使用try-catch捕获JSON解析异常
            return JSON.parseObject(config, configModuleClass);
        } catch (JSONException e) {
            // 异常处理，可以根据实际需求调整
            // 例如，记录日志、抛出自定义异常等
            log.error("JSON parse error: {}", e.getMessage());
            return null;
        }
    }

    public static AccountsModule convert(FundManagementCompanyEnum fundManagementCompanyEnum, AccountsDTO accounts) {
        switch (fundManagementCompanyEnum) {
            case MY_BANK:
                return MyBankAccountModule.convert(accounts.getMyBankAccounts());
            case PAB:
                return PabAccountModule.convert(accounts.getPabAccounts());
            case CITIC:
                return CiticAccountModule.convert(accounts.getCiticAccounts());
            default:
                return null;
        }
    }
}

