package com.wosai.cua.brand.business.service.domain.tripartite.request.enums;

public enum FunctionEnum {
    /**
     * 平安银行接口
     */
    PAB_FUNCTION,
    /**
     * 网商商户入驻
     */
    MY_BANK_MERCHANT_REGISTER,
    /**
     * 网商商户预注册
     */
    MY_BANK_MERCHANT_APPLET_PRE_REGISTER,
    /**
     * 网商商户上传照片
     */
    MY_BANK_UPLOAD_PHOTO,

    /**
     * 网商资金账号开通
     */
    MY_BANK_OPEN_FUNDS_ACCOUNT,

    /**
     * 网商商户解约
     */
    MY_BANK_RESCIND_AN_AGREEMENT,

    /**
     * 网商商户代扣协议查询
     */
    MY_BANK_MERCHANT_ARRANGEMENT_INFO_QUERY,

    /**
     * 网商入驻查询
     */
    MY_BANK_APPLET_REGISTER_QUERY,

    /**
     * 中信银行注册用户接口
     */
    CITIC_REGISTERED_USER,

    CITIC_UPDATE_USER,
    /**
     * 中信银行添加银行卡接口
     */
    CITIC_CREATE_BANK_CARD,
    /**
     * 中信银行设置默认银行卡接口
     */
    CITIC_SET_DEFAULT_BANK_CARD,
    /**
     * 中信银行删除商户接口
     */
    CITIC_DELETE_MERCHANT,
    /**
     * 中信银行查询商户接口
     */
    CITIC_QUERY_MERCHANT,
    /**
     * 富友开户
     */
    FUIOU_OPEN_ACCOUNT,

    FUIOU_INVALID_ALLOCATE_ACCOUNT,

    FUIOU_ACTIVE_ALLOCATE_ACCOUNT,

    FUIOU_MODIFY_ACCOUNT_IN_CARD,

    FUIOU_QUERY_ALLOCATE_ACCOUNT,

    FUIOU_CLOSE_ACCOUNT,
    FUIOU_CREATE_SUB_ACCOUNT,
    FUIOU_ADD_CONCENTRATE_RELATION,

    FUIOU_CANCEL_CONCENTRATE_RELATION_APPLY,
    FUIOU_CANCEL_CONCENTRATE_RELATION,

    FUIOU_QUERY_CONCENTRATE_RELATION,
    ;
}
