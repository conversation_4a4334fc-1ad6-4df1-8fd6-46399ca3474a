package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandSubTaskDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandSubTaskMapper extends BaseMapper<BrandSubTaskDO> {

    /**
     * 批量插入子任务
     *
     * @param brandSubTasks 子任务列表
     * @return 插入数量
     */
    int batchInsertSubTasks(@Param("list") List<BrandSubTaskDO> brandSubTasks);

    /**
     * 根据任务ID查询子任务列表
     *
     * @param taskId 任务ID
     * @return 子任务列表
     */
    List<BrandSubTaskDO> selectByTaskId(Long taskId);
}