/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.cua.brand.business.service.kafka.avro;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class BaseBrandMsg extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -8103609809076613428L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"BaseBrandMsg\",\"namespace\":\"com.wosai.cua.brand.business.service.kafka.avro\",\"fields\":[{\"name\":\"messageId\",\"type\":[\"string\",\"null\"],\"meta\":\"消息id\"},{\"name\":\"message\",\"type\":[\"string\",\"null\"],\"meta\":\"消息内容\"},{\"name\":\"messageType\",\"type\":[\"string\",\"null\"],\"meta\":\"消息类型\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<BaseBrandMsg> ENCODER =
      new BinaryMessageEncoder<BaseBrandMsg>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<BaseBrandMsg> DECODER =
      new BinaryMessageDecoder<BaseBrandMsg>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<BaseBrandMsg> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<BaseBrandMsg> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<BaseBrandMsg>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this BaseBrandMsg to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a BaseBrandMsg from a ByteBuffer. */
  public static BaseBrandMsg fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence messageId;
  @Deprecated public java.lang.CharSequence message;
  @Deprecated public java.lang.CharSequence messageType;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public BaseBrandMsg() {}

  /**
   * All-args constructor.
   * @param messageId The new value for messageId
   * @param message The new value for message
   * @param messageType The new value for messageType
   */
  public BaseBrandMsg(java.lang.CharSequence messageId, java.lang.CharSequence message, java.lang.CharSequence messageType) {
    this.messageId = messageId;
    this.message = message;
    this.messageType = messageType;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return messageId;
    case 1: return message;
    case 2: return messageType;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: messageId = (java.lang.CharSequence)value$; break;
    case 1: message = (java.lang.CharSequence)value$; break;
    case 2: messageType = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'messageId' field.
   * @return The value of the 'messageId' field.
   */
  public java.lang.CharSequence getMessageId() {
    return messageId;
  }

  /**
   * Sets the value of the 'messageId' field.
   * @param value the value to set.
   */
  public void setMessageId(java.lang.CharSequence value) {
    this.messageId = value;
  }

  /**
   * Gets the value of the 'message' field.
   * @return The value of the 'message' field.
   */
  public java.lang.CharSequence getMessage() {
    return message;
  }

  /**
   * Sets the value of the 'message' field.
   * @param value the value to set.
   */
  public void setMessage(java.lang.CharSequence value) {
    this.message = value;
  }

  /**
   * Gets the value of the 'messageType' field.
   * @return The value of the 'messageType' field.
   */
  public java.lang.CharSequence getMessageType() {
    return messageType;
  }

  /**
   * Sets the value of the 'messageType' field.
   * @param value the value to set.
   */
  public void setMessageType(java.lang.CharSequence value) {
    this.messageType = value;
  }

  /**
   * Creates a new BaseBrandMsg RecordBuilder.
   * @return A new BaseBrandMsg RecordBuilder
   */
  public static com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder newBuilder() {
    return new com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder();
  }

  /**
   * Creates a new BaseBrandMsg RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new BaseBrandMsg RecordBuilder
   */
  public static com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder newBuilder(com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder other) {
    return new com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder(other);
  }

  /**
   * Creates a new BaseBrandMsg RecordBuilder by copying an existing BaseBrandMsg instance.
   * @param other The existing instance to copy.
   * @return A new BaseBrandMsg RecordBuilder
   */
  public static com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder newBuilder(com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg other) {
    return new com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder(other);
  }

  /**
   * RecordBuilder for BaseBrandMsg instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<BaseBrandMsg>
    implements org.apache.avro.data.RecordBuilder<BaseBrandMsg> {

    private java.lang.CharSequence messageId;
    private java.lang.CharSequence message;
    private java.lang.CharSequence messageType;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.messageId)) {
        this.messageId = data().deepCopy(fields()[0].schema(), other.messageId);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.message)) {
        this.message = data().deepCopy(fields()[1].schema(), other.message);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.messageType)) {
        this.messageType = data().deepCopy(fields()[2].schema(), other.messageType);
        fieldSetFlags()[2] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing BaseBrandMsg instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.messageId)) {
        this.messageId = data().deepCopy(fields()[0].schema(), other.messageId);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.message)) {
        this.message = data().deepCopy(fields()[1].schema(), other.message);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.messageType)) {
        this.messageType = data().deepCopy(fields()[2].schema(), other.messageType);
        fieldSetFlags()[2] = true;
      }
    }

    /**
      * Gets the value of the 'messageId' field.
      * @return The value.
      */
    public java.lang.CharSequence getMessageId() {
      return messageId;
    }

    /**
      * Sets the value of the 'messageId' field.
      * @param value The value of 'messageId'.
      * @return This builder.
      */
    public com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder setMessageId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.messageId = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'messageId' field has been set.
      * @return True if the 'messageId' field has been set, false otherwise.
      */
    public boolean hasMessageId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'messageId' field.
      * @return This builder.
      */
    public com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder clearMessageId() {
      messageId = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'message' field.
      * @return The value.
      */
    public java.lang.CharSequence getMessage() {
      return message;
    }

    /**
      * Sets the value of the 'message' field.
      * @param value The value of 'message'.
      * @return This builder.
      */
    public com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder setMessage(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.message = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'message' field has been set.
      * @return True if the 'message' field has been set, false otherwise.
      */
    public boolean hasMessage() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'message' field.
      * @return This builder.
      */
    public com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder clearMessage() {
      message = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'messageType' field.
      * @return The value.
      */
    public java.lang.CharSequence getMessageType() {
      return messageType;
    }

    /**
      * Sets the value of the 'messageType' field.
      * @param value The value of 'messageType'.
      * @return This builder.
      */
    public com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder setMessageType(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.messageType = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'messageType' field has been set.
      * @return True if the 'messageType' field has been set, false otherwise.
      */
    public boolean hasMessageType() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'messageType' field.
      * @return This builder.
      */
    public com.wosai.cua.brand.business.service.kafka.avro.BaseBrandMsg.Builder clearMessageType() {
      messageType = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public BaseBrandMsg build() {
      try {
        BaseBrandMsg record = new BaseBrandMsg();
        record.messageId = fieldSetFlags()[0] ? this.messageId : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.message = fieldSetFlags()[1] ? this.message : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.messageType = fieldSetFlags()[2] ? this.messageType : (java.lang.CharSequence) defaultValue(fields()[2]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<BaseBrandMsg>
    WRITER$ = (org.apache.avro.io.DatumWriter<BaseBrandMsg>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<BaseBrandMsg>
    READER$ = (org.apache.avro.io.DatumReader<BaseBrandMsg>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
