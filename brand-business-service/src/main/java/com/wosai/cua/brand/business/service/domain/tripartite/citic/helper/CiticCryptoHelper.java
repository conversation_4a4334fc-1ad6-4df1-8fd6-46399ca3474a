package com.wosai.cua.brand.business.service.domain.tripartite.citic.helper;

import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.asn1.x500.X500Name;
import org.bouncycastle.asn1.x509.BasicConstraints;
import org.bouncycastle.asn1.x509.Extension;
import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.cert.CertIOException;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.cert.jcajce.JcaX509ExtensionUtils;
import org.bouncycastle.cert.jcajce.JcaX509v3CertificateBuilder;
import org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import org.bouncycastle.crypto.util.PublicKeyFactory;
import org.bouncycastle.crypto.util.SubjectPublicKeyInfoFactory;
import org.bouncycastle.operator.OperatorCreationException;
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder;
import org.bouncycastle.pkcs.PKCS10CertificationRequest;
import org.bouncycastle.pkcs.PKCS10CertificationRequestBuilder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.operator.ContentSigner;
import org.bouncycastle.pkcs.PKCSException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Security;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;
import java.util.StringTokenizer;

/**
 * <AUTHOR> Date: 2024/08/08 Time: 3:53 PM
 * 加密解密工具类
 */
@Slf4j
public class CiticCryptoHelper {

    static {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }

    }
    /**
     * 密钥算法
     */
    public static final String PROVIDER = "BC";
    /**
     * 密钥长度
     */
    public static final String SERIAL = "0";

    private CiticCryptoHelper() {
        throw new UnsupportedOperationException("CiticCryptoHelper is a utility class and cannot be instantiated");
    }
    public static PrivateKey decryptPrivateKey(byte[] encryptedPrivateKey, char[] password) throws RuntimeException {
        return decryptPrivateKey(new ByteArrayInputStream(encryptedPrivateKey), password);
    }

    public static PrivateKey decryptPrivateKey(InputStream isEncryptedPrivateKey, char[] password) throws RuntimeException {
        PrivateKey privateKey = null;
        try {
            // Read the length of the algorithm name
            int algorithmNameLength = isEncryptedPrivateKey.read();
            if (algorithmNameLength == -1) {
                throw new RuntimeException("Could not read the algorithm name length");
            }
            log.info("Algorithm name length: " + algorithmNameLength);

            // Read the algorithm name
            byte[] algorithmNameBytes = new byte[algorithmNameLength];
            if (isEncryptedPrivateKey.read(algorithmNameBytes) != algorithmNameLength) {
                throw new RuntimeException("Could not read the full algorithm name");
            }
            String algorithmName = new String(algorithmNameBytes);
            log.info("Algorithm name: " + algorithmName);

            // Read the salt
            byte[] salt = new byte[8];
            if (isEncryptedPrivateKey.read(salt) != 8) {
                throw new RuntimeException("Could not read the salt");
            }
            log.info("Salt: " + Arrays.toString(salt));

            // Read the remaining encrypted private key data
            byte[] remainingCipherData = readAllBytes(isEncryptedPrivateKey);
            log.info("Remaining cipher data length: " + remainingCipherData.length);

            // Choose the PBE algorithm based on password length
            String pbeAlg = password.length < 8 ? "PBEWithSHAAndTwofish-CBC" : "PBEWithMD5AndDES";
            log.info("Chosen PBE algorithm: {}", pbeAlg);

            // Generate the secret key
            PBEKeySpec keySpec = new PBEKeySpec(password);
            Security.addProvider(new BouncyCastleProvider());
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(pbeAlg);
            SecretKey key = keyFactory.generateSecret(keySpec);
            log.info("Secret key generated");

            // Initialize the cipher for decryption
            PBEParameterSpec paramSpec = new PBEParameterSpec(salt, 1000);
            Cipher cipher = Cipher.getInstance(pbeAlg);
            cipher.init(Cipher.DECRYPT_MODE, key, paramSpec);
            log.info("Cipher initialized for decryption");

            // Decrypt the private key bytes
            byte[] keyBytes = cipher.doFinal(remainingCipherData);
            log.info("Decrypted key bytes length: {}", Optional.of(keyBytes.length));

            // Generate the PrivateKey object
            privateKey = generatePrivateKey(keyBytes, algorithmName);
            log.info("Private key generated");

        } catch (Exception e) {
            log.error("decryptPrivateKey error >>>异常栈>>>", e);
        } finally {
            try {
                if (isEncryptedPrivateKey != null) {
                    isEncryptedPrivateKey.close();
                }
            } catch (Exception e) {
                log.error("decryptPrivateKey error >>>异常栈>>>", e);
            }
        }
        return privateKey;
    }

    private static PrivateKey generatePrivateKey(byte[] keyBytes, String algorithm) throws Exception {
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        return keyFactory.generatePrivate(keySpec);
    }

    public static X509Certificate generateX509Certificate(byte[] derX509Crt) throws Exception {
        if (derX509Crt.length == 0) {
            throw new Exception("The provided byte array is empty.");
        }
        X509Certificate x509Crt;
        try (ByteArrayInputStream bais = new ByteArrayInputStream(derX509Crt)) {
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            x509Crt = (X509Certificate) cf.generateCertificate(bais);
        }
        return x509Crt;

    }

    public static KeyStore generateKeyStore(String password, String keyStorePath) {
        try (FileInputStream is = new FileInputStream(keyStorePath)) {
            KeyStore ks = KeyStore.getInstance("JKS");
            ks.load(is, password.toCharArray());
            return ks;
        } catch (Exception ex) {
            log.error("generateKeyStore异常>>", ex);
        }
        return null;
    }

    private static byte[] readAllBytes(InputStream inputStream) {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();

        try {
            byte[] data = new byte[1024];
            int nRead;
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
        } catch (IOException e) {
            log.warn("读取流异常",e);
        }

        return buffer.toByteArray();

    }

    public static KeyPair generateKeyPair(String algorithm, String length, String provider) throws NoSuchProviderException, NoSuchAlgorithmException {
        KeyPairGenerator keyGen;
        if (provider != null) {
            keyGen = KeyPairGenerator.getInstance(algorithm, provider);
        } else {
            keyGen = KeyPairGenerator.getInstance(algorithm);
        }

        keyGen.initialize(Integer.parseInt(length));
        return keyGen.generateKeyPair();
    }

    public static PKCS10CertificationRequest generatePKCS10CertificateRequest(KeyPair kp, String userdn, String algorithm, String provider)
            throws OperatorCreationException, PKCSException {
        // 使用 X500Name 替代 X509Name
        X500Name subject = new X500Name(userdn);

        // 获取 PublicKey
        PublicKey publicKey = kp.getPublic();

        try {
            // 将 PublicKey 转换为字节数组
            byte[] publicKeyBytes = publicKey.getEncoded();
            // 使用字节数组创建 AsymmetricKeyParameter
            AsymmetricKeyParameter asymmetricKeyParameter = PublicKeyFactory.createKey(publicKeyBytes);

            // 将 AsymmetricKeyParameter 转换为 SubjectPublicKeyInfo
            SubjectPublicKeyInfo subjectPublicKeyInfo = SubjectPublicKeyInfoFactory.createSubjectPublicKeyInfo(asymmetricKeyParameter);

            // 创建 PKCS10CertificationRequestBuilder
            PKCS10CertificationRequestBuilder builder = new PKCS10CertificationRequestBuilder(subject, subjectPublicKeyInfo);

            // 创建 ContentSigner
            ContentSigner signer;
            if (provider != null) {
                signer = new JcaContentSignerBuilder(algorithm).setProvider(provider).build(kp.getPrivate());
            } else {
                signer = new JcaContentSignerBuilder(algorithm).build(kp.getPrivate());
            }

            // 生成 PKCS10CertificationRequest
            return builder.build(signer);
        } catch (OperatorCreationException e) {
            throw e;
        } catch (Exception e) {
            throw new PKCSException("Unexpected error while generating PKCS#10 request", e);
        }
    }

    public static X509Certificate generateSelfSignedCertificate(KeyPair kp, String subject, byte[] serial, String algorithm, String validity, String provider) throws CertificateException, OperatorCreationException, CertIOException, NoSuchAlgorithmException, CertIOException {
        // 生成证书序列号
        BigInteger serialNumber = new BigInteger(serial);

        // 处理主题信息
        X500Name subjectName = createX500Name(subject);

        // 定义证书有效期
        Date notBefore = new Date(System.currentTimeMillis());
        Date notAfter = new Date(System.currentTimeMillis() + 86400000L * Long.parseLong(validity));

        // 创建证书构建器
        JcaX509v3CertificateBuilder certBuilder = new JcaX509v3CertificateBuilder(
                subjectName,
                serialNumber,
                notBefore,
                notAfter,
                subjectName,
                kp.getPublic()
        );

        // 添加扩展
        JcaX509ExtensionUtils extUtils = new JcaX509ExtensionUtils();
        certBuilder.addExtension(Extension.subjectKeyIdentifier, false, extUtils.createSubjectKeyIdentifier(kp.getPublic()));
        certBuilder.addExtension(Extension.authorityKeyIdentifier, false, extUtils.createAuthorityKeyIdentifier(kp.getPublic()));
        certBuilder.addExtension(Extension.basicConstraints, false, new BasicConstraints(true));

        // 创建签名者
        ContentSigner signer;
        if (provider != null) {
            signer = new JcaContentSignerBuilder(algorithm).setProvider(provider).build(kp.getPrivate());
        } else {
            signer = new JcaContentSignerBuilder(algorithm).build(kp.getPrivate());
        }

        // 生成证书
        X509CertificateHolder certHolder = certBuilder.build(signer);
        X509Certificate cert = new JcaX509CertificateConverter().getCertificate(certHolder);

        // 验证证书
        try {
            cert.checkValidity(new Date());
            cert.verify(kp.getPublic());
        } catch (Exception e) {
            throw new CertificateException("Certificate verification failed", e);
        }

        // 设置友好名称
        // 注意：这里没有直接对应的 PKCS12BagAttributeCarrier 方式，需要根据实际需求处理
        // 这里仅作为示例，不做实际的 PKCS12 相关操作

        return cert;
    }

    private static X500Name createX500Name(String subject) {
        StringBuilder sb = new StringBuilder();
        StringTokenizer st = new StringTokenizer(subject, ",");
        if (st.hasMoreElements()) {
            sb.append(st.nextElement());
        }
        while (st.hasMoreElements()) {
            sb.insert(0, ",");
            sb.insert(0, st.nextElement());
        }
        return new X500Name(sb.toString());
    }

    /**
     * 生成私钥的加密数据
     * @param privateKey
     * @param password
     * @param iterations
     * @return
     */
    public static byte[] encryptPrivateKey(PrivateKey privateKey, char[] password, int iterations) {
        byte[] encryptedData;
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 使用 SecureRandom 生成盐值
            SecureRandom secureRandom = new SecureRandom();
            byte[] salt = new byte[8];
            secureRandom.nextBytes(salt);

            // 创建 PBE 密钥规范
            PBEKeySpec keySpec = new PBEKeySpec(password);
            String pbeAlg = password.length < 8 ? "PBEWithSHAAndTwofish-CBC" : "PBEWithMD5AndDES";
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(pbeAlg);
            SecretKey key = keyFactory.generateSecret(keySpec);

            // 创建 PBE 参数规范
            PBEParameterSpec paramSpec = new PBEParameterSpec(salt, iterations);

            // 初始化 Cipher 进行加密
            Cipher cipher = Cipher.getInstance(pbeAlg);
            cipher.init(Cipher.ENCRYPT_MODE, key, paramSpec);

            // 加密私钥
            byte[] encryptedPrivateKey = cipher.doFinal(privateKey.getEncoded());

            // 获取加密算法信息
            byte[] algorithm = privateKey.getAlgorithm().getBytes();

            // 将算法长度、算法信息、盐值和加密后的私钥写入 ByteArrayOutputStream
            baos.write((byte) algorithm.length);
            baos.write(algorithm);
            baos.write(salt);
            baos.write(encryptedPrivateKey);

            // 获取最终的加密数据
            encryptedData = baos.toByteArray();
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | NoSuchPaddingException | InvalidKeyException |
                 InvalidAlgorithmParameterException | BadPaddingException | IOException | IllegalBlockSizeException e) {
            log.warn("使用基于口令的加密方法加密私钥失败", e);
            throw new BrandBusinessException("使用基于口令的加密方法加密私钥失败");
        }
        return encryptedData;
    }

    public static CiticKeyValue keyGenerator(String keyAlgorithm, String length, String subject,String signatureAlgorithm,String validity,String password) throws NoSuchAlgorithmException, NoSuchProviderException, CertificateException, OperatorCreationException, CertIOException {
        KeyPair keyPair = CiticCryptoHelper.generateKeyPair(keyAlgorithm, length, PROVIDER);
        X509Certificate certificate = CiticCryptoHelper.generateSelfSignedCertificate(keyPair, subject, SERIAL.getBytes(), signatureAlgorithm, validity, PROVIDER);
        byte[] bytesPrivateKey = CiticCryptoHelper.encryptPrivateKey(keyPair.getPrivate(), password.toCharArray(),1000);
        return CiticKeyValue.builder().publicKey(new String(Base64.encodeBase64(certificate.getEncoded()))).privateKey(new String(Base64.encodeBase64(bytesPrivateKey))).build();
    }

    @Data
    @Builder
    public static class CiticKeyValue {
        private String publicKey;
        private String privateKey;
    }

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();

    public static String generateRandomPassword(int length) {
        if (length < 1) {
            throw new IllegalArgumentException("Password length must be at least 1");
        }

        StringBuilder password = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = RANDOM.nextInt(CHARACTERS.length());
            password.append(CHARACTERS.charAt(index));
        }
        return password.toString();
    }
}