package com.wosai.cua.brand.business.service.config.redis;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import redis.clients.jedis.JedisPoolConfig;

@Configuration
public class RedisConfig {

    @Value("${spring.redis-cluster.host}")
    private String redisCoreTotalHost;

    @Value("${spring.redis-cluster.port}")
    private Integer redisCoreTotalPort;

    @Value("${spring.redis-cluster.password}")
    private String redisCoreTotalPassword;

    @Value("${spring.redis-cluster.pool.max-active}")
    private int redisCoreTotalMaxActive;

    @Value("${spring.redis-cluster.pool.max-idle}")
    private int redisCoreTotalMaxIdle;

    @Value("${spring.redis-cluster.pool.min-idle}")
    private int redisCoreTotalMinIdle;

    @Value("${spring.redis-cluster.pool.max-wait}")
    private int redisCoreTotalMaxWait;

    @Value("${spring.redis-cluster.database}")
    private int redisCoreTotalDatabase;

    @Bean(name = "redisClusterTemplate")
    public StringRedisTemplate redisCoreTotalTemplate(@Qualifier("redisClusterConnectionFactory") RedisConnectionFactory redisClusterConnectionFactory) {
        StringRedisTemplate redisTemplate = new StringRedisTemplate();
        redisTemplate.setConnectionFactory(redisClusterConnectionFactory);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean(name = "redisClusterConnectionFactory")
    @ConfigurationProperties(prefix = "spring.redis-cluster")
    public RedisConnectionFactory redisCoreTotalConnectionFactory() {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(redisCoreTotalMaxActive);
        config.setMaxIdle(redisCoreTotalMaxIdle);
        config.setMinIdle(redisCoreTotalMinIdle);
        config.setMaxWaitMillis(redisCoreTotalMaxWait);
        // 关闭 testOn[Borrow|Return]，防止产生额外的PING。
        config.setTestOnBorrow(false);
        config.setTestOnReturn(false);
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setDatabase(redisCoreTotalDatabase);
        jedisConnectionFactory.setHostName(redisCoreTotalHost);
        jedisConnectionFactory.setPort(redisCoreTotalPort);
        jedisConnectionFactory.setPassword(redisCoreTotalPassword);
        jedisConnectionFactory.setPoolConfig(config);
        jedisConnectionFactory.afterPropertiesSet();
        return jedisConnectionFactory;
    }
}
