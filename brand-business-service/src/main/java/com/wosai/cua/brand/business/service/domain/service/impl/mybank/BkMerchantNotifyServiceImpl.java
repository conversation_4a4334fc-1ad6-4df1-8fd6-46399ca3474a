package com.wosai.cua.brand.business.service.domain.service.impl.mybank;


import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.EnrollChannelEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.domain.dao.BrandCallbackRecordsMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantCreationRecordMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandCallbackRecordsDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantCreationRecordDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantsAnalyzeService;
import com.wosai.cua.brand.business.service.domain.service.MyBankNotifyService;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.parse.xml.XmlConverter;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantNotifyRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantprodMerchNotifyModel;
import com.wosai.cua.brand.business.service.enums.third.MyBankAccountTypeEnum;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantEnrollEvent;
import com.wosai.cua.brand.business.service.event.model.MerchantOpenStatusEvent;
import com.wosai.cua.brand.business.service.event.model.MyBankOpenAccountEvent;
import com.wosai.cua.brand.business.service.event.publisher.DefaultEventPublisher;
import com.wosai.cua.brand.business.service.helper.RedisLockHelper;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import com.wosai.cua.brand.business.service.module.merchant.BrandCallbackRecordModule;
import com.wosai.cua.brand.business.service.mybank.manage.CommonRequestHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 商户入驻结果通知接口通知实现
 * ant.mybank.merchantprod.merch.notify
 **/
@Service(value = "bkMerchantprodMerchNotifyService")
@Slf4j
public class BkMerchantNotifyServiceImpl implements MyBankNotifyService {

    /**
     * 预入驻结果
     */
    private static final String PRE_REGISTER_ASYNC_NOTIFY = "PRE_REGISTER_ASYNC_NOTIFY";

    /**
     * 预入驻激活结果
     */
    private static final String PRE_REGISTER_ACTIVATE_ASYNC_NOTIFY = "PRE_REGISTER_ACTIVATE_ASYNC_NOTIFY";

    private final CommonRequestHandle commonRequestHandle;

    private final BrandMerchantCreationRecordMapper brandMerchantCreationRecordMapper;

    private final BrandMerchantMapper brandMerchantMapper;

    private final BrandConfigDOMapper brandConfigMapper;

    private final DefaultEventPublisher defaultEventPublisher;

    private final RedisLockHelper redisLockHelper;

    private final BrandCallbackRecordsMapper brandCallbackRecordsMapper;

    private final BrandMapper brandMapper;

    @Autowired
    public BkMerchantNotifyServiceImpl(CommonRequestHandle commonRequestHandle, BrandMerchantCreationRecordMapper brandMerchantCreationRecordMapper, BrandMerchantMapper brandMerchantMapper, BrandConfigDOMapper brandConfigMapper, DefaultEventPublisher defaultEventPublisher, RedisLockHelper redisLockHelper, BrandCallbackRecordsMapper brandCallbackRecordsMapper, BrandMapper brandMapper) {
        this.commonRequestHandle = commonRequestHandle;
        this.brandMerchantCreationRecordMapper = brandMerchantCreationRecordMapper;
        this.brandMerchantMapper = brandMerchantMapper;
        this.brandConfigMapper = brandConfigMapper;
        this.defaultEventPublisher = defaultEventPublisher;
        this.redisLockHelper = redisLockHelper;
        this.brandCallbackRecordsMapper = brandCallbackRecordsMapper;
        this.brandMapper = brandMapper;
    }

    @Override
    public String notifyHandle(String context, String function) throws Exception {
        //通知内容转换
        MerchantNotifyRequest merchantNotifyRequest =
                XmlConverter.getInstance().toResponse(context, MerchantNotifyRequest.class);
        RequestHead head = merchantNotifyRequest.getMerchantprodMerchNotify().getRequestHead();
        //通知内容保存
        boolean flag = saveNotify(merchantNotifyRequest.getMerchantprodMerchNotify().getMerchantprodMerchNotifyModel(), null, function, StringUtils.isEmpty(head.getAppId()) ? head.getAppid() : head.getAppId());
        //响应结果根据执行结果统一处理
        return commonRequestHandle.getSignResult(flag, head);
    }

    @Override
    public void notifyHandle(BrandCallbackRecordModule baseNotifyModule) {
        if (Objects.isNull(baseNotifyModule)) {
            log.error("通知内容对象为空，无法处理");
            return;
        }
        saveNotify(JSON.parseObject(baseNotifyModule.getContent(), MerchantprodMerchNotifyModel.class), baseNotifyModule, baseNotifyModule.getFunction(), baseNotifyModule.getAppId());
    }

    private boolean saveNotify(MerchantprodMerchNotifyModel model, BrandCallbackRecordModule baseNotifyModule, String function, String appId) {
        log.info("商户入驻结果通知内容对象为：{}", JSON.toJSONString(model));
        String lockKey = String.format(RedisLockHelper.MY_BANK_NOTIFY_MERCHANT_LOCK_KEY, model.getMerchantId());
        String lockValue = model.getMerchantId();

        // 尝试获取锁，增加超时时间以避免死锁
        if (!redisLockHelper.tryLock(lockKey, lockValue, 10)) { // 超时时间为10秒
            log.error("Failed to acquire lock for model: {}", JSON.toJSONString(model));
            return false; // 无法获取锁则直接返回失败
        }
        try {
            if (StringUtils.isBlank(model.getAsyncNotifyType())) {
                MyBankConfigModule myBankConfigModule = this.getMyBankConfigModuleByAppId(appId);
                if (Objects.isNull(myBankConfigModule)) {
                    log.error("未找到品牌配置信息，无法处理创建结果通知，通知内容：{}", JSON.toJSONString(model));
                    this.handleRecord(model, baseNotifyModule, function, appId, 0);
                    return true;
                }
                BrandDO brandDO = brandMapper.selectOne(new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getBrandId, myBankConfigModule.getBrandId()).eq(BrandDO::getDeleted, 0));
                if (Objects.isNull(brandDO)) {
                    log.error("未找到品牌信息，无法处理创建结果通知，通知内容：{}", JSON.toJSONString(model));
                    this.handleRecord(model, baseNotifyModule, function, appId, 0);
                    return true;
                }
                BrandMerchantDO brandMerchant = brandMerchantMapper.selectOne(new LambdaQueryWrapper<BrandMerchantDO>().eq(BrandMerchantDO::getBrandId, myBankConfigModule.getBrandId()).eq(BrandMerchantDO::getMerchantSn, model.getOutMerchantId()).eq(BrandMerchantDO::getDeleted, 0));
                if (Objects.isNull(brandMerchant)) {
                    log.error("未找到商户信息，无法处理创建结果通知，通知内容：{}", JSON.toJSONString(model));
                    this.handleRecord(model, baseNotifyModule, function, appId, 0);
                    return true;
                }
                brandMerchant.setMemberId(model.getMerchantId());
                brandMerchant.setSubAccountNo(model.getMerchantId());
                brandMerchant.setUpdatedTime(null);
                if (MerchantprodMerchNotifyModel.REGISTER_STATUS_FAIL.equals(model.getRegisterStatus())) {
                    brandMerchant.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    brandMerchant.setAccountOpenFailureReason(model.getFailReason());
                    this.handleRecord(model, baseNotifyModule, function, appId, 0);
                } else {
                    brandMerchant.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
                    brandMerchant.setAccountOpenFailureReason("");
                    this.handleRecord(model, baseNotifyModule, function, appId, 1);
                    // 事务提交后执行的业务代码
                    pulishMessage(model, myBankConfigModule, brandDO, brandMerchant);
                }
                brandMerchantMapper.updateById(brandMerchant);
                return true;
            }
            if (PRE_REGISTER_ASYNC_NOTIFY.equals(model.getAsyncNotifyType())) {
                LambdaQueryWrapper<BrandMerchantCreationRecordDO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(BrandMerchantCreationRecordDO::getRecordId, model.getOutTradeNo());
                BrandMerchantCreationRecordDO brandMerchantCreationRecord = brandMerchantCreationRecordMapper.selectOne(wrapper);
                if (Objects.isNull(brandMerchantCreationRecord)) {
                    log.error("未找到预创建记录，无法处理预创建结果通知，通知内容：{}", JSON.toJSONString(model));
                    this.handleRecord(model, baseNotifyModule, function, appId, 0);
                    return true;
                }
                String brandId = brandMerchantCreationRecord.getBrandId();
                String merchantSn = model.getOutMerchantId();
                LambdaQueryWrapper<BrandDO> brandLambdaQueryWrapper = new LambdaQueryWrapper<>();
                brandLambdaQueryWrapper.eq(BrandDO::getBrandId, brandId);
                brandLambdaQueryWrapper.eq(BrandDO::getDeleted, 0);
                BrandDO brandDO = brandMapper.selectOne(brandLambdaQueryWrapper);
                LambdaUpdateWrapper<BrandMerchantDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(BrandMerchantDO::getBrandId, brandId);
                updateWrapper.eq(BrandMerchantDO::getMerchantSn, merchantSn);
                updateWrapper.set(BrandMerchantDO::getMemberId, model.getMerchantId());
                updateWrapper.set(BrandMerchantDO::getSubAccountNo, model.getMerchantId());
                updateWrapper.set(BrandMerchantDO::getAccountOpenStatus, BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus());
                if (MerchantprodMerchNotifyModel.REGISTER_STATUS_FAIL.equals(model.getRegisterStatus())) {
                    updateWrapper.set(BrandMerchantDO::getAccountOpenStatus, BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    updateWrapper.set(BrandMerchantDO::getAccountOpenFailureReason, model.getFailReason());
                    brandMerchantCreationRecord.setStatus(2);
                    brandMerchantCreationRecord.setResult(JSON.toJSONString(model));
                    brandMerchantCreationRecord.setUpdatedTime(null);
                    brandMerchantCreationRecordMapper.updateById(brandMerchantCreationRecord);
                    int update = brandMerchantMapper.update(updateWrapper);
                    if (update > 0) {
                        this.handleRecord(model, baseNotifyModule, function, appId, 1);
                    } else {
                        this.handleRecord(model, baseNotifyModule, function, appId, 0);
                    }
                    return false;
                }
                updateWrapper.set(BrandMerchantDO::getAccountOpenFailureReason, "");
                brandMerchantMapper.update(updateWrapper);
                brandMerchantCreationRecord.setStatus(1);
                brandMerchantCreationRecord.setResult(JSON.toJSONString(model));
                brandMerchantCreationRecord.setUpdatedTime(null);
                brandMerchantCreationRecordMapper.updateById(brandMerchantCreationRecord);
                this.handleRecord(model, baseNotifyModule, function, appId, 1);
                LambdaQueryWrapper<BrandMerchantDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BrandMerchantDO::getBrandId, brandId);
                queryWrapper.eq(BrandMerchantDO::getMerchantSn, model.getOutMerchantId());
                BrandMerchantDO brandMerchant = brandMerchantMapper.selectOne(queryWrapper);
                if (Objects.nonNull(brandMerchant)) {
                    defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchant.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandDO, brandMerchant)).build());
                }
            }
            if (PRE_REGISTER_ACTIVATE_ASYNC_NOTIFY.equals(model.getAsyncNotifyType())) {
                LambdaQueryWrapper<BrandMerchantDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BrandMerchantDO::getMerchantSn, model.getOutMerchantId());
                queryWrapper.eq(BrandMerchantDO::getDeleted, 0);
                List<BrandMerchantDO> brandMerchants = brandMerchantMapper.selectList(queryWrapper);
                if (CollectionUtils.isEmpty(brandMerchants)) {
                    log.error("未找到商户记录，无法处理商户激活结果通知，通知内容：{}", JSON.toJSONString(model));
                    this.handleRecord(model, baseNotifyModule, function, appId, 0);
                    return true;
                }
                BrandMerchantDO brandMerchant = brandMerchants.get(0);
                LambdaQueryWrapper<BrandDO> brandLambdaQueryWrapper = new LambdaQueryWrapper<>();
                brandLambdaQueryWrapper.eq(BrandDO::getBrandId, brandMerchant.getBrandId());
                brandLambdaQueryWrapper.eq(BrandDO::getDeleted, 0);
                BrandDO brandDO = brandMapper.selectOne(brandLambdaQueryWrapper);
                if (Objects.isNull(brandDO)) {
                    log.error("品牌不存在。brandId = {}", brandMerchant.getBrandId());
                    this.handleRecord(model, baseNotifyModule, function, appId, 0);
                    return true;
                }
                brandMerchant.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
                MyBankConfigModule myBankConfigModule = this.getMyBankConfigModule(brandMerchant.getBrandId());
                if (Objects.isNull(myBankConfigModule)) {
                    log.error("未获取到品牌配置信息");
                    this.handleRecord(model, baseNotifyModule, function, appId, 0);
                    return false;
                }
                // 事务提交后执行的业务代码
                pulishMessage(model, myBankConfigModule, brandDO, brandMerchant);
                brandMerchantMapper.updateById(brandMerchant);
                this.handleRecord(model, baseNotifyModule, function, appId, 1);
            }
        } finally {
            redisLockHelper.unlock(lockKey, lockValue); // 确保锁被释放
        }
        return true;
    }

    private void pulishMessage(MerchantprodMerchNotifyModel model, MyBankConfigModule myBankConfigModule, BrandDO brandDO, BrandMerchantDO brandMerchant) {
        defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandMerchant.getBrandId()).merchantSn(brandMerchant.getMerchantSn()).enrollChannelEnum(EnrollChannelEnum.MY_BANK).build());
        if (MerchantprodMerchNotifyModel.REGISTER_STATUS_SUCCESS.equals(model.getRegisterStatus()) && Boolean.TRUE.equals(this.getNeedCreateTopUpAccountConfig(myBankConfigModule))) {
            log.info("商户开通成功，创建充值账户");
            defaultEventPublisher.publish(
                    MyBankOpenAccountEvent.builder()
                            .brandId(brandMerchant.getBrandId())
                            .merchantSn(brandMerchant.getMerchantSn())
                            .myBankMerchantId(model.getMerchantId())
                            .myBankAccountType(MyBankAccountTypeEnum.TRADE_DEPOSIT.getCode())
                            .myBankConfigModule(myBankConfigModule)
                            .build()
            );
            defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchant.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandDO, brandMerchant)).build());
        }
    }

    private void handleRecord(MerchantprodMerchNotifyModel model, BrandCallbackRecordModule baseNotifyModule, String function, String appId, int result) {
        if (Objects.nonNull(baseNotifyModule)) {
            baseNotifyModule.setRetryTimes(baseNotifyModule.getRetryTimes() + 1);
            baseNotifyModule.setMtime(new Date());
            baseNotifyModule.setResult(result);
            brandCallbackRecordsMapper.updateById(BrandCallbackRecordModule.convert2Do(baseNotifyModule));
        } else {
            LambdaQueryWrapper<BrandCallbackRecordsDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BrandCallbackRecordsDO::getTradeNo, model.getOutTradeNo());
            wrapper.eq(BrandCallbackRecordsDO::getMerchantSn, model.getOutMerchantId());
            wrapper.eq(BrandCallbackRecordsDO::getFunction, function);
            wrapper.eq(BrandCallbackRecordsDO::getFundManagementCompanyCode, FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
            wrapper.eq(BrandCallbackRecordsDO::getAppId, appId);
            BrandCallbackRecordsDO brandCallbackRecordsDO = brandCallbackRecordsMapper.selectOne(wrapper);
            if (Objects.nonNull(brandCallbackRecordsDO)) {
                brandCallbackRecordsDO.setRetryTimes(brandCallbackRecordsDO.getRetryTimes() + 1);
                brandCallbackRecordsDO.setMtime(new Date());
                brandCallbackRecordsDO.setResult(result);
                brandCallbackRecordsMapper.updateById(brandCallbackRecordsDO);
                return;
            }
            brandCallbackRecordsDO = new BrandCallbackRecordsDO();
            brandCallbackRecordsDO.setFunction(function);
            brandCallbackRecordsDO.setContent(JSON.toJSONString(model));
            brandCallbackRecordsDO.setMerchantSn(model.getOutMerchantId());
            brandCallbackRecordsDO.setTradeNo(model.getOutTradeNo());
            brandCallbackRecordsDO.setFundManagementCompanyCode(FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
            brandCallbackRecordsDO.setAppId(appId);
            brandCallbackRecordsDO.setResult(result);
            brandCallbackRecordsMapper.insert(brandCallbackRecordsDO);
        }
    }

    private MyBankConfigModule getMyBankConfigModule(String brandId) {
        LambdaQueryWrapper<BrandConfigDO> wrapper = BrandMerchantsAnalyzeService.getBrandIdQueryWrapper(brandId);
        BrandConfigDO brandConfig = brandConfigMapper.selectOne(wrapper);
        if (Objects.isNull(brandConfig)) {
            return null;
        }
        return JSON.parseObject(brandConfig.getConfig(), MyBankConfigModule.class);
    }

    private MyBankConfigModule getMyBankConfigModuleByAppId(String appId) {
        List<BrandConfigDO> brandConfigList = brandConfigMapper.selectList(new LambdaQueryWrapper<BrandConfigDO>().eq(BrandConfigDO::getChannelId, appId));
        if (CollectionUtils.isEmpty(brandConfigList)) {
            return null;
        }
        BrandConfigDO brandConfigDO = brandConfigList.get(0);
        MyBankConfigModule configModule = JSON.parseObject(brandConfigDO.getConfig(), MyBankConfigModule.class);
        if (Objects.isNull(configModule)) {
            return null;
        }
        configModule.setBrandId(brandConfigDO.getBrandId());
        return configModule;
    }

    private Boolean getNeedCreateTopUpAccountConfig(MyBankConfigModule configModule) {
        log.info("配置信息为：{}", JSON.toJSONString(configModule));
        return Objects.nonNull(configModule.getOpenTopUpAccount()) && configModule.getOpenTopUpAccount();
    }
}
