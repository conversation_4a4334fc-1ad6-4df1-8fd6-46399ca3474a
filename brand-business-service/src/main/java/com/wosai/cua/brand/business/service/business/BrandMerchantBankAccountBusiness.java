package com.wosai.cua.brand.business.service.business;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardAdvanceDTO;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBankCardDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantBankCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantBankCardsDTO;
import com.wosai.cua.brand.business.api.enums.BankAccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BankCardActivateTypeEnum;
import com.wosai.cua.brand.business.api.enums.BankCardCheckModeEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.BusinessLicenseEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.https.enums.CardStatusEnum;
import com.wosai.cua.brand.business.service.domain.service.BankCardDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.MerchantDomainService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.BindBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.SetDefaultBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.card.ModifyAccountInCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.card.ModifyAccountInCardRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.ChangeDefaultBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.CreateBankCardAdvanceRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.CreateBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.QueryBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.UnbindBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.BindBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.SetDefaultBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.card.ModifyAccountInCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.CreateBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.QueryBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.VfinanceBaseResponse;
import com.wosai.cua.brand.business.service.enums.VFinanceResponseCodeEnum;
import com.wosai.cua.brand.business.service.enums.third.AgencyClientFlageEnum;
import com.wosai.cua.brand.business.service.enums.third.BankTypeEnum;
import com.wosai.cua.brand.business.service.enums.third.CardAttributeEnum;
import com.wosai.cua.brand.business.service.enums.third.DefaultEnum;
import com.wosai.cua.brand.business.service.enums.third.IndivBusinessFlagEnum;
import com.wosai.cua.brand.business.service.enums.third.MemberGlobalTypeEnum;
import com.wosai.cua.brand.business.service.enums.third.RepFlagEnum;
import com.wosai.cua.brand.business.service.enums.third.RepGlobalTypeEnum;
import com.wosai.cua.brand.business.service.helper.BankOfDepositHelper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.bank.MerchantBizBankAccountModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.brand.MerchantBrandDetailModule;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.tools.service.InfoQueryService;
import com.wosai.tools.vo.BankUnionCardReq;
import com.wosai.tools.vo.BankUnionCardVo;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountRes;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountUpdateReq;
import com.wosai.upay.bank.model.bizbankaccount.MerchantBizBankAccount;
import com.wosai.upay.bank.model.bizbankaccount.QueryBizBankAccountReq;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.common.bean.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BrandMerchantBankAccountBusiness {

    private final BankCardDomainService bankCardDomainService;

    private final BrandDomainService brandDomainService;

    private final VfinanceInterfaceService vfinanceInterfaceService;

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = new EnumMap<>(FundManagementCompanyEnum.class);

    private final BrandConfigDomainService brandConfigDomainService;

    private final MerchantDomainService merchantDomainService;

    private final InfoQueryService infoQueryService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBizBankAccountService merchantBizBankAccountService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Value("${spring.application.biz}")
    private String biz;

    @Value("${fuiou.open.account.check_type}")
    private String fuiouCheckType;

    @Autowired
    public BrandMerchantBankAccountBusiness(BankCardDomainService bankCardDomainService, BrandDomainService brandDomainService, VfinanceInterfaceService vfinanceInterfaceService, List<TripartiteSystemCallService> tripartiteSystemCallServices, BrandConfigDomainService brandConfigDomainService, MerchantDomainService merchantDomainService, InfoQueryService infoQueryService, BankOfDepositHelper bankOfDepositHelper) {
        this.bankCardDomainService = bankCardDomainService;
        this.brandDomainService = brandDomainService;
        this.vfinanceInterfaceService = vfinanceInterfaceService;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.brandConfigDomainService = brandConfigDomainService;
        this.merchantDomainService = merchantDomainService;
        this.infoQueryService = infoQueryService;
    }

    @PostConstruct
    public void init() {
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }

    public BankCardResponseDTO createBankCard(CreateBankCardDTO createBankCard) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(createBankCard.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (StringUtils.isNotBlank(createBankCard.getMerchantId())) {
            MerchantInfo merchant = merchantService.getMerchantById(createBankCard.getMerchantId(), null);
            if (Objects.isNull(merchant)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
            }
            return this.getBankCardResponseDTO(createBankCard);
        }
        if (StringUtils.isNotBlank(createBankCard.getMerchantSn())) {
            MerchantInfo merchant = merchantService.getMerchantBySn(createBankCard.getMerchantSn(), null);
            if (Objects.isNull(merchant)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
            }
            createBankCard.setMerchantId(merchant.getId());
            return this.getBankCardResponseDTO(createBankCard);
        }
        throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_SN_OR_ID_NOT_BE_NULL_IN_SAME_TIME);
    }

    private BankCardResponseDTO getBankCardResponseDTO(CreateBankCardDTO createBankCard) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(createBankCard.getBrandId(), createBankCard.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        if (BrandMerchantDockingModeEnum.COLLECTION.getCode().equals(brandMerchantModule.getMerchantDockingMode())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_DOCKING_MODE_IS_COLLECTION);
        }
        //先查卡是否存在
        QueryBizBankAccountReq queryBizBankAccountReq = new QueryBizBankAccountReq();
        queryBizBankAccountReq.setMerchantId(createBankCard.getMerchantId());
        queryBizBankAccountReq.setBiz(biz);
        queryBizBankAccountReq.setNumber(createBankCard.getBankCardNo());
        BizBankAccountRes account = merchantBizBankAccountService.getMerchantBizBankAccountByMerchantIdAndNumber(queryBizBankAccountReq);
        List<BankCardModule> bankCardModulesByCarIdList = bankCardDomainService.getBankCardModulesByCarIdList(brandMerchantModule.getBrandId(), brandMerchantModule.getMerchantId(), null, null, null);
        if (Objects.nonNull(account) && CollectionUtils.isNotEmpty(bankCardModulesByCarIdList) && bankCardModulesByCarIdList.stream().map(BankCardModule::getBankCardId).collect(Collectors.toList()).contains(account.getId())){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_CARD_EXIST);
        }
        if (CollectionUtils.isNotEmpty(bankCardModulesByCarIdList) && bankCardModulesByCarIdList.size() >= 3) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_CARD_NUM_OUT_OF_RANGE);
        }
        MerchantBizBankAccount merchantBizBankAccount;
        if (Objects.nonNull(account)) {
            BizBankAccountUpdateReq req = JSON.parseObject(JSON.toJSONString(account), BizBankAccountUpdateReq.class);
            req.setHolder(createBankCard.getHolder());
            req.setOpening_number(createBankCard.getOpeningNumber());
            req.setSet_default(Objects.nonNull(createBankCard.getSetDefault()) && createBankCard.getSetDefault());
            req.setNeedCheckBiz(false);
            merchantBizBankAccount = merchantBizBankAccountService.updateBizBankAccount(req);
        } else {
            BizBankAccountAddReq req = JSON.parseObject(JSON.toJSONString(createBankCard), BizBankAccountAddReq.class);
            req.setBiz(biz);
            req.setId_type(createBankCard.getIdType());
            req.setIdentity(createBankCard.getIdentity());
            req.setMerchant_id(createBankCard.getMerchantId());
            req.setOpening_number(createBankCard.getOpeningNumber());
            req.setSet_default(Objects.nonNull(createBankCard.getSetDefault()) && createBankCard.getSetDefault());
            req.setNumber(createBankCard.getBankCardNo());
            merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(req);
        }
        BankCardModule bankCardModule = new BankCardModule();
        bankCardModule.setBankCardId(merchantBizBankAccount.getId());
        bankCardModule.setMerchantId(merchantBizBankAccount.getMerchant_id());
        bankCardModule.setReservedMobileNumber(createBankCard.getReservedMobileNumber());
        bankCardModule.setBrandId(createBankCard.getBrandId());
        bankCardModule.setAccountType(createBankCard.getType());
        bankCardModule.setIsDefault(createBankCard.getSetDefault());
        if (Boolean.TRUE.equals(createBankCard.getSetDefault())) {
            bankCardDomainService.updateAllBankCardIsNotDefault(createBankCard.getBrandId(), createBankCard.getMerchantId());
        }
        bankCardModule.setStatus(0);
        bankCardModule.setMemberId(brandMerchantModule.getMemberId());
        bankCardDomainService.createBankCard(bankCardModule);
        bankCardModule = bankCardDomainService.getBankCardModuleById(bankCardModule.getBankCardId());
        return new BankCardResponseDTO(bankCardModule.getBankCardId());
    }

    private void bindCiticBankCard(BrandModule brandModule, BrandMerchantModule brandMerchantModule, MerchantInfo merchant, BankCardModule bankCardModule, BizBankAccountRes bankAccount, MerchantBusinessLicenseInfo license) {
        CiticBankConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), CiticBankConfigModule.class);
        BindBankCardRequest bindBankCardRequest;
        try {
            bindBankCardRequest = BindBankCardRequest.buildBindRequest(configModule, brandMerchantModule, merchant, bankCardModule, bankAccount, license);
        } catch (BrandBusinessException e) {
            bankCardModule.setActivateFailReason(e.getMessage());
            bankCardDomainService.updateBankCard(bankCardModule);
            throw e;
        }
        BindBankCardResponse callResponse = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(bindBankCardRequest, BindBankCardResponse.class, configModule);
        if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(callResponse.getResultCode())) {
            log.error("绑定银行卡失败，失败原因：{}", callResponse.getResultMsg());
            bankCardModule.setActivateFailReason(callResponse.getResultMsg());
            bankCardDomainService.updateBankCard(bankCardModule);
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, callResponse.getResultMsg());
        }
        if (TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.equals(callResponse.getResultCode())) {
            bankCardModule.setStatus(1);
            bankCardModule.setThirdBankCardId(bindBankCardRequest.getPan());
            bankCardModule.setActivationTime(new Date());
            bankCardModule.setActivateFailReason("");
            if (Boolean.TRUE.equals(bankCardModule.getIsDefault())) {
                brandMerchantModule.setBankCardActivateStatus(BankCardActivateStatusEnum.ACTIVATED.getActivateStatus());
                brandDomainService.updateBrandMerchant(brandMerchantModule);
                try {
                    // 激活成功如果是默认卡再到中信设置一下
                    citicSetDefault(brandModule, bankCardModule);
                } catch (Exception e) {
                    log.warn("设置默认银行卡失败", e);
                }
            }
        }
        bankCardDomainService.updateBankCard(bankCardModule);
    }

    public Boolean activateBankCard(ActivateBankCardDTO activateBankCard) {
        List<MerchantBrandDetailModule> brandModules = brandDomainService.getBrandModuleByMerchantId(activateBankCard.getMerchantId());
        if (CollectionUtils.isEmpty(brandModules)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandModule brandModule = brandModules.get(0);
        activateBankCard.validate(brandModule.getFundManagementCompanyCode());
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(activateBankCard.getBrandId(), activateBankCard.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        MerchantInfo merchant = merchantService.getMerchantById(activateBankCard.getMerchantId(), null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
        BankCardModule bankCardModule = bankCardDomainService.getBankCardModuleById(activateBankCard.getBankCardId());
        if (Objects.isNull(bankCardModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_ADD_BANK_CARD);
        }
        if (!bankCardModule.getBrandId().equals(brandModule.getBrandId()) || !bankCardModule.getMerchantId().equals(activateBankCard.getMerchantId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
        BizBankAccountRes bankAccount = merchantBizBankAccountService.getBankAccountById(activateBankCard.getBankCardId());
        if (Objects.isNull(bankAccount)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        switch (brandModule.getFundManagementCompanyCode()) {
            case PAB:
                this.activePabCard(brandModule, activateBankCard, merchantBusinessLicense, bankCardModule, brandConfigModule, brandMerchantModule, merchant, bankAccount);
                break;
            case CITIC:
                this.bindCiticBankCard(brandModule, brandMerchantModule, merchant, bankCardModule, bankAccount, merchantBusinessLicense);
                break;
            case MY_BANK:
                break;
            case FUIOU:
                this.handleFuiouCard(brandModule, brandMerchantModule, bankCardModule, bankAccount, merchantBusinessLicense, "1");
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_SERVICE);
        }
        return true;
    }

    public void activateBankCardByBrandId(String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandId);
        if (Objects.isNull(brandModule) || Objects.isNull(brandConfigModule)) {
            return;
        }
        Long startId = 0L;
        List<BankCardModule> bankCardList;
        do {
            bankCardList = bankCardDomainService.getBankCardListByBrandId(brandId, 100, startId);
            bankCardList.forEach(bankCardModule -> {
                MerchantInfo merchant = merchantService.getMerchantById(bankCardModule.getMerchantId(), null);
                BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, bankCardModule.getMerchantId());
                if (Objects.isNull(merchant) || Objects.isNull(brandMerchantModule)) {
                    return;
                }
                BizBankAccountRes bankAccount = merchantBizBankAccountService.getBankAccountById(bankCardModule.getBankCardId());
                if (Objects.isNull(bankAccount)) {
                    return;
                }
                MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
                try {
                    this.bindCiticBankCard(brandModule, brandMerchantModule, merchant, bankCardModule, bankAccount, merchantBusinessLicense);
                } catch (Exception e) {
                    log.error("激活卡出错", e);
                }
            });
            startId = bankCardList.get(bankCardList.size() - 1).getId();
        } while (CollectionUtils.isNotEmpty(bankCardList));
    }

    private void handleFuiouCard(BrandModule brandModule, BrandMerchantModule brandMerchantModule, BankCardModule bankCardModule, BizBankAccountRes bankAccount, MerchantBusinessLicenseInfo license, String type) {
        FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), FuiouConfigModule.class);
        ModifyAccountInCardRequest modifyAccountInCardRequest = new ModifyAccountInCardRequest();
        ModifyAccountInCardRequestBody body = new ModifyAccountInCardRequestBody(configModule.getMerchantNo());
        body.setTraceNo(brandMerchantModule.getMerchantSn() + System.currentTimeMillis());
        body.setAccountIn(brandMerchantModule.getMemberId());
        body.setOutAcntNm(bankAccount.getHolder());
        body.setOutAcntNo(bankAccount.getNumber());
        body.setInterBankNo(bankAccount.getOpeningNumber());
        body.setCheckType(fuiouCheckType);
        body.setType(type);
        body.setMobile(bankCardModule.getReservedMobileNumber());
        //个体工商户
        if (license.getType() == 1) {
            body.setOutAcntNoType("01");
            body.setShxyNo(license.getNumber());
            body.setCertNo(license.getLegal_person_id_number());
        }
        if (license.getType() == 2) {
            body.setShxyNo(license.getNumber());
        }
        modifyAccountInCardRequest.setBody(body);
        ModifyAccountInCardResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(modifyAccountInCardRequest, ModifyAccountInCardResponse.class, configModule);
        if (response.isSuccess()) {
            if (StringUtils.isBlank(body.getOutAcntNoType()) || "01".equals(body.getOutAcntNoType())) {
                bankCardModule.setStatus(1);
                bankCardModule.setActivationTime(new Date());
            }
            if (StringUtils.isNotBlank(body.getOutAcntNoType()) && "02".equals(body.getOutAcntNoType())) {
                bankCardModule.setStatus(0);
            }
            bankCardModule.setActivateFailReason(response.getBody().getKsCheckUrl());
            bankCardDomainService.updateBankCard(bankCardModule);
        } else {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, response.getResultMsg());
        }
    }

    private void handleCiticCard(BrandMerchantModule brandMerchantModule, BizBankAccountRes bankAccount) {
        CiticBankConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandMerchantModule.getBrandId(), CiticBankConfigModule.class);
        BindBankCardRequest bindBankCardRequest = BindBankCardRequest.buildUnbindRequest(configModule, brandMerchantModule, bankAccount);
        BindBankCardResponse call = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(bindBankCardRequest, BindBankCardResponse.class, configModule);
        if (!call.isSuccess()) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, call.getResultMsg());
        }
    }

    private void activePabCard(BrandModule brandModule, ActivateBankCardDTO activateBankCard, MerchantBusinessLicenseInfo merchantBusinessLicense, BankCardModule bankCardModule, BrandConfigModule brandConfigModule, BrandMerchantModule brandMerchantModule, MerchantInfo merchant, BizBankAccountRes bankAccount) {
        boolean notAllowedCheckMode = (merchantBusinessLicense.getType().equals(BusinessLicenseEnum.ENTERPRISE_BUSINESS_LICENSE.getType())
                || merchantBusinessLicense.getType().equals(BusinessLicenseEnum.UNIFIED_SOCIAL_CREDIT_IDENTIFIER.getType()))
                && activateBankCard.getCheckMode().equals(BankCardCheckModeEnum.UNION_PAY_CHECK.getCheckMode());
        if (notAllowedCheckMode) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_ALLOWED_CHECK_MODE);
        }
        activatePabBankCard(activateBankCard, bankCardModule, brandConfigModule, brandMerchantModule, merchant, merchantBusinessLicense, bankAccount);
        brandDomainService.pabRegister(brandModule, brandMerchantModule);
    }

    private void activatePabBankCard(ActivateBankCardDTO activateBankCard, BankCardModule bankCardModule, BrandConfigModule brandConfigModule, BrandMerchantModule brandMerchantModule, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BizBankAccountRes bankAccount) {
        if (StringUtils.isEmpty(activateBankCard.getMobile())) {
            activateBankCard.setMobile(bankCardModule.getReservedMobileNumber());
        }
        PabConfigModule pabConfigModule = JSON.parseObject(brandConfigModule.getConfig(), PabConfigModule.class);
        CreateBankCardRequest request = this.getCreateBankCardRequest(
                activateBankCard,
                pabConfigModule,
                brandMerchantModule,
                merchant,
                bankCardModule,
                merchantBusinessLicense,
                bankAccount.getNumber(),
                bankAccount.getHolder(),
                bankAccount.getOpeningNumber(),
                bankAccount.getBankName()
        );
        log.info("调用维金接口CreateBankCard，入参为：{}", JSON.toJSONString(request));
        CreateBankCardResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.PAB).call(request, CreateBankCardResponse.class, pabConfigModule);
        if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
            bankCardModule.setActivateFailReason(response.getErrorMessage());
            bankCardDomainService.updateBankCard(bankCardModule);
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), response.getErrorMessage());
        }
        bankCardModule.setThirdBankCardId(response.getBankCardId());
        bankCardModule.setCreatedTime(null);
        bankCardModule.setUpdatedTime(null);
        bankCardModule.setActivateFailReason("");
        bankCardDomainService.updateBankCard(bankCardModule);
    }

    private CreateBankCardRequest getCreateBankCardRequest(ActivateBankCardDTO activateBankCard,
                                                           PabConfigModule pabConfigModule,
                                                           BrandMerchantModule brandMerchantModule,
                                                           MerchantInfo merchant,
                                                           BankCardModule bankCardModule,
                                                           MerchantBusinessLicenseInfo merchantBusinessLicense,
                                                           String bankAccountNumber,
                                                           String accountName,
                                                           String openingNumber,
                                                           String bankName
    ) {
        CreateBankCardRequest request = new CreateBankCardRequest(pabConfigModule.getPartnerId());
        request.setMemberId(brandMerchantModule.getMemberId());
        request.setCardType("DEBIT");
        request.setCardAttribute(bankCardModule.getAccountType() == 1 ? CardAttributeEnum.TOC.getCardAttribute() : CardAttributeEnum.TOB.getCardAttribute());
//        request.setBankCode(bankCardModule.getBankOfDeposit());
        request.setBankName(bankName);
        request.setBankAccountNo(bankAccountNumber);
        request.setAccountName(accountName);
        request.setMobile(activateBankCard.getMobile());
        request.setCheckMode(activateBankCard.getCheckMode());
        request.setIsDefault(Boolean.TRUE.equals(bankCardModule.getIsDefault()) ? DefaultEnum.DEFAULT_BANK_CARD_YES.getType() : DefaultEnum.DEFAULT_BANK_CARD_NO.getType());
        if (bankName.contains("平安银行")) {
            request.setBankType(BankTypeEnum.PAB.getBankType());
        } else {
            request.setBankType(BankTypeEnum.OTHER.getBankType());
        }
        request.setBranchNo(openingNumber);
        if (merchantBusinessLicense.getType().equals(BusinessLicenseEnum.MICRO_MERCHANT.getType())) {
            this.microMerchantBuildRequest(merchant, merchantBusinessLicense, request);
        }
        if (merchantBusinessLicense.getType().equals(BusinessLicenseEnum.INDIVIDUAL_BUSINESS_MERCHANT.getType())) {
            this.individualBusinessMerchantBuildRequest(activateBankCard, merchant, merchantBusinessLicense, request);
        }
        if (merchantBusinessLicense.getType().equals(BusinessLicenseEnum.ENTERPRISE_BUSINESS_LICENSE.getType()) || merchantBusinessLicense.getType().equals(BusinessLicenseEnum.UNIFIED_SOCIAL_CREDIT_IDENTIFIER.getType())) {
            this.companyBuildRequest(activateBankCard, merchant, merchantBusinessLicense, request);
        }
        return request;
    }

    private void companyBuildRequest(ActivateBankCardDTO activateBankCard, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, CreateBankCardRequest request) {
        request.setMemberName(merchant.getName());
        request.setIndicBusinessFlag(IndivBusinessFlagEnum.COMPANY_OR_PERSON.getFlag());
        request.setMemberGlobalType(MemberGlobalTypeEnum.MEMBER_GLOBAL_TYPE_COMPANY.getType());
        request.setMemberGlobalId(merchantBusinessLicense.getNumber());
        request.setShopName(merchant.getName());
        request.setLegalName(merchantBusinessLicense.getLegal_person_name());
        this.setReprGlobalType(merchantBusinessLicense, request);
        request.setErectileCode(merchantBusinessLicense.getLegal_person_id_number());
        checkIsAgencyActive(activateBankCard, request);
        request.setRepFlag(RepFlagEnum.REP_FLAG_NO.getFlag());
    }

    private void individualBusinessMerchantBuildRequest(ActivateBankCardDTO activateBankCard, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, CreateBankCardRequest request) {
        request.setIndicBusinessFlag(IndivBusinessFlagEnum.INDIVIDUAL.getFlag());
        request.setCompanyName(merchant.getName());
        request.setCreditType(MemberGlobalTypeEnum.MEMBER_GLOBAL_TYPE_COMPANY.getType());
        request.setCreditCode(merchantBusinessLicense.getNumber());
        request.setLegalName(merchantBusinessLicense.getLegal_person_name());
        this.setReprGlobalType(merchantBusinessLicense, request);
        request.setErectileCode(merchantBusinessLicense.getLegal_person_id_number());
        request.setShopName(merchant.getName());
        request.setRepFlag(RepFlagEnum.REP_FLAG_YES.getFlag());
        if (request.getCardAttribute().equals(CardAttributeEnum.TOC.getCardAttribute())) {
            request.setMemberName(merchantBusinessLicense.getLegal_person_name());
            request.setMemberGlobalType(MemberGlobalTypeEnum.getTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
            request.setMemberGlobalId(merchantBusinessLicense.getLegal_person_id_number());
        } else {
            request.setMemberName(merchant.getName());
            request.setMemberGlobalType(MemberGlobalTypeEnum.MEMBER_GLOBAL_TYPE_COMPANY.getType());
            request.setMemberGlobalId(merchantBusinessLicense.getNumber());
        }
        checkIsAgencyActive(activateBankCard, request);
    }

    private void checkIsAgencyActive(ActivateBankCardDTO activateBankCard, CreateBankCardRequest request) {
        if (BankCardActivateTypeEnum.AGENT.getActivateType().equals(activateBankCard.getActivateType())) {
            request.setAgencyClientFlag(AgencyClientFlageEnum.YES.getAgencyClientFlag());
            request.setAgentName(activateBankCard.getAgentName());
            request.setAgencyGlobalType("1");
            request.setAgentPhone(activateBankCard.getAgentPhone());
            request.setAgentIdCode(activateBankCard.getAgentIdCode());
        } else {
            request.setAgencyClientFlag(AgencyClientFlageEnum.NO.getAgencyClientFlag());
        }
    }

    private void microMerchantBuildRequest(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, CreateBankCardRequest request) {
        request.setShopName(merchant.getName());
        request.setMemberName(merchantBusinessLicense.getLegal_person_name());
        request.setAgencyClientFlag(AgencyClientFlageEnum.NO.getAgencyClientFlag());
        request.setIndicBusinessFlag(IndivBusinessFlagEnum.COMPANY_OR_PERSON.getFlag());
        request.setMemberGlobalType(MemberGlobalTypeEnum.getTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
        request.setMemberGlobalId(merchantBusinessLicense.getLegal_person_id_number());
        request.setRepFlag(RepFlagEnum.REP_FLAG_YES.getFlag());
    }

    private void setReprGlobalType(MerchantBusinessLicenseInfo merchantBusinessLicense, CreateBankCardRequest request) {
        switch (merchantBusinessLicense.getLegal_person_id_type()) {
            case 2:
                request.setReprGlobalType(RepGlobalTypeEnum.FOREIGN_PASSPORTS.getRepGlobalType());
                break;
            case 3:
                request.setReprGlobalType(RepGlobalTypeEnum.TAIWAN_COMPATRIOT_CERTIFICATE.getRepGlobalType());
                break;
            case 4:
                request.setReprGlobalType(RepGlobalTypeEnum.HK_MACAU_HOME_RETURN_PERMIT_CARD.getRepGlobalType());
                break;
            default:
                request.setReprGlobalType(RepGlobalTypeEnum.IDENTITY_CARD.getRepGlobalType());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeDefaultBankCard(String bankCardId) {
        BankCardModule bankCardModule = bankCardDomainService.getBankCardModuleById(bankCardId);
        if (Objects.isNull(bankCardModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(bankCardModule.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        bankCardModule.setIsDefault(true);
        bankCardModule.setUpdatedTime(null);
        bankCardModule.setCreatedTime(null);
        bankCardDomainService.updateAllBankCardIsNotDefault(bankCardModule.getBrandId(), bankCardModule.getMerchantId());
        bankCardDomainService.updateBankCard(bankCardModule);
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.PAB)) {
            this.pabSetDefault(brandModule, bankCardModule);
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.CITIC)) {
            this.citicSetDefault(brandModule, bankCardModule);
        }
        merchantBizBankAccountService.replaceBizBankAccount(bankCardId);
        if (bankCardModule.getStatus() == 0) {
            BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(bankCardModule.getBrandId(), bankCardModule.getMerchantId());
            brandMerchantModule.setBankCardActivateStatus(BankCardActivateStatusEnum.UNACTIVATED.getActivateStatus());
            brandDomainService.updateBrandMerchant(brandMerchantModule);
        }
    }

    private void pabSetDefault(BrandModule brandModule, BankCardModule bankCardModule) {
        PabConfigModule pabConfigModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), PabConfigModule.class);
        // 修改商户银行卡系统的默认卡
        ChangeDefaultBankCardRequest changeDefaultBankCardRequest = new ChangeDefaultBankCardRequest(pabConfigModule.getPartnerId());
        changeDefaultBankCardRequest.setBankCardId(bankCardModule.getThirdBankCardId());
        changeDefaultBankCardRequest.setMemberId(bankCardModule.getMemberId());
        log.info("调用维金接口ChangeDefaultBankCard，入参为：{}", JSON.toJSONString(changeDefaultBankCardRequest));
        String s = vfinanceInterfaceService.invokeService(changeDefaultBankCardRequest);
        VfinanceBaseResponse response = JSON.parseObject(s, VfinanceBaseResponse.class);
        if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), response.getErrorMessage());
        }
    }

    private void citicSetDefault(BrandModule brandModule, BankCardModule bankCardModule) {
        BizBankAccountRes bankAccount = merchantBizBankAccountService.getBankAccountById(bankCardModule.getBankCardId());
        CiticBankConfigModule citicBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), CiticBankConfigModule.class);
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandModule.getBrandId(), bankCardModule.getMerchantId());
        SetDefaultBankCardRequest setDefaultBankCardRequest = SetDefaultBankCardRequest.builder(citicBankConfigModule, brandMerchantModule, bankAccount);
        // 修改商户银行卡系统的默认卡
        SetDefaultBankCardResponse call = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(brandModule.getFundManagementCompanyCode()).call(setDefaultBankCardRequest, SetDefaultBankCardResponse.class, citicBankConfigModule);
        if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(call.getResultCode())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), call.getResultMsg());
        }
    }

    /**
     * 银行卡激活跟进
     *
     * @param activateBankCardAdvanceDto 银行卡激活跟进对象
     * @return 激活是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean activateBankCardAdvance(ActivateBankCardAdvanceDTO activateBankCardAdvanceDto) {
        List<MerchantBrandDetailModule> brandModules = brandDomainService.getBrandModuleByMerchantId(activateBankCardAdvanceDto.getMerchantId());
        if (CollectionUtils.isEmpty(brandModules)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BankCardModule bankCardModule = bankCardDomainService.getBankCardModuleById(activateBankCardAdvanceDto.getBankCardId());
        CreateBankCardAdvanceRequest createBankCardAdvanceRequest = this.getCreateBankCardAdvanceRequest(activateBankCardAdvanceDto, bankCardModule, brandModules);
        CreateBankCardResponse response;
        try {
            log.info("调用维金接口CreateBankCardAdvance，入参为：{}", JSON.toJSONString(createBankCardAdvanceRequest));
            response = JSON.parseObject(vfinanceInterfaceService.invokeService(createBankCardAdvanceRequest), CreateBankCardResponse.class);
        } catch (Exception e) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL);
        }
        if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
            bankCardModule.setActivateFailReason(response.getErrorMessage());
            bankCardDomainService.updateBankCard(bankCardModule);
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), response.getErrorMessage());
        }
        if (Boolean.TRUE.equals(bankCardModule.getIsDefault())) {
            BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(bankCardModule.getBrandId(), bankCardModule.getMerchantId());
            brandMerchantModule.setBankCardActivateStatus(BankCardActivateStatusEnum.ACTIVATED.getActivateStatus());
            brandDomainService.updateBrandMerchant(brandMerchantModule);
            // 如果是默认卡再到平安设置一下默认卡
            BrandModule brandModule = new BrandModule();
            brandModule.setBrandId(bankCardModule.getBrandId());
            try {
                pabSetDefault(brandModule, bankCardModule);
            } catch (Exception e) {
                log.warn("设置默认卡失败", e);
            }
        }
        bankCardModule.setStatus(1);
        bankCardModule.setCreatedTime(null);
        bankCardModule.setUpdatedTime(null);
        bankCardModule.setActivationTime(new Date());
        bankCardDomainService.updateBankCard(bankCardModule);
        return true;
    }

    private CreateBankCardAdvanceRequest getCreateBankCardAdvanceRequest(ActivateBankCardAdvanceDTO activateBankCardAdvanceDto, BankCardModule bankCardModule, List<MerchantBrandDetailModule> brandModules) {
        if (Objects.isNull(bankCardModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        BrandModule brandModule = brandModules.get(0);
        if (!bankCardModule.getBrandId().equals(brandModule.getBrandId()) || !bankCardModule.getMerchantId().equals(activateBankCardAdvanceDto.getMerchantId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
        return this.getCreateBankCardAdvanceRequest(activateBankCardAdvanceDto, bankCardModule, brandModule);
    }

    private CreateBankCardAdvanceRequest getCreateBankCardAdvanceRequest(ActivateBankCardAdvanceDTO activateBankCardAdvanceDto, BankCardModule bankCardModule, BrandModule brandModule) {
        CreateBankCardAdvanceRequest createBankCardAdvanceRequest = new CreateBankCardAdvanceRequest(brandModule.getPartnerId());
        createBankCardAdvanceRequest.setBankCardId(bankCardModule.getThirdBankCardId());
        createBankCardAdvanceRequest.setBankPaymentAmount(activateBankCardAdvanceDto.getBankPaymentAmount());
        createBankCardAdvanceRequest.setCheckMode(activateBankCardAdvanceDto.getCheckMode());
        createBankCardAdvanceRequest.setMsgCode(activateBankCardAdvanceDto.getVerificationCode());
        return createBankCardAdvanceRequest;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBankCard(String bankCardId, boolean isDeleteMerchant, boolean forceDelete) {
        BankCardModule bankCardModule = bankCardDomainService.getBankCardModuleById(bankCardId);
        if (Objects.isNull(bankCardModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        if (Boolean.TRUE.equals(bankCardModule.getIsDefault()) && bankCardModule.getStatus() == 1 && !isDeleteMerchant && !forceDelete) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.DEFAULT_BANK_CARD_CAN_NOT_DELETE);
        }
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(bankCardModule.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BizBankAccountRes bankAccount = merchantBizBankAccountService.getBankAccountById(bankCardId);
        if (Objects.isNull(bankAccount)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandModule.getBrandId(), bankCardModule.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        bankCardModule.setDeleted(1);
        bankCardModule.setUpdatedTime(null);
        bankCardModule.setCreatedTime(null);
        if (bankCardModule.getStatus() == 1) {
            this.unbindFundManagementBankCard(brandModule, bankCardModule, brandMerchantModule, bankAccount);
        }
        bankCardModule.setActivationTime(null);
        bankCardDomainService.updateBankCard(bankCardModule);
        return true;
    }

    private void unbindFundManagementBankCard(BrandModule brandModule, BankCardModule bankCardModule, BrandMerchantModule brandMerchantModule, BizBankAccountRes bankAccount) {
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.PAB) && StringUtils.isNotEmpty(bankCardModule.getThirdBankCardId()) && StringUtils.isNotEmpty(bankCardModule.getMemberId())) {
            PabConfigModule pabConfigModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), PabConfigModule.class);
            if (Objects.nonNull(pabConfigModule)) {
                // 修改商户银行卡系统的默认卡
                UnbindBankCardRequest unbindBankCardRequest = new UnbindBankCardRequest(pabConfigModule.getPartnerId());
                unbindBankCardRequest.setBankCardId(bankCardModule.getThirdBankCardId());
                unbindBankCardRequest.setMemberId(bankCardModule.getMemberId());
                log.info("调用维金接口UnbindBankCard，入参为：{}", JSON.toJSONString(unbindBankCardRequest));
                String s = vfinanceInterfaceService.invokeService(unbindBankCardRequest);
                VfinanceBaseResponse response = JSON.parseObject(s, VfinanceBaseResponse.class);
                if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
                    log.warn("调用维金接口UnbindBankCard失败，失败原因为：{}", response.getErrorMessage());
                }
            }
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU)) {
            MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(bankCardModule.getMerchantId(), null);
            //富友解绑银行卡
            this.handleFuiouCard(brandModule, brandMerchantModule, bankCardModule, bankAccount, merchantBusinessLicense, "2");
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.CITIC)) {
            //中信解绑银行卡
            this.handleCiticCard(brandMerchantModule, bankAccount);
        }
    }

    public BrandMerchantBankCardResponseDTO getDefaultBankCard(String brandId, String merchantId) {
        BankCardModule defaultBankCardModule = bankCardDomainService.getDefaultBankCardModule(brandId, merchantId);
        if (Objects.isNull(defaultBankCardModule)) {
            return null;
        }
        BrandMerchantBankCardResponseDTO response = JSON.parseObject(JSON.toJSONString(defaultBankCardModule), BrandMerchantBankCardResponseDTO.class);
        BizBankAccountRes bankAccountById = null;
        try {
            bankAccountById = merchantBizBankAccountService.getBankAccountById(defaultBankCardModule.getBankCardId());
        } catch (Exception e) {
            return null;
        }
        if (Objects.isNull(bankAccountById)) {
            return null;
        }
        response.setBankAccountName(bankAccountById.getHolder());
        response.setBankCardNumber(bankAccountById.getNumber());
        response.setOpeningNumber(bankAccountById.getOpeningNumber());
        response.setBankBack(bankAccountById.getBankBack());
        response.setBankIcon(bankAccountById.getBankIcon());
        response.setBankName(bankAccountById.getBankName());
        response.setBranchName(bankAccountById.getBranchName());
        response.setAccountTypeDesc(BankAccountTypeEnum.getDescByAccountType(response.getAccountType()));
        switch (defaultBankCardModule.getStatus()) {
            case 0: {
                response.setActivateStatus(BankCardActivateStatusEnum.UNACTIVATED.getActivateStatus());
                response.setActivateStatusDesc(BankCardActivateStatusEnum.UNACTIVATED.getDesc());
                break;
            }
            case 1: {
                response.setActivateStatus(BankCardActivateStatusEnum.ACTIVATED.getActivateStatus());
                response.setActivateStatusDesc(BankCardActivateStatusEnum.ACTIVATED.getDesc());
                break;
            }
            default: {
                response.setActivateStatus(BankCardActivateStatusEnum.NOT_BOUND.getActivateStatus());
                response.setActivateStatusDesc(BankCardActivateStatusEnum.NOT_BOUND.getDesc());
            }
        }
        return response;
    }

    public PageBrandMerchantBankCardsDTO pageFindBankCardList(PageQueryBankCardDTO pageQueryBankCardDto) {
        // 输入验证
        this.validatePageQueryBankCardDto(pageQueryBankCardDto);

        String merchantId = this.extractMerchantId(pageQueryBankCardDto);

        // 商户品牌详情模块获取及验证
        MerchantBrandDetailModule brandModule = this.getBrandModule(merchantId);

        // 初始化返回对象
        PageBrandMerchantBankCardsDTO pageBrandMerchantBankCardsDto = this.initPage();

        // 根据商户ID查询业务银行账户信息
        ListResult bizBankAccount = this.queryBizBankAccount(pageQueryBankCardDto, merchantId);

        if (CollectionUtils.isEmpty(bizBankAccount.getRecords())) {
            return pageBrandMerchantBankCardsDto;
        }

        // 银行卡信息组装
        this.assembleBankCards(pageBrandMerchantBankCardsDto, brandModule, bizBankAccount, merchantId);
        return pageBrandMerchantBankCardsDto;
    }

    private void validatePageQueryBankCardDto(PageQueryBankCardDTO pageQueryBankCardDto) {
        // 实现页面查询参数的合法性验证
        if (StringUtils.isEmpty(pageQueryBankCardDto.getMerchantId()) && StringUtils.isEmpty(pageQueryBankCardDto.getMerchantSn())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_SN_OR_ID_NOT_BE_NULL_IN_SAME_TIME);
        }
    }

    private String extractMerchantId(PageQueryBankCardDTO pageQueryBankCardDto) {
        // 实现根据条件提取商户ID的逻辑
        String merchantId = pageQueryBankCardDto.getMerchantId();
        if (StringUtils.isEmpty(merchantId)) {
            MerchantInfo merchant = merchantService.getMerchantBySn(pageQueryBankCardDto.getMerchantSn(), null);
            if (Objects.isNull(merchant)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
            }
            merchantId = merchant.getId();
        }
        return merchantId;
    }

    private MerchantBrandDetailModule getBrandModule(String merchantId) {
        // 实现根据商户ID获取品牌详情模块并进行异常处理的逻辑
        List<MerchantBrandDetailModule> brandModules = brandDomainService.getBrandModuleByMerchantId(merchantId);
        if (CollectionUtils.isEmpty(brandModules)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        return brandModules.get(0);
    }

    private PageBrandMerchantBankCardsDTO initPage() {
        PageBrandMerchantBankCardsDTO dto = new PageBrandMerchantBankCardsDTO();
        dto.setTotal(0);
        dto.setRecords(Lists.newArrayList());
        return dto;
    }

    private ListResult queryBizBankAccount(PageQueryBankCardDTO pageQueryBankCardDto, String merchantId) {
        // 实现根据条件查询业务银行账户信息的逻辑
        PageBrandMerchantBankCardsDTO pageBrandMerchantBankCardsDto = new PageBrandMerchantBankCardsDTO();
        pageBrandMerchantBankCardsDto.setTotal(0);
        pageBrandMerchantBankCardsDto.setRecords(Lists.newArrayList());
        Map<String, Object> req = Maps.newHashMap();
        req.put("page", pageQueryBankCardDto.getPage());
        req.put("page_size", pageQueryBankCardDto.getPageSize());
        req.put("biz", biz);
        req.put("merchant_id", merchantId);
        return merchantBizBankAccountService.findBizBankAccount(req);
    }

    private void assembleBankCards(PageBrandMerchantBankCardsDTO pageBrandMerchantBankCardsDto,
                                   MerchantBrandDetailModule brandModule, ListResult bizBankAccount, String merchantId) {
        if (CollectionUtils.isEmpty(bizBankAccount.getRecords()) || bizBankAccount.getTotal() == 0) {
            pageBrandMerchantBankCardsDto.setTotal(0);
            return;
        }
        pageBrandMerchantBankCardsDto.setTotal((int) bizBankAccount.getTotal());
        List<MerchantBizBankAccountModule> merchantBizBankAccountModules = convertRecordsToMerchantBizBankAccountModules(bizBankAccount.getRecords());
        List<BankCardModule> bankCardModules = bankCardDomainService.getBankCardModulesByCarIdList(brandModule.getBrandId(), merchantId, getBankCardIds(merchantBizBankAccountModules), null, null);
        if (CollectionUtils.isEmpty(bankCardModules)) {
            pageBrandMerchantBankCardsDto.setTotal(0);
            return;
        }
        Map<String, BankCardModule> bankCardModuleMap = bankCardModules.stream().collect(Collectors.toMap(BankCardModule::getBankCardId, Function.identity(), (v1, v2) -> v1));

        Map<String, QueryBankCardResponse.BankCardBean> bankCardBeanMap = Maps.newHashMap();
        Optional.ofNullable(brandModule.getMemberId()).ifPresent(memberId -> this.getThirdBankCardResponseMap(bankCardBeanMap, brandModule, memberId));

        List<BankCardDetailDTO> bankCardDetails = Lists.newArrayList();
        merchantBizBankAccountModules.forEach(merchantBizBankAccountModule -> {
            BankCardDetailDTO bankCardDetail = new BankCardDetailDTO();
            BankCardModule bankCardModule = bankCardModuleMap.get(merchantBizBankAccountModule.getId());
            if (Objects.nonNull(bankCardModule)) {
                populateBankCardDetail(bankCardDetail, bankCardModule);
                Optional.ofNullable(bankCardModule.getThirdBankCardId()).ifPresent(thirdBankCardId -> this.updateBankCardDetailWithStatus(bankCardDetail, bankCardBeanMap.get(thirdBankCardId)));
                bankCardDetail.setBankCardNumber(merchantBizBankAccountModule.getNumber());
                bankCardDetail.setBankIcon(merchantBizBankAccountModule.getBankIcon());
                bankCardDetail.setBankCardId(merchantBizBankAccountModule.getId());
                bankCardDetail.setBankName(merchantBizBankAccountModule.getBankName());
                bankCardDetail.setBankBackPicture(merchantBizBankAccountModule.getBankBack());
                bankCardDetail.setBranchName(merchantBizBankAccountModule.getBranchName());
                bankCardDetail.setAccountName(merchantBizBankAccountModule.getHolder());
                bankCardDetail.setAccountType(merchantBizBankAccountModule.getType());
                bankCardDetail.setAccountTypeDesc(BankAccountTypeEnum.getDescByAccountType(merchantBizBankAccountModule.getType()));
                bankCardDetail.setMerchantId(merchantId);
                bankCardDetails.add(bankCardDetail);
            }
        });
        pageBrandMerchantBankCardsDto.setRecords(bankCardDetails);
    }

    private List<MerchantBizBankAccountModule> convertRecordsToMerchantBizBankAccountModules(List<?> records) {
        return JSON.parseArray(JSON.toJSONString(records), MerchantBizBankAccountModule.class);
    }

    private List<String> getBankCardIds(List<MerchantBizBankAccountModule> merchantBizBankAccountModules) {
        return merchantBizBankAccountModules.stream().map(MerchantBizBankAccountModule::getId).collect(Collectors.toList());
    }

    private void getThirdBankCardResponseMap(Map<String, QueryBankCardResponse.BankCardBean> bankCardBeanMap, MerchantBrandDetailModule brandModule, String memberId) {
        QueryBankCardRequest request = new QueryBankCardRequest(brandModule.getPartnerId());
        request.setMemberId(memberId);
        try {
            log.info("调用维金接口QUERY_BANK_CARD，入参为：{}", JSON.toJSONString(request));
            String responseStr = vfinanceInterfaceService.invokeService(request);
            if (StringUtils.isNotEmpty(responseStr)) {
                QueryBankCardResponse response = JSON.parseObject(responseStr, QueryBankCardResponse.class);
                if (CollectionUtils.isNotEmpty(response.getCardList())) {
                    bankCardBeanMap.putAll(response.getCardList().stream().collect(Collectors.toMap(QueryBankCardResponse.BankCardBean::getBankCardId, Function.identity())));
                }
            }
        } catch (Exception e) {
            log.error("调用维金系统查询会员银行卡信息失败", e);
        }
    }

    private void populateBankCardDetail(BankCardDetailDTO bankCardDetail, BankCardModule bankCardModule) {
        bankCardDetail.setActivateStatus(bankCardModule.getStatus());
        bankCardDetail.setDefaultCard(bankCardModule.getIsDefault());
        bankCardDetail.setActivateFailReason(bankCardModule.getActivateFailReason());
        bankCardDetail.setReservedMobileNumber(bankCardModule.getReservedMobileNumber());
        bankCardDetail.setActivationTime(bankCardModule.getActivationTime());
    }

    private void updateBankCardDetailWithStatus(BankCardDetailDTO bankCardDetail, QueryBankCardResponse.BankCardBean bankCardBean) {
        if (Objects.nonNull(bankCardBean) && CardStatusEnum.LOSE_EFFECTIVENESS.getCode().equals(bankCardBean.getCardStatus())) {
            bankCardDetail.setActivateStatus(-1);
            bankCardDetail.setActivateStatusDesc("已失效");
        }
    }


    public void checkOperationPermission(String merchantId, String bankCardId) {
        List<MerchantBrandDetailModule> brandModules = brandDomainService.getBrandModuleByMerchantId(merchantId);
        if (CollectionUtils.isEmpty(brandModules)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BankCardModule bankCardModule = bankCardDomainService.getBankCardModuleById(bankCardId);
        if (Objects.isNull(bankCardModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        BrandModule brandModule = brandModules.get(0);
        if (!bankCardModule.getBrandId().equals(brandModule.getBrandId()) || !bankCardModule.getMerchantId().equals(merchantId)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
    }

    public BankCardActivateStatusEnum getBankCardActivateStatusByMerchantId(String merchantId) {
        List<MerchantBrandDetailModule> brandModules = brandDomainService.getBrandModuleByMerchantId(merchantId);
        if (CollectionUtils.isEmpty(brandModules)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        MerchantBrandDetailModule module = brandModules.get(0);
        if (MerchantTypeEnum.BRAND_ADMIN.name().equals(module.getMerchantType())) {
            return BankCardActivateStatusEnum.NOT_NEED_BOUND;
        }
        BankCardModule defaultBankCardModule = bankCardDomainService.getDefaultBankCardModule(module.getBrandId(), merchantId);
        if (Objects.isNull(defaultBankCardModule)) {
            return BankCardActivateStatusEnum.NOT_BOUND;
        }
        switch (defaultBankCardModule.getStatus()) {
            case 0:
                return BankCardActivateStatusEnum.UNACTIVATED;
            case 1:
                return BankCardActivateStatusEnum.ACTIVATED;
            default:
                return BankCardActivateStatusEnum.NOT_BOUND;
        }
    }

    public List<BankCardDetailDTO> getActivatedBankCardList(String brandId, String merchantId) {
        List<BankCardModule> bankCardModules = bankCardDomainService.getBankCardModulesByCarIdList(brandId, merchantId, null, 1, null);
        if (CollectionUtils.isEmpty(bankCardModules)) {
            return Lists.newArrayList();
        }
        // 过滤已激活过的银行卡
        List<BankCardModule> activatedBankCardList = bankCardModules.stream().filter(bankCardModule -> bankCardModule.getStatus() == 1).collect(Collectors.toList());
        return this.getBankCardList(activatedBankCardList);
    }

    /**
     * 获取未激活的银行卡列表
     *
     * @param brandId
     * @param merchantId
     * @return
     */
    public List<BankCardDetailDTO> getUnActivatedBankCardList(String brandId, String merchantId) {
        List<BankCardModule> bankCardModules = bankCardDomainService.getBankCardModulesByCarIdList(brandId, merchantId, null, 0, 1);
        if (CollectionUtils.isEmpty(bankCardModules)) {
            return Lists.newArrayList();
        }
        return this.getBankCardList(bankCardModules);
    }


    private List<BankCardDetailDTO> getBankCardList(List<BankCardModule> bankCardModules) {
        if (CollectionUtils.isEmpty(bankCardModules)) {
            return Lists.newArrayList();
        }
        List<String> merchantIds = bankCardModules.stream().map(BankCardModule::getMerchantId).distinct().collect(Collectors.toList());
        Map<String, MerchantModule> merchantIdMap = Maps.newHashMap();
        List<MerchantModule> merchantInfoList = merchantDomainService.batchGetMerchantModule(merchantIds);
        if (CollectionUtils.isNotEmpty(merchantInfoList)) {
            merchantIdMap.putAll(merchantInfoList.stream().collect(Collectors.toMap(MerchantModule::getId, Function.identity())));
        }
        List<BankCardDetailDTO> responses = Lists.newArrayList();
        bankCardModules.forEach(bankCardModule -> {
            BizBankAccountRes bankAccount = merchantBizBankAccountService.getBankAccountById(bankCardModule.getBankCardId());
            BankCardDetailDTO bankCardDetail = new BankCardDetailDTO();
            bankCardDetail.setBankCardNumber(bankAccount.getNumber());
            bankCardDetail.setBankIcon(bankAccount.getBankIcon());
            bankCardDetail.setBankCardId(bankAccount.getId());
            bankCardDetail.setBankName(bankAccount.getBankName());
            bankCardDetail.setBankBackPicture(bankAccount.getBankBack());
            bankCardDetail.setBranchName(bankAccount.getBranchName());
            bankCardDetail.setAccountName(bankAccount.getHolder());
            bankCardDetail.setAccountType(bankAccount.getType());
            bankCardDetail.setAccountTypeDesc(BankAccountTypeEnum.getDescByAccountType(bankAccount.getType()));
            bankCardDetail.setActivateStatus(bankCardModule.getStatus());
            bankCardDetail.setDefaultCard(bankCardModule.getIsDefault());
            bankCardDetail.setActivateFailReason(bankCardModule.getActivateFailReason());
            bankCardDetail.setReservedMobileNumber(bankCardModule.getReservedMobileNumber());
            MerchantModule merchantInfo = merchantIdMap.get(bankCardModule.getMerchantId());
            if (Objects.nonNull(merchantInfo)) {
                bankCardDetail.setMerchantSn(merchantInfo.getSn());
                bankCardDetail.setMerchantName(merchantInfo.getName());
                bankCardDetail.setMerchantId(merchantInfo.getId());
                MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantInfo.getId(), null);
                if (bankAccount.getType() == 1) {
                    bankCardDetail.setIdNumber(Objects.nonNull(license) ? license.getLegal_person_id_number() : null);
                }
                if (bankAccount.getType() == 2) {
                    bankCardDetail.setIdNumber(Objects.nonNull(license) ? license.getNumber() : null);
                }
            }
            responses.add(bankCardDetail);
        });
        return responses;
    }

    public BankCardResponseDTO modifyBankCard(ModifyBankCardDTO modifyBankCard) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(modifyBankCard.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (StringUtils.isNotBlank(modifyBankCard.getMerchantId())) {
            MerchantInfo merchant = merchantService.getMerchantById(modifyBankCard.getMerchantId(), null);
            if (Objects.isNull(merchant)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
            }
            return this.getModifyBankCard(modifyBankCard);
        }
        if (StringUtils.isNotBlank(modifyBankCard.getMerchantSn())) {
            MerchantInfo merchant = merchantService.getMerchantBySn(modifyBankCard.getMerchantSn(), null);
            if (Objects.isNull(merchant)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
            }
            modifyBankCard.setMerchantId(merchant.getId());
            return this.getModifyBankCard(modifyBankCard);
        }
        throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_SN_OR_ID_NOT_BE_NULL_IN_SAME_TIME);
    }

    public BankCardResponseDTO getModifyBankCard(ModifyBankCardDTO modifyBankCard) {
        BankCardResponseDTO bankCardResponseDTO = new BankCardResponseDTO(modifyBankCard.getBankCardId());

        BankCardModule bankCardModule = bankCardDomainService.getBankCardModuleById(modifyBankCard.getBankCardId());
        if (Objects.isNull(bankCardModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_ADD_BANK_CARD);
        }
        if (bankCardModule.getStatus() == 1) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_CARD_ALREADY_ACTIVATED);
        }
        BizBankAccountRes bankAccountById = merchantBizBankAccountService.getBankAccountById(modifyBankCard.getBankCardId());
        if (Objects.isNull(bankAccountById)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        BizBankAccountUpdateReq bankAccountUpdateReq = JSON.parseObject(JSON.toJSONString(bankAccountById), BizBankAccountUpdateReq.class);
        if (StringUtils.isNotBlank(modifyBankCard.getBankCardNo())) {
            bankAccountUpdateReq.setNumber(modifyBankCard.getBankCardNo());
        }
        if (StringUtils.isNotBlank(modifyBankCard.getOpeningNumber())) {
            bankAccountUpdateReq.setOpening_number(modifyBankCard.getOpeningNumber());
        }
        if (StringUtils.isNotBlank(modifyBankCard.getHolder())) {
            bankAccountUpdateReq.setHolder(modifyBankCard.getHolder());
        }
        if (StringUtils.isNotBlank(modifyBankCard.getReservedMobileNumber())) {
            bankCardModule.setReservedMobileNumber(modifyBankCard.getReservedMobileNumber());
        }
        if (Objects.nonNull(modifyBankCard.getType())) {
            bankCardModule.setAccountType(modifyBankCard.getType());
            bankAccountUpdateReq.setType(modifyBankCard.getType());
        }
        bankCardModule.setBankOfDeposit("");
        bankCardDomainService.updateBankCard(bankCardModule);
        return bankCardResponseDTO;
    }

    /**
     * 获取银行卡详情
     *
     * @param bankCardId 银行卡id
     * @return 详情
     */
    public BankCardDetailResponseDTO getBankCardDetail(String bankCardId) {
        BankCardDetailResponseDTO bankCardDetailResponse = new BankCardDetailResponseDTO();
        BizBankAccountRes bankAccountById = merchantBizBankAccountService.getBankAccountById(bankCardId);
        if (Objects.isNull(bankAccountById)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        BankCardModule module = bankCardDomainService.getBankCardModuleById(bankCardId);
        if (Objects.isNull(module)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_ADD_BANK_CARD);
        }
        bankCardDetailResponse.setBankCardNo(bankAccountById.getNumber());
        bankCardDetailResponse.setBankCardId(bankAccountById.getId());
        bankCardDetailResponse.setType(bankAccountById.getType());
        bankCardDetailResponse.setOpeningNumber(bankAccountById.getOpeningNumber());
        bankCardDetailResponse.setReservedMobileNumber(module.getReservedMobileNumber());
        bankCardDetailResponse.setHolder(bankAccountById.getHolder());
        bankCardDetailResponse.setMerchantId(module.getMerchantId());
        bankCardDetailResponse.setBrandId(module.getBrandId());
        bankCardDetailResponse.setBankName(bankAccountById.getBankName());
        bankCardDetailResponse.setBranchName(bankAccountById.getBranchName());
        bankCardDetailResponse.setBankBackPicture(bankAccountById.getBankBack());
        bankCardDetailResponse.setBankIcon(bankAccountById.getBankIcon());
        bankCardDetailResponse.setActivateStatus(module.getStatus());
        bankCardDetailResponse.setActivateStatusDesc(module.getStatus() == 1 ? "已激活" : "未激活");
        bankCardDetailResponse.setDefaultCard(module.getIsDefault());
        bankCardDetailResponse.setActivateFailReason(module.getActivateFailReason());
        return bankCardDetailResponse;
    }

    public BankInfoResponseDTO getBankInfoByCardNumber(String cardNumber) {
        BankUnionCardReq bankUnionCardReq = new BankUnionCardReq();
        bankUnionCardReq.setCardNum(cardNumber);
        bankUnionCardReq.setPlatform("trade");
        bankUnionCardReq.setBusinessCase("brand_business");
        BankUnionCardVo bankUnionCardVo;
        try {
            bankUnionCardVo = infoQueryService.bankUnionCard(bankUnionCardReq);
        } catch (Exception e) {
            log.warn("获取银行卡信息失败,接口异常", e);
            throw new BrandBusinessException(BrandBusinessExceptionEnum.GET_BANK_INFO_FAIL);
        }
        if (Objects.isNull(bankUnionCardVo) || StringUtils.isBlank(bankUnionCardVo.getCnaps())) {
            log.info("获取银行卡信息失败,bankUnionCardVo为空或者开户行行号为空，{}", cardNumber);
            throw new BrandBusinessException(BrandBusinessExceptionEnum.GET_BANK_INFO_FAIL);
        }
        return new BankInfoResponseDTO(bankUnionCardVo.getBank(), bankUnionCardVo.getCardNum(), bankUnionCardVo.getCnaps(), bankUnionCardVo.getCardType(), bankUnionCardVo.getName());
    }
}
