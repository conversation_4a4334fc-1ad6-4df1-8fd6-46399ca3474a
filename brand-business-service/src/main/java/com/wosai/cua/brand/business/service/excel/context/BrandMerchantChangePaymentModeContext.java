package com.wosai.cua.brand.business.service.excel.context;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.BrandMerchantChangePaymentModeDTO;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
public class BrandMerchantChangePaymentModeContext extends AbstractImportContext {

    @Getter
    private BrandModule brandModule;
    @Getter
    private final BrandMerchantChangePaymentModeDTO brandMerchantChangePaymentModeDTO;

    private BrandMerchantChangePaymentModeContext(BrandTaskLogModule brandTaskLogModule) {
        super(brandTaskLogModule);
        this.brandMerchantChangePaymentModeDTO = JSON.parseObject(JSON.toJSONString(getFormData()), BrandMerchantChangePaymentModeDTO.class);
    }

    public static BrandMerchantChangePaymentModeContext newInstance(BrandTaskLogModule brandTaskLogModule) {
        return new BrandMerchantChangePaymentModeContext(brandTaskLogModule);
    }

    public void bindBrandModule(BrandModule brandModule) {
        this.brandModule = brandModule;
    }

}
