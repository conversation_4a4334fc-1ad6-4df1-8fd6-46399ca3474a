package com.wosai.cua.brand.business.service.externalservice.coreb.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@Data
@Accessors(chain = true)
public class MerchantFindRequest {

    private List<String> merchantIds;
    private List<String> merchantSns;
    private String merchantSn;
    private String merchantName;
    private String merchantBusinessName;
    private String contactCellphone;
}
