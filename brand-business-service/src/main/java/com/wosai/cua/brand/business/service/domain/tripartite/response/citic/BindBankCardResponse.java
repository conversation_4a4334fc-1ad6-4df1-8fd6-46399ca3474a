package com.wosai.cua.brand.business.service.domain.tripartite.response.citic;

import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BindBankCardResponse extends TripartiteSystemCallResponse {
    /**
     * 流水号
     */
    private String serialNumber;
    /**
     * 返回行内动态口令服务器生成的动态密码句柄，调短信验证接口时需要上送，用于校验短信验证码
     */
    private String pwdId;

    /**
     * 返回e管家生成的用户基本信息临时表的修改序号，调短信验证接口时需要上送，用于校验申请时提交的银行卡相关信息
     */
    private String transId;
}
