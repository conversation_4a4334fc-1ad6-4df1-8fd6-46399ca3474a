package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.card;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

/**
 * 接口说明：
 */
@Data
public class ModifyAccountInCardRequest implements TripartiteSystemCallRequest {

    public static final String MODIFY_ACCOUNT_IN_CARD_URL = "/modifyAccountInCard.fuiou";

    private ModifyAccountInCardRequestBody body;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_MODIFY_ACCOUNT_IN_CARD;
    }
}
