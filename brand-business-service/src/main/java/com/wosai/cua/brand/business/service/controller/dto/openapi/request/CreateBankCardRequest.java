package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CreateBankCardRequest extends OpenApiBaseRequest{

    /**
     * 商户编号
     */
    @NotBlank(message = "商户编号merchantSn必传")
    @JsonProperty("merchant_sn")
    private String merchantSn;
    /**
     * 账户类型 1 个人账户 2 企业账户
     */
    @NotNull(message = "type不能为空")
    private Integer type;
    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号bankCardNo不能为空")
    @JsonProperty("bank_card_no")
    private String bankCardNo;

    /**
     * 开户行号
     */
    @NotBlank(message = "开户行号openingNumber，不能为空")
    @JsonProperty("opening_number")
    private String openingNumber;

    /**
     * 银行预留手机号
     */
    @NotBlank(message = "银行预留手机号不能为空")
    @JsonProperty("reserved_mobile_number")
    private String reservedMobileNumber;

    /**
     * 账户持有人名称
     */
    @NotBlank(message = "账户持有人名称holder不能为空")
    private String holder;

    /**
     * 账户持有人证件类型：1 身份证；2 港澳居民来往内地通行证； 3 台湾居民来往大陆通行证； 4 非中华人民共和国护照； 5 中国护照
     */
    @JsonProperty("id_type")
    private Integer idType;

    /**
     * 账户持有人证件编号
     */
    private String identity;

    /**
     * 是否设置为默认卡
     */
    @JsonProperty("set_default")
    private Boolean setDefault;
}
