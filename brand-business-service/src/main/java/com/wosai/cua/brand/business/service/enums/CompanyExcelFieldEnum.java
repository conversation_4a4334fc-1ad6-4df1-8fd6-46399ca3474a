package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum CompanyExcelFieldEnum {
    /**
     *
     */
    BRAND_MERCHANT_TYPE(0,"brandMerchantType","未填写或填写错误品牌商户类型！",true, false, "", ""),
    /**
     *
     */
    COMPANY_NAME(1,"companyName","未填写公司名称！",true, false, "", ""),
    /**
     *
     */
    LICENSE_TYPE(2,"licenseType","未填写企业证照类型或企业证照类型填写错误！",true, false, "", ""),
    /**
     *
     */
    LICENSE_NUMBER(3,"licenseNumber","未填写企业证件号码",true, false, "", ""),
    /**
     *
     */
    PROVINCE(4,"province","未填写企业所在省份",true, false, "", ""),
    /**
     *
     */
    CITY(5,"city","未填写企业所在市",true, false, "", ""),
    /**
     *
     */
    DISTRICT(6,"district","",false, false, "", ""),
    /**
     *
     */
    CONTACT_NAME(7,"contactName","未填写联系人姓名",true, false, "", ""),
    /**
     *
     */
    CONTACT_PHONE(8,"contactPhone","未填写联系人手机号",true, true, "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$", "联系人手机号码格式不正确"),
    /**
     *
     */
    CONTACT_LICENSE_TYPE(9,"personalLicenseType","未填写或填写错误的联系人证件类型",true, false, "", ""),
    /**
     *
     */
    CONTACT_PERSONAL_ID(10,"personalId","未填写企业联系人的证件号",true, false, "", ""),
    /**
     *
     */
    CONTACT_EMAIL(11,"contactEmail","",false, true, "^[0-9A-Za-z_]+([-+.][0-9A-Za-z_]+)*@[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*\\.[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*$", "邮箱格式不正确"),
    /**
     *
     */
    ADDRESS(12,"address","未填写企业所在街道地址",true, false, "", ""),
    /**
     *
     */
    BANK_NAME(13,"bankName","银行卡开户行未填写",true, false, "", ""),
    /**
     *
     */
    CORRESPONDENT_NUMBER(14,"openingNumber","银行卡开户行联行号未填写",false, false, "", ""),
    /**
     *
     */
    BANK_ACCOUNT_NAME(15,"bankAccountName","银行卡的对公账户名称未填写",true, false, "", ""),
    /**
     *
     */
    BANK_NUMBER(16,"bankNumber","银行卡卡号未填写",true, false, "", ""),
    /**
     *
     */
    CELL_PHONE_NUMBER(17,"cellPhoneNumber","预留手机号未填写",true, true, "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$", "预留手机号码格式不正确"),
    /**
     *
     */
    LEGAL_PERSON_NAME(18,"legalPersonName","法人姓名未填写",true, false, "", ""),
    /**
     *
     */
    LEGAL_PERSON_LICENSE_TYPE(19,"legalPersonLicenseType","法人证件类型未填写或未填写正确内容",true, false, "", ""),
    /**
     *
     */
    LEGAL_PERSON_LICENSE_NUMBER(20,"legalPersonLicenseNumber","法人证件号未填写",true, false, "", ""),
    /**
     *
     */
    STORE_NAME(21,"storeSn","门店编号未填写",true, false, "", ""),
    /**
     *
     */
    MEI_TUAN_STORE_SN(22,"meiTuanStoreSn","",false, false, "", ""),
    /**
     *
     */
    ELM_STORE_SN(23,"elmStoreSn","",false, false, "", ""),

    DY_STORE_SN(24,"dyStoreSn","",false, false, "", ""),
    /**
     *
     */
    OUT_MERCHANT_ID(25,"outMerchantNo","",false, false, "", ""),
    ;

    private final int columnNo;

    private final String fieldName;

    private final String checkMessage;

    private final boolean needRequired;

    private final boolean needFormatCheck;

    private final String regex;

    private final String formatCheckMsg;


    CompanyExcelFieldEnum(int columnNo, String fieldName, String checkMessage, boolean needRequired, boolean needFormatCheck, String regex, String formatCheckMsg) {
        this.columnNo = columnNo;
        this.fieldName = fieldName;
        this.checkMessage = checkMessage;
        this.needRequired = needRequired;
        this.needFormatCheck = needFormatCheck;
        this.regex = regex;
        this.formatCheckMsg = formatCheckMsg;
    }

    public int getColumnNo() {
        return columnNo;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getCheckMessage() {
        return checkMessage;
    }

    public boolean isNeedRequired() {
        return needRequired;
    }
}
