package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * API基础响应信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class MybankResponse extends TripartiteSystemCallResponse implements Serializable {

    private static final long serialVersionUID = 2291511131835459881L;

    private String xmlContent;

    private String requestContent;
}