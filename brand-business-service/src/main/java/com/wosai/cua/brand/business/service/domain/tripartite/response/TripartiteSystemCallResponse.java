package com.wosai.cua.brand.business.service.domain.tripartite.response;

import lombok.Data;
import lombok.Getter;

@Data
public class TripartiteSystemCallResponse {
    /**
     * 调用结果
     */
    protected ResultCodeEnum resultCode;

    /**
     * 调用结果描述
     */
    protected String resultMsg;

    public boolean isSuccess() {
        return ResultCodeEnum.SUCCESS.equals(resultCode);
    }

    @Getter
    public enum ResultCodeEnum {
        SUCCESS("SUCCESS", "S","调用成功"),
        FAIL("FAIL", "F","调用失败"),
        UNKNOWN("UNKNOWN","U","未知");

        private final String code;
        private final String myBankCode;
        private final String desc;

        ResultCodeEnum(String code, String myBankCode, String desc) {
            this.code = code;
            this.myBankCode = myBankCode;
            this.desc = desc;
        }

        public static ResultCodeEnum getByCode(String code) {
            for (ResultCodeEnum resultCodeEnum : ResultCodeEnum.values()) {
                if (resultCodeEnum.getCode().equals(code)) {
                    return resultCodeEnum;
                }
            }
            return UNKNOWN;
        }

        public static ResultCodeEnum getByMyBankCode(String myBankCode) {
            for (ResultCodeEnum resultCodeEnum : ResultCodeEnum.values()) {
                if (resultCodeEnum.getMyBankCode().equals(myBankCode)) {
                    return resultCodeEnum;
                }
            }
            return UNKNOWN;
        }
    }
}
