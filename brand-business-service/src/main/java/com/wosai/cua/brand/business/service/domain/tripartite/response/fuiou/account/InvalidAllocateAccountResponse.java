package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class InvalidAllocateAccountResponse extends TripartiteSystemCallResponse {

    private InvalidAllocateAccountResponseBody body;
}
