package com.wosai.cua.brand.business.service.module.common.industry;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class IndustryModule {

    /**
     * id : 7a47fbdc-312d-11e6-aebb-ecf4bbdee2f0
     * level1 : 医疗健康
     * level2 : 药店/药房/医疗器械
     * level3 :
     * level4 :
     * depth : 2
     * code1 : 004
     * code2 : 004010
     * version : 84
     * status : 1
     * need_license : 1
     * license_types : [1,2]
     * need_trade_license : 1
     * trade_license_types : [15,45]
     * level1_sort : 7
     * level2_sort : 57
     * store_types : 1
     * mtime : 1707150660590
     * trade_license_mapping : [{"id":15,"name":"药品经营许可证","status":1,"ctime":1586252074634,"mtime":1586252074634,"version":1},{"id":45,"name":"医疗器械许可证","status":1,"ctime":1684749093601,"mtime":1684749093601,"version":1}]
     */

    private String id;

    private String level1;

    private String level2;

    private String level3;

    private String level4;

    private Integer depth;

    private String code1;

    private String code2;

    private Integer version;

    private Integer status;

    @JSONField(name = "need_license")
    private Integer needLicense;

    @JSONField(name = "need_trade_license")
    private Integer needTradeLicense;

    @JSONField(name = "level1_sort")
    private Integer level1Sort;

    @JSONField(name = "level2_sort")
    private Integer level2Sort;

    @JSONField(name = "store_types")
    private String storeTypes;

    private Long mtime;

    @JSONField(name = "license_types")
    private List<Integer> licenseTypes;

    @JSONField(name = "trade_license_types")
    private List<Integer> tradeLicenseTypes;

    @JSONField(name = "trade_license_mapping")
    private List<TradeLicenseMappingModule> tradeLicenseMapping;
}
