package com.wosai.cua.brand.business.service.helper;

import cn.hutool.core.util.ZipUtil;
import com.wosai.common.exception.CommonIOException;
import com.wosai.common.utils.WosaiStringUtils;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.tools.tar.TarEntry;
import org.apache.tools.tar.TarInputStream;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;
import org.apache.tools.zip.ZipOutputStream;
import org.springframework.util.StringUtils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.zip.GZIPInputStream;

/**
 *
 */
@Slf4j
public class ZipHelper {

    private static final byte[] _byte = new byte[1024];


    /**
     * 将存放在sourceFilePath目录下的源文件，打包成fileName名称的zip文件，并存放到zipFilePath路径下
     *
     * @param sourceFilePath :待压缩的文件路径
     * @param zipFilePath    :压缩后存放路径
     * @param fileName       :压缩后文件的名称
     * @return
     */
    public static boolean fileToZip(String sourceFilePath, String zipFilePath, String fileName) {
        boolean flag = false;
        File sourceFile = new File(sourceFilePath);
        FileInputStream fis;
        BufferedInputStream bis = null;
        FileOutputStream fos;
        ZipOutputStream zos = null;

        if (!sourceFile.exists()) {
            log.warn("待压缩的文件目录：{}不存在。", sourceFilePath);
        } else {
            try {
                File zipFile = new File(zipFilePath + "/" + fileName + ".zip");
                if (zipFile.exists()) {
                    log.info(zipFilePath + "目录下存在名字为:" + fileName + ".zip" + "打包文件.");
                } else {
                    File[] sourceFiles = sourceFile.listFiles();
                    if (null == sourceFiles || sourceFiles.length < 1) {
                        log.info("待压缩的文件目录：" + sourceFilePath + "里面不存在文件，无需压缩.");
                    } else {
                        fos = new FileOutputStream(zipFile);
                        zos = new ZipOutputStream(new BufferedOutputStream(fos));
                        byte[] buf = new byte[1024 * 10];
                        for (File file : sourceFiles) {
                            //创建ZIP实体，并添加进压缩包
                            ZipEntry zipEntry = new ZipEntry(file.getName());
                            zos.putNextEntry(zipEntry);
                            //读取待压缩的文件并写进压缩包里
                            fis = new FileInputStream(file);
                            bis = new BufferedInputStream(fis, 1024 * 10);
                            int read = 0;
                            while ((read = bis.read(buf, 0, 1024 * 10)) != -1) {
                                zos.write(buf, 0, read);
                            }
                        }
                        flag = true;
                    }
                }
            } catch (IOException e) {
                log.warn("ZipHelper IOException ", e);
                return false;
            } finally {
                //关闭流
                try {
                    if (null != bis) {
                        bis.close();
                    }
                    if (null != zos) {
                        zos.close();
                    }
                } catch (IOException ignored) {
                    log.warn("ZipHelper IOException ", ignored);
                }
            }
        }
        return flag;
    }


    /**
     * 压缩文件或路径
     *
     * @param zip      压缩的目的地址
     * @param srcFiles 压缩的源文件
     */
    public static void zipFile(String zip, List<File> srcFiles) {
        try {
            if (zip.endsWith(".zip") || zip.endsWith(".ZIP")) {
                ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(new File(zip).toPath()));
                zipOut.setEncoding("GBK");
                for (File f : srcFiles) {
                    handlerFile(zip, zipOut, f, "");
                }
                zipOut.close();
            } else {
                log.info("target file[" + zip + "] is not .zip type file");
            }
        } catch (IOException e) {
            log.info("zipFile exception:", e);
        }
    }

    /**
     * @param zip     压缩的目的地址
     * @param zipOut
     * @param srcFile 被压缩的文件信息
     * @param path    在zip中的相对路径
     * @throws IOException
     */
    private static void handlerFile(String zip, ZipOutputStream zipOut, File srcFile, String path) throws IOException {
        log.info(" begin to compression file[" + srcFile.getName() + "]");
        if (!"".equals(path) && !path.endsWith(File.separator)) {
            path += File.separator;
        }
        if (!srcFile.getPath().equals(zip)) {
            if (srcFile.isDirectory()) {
                File[] files = srcFile.listFiles();
                if (files.length == 0) {
                    zipOut.putNextEntry(new ZipEntry(path + srcFile.getName() + File.separator));
                    zipOut.closeEntry();
                } else {
                    for (File f : files) {
                        handlerFile(zip, zipOut, f, path + srcFile.getName());
                    }
                }
            } else {
                InputStream inputStream = Files.newInputStream(srcFile.toPath());
                zipOut.putNextEntry(new ZipEntry(path + srcFile.getName()));
                int len;
                while ((len = inputStream.read(_byte)) > 0) {
                    zipOut.write(_byte, 0, len);
                }
                inputStream.close();
                zipOut.closeEntry();
            }
        }
    }


    /**
     * 解压.gz文件
     *
     * @param sourcedir
     */
    public static void unGzipFile(String sourcedir) {
        String ouputfile = "";
        //建立gzip压缩文件输入流
        FileInputStream fin = null;
        GZIPInputStream gzin = null;
        try {
            //建立gzip压缩文件输入流
            fin = new FileInputStream(sourcedir);
            //建立gzip解压工作流
            gzin = new GZIPInputStream(fin);
            //建立解压文件输出流
            ouputfile = sourcedir.substring(0, sourcedir.lastIndexOf('.'));
            ouputfile = ouputfile.substring(0, ouputfile.lastIndexOf('.'));
            FileOutputStream fout = new FileOutputStream(ouputfile);

            int num;
            byte[] buf = new byte[1024];

            while ((num = gzin.read(buf, 0, buf.length)) != -1) {
                fout.write(buf, 0, num);
            }

            gzin.close();
            fout.close();
            fin.close();
        } catch (Exception ex) {
            log.warn("unGzipFile exception", ex);
        } finally {
            try {
                if (Objects.nonNull(fin)){
                    fin.close();
                }
                if (Objects.nonNull(gzin)){
                    gzin.close();
                }
            } catch (IOException e) {
                log.warn("关闭错误。",e);
            }
        }
    }


    /**
     * 解压tar.gz 文件
     *
     * @param file      要解压的tar.gz文件对象
     * @param outputDir 要解压到某个指定的目录下
     * @throws IOException
     */
    public static void unTarGz(File file, String outputDir) throws IOException {
        TarInputStream tarIn = null;
        try {
            tarIn = new TarInputStream(new GZIPInputStream(
                    new BufferedInputStream(new FileInputStream(file))),
                    1024 * 2);

            //创建输出目录
            createDirectory(outputDir, null);

            TarEntry entry = null;
            while ((entry = tarIn.getNextEntry()) != null) {

                if (entry.isDirectory()) {
                    entry.getName();
                    //创建空目录
                    createDirectory(outputDir, entry.getName());
                } else {//是文件
                    File tmpFile = new File(outputDir + "/" + entry.getName());
                    //创建输出目录
                    createDirectory(tmpFile.getParent() + "/", null);
                    OutputStream out = null;
                    try {
                        out = Files.newOutputStream(tmpFile.toPath());
                        int length = 0;

                        byte[] b = new byte[2048];

                        while ((length = tarIn.read(b)) != -1) {
                            out.write(b, 0, length);
                        }

                    } catch (IOException ex) {
                        throw ex;
                    } finally {
                        if (out != null) {
                            out.close();
                        }
                    }
                }
            }
        } catch (IOException ex) {
            throw new IOException("解压归档文件出现异常", ex);
        } finally {
            try {
                if (tarIn != null) {
                    tarIn.close();
                }
            } catch (IOException ex) {
                throw new IOException("关闭tarFile出现异常", ex);
            }
        }
    }


    /**
     * 构建目录
     *
     * @param outputDir
     * @param subDir
     */
    public static void createDirectory(String outputDir, String subDir) {
        File file = new File(outputDir);
        //子目录不为空
        if (!(subDir == null || subDir.trim().equals(""))) {
            file = new File(outputDir + "/" + subDir);
        }
        if (!file.exists()) {
            if (!file.getParentFile().exists())
                file.getParentFile().mkdirs();
            file.mkdirs();
        }
    }

    /**
     * 解压缩ZIP文件，将ZIP文件里的内容解压到targetDIR目录下
     *
     * @param zipPath 待解压缩的ZIP文件名
     * @param descDir 目标目录
     */
    public static List<File> upzipFile(String zipPath, String descDir) throws IOException {
        return upzipFile(new File(zipPath), descDir);
    }

    /**
     * 对.zip文件进行解压缩
     *
     * @param zipFile 解压缩文件
     * @param descDir 压缩的目标地址，如：D:\\测试 或 /mnt/d/测试
     * @return
     */
    @SuppressWarnings("rawtypes")
    private static List<File> upzipFile(File zipFile, String descDir) throws IOException {
        List<File> _list = new ArrayList<File>();
        ZipFile _zipFile = new ZipFile(zipFile, "GBK");
        for (Enumeration entries = _zipFile.getEntries(); entries.hasMoreElements(); ) {
            ZipEntry entry = (ZipEntry) entries.nextElement();
            File _file = new File(descDir + File.separator + entry.getName());
            if (entry.isDirectory()) {
                _file.mkdirs();
            } else {
                File _parent = _file.getParentFile();
                if (!_parent.exists()) {
                    _parent.mkdirs();
                }
                InputStream _in = _zipFile.getInputStream(entry);
                OutputStream _out = new FileOutputStream(_file);
                int len = 0;
                while ((len = _in.read(_byte)) > 0) {
                    _out.write(_byte, 0, len);
                }
                _in.close();
                _out.flush();
                _out.close();
                _list.add(_file);
            }
        }

        return _list;
    }

    /**
     * 对临时生成的文件夹和文件夹下的文件进行删除
     */
    public static void deletefile(String delpath) {
        File file = new File(delpath);
        if (!file.isDirectory()) {
            file.delete();
        } else if (file.isDirectory()) {
            String[] filelist = file.list();
            for (int i = 0; i < filelist.length; i++) {
                File delfile = new File(delpath + File.separator + filelist[i]);
                if (!delfile.isDirectory()) {
                    delfile.delete();
                } else if (delfile.isDirectory()) {
                    deletefile(delpath + File.separator + filelist[i]);
                }
            }
            file.delete();
        }

    }

    public static File unzip(String url) {
        if (WosaiStringUtils.isEmpty(url)) {
            return null;
        }
        String format;
        if (url.contains("?")) {
            format = FilenameUtils.getExtension(url.substring(0, url.lastIndexOf("?")));
        } else {
            format = FilenameUtils.getExtension(url);
        }
        File file = null;
        try {
            file = File.createTempFile(UUID.randomUUID().toString(), "." + format);
            FileUtils.copyURLToFile(new URL(url), file, 15000, 15000);
            return ZipUtil.unzip(file);
        } catch (IOException e) {
            log.error("文件解压缩失败: {}", url, e);
            throw new CommonIOException("文件解压缩失败");
        } finally {
            FileUtils.deleteQuietly(file);
        }
    }

    public static File zipPic(File file, int mb) throws IOException {
        return zipPic(file, mb * 1000L * 1000L, 0.8);
    }

    public static File zipPic(File file, long fileSize, double scale) throws IOException {
        String format = FilenameUtils.getExtension(file.getName());
        if (StringUtils.isEmpty(format)) {
            format = "jpg";
        }
        File tmpFile;
        //压缩图片
        int count = 0;
        while (file.length() > fileSize) {
            //设置循环次数防止死循环
            if (count > 30) {
                return file;
            }
            tmpFile = File.createTempFile(UUID.randomUUID().toString(), "." + format);
            try {
                Thumbnails.of(file).scale(scale).outputFormat(format).toFile(tmpFile);
            } catch (Exception e) {
                //压缩失败就不压缩了
                FileUtils.deleteQuietly(tmpFile);
                return file;
            }
            FileUtils.deleteQuietly(file);
            file = tmpFile;
            log.info("tmpfile:{}", file.length());
            count++;
        }
        return file;
    }
}
