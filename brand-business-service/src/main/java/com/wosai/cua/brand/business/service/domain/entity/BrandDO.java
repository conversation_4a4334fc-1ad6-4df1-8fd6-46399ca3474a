package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "brand")
public class BrandDO {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id
     */
    @TableField(value = "brand_id")
    private String brandId;

    /**
     * 品牌编号
     */
    private String sn;

    /**
     * 集团ID
     */
    @TableField(value = "group_id")
    private String groupId;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌简称
     */
    private String alias;

    /**
     * 商户编号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;

    /**
     * 对接方式：INTERFACE-接口，CONFIGURATION-配置
     */
    @TableField(value = "docking_mode")
    private String dockingMode;

    /**
     * 父品牌id
     */
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 行业
     */
    private String industry;

    /**
     * 联系人姓名
     */
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @TableField(value = "contact_phone")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @TableField(value = "contact_email")
    private String contactEmail;


    /**
     * 合作方id
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_config表中
     */
    @Deprecated
    @TableField(value = "partner_id")
    private String partnerId;

    /**
     * 资管机构编号，MY_BANK-网商银行
     */
    @TableField(value = "fund_management_company_code")
    private String fundManagementCompanyCode;
    /**
     * 资管机构，PAB-平安银行
     */
    @TableField(value = "fund_management_company")
    private String fundManagementCompany;

    /**
     * 资金汇总专用账名
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    @TableField(value = "fund_summary_account_name")
    private String fundSummaryAccountName;

    /**
     * 资金汇总专用账户
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    @TableField(value = "fund_summary_account")
    private String fundSummaryAccount;

    /**
     * 资金归集账户
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    @TableField(value = "fund_gather_account")
    private String fundGatherAccount;

    /**
     * 资金结算账户名称
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    @TableField(value = "fund_settlement_account_name")
    private String fundSettlementAccountName;

    /**
     * 资金结算账户
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在brand_account表中
     */
    @Deprecated
    @TableField(value = "fund_settlement_account")
    private String fundSettlementAccount;

    /**
     * 品牌层级
     */
    private Integer level;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态：0:关闭  1: 正常  2:禁用
     */
    private Integer status;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private Date updatedTime;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 是否开通收付通：0-否，1-是
     */
    @TableField(value = "sft_tag")
    private Integer sftTag;

    /**
     * 是否启用收付通：0-否，1-是
     */
    @TableField(value = "enable_sft")
    private Integer enableSft;
}