package com.wosai.cua.brand.business.service.module.account.mybank;

import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.mybank.MyBankAccountsDTO;
import com.wosai.cua.brand.business.service.module.account.AccountsModule;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class MyBankAccountModule extends AccountsModule {
    /**
     * 订单账款管理专户（网商）
     */
    private String orderManagementAccount;
    /**
     * 收入结算账户（网商）
     */
    private String incomeSettlementAccount;
    /**
     * 支出结算账户（网商）
     */
    private String expenditureSettlementAccount;

    /**
     * 营销金专户（网商）
     */
    private String marketingFundAccount;

    public static MyBankAccountModule convert(MyBankAccountsDTO account) {
        if (account == null) {
            return null;
        }
        MyBankAccountModule myBankAccountModule = new MyBankAccountModule();
        myBankAccountModule.setOrderManagementAccount(account.getOrderManagementAccount());
        myBankAccountModule.setIncomeSettlementAccount(account.getIncomeSettlementAccount());
        myBankAccountModule.setExpenditureSettlementAccount(account.getExpenditureSettlementAccount());
        myBankAccountModule.setMarketingFundAccount(account.getMarketingFundAccount());
        return myBankAccountModule;
    }

    public static AccountsDTO convertDto(MyBankAccountModule accountsModule) {
        if (accountsModule == null) {
            return null;
        }
        AccountsDTO accounts = new AccountsDTO();
        MyBankAccountsDTO myBankAccounts =  new MyBankAccountsDTO();
        myBankAccounts.setExpenditureSettlementAccount(accountsModule.getExpenditureSettlementAccount());
        myBankAccounts.setMarketingFundAccount(accountsModule.getMarketingFundAccount());
        myBankAccounts.setIncomeSettlementAccount(accountsModule.getIncomeSettlementAccount());
        myBankAccounts.setOrderManagementAccount(accountsModule.getOrderManagementAccount());
        accounts.setMyBankAccounts(myBankAccounts);
        return accounts;
    }
}
