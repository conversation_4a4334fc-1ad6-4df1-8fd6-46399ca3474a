package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify.MerchantArrangementInfoNotifyModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class MerchantArrangementInfoNotifyRequest extends MybankResponse {


    private static final long serialVersionUID = -7615224846715082893L;

    @XmlElementRef
    private MerchantprodMerchantArrangementInfoNotify merchantprodMerchantArrangementInfoNotify;

    public MerchantprodMerchantArrangementInfoNotify getMerchantprodMerchantArrangementInfoNotify() {
        return merchantprodMerchantArrangementInfoNotify;
    }

    public void setMerchantprodMerchantArrangementInfoNotify(MerchantprodMerchantArrangementInfoNotify merchantprodMerchantArrangementInfoNotify) {
        this.merchantprodMerchantArrangementInfoNotify = merchantprodMerchantArrangementInfoNotify;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "request")
    public static class MerchantprodMerchantArrangementInfoNotify extends MybankObject {

        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private RequestHead requestHead;


        @XmlElementRef
        private MerchantArrangementInfoNotifyModel merchantprodMerchantArrangementInfoNotifyModel;


        public RequestHead getRequestHead() {
            return requestHead;
        }

        public void setRequestHead(RequestHead requestHead) {
            this.requestHead = requestHead;
        }

        public MerchantArrangementInfoNotifyModel getMerchantprodMerchantArrangementInfoNotifyModel() {
            return merchantprodMerchantArrangementInfoNotifyModel;
        }

        public void setMerchantprodMerchantArrangementInfoNotifyModel(MerchantArrangementInfoNotifyModel merchantprodMerchantArrangementInfoNotifyModel) {
            this.merchantprodMerchantArrangementInfoNotifyModel = merchantprodMerchantArrangementInfoNotifyModel;
        }
    }

}
