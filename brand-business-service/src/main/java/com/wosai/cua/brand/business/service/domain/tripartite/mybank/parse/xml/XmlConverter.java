package com.wosai.cua.brand.business.service.domain.tripartite.mybank.parse.xml;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.enums.MybankApiExceptionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping.Converter;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.JaxbUtil;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.XmlUtils;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

/**
 * XML格式转换器
 */
public class XmlConverter implements Converter {

    private XmlConverter() {
    }

    public static XmlConverter getInstance() {
        return XmlConverterInstance.instance;
    }

    @Override
    public <T extends MybankResponse> T toResponse(String responseXml, Class<T> clazz) throws MybankApiException {
        if (!XmlUtils.isXmlDocument(responseXml))
            throw new MybankApiException(MybankApiExceptionEnum.NOT_XML_TYPE);
        return getModelFromXML(responseXml, clazz);
    }

    private static class XmlConverterInstance {

        static XmlConverter instance = new XmlConverter();

    }

    private <T> T getModelFromXML(String responseXml, Class<T> clazz) throws MybankApiException {
        return JaxbUtil.convertToJavaBean(responseXml, clazz);
    }
}