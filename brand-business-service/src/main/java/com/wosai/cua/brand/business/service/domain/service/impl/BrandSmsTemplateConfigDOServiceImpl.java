package com.wosai.cua.brand.business.service.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wosai.cua.brand.business.api.enums.SmsTemplateMethodEnum;
import com.wosai.cua.brand.business.service.domain.entity.BrandSmsTemplateConfigDO;
import com.wosai.cua.brand.business.service.domain.service.BrandSmsTemplateConfigDOService;
import com.wosai.cua.brand.business.service.domain.dao.BrandSmsTemplateConfigDOMapper;
import com.wosai.cua.brand.business.service.module.config.TemplateConfigModule;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【brand_sms_template_config(品牌短信模板配置表)】的数据库操作Service实现
 * @createDate 2025-01-14 09:55:09
 */
@Service
public class BrandSmsTemplateConfigDOServiceImpl
        implements BrandSmsTemplateConfigDOService {

    private final BrandSmsTemplateConfigDOMapper brandSmsTemplateConfigMapper;

    @Autowired
    public BrandSmsTemplateConfigDOServiceImpl(BrandSmsTemplateConfigDOMapper brandSmsTemplateConfigMapper) {
        this.brandSmsTemplateConfigMapper = brandSmsTemplateConfigMapper;
    }

    @Override
    public void saveBrandSmsTemplateConfig(String brandId, List<TemplateConfigModule> templateConfigModules) {
        List<BrandSmsTemplateConfigDO> brandSmsTemplateConfigList = TemplateConfigModule.getBrandSmsTemplateConfigDOList(brandId, templateConfigModules);
        brandSmsTemplateConfigList.forEach(r -> {
            LambdaQueryWrapper<BrandSmsTemplateConfigDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BrandSmsTemplateConfigDO::getBrandId, r.getBrandId());
            queryWrapper.eq(BrandSmsTemplateConfigDO::getTemplateCode, r.getTemplateCode());
            BrandSmsTemplateConfigDO brandSmsTemplateConfigDO = brandSmsTemplateConfigMapper.selectOne(queryWrapper);
            if (brandSmsTemplateConfigDO == null) {
                r.setTextContent(Objects.isNull(r.getTextContent()) ? "" : r.getTextContent());
                brandSmsTemplateConfigMapper.insert(r);
            } else {
                r.setId(brandSmsTemplateConfigDO.getId());
                r.setTextContent(Objects.isNull(r.getTextContent()) ? "" : r.getTextContent());
                r.setDeleted(0);
                brandSmsTemplateConfigMapper.updateById(r);
            }
        });
    }

    @Override
    public List<TemplateConfigModule> getBrandSmsTemplateConfig(String brandId, SmsTemplateMethodEnum method, List<String> templateCode) {
        LambdaQueryWrapper<BrandSmsTemplateConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandSmsTemplateConfigDO::getBrandId, brandId);
        if (CollectionUtils.isNotEmpty(templateCode)) {
            queryWrapper.in(BrandSmsTemplateConfigDO::getTemplateCode, templateCode);
        }
        if (Objects.nonNull(method)) {
            queryWrapper.eq(BrandSmsTemplateConfigDO::getMethod, method.getMethod());
        }
        queryWrapper.eq(BrandSmsTemplateConfigDO::getDeleted, 0);
        return TemplateConfigModule.convertTemplateModuleByDo(brandSmsTemplateConfigMapper.selectList(queryWrapper));
    }
}




