package com.wosai.cua.brand.business.service.module.brand;

import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class ChangeBrandMerchantModule {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 父品牌id
     */
    private String parentBrandId;

    /**
     * 商户id
     */
    private String merchantId;

    private String ucUserId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户sn
     */
    private String merchantSn;

    /**
     * 商户类型：
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum
     */
    private String type;

    /**
     * 商户角色：FRANCHISEE-加盟商、SUPPLIER-供应商、BRAND_OWNER-品牌商、BRAND_OPERATED_STORES-品牌自营门店、SERVICE_PROVIDER_SQB-服务商（收钱吧）
     * @see MerchantTypeEnum
     */
    private String merchantType;

    /**
     * 子账号
     */
    private String subAccountNo;

    /**
     * 系统会员id
     */
    private String memberId;

    /**
     *
     */
    private String topUpAccountNo;

    private String topUpAccountName;

    /**
     * 关联品牌商户门店编号
     */
    private String associatedSqbStoreId;

    private String sqbStoreSn;

    /**
     * 关联美团门店号
     */
    private String associatedMeituanStoreSn;

    /**
     * 关联饿了么门店号
     */
    private String associatedElmStoreSn;

    /**
     * 抖音门店号
     */
    private String dyStoreSn;

    /**
     * 外部商户号
     */
    private String outMerchantNo;

   /**
    * 第三方商户编号
    */
   private String threePartyMerchantSn;

    private Long strategyId;

    private String accountOpenStatus;

    private String accountOpenFailureReason;

    /**
     * 额外信息
     */
    private String extra;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;

    private String bankCardActivateStatus;

    private Date accountOpenedTime;
}
