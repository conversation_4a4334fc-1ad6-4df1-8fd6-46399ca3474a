package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.common.dictionary.DictionaryModule;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DictionaryDomainService {
    /**
     * 获取字典名称
     * @return 字典名称
     */
    String dictionary();

    /**
     * 返回字典模型
     * @return 字典模型集合
     */
    List<DictionaryModule> getDictionaryModules();
}
