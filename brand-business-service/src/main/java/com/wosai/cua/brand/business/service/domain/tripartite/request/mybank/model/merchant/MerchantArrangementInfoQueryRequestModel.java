package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.CDataAdapter;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 *ant.mybank.merchantprod.merchant.arrangement.info.query
 * */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name ="body")
public class MerchantArrangementInfoQueryRequestModel extends RequestBody {


    private static final long serialVersionUID = 6290109975518976390L;

    /**
     * 合作方机构号
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;
    /**
     * 商户号
     * <p>
     * 网商为商户分配的商户号，通过商户入驻结果查询接口获取。
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MerchantId")
    private String merchantId;

    /***
     * 合约类型
     * */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "ArrangementType")
    private String arrangementType;


    public String getIsvOrgId() {
        return isvOrgId;
    }

    public void setIsvOrgId(String isvOrgId) {
        this.isvOrgId = isvOrgId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getArrangementType() {
        return arrangementType;
    }

    public void setArrangementType(String arrangementType) {
        this.arrangementType = arrangementType;
    }
}