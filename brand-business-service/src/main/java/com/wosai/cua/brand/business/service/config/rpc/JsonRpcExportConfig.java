package com.wosai.cua.brand.business.service.config.rpc;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Configuration
public class JsonRpcExportConfig {

    /**
     * Json-rpc
     *
     * @return
     */
    @Bean
    public static AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter() {
        AutoJsonRpcServiceImplExporter exp = new AutoJsonRpcServiceImplExporter();
        exp.setInvocationListener(new JsonRpcLogAspect());
        exp.setErrorResolver(new JsonRpcErrorResolver());
        return exp;
    }
}
