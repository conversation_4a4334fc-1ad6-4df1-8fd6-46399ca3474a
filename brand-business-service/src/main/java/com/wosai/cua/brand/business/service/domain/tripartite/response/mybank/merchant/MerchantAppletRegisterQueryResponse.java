package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.MerchantAppletRegisterQueryResponseModel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Mybank Api: ant.mybank.merchantprod.merch.applet.register.query
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class MerchantAppletRegisterQueryResponse extends MybankResponse {

    private static final long serialVersionUID = -3658440337800559097L;
    @XmlElementRef
    private MerchantAppletRegisterQuery merchantAppletRegisterQuery;

    public MerchantAppletRegisterQuery getMerchantAppletRegisterQuery() {
        return merchantAppletRegisterQuery;
    }

    public void setMerchantAppletRegisterQuery(MerchantAppletRegisterQuery merchantAppletRegisterQuery) {
        this.merchantAppletRegisterQuery = merchantAppletRegisterQuery;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "response")
    public static class MerchantAppletRegisterQuery extends MybankObject {

        private static final long serialVersionUID = -3828782695524613228L;
        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private ResponseHead responseHead;

        /**
         * 业务应答参数（body）
         */
        @XmlElementRef
        private MerchantAppletRegisterQueryResponseModel merchantAppletRegisterQueryResponseModel;

        public ResponseHead getResponseHead() {
            return responseHead;
        }

        public void setResponseHead(ResponseHead responseHead) {
            this.responseHead = responseHead;
        }

        public MerchantAppletRegisterQueryResponseModel getMerchantAppletRegisterQueryResponseModel() {
            return merchantAppletRegisterQueryResponseModel;
        }

        public void setMerchantAppletRegisterQueryResponseModel(MerchantAppletRegisterQueryResponseModel merchantAppletRegisterQueryResponseModel) {
            this.merchantAppletRegisterQueryResponseModel = merchantAppletRegisterQueryResponseModel;
        }
    }
}