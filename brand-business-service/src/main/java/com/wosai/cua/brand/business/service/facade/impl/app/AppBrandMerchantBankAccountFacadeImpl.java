package com.wosai.cua.brand.business.service.facade.impl.app;

import com.alibaba.fastjson2.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardAdvanceDTO;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.SearchBankInfoRequest;
import com.wosai.cua.brand.business.api.dto.request.app.AppBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppBaseBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppCreateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppModifyBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppRequestBaseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantBankCardsDTO;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.app.AppBrandMerchantBankAccountFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class AppBrandMerchantBankAccountFacadeImpl implements AppBrandMerchantBankAccountFacade {

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    private final BrandBusiness business;

    @Autowired
    public AppBrandMerchantBankAccountFacadeImpl(BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness, BrandBusiness business) {
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
        this.business = business;
    }

    @Override
    public BankCardResponseDTO createBankCard(AppCreateBankCardDTO appCreateBankCard) {
        CreateBankCardDTO createBankCard = getCreateBankCard(appCreateBankCard);
        // 替换商户id的逻辑
        createBankCard.setMerchantId(business.replaceOwnerMerchantId(createBankCard.getBrandId(), createBankCard.getMerchantId()));
        return brandMerchantBankAccountBusiness.createBankCard(createBankCard);
    }

    private @NotNull CreateBankCardDTO getCreateBankCard(AppCreateBankCardDTO appCreateBankCard) {
        CreateBankCardDTO createBankCard = JSON.parseObject(JSON.toJSONString(appCreateBankCard), CreateBankCardDTO.class);
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(appCreateBankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        createBankCard.setBrandId(brandSimpleInfoList.get(0).getBrandId());
        return createBankCard;
    }

    @Override
    public BankCardResponseDTO createBankCardBySqbStoreId(AppCreateBankCardDTO createBankCard) {
        if (StringUtils.isBlank(createBankCard.getSqbStoreId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), "收钱吧门店id不能为空！");
        }
        CreateBankCardDTO createBankCardDTO = getCreateBankCard(createBankCard);
        BrandMerchantModule brandMerchantModule = business.getBrandMerchantBySqbStoreId(createBankCardDTO.getBrandId(), createBankCard.getSqbStoreId());
        if (Objects.isNull(brandMerchantModule)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        createBankCardDTO.setMerchantId(brandMerchantModule.getMerchantId());
        createBankCardDTO.setMerchantSn(brandMerchantModule.getMerchantSn());
        return brandMerchantBankAccountBusiness.createBankCard(createBankCardDTO);
    }

    @Override
    public Boolean activateBankCard(ActivateBankCardDTO activateBankCard) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(activateBankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        activateBankCard.setMerchantId(business.replaceOwnerMerchantId(brandSimpleInfoList.get(0).getBrandId(), activateBankCard.getMerchantId()));
        return brandMerchantBankAccountBusiness.activateBankCard(activateBankCard);
    }

    @Override
    public Boolean activateBankCardBySqbStoreId(ActivateBankCardDTO activateBankCard) {
        if (StringUtils.isBlank(activateBankCard.getSqbStoreId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), "收钱吧门店id不能为空！");
        }
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(activateBankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandMerchantModule brandMerchantModule = business.getBrandMerchantBySqbStoreId(brandSimpleInfoList.get(0).getBrandId(), activateBankCard.getSqbStoreId());
        if (Objects.isNull(brandMerchantModule)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        activateBankCard.setMerchantId(brandMerchantModule.getMerchantId());
        return brandMerchantBankAccountBusiness.activateBankCard(activateBankCard);
    }

    @Override
    public Boolean activateBankCardAdvance(ActivateBankCardAdvanceDTO activateBankCardAdvanceDto) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(activateBankCardAdvanceDto.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        activateBankCardAdvanceDto.setMerchantId(business.replaceOwnerMerchantId(brandSimpleInfoList.get(0).getBrandId(), activateBankCardAdvanceDto.getMerchantId()));
        return brandMerchantBankAccountBusiness.activateBankCardAdvance(activateBankCardAdvanceDto);
    }

    @Override
    public Boolean activateBankCardAdvanceBySqbStoreId(ActivateBankCardAdvanceDTO activateBankCardAdvanceDto) {
        if (StringUtils.isBlank(activateBankCardAdvanceDto.getSqbStoreId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), "收钱吧门店id不能为空！");
        }
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(activateBankCardAdvanceDto.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandMerchantModule brandMerchantModule = business.getBrandMerchantBySqbStoreId(brandSimpleInfoList.get(0).getBrandId(), activateBankCardAdvanceDto.getSqbStoreId());
        if (Objects.isNull(brandMerchantModule)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        activateBankCardAdvanceDto.setMerchantId(brandMerchantModule.getMerchantId());
        return brandMerchantBankAccountBusiness.activateBankCardAdvance(activateBankCardAdvanceDto);
    }

    @Override
    public Boolean changeDefaultBankCard(AppBankCardDTO appBankCard) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(appBankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        appBankCard.setMerchantId(business.replaceOwnerMerchantId(brandSimpleInfoList.get(0).getBrandId(), appBankCard.getMerchantId()));
        brandMerchantBankAccountBusiness.checkOperationPermission(appBankCard.getMerchantId(), appBankCard.getBankCardId());
        brandMerchantBankAccountBusiness.changeDefaultBankCard(appBankCard.getBankCardId());
        return true;
    }

    @Override
    public Boolean changeDefaultBankCardBySqbStoreId(AppBankCardDTO appBankCard) {
        this.processRequest(appBankCard);
        brandMerchantBankAccountBusiness.changeDefaultBankCard(appBankCard.getBankCardId());
        return true;
    }

    @Override
    public Boolean deleteBankCard(AppBankCardDTO appBankCard) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(appBankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        appBankCard.setMerchantId(business.replaceOwnerMerchantId(brandSimpleInfoList.get(0).getBrandId(), appBankCard.getMerchantId()));
        brandMerchantBankAccountBusiness.checkOperationPermission(appBankCard.getMerchantId(), appBankCard.getBankCardId());
        return brandMerchantBankAccountBusiness.deleteBankCard(appBankCard.getBankCardId(), false,false);
    }

    @Override
    public Boolean deleteBankCardBySqbStoreId(AppBankCardDTO appBankCard) {
        this.processRequest(appBankCard);
        return brandMerchantBankAccountBusiness.deleteBankCard(appBankCard.getBankCardId(), false,false);
    }

    private void processRequest(AppBankCardDTO appBankCard) {
        if (StringUtils.isBlank(appBankCard.getSqbStoreId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), "收钱吧门店id不能为空！");
        }
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(appBankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandMerchantModule brandMerchantModule = business.getBrandMerchantBySqbStoreId(brandSimpleInfoList.get(0).getBrandId(), appBankCard.getSqbStoreId());
        if (Objects.isNull(brandMerchantModule)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        appBankCard.setMerchantId(brandMerchantModule.getMerchantId());
        brandMerchantBankAccountBusiness.checkOperationPermission(appBankCard.getMerchantId(), appBankCard.getBankCardId());
    }

    @Override
    public PageBrandMerchantBankCardsDTO pageFindBankCardList(PageQueryBankCardDTO pageQueryBankCardDto) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(pageQueryBankCardDto.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        pageQueryBankCardDto.setMerchantId(business.replaceOwnerMerchantId(brandSimpleInfoList.get(0).getBrandId(), pageQueryBankCardDto.getMerchantId()));
        return brandMerchantBankAccountBusiness.pageFindBankCardList(pageQueryBankCardDto);
    }

    @Override
    public PageBrandMerchantBankCardsDTO pageFindBankCardListBySqbStoreId(PageQueryBankCardDTO pageQueryBankCardDto) {
        if (StringUtils.isEmpty(pageQueryBankCardDto.getSqbStoreId())) {
            throw new BrandBusinessException("收钱吧门店id不能为空");
        }
        String brandId = business.getBrandIdByMerchantId(pageQueryBankCardDto.getMerchantId());
        if (StringUtils.isBlank(brandId)) {
            log.warn("未查到品牌信息。");
            return new PageBrandMerchantBankCardsDTO();
        }
        QueryBrandMerchantInfoDTO queryBrandMerchantInfo = new QueryBrandMerchantInfoDTO();
        queryBrandMerchantInfo.setBrandId(brandId);
        queryBrandMerchantInfo.setSqbStoreId(pageQueryBankCardDto.getSqbStoreId());
        List<BrandMerchantModule> brandMerchantByConditions = business.getBrandMerchantByConditions(queryBrandMerchantInfo);
        if (CollectionUtils.isEmpty(brandMerchantByConditions)) {
            queryBrandMerchantInfo.setMerchantId(pageQueryBankCardDto.getMerchantId());
            queryBrandMerchantInfo.setSqbStoreId(null);
            brandMerchantByConditions = business.getBrandMerchantByConditions(queryBrandMerchantInfo);
            if (CollectionUtils.isEmpty(brandMerchantByConditions)) {
                return new PageBrandMerchantBankCardsDTO();
            }
        }
        BrandMerchantModule brandMerchantModule = brandMerchantByConditions.get(0);
        pageQueryBankCardDto.setMerchantId(brandMerchantModule.getMerchantId());
        pageQueryBankCardDto.setBrandId(brandId);
        return brandMerchantBankAccountBusiness.pageFindBankCardList(pageQueryBankCardDto);
    }

    @Override
    public BankCardActivateStatusEnum getBankCardActivateStatus(AppBankCardDTO appBankCard) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(appBankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        appBankCard.setMerchantId(business.replaceOwnerMerchantId(brandSimpleInfoList.get(0).getBrandId(), appBankCard.getMerchantId()));
        return brandMerchantBankAccountBusiness.getBankCardActivateStatusByMerchantId(appBankCard.getMerchantId());
    }

    @Override
    public List<BankCardDetailDTO> getActivatedBankCardList(AppRequestBaseDTO requestBaseDto) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(requestBaseDto.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        requestBaseDto.setMerchantId(business.replaceOwnerMerchantId(brandSimpleInfoList.get(0).getBrandId(), requestBaseDto.getMerchantId()));
        return brandMerchantBankAccountBusiness.getActivatedBankCardList(brandSimpleInfoList.get(0).getBrandId(), requestBaseDto.getMerchantId());
    }

    @Override
    public BankCardResponseDTO modifyBankCard(AppModifyBankCardDTO appModifyBankCard) {
        ModifyBankCardDTO modifyBankCard = JSON.parseObject(JSON.toJSONString(appModifyBankCard), ModifyBankCardDTO.class);
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(appModifyBankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        modifyBankCard.setBrandId(brandSimpleInfoList.get(0).getBrandId());
        // 替换商户id的逻辑
        modifyBankCard.setMerchantId(business.replaceOwnerMerchantId(modifyBankCard.getBrandId(), modifyBankCard.getMerchantId()));
        BankCardDetailResponseDTO bankCardDetail = brandMerchantBankAccountBusiness.getBankCardDetail(appModifyBankCard.getBankCardId());
        if (!modifyBankCard.getBrandId().equals(bankCardDetail.getBrandId()) || !modifyBankCard.getMerchantId().equals(bankCardDetail.getMerchantId())){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
        return brandMerchantBankAccountBusiness.modifyBankCard(modifyBankCard);
    }

    @Override
    public BankCardDetailResponseDTO getBankCardDetail(AppBaseBankCardDTO bankCard) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(bankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        bankCard.setBrandId(brandSimpleInfoList.get(0).getBrandId());
        // 替换商户id的逻辑
        bankCard.setMerchantId(business.replaceOwnerMerchantId(bankCard.getBrandId(), bankCard.getMerchantId()));
        BankCardDetailResponseDTO bankCardDetail = brandMerchantBankAccountBusiness.getBankCardDetail(bankCard.getBankCardId());
        if (!bankCard.getBrandId().equals(bankCardDetail.getBrandId()) || !bankCard.getMerchantId().equals(bankCardDetail.getMerchantId())){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
        return bankCardDetail;
    }

    @Override
    public BankCardDetailResponseDTO getBankCardDetailBySqbStoreId(AppBaseBankCardDTO bankCard) {
        if (StringUtils.isBlank(bankCard.getSqbStoreId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), "收钱吧门店id不能为空！");
        }
        List<BrandSimpleInfoDTO> brandSimpleInfoList = business.getBrandInfoListByMerchantId(bankCard.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandMerchantModule brandMerchantModule = business.getBrandMerchantBySqbStoreId(brandSimpleInfoList.get(0).getBrandId(), bankCard.getSqbStoreId());
        if (Objects.isNull(brandMerchantModule)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        bankCard.setMerchantId(brandMerchantModule.getMerchantId());
        BankCardDetailResponseDTO bankCardDetail = brandMerchantBankAccountBusiness.getBankCardDetail(bankCard.getBankCardId());
        if (!bankCard.getBrandId().equals(bankCardDetail.getBrandId()) || !bankCard.getMerchantId().equals(bankCardDetail.getMerchantId())){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
        return bankCardDetail;
    }

    @Override
    public BankInfoResponseDTO getBankInfoByCardNumber(SearchBankInfoRequest searchBankInfoRequest) {
        return brandMerchantBankAccountBusiness.getBankInfoByCardNumber(searchBankInfoRequest.getCardNumber());
    }
}
