package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
public class CancelConcentrateRelationRequestBody extends BaseRequestBody {
    /**
     * 被归集的商户号
     */
    @Sign
    private String mchntCdConcentrate;

    public CancelConcentrateRelationRequestBody(String mchntCd) {
        super(mchntCd);
    }
}
