package com.wosai.cua.brand.business.service.excel.handler;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.BrandBatchAddMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.PaymentHubDevParamDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.excel.context.BusinessOpenAddMerchantContext;
import com.wosai.cua.brand.business.service.excel.data.BusinessOpenAddNoMicroMerchantOpData;
import com.wosai.cua.brand.business.service.excel.groups.Default;
import com.wosai.cua.brand.business.service.excel.groups.Indirect;
import com.wosai.cua.brand.business.service.excel.model.BusinessOpenBasicModel;
import com.wosai.cua.brand.business.service.excel.model.IdTypeEnum;
import com.wosai.cua.brand.business.service.excel.model.MerchantPhotoModel;
import com.wosai.cua.brand.business.service.excel.model.PayTypeEnum;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.BankInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.DistrictInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.IndustryInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.StoreExtAndPicturesQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.model.UcUserInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.salespoi.model.PoiDetailQueryResult;
import com.wosai.cua.brand.business.service.helper.OssFileHelper;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.helper.ValidationUtils;
import com.wosai.cua.brand.business.service.helper.ValidityTransferHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.Store;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 批量新增非小微商户关联品牌
 *
 * <AUTHOR>
 * @date 2024/9/2
 */
@Slf4j
@Component
public class BusinessOpenAddNoMicroMerchantHandler extends AbstractAddMerchantHandler<BusinessOpenAddMerchantContext, BusinessOpenAddNoMicroMerchantOpData> {

    @Override
    public BusinessOpenAddMerchantContext initContext(BrandTaskLogModule brandTaskLogModule) {
        return BusinessOpenAddMerchantContext.newInstance(brandTaskLogModule);
    }

    @Override
    public ExcelImportTypeEnum getAuditImportType() {
        return ExcelImportTypeEnum.BUSINESS_OPEN_ADD_NO_MICRO_MERCHANT;
    }

    @Override
    public void checkData(BusinessOpenAddMerchantContext context, BusinessOpenAddNoMicroMerchantOpData data) {
        BrandBatchAddMerchantsDTO brandBatchAddMerchantsDTO = context.getBrandBatchAddMerchantsDTO();
        if (brandBatchAddMerchantsDTO.getOpenIndirect()) {
            doCheckOpenIndirect(data);
        }
    }

    private void doCheckOpenIndirect(BusinessOpenAddNoMicroMerchantOpData data) {
        ValidationUtils.ValidationResult validate = ValidationUtils.validate(data, Default.class, Indirect.class);
        if (validate.isInvalid()) {
            if (WosaiStringUtils.isNotEmpty(data.getSequenceNo())) {
                throw new BrandBusinessException(String.format("序号:%s %s", data.getSequenceNo(), validate.getMsg()));
            } else {
                throw new BrandBusinessException(validate.getMsg());
            }
        }
        if (Objects.isNull(data.getLegalPersonIdType())) {
            throw new BrandBusinessException(String.format("序号:%s 法人证件类型不正确", data.getSequenceNo()));
        }
        // 校验身份证号
        if(IdTypeEnum.ID_CARD.equals(data.getLegalPersonIdType()) && !ParamsCheckHelper.isValidChineseID(data.getLegalPersonIdNumber())) {
            throw new BrandBusinessException(String.format("序号:%s 身份证号格式不正确", data.getSequenceNo()));
        }
        // 校验证件有效期
        if (!ParamsCheckHelper.checkIdValidityStart(data.getIdValidityStart())) {
            throw new BrandBusinessException(String.format("序号:%s 证件有效期开始日期格式不正确", data.getSequenceNo()));
        }
        if (!ParamsCheckHelper.checkIdValidityEnd(data.getIdValidityEnd())) {
            throw new BrandBusinessException(String.format("序号:%s 证件有效期截止日期格式不正确", data.getSequenceNo()));
        }
        // 校验银行卡有效期
        if (WosaiStringUtils.isNotEmpty(data.getCardValidity()) && !ParamsCheckHelper.checkCardValidity(data.getCardValidity())) {
            throw new BrandBusinessException(String.format("序号:%s 银行卡有效期格式不正确", data.getSequenceNo()));
        }
        // 对公结算
        if (data.isPublicSettlement()) {
            if (WosaiStringUtils.isEmpty(data.getPublicHolder())) {
                throw new BrandBusinessException(String.format("序号:%s 对公账户名称为空", data.getSequenceNo()));
            }
        } else if (data.isNoLegalSettlement()) {
            // 非法人结算
            if (Objects.isNull(data.getHolderIdType())) {
                throw new BrandBusinessException(String.format("序号:%s 结算人证件类型为空", data.getSequenceNo()));
            }
            if (WosaiStringUtils.isEmpty(data.getHolder())) {
                throw new BrandBusinessException(String.format("序号:%s 结算人姓名为空", data.getSequenceNo()));
            }
            if (WosaiStringUtils.isEmpty(data.getIdentity())) {
                throw new BrandBusinessException(String.format("序号:%s 结算人证件号为空", data.getSequenceNo()));
            }
            if (WosaiStringUtils.isEmpty(data.getHolderIdValidityStart())) {
                throw new BrandBusinessException(String.format("序号:%s 结算人证件有效开始日期为空", data.getSequenceNo()));
            }
            if (WosaiStringUtils.isEmpty(data.getHolderIdValidityEnd())) {
                throw new BrandBusinessException(String.format("序号:%s 结算人证件有效截止日期为空", data.getSequenceNo()));
            }
            // 校验身份证号
            if(IdTypeEnum.ID_CARD.equals(data.getHolderIdType()) && !ParamsCheckHelper.isValidChineseID(data.getIdentity())) {
                throw new BrandBusinessException(String.format("序号:%s 身份证号格式不正确", data.getSequenceNo()));
            }
            // 校验证件有效期
            if (!ParamsCheckHelper.checkIdValidityStart(data.getHolderIdValidityStart())) {
                throw new BrandBusinessException(String.format("序号:%s 结算人证件有效期开始日期格式不正确", data.getSequenceNo()));
            }
            if (!ParamsCheckHelper.checkIdValidityEnd(data.getHolderIdValidityEnd())) {
                throw new BrandBusinessException(String.format("序号:%s 结算人证件有效期截止日期格式不正确", data.getSequenceNo()));
            }
            if (WosaiStringUtils.isEmpty(data.getCardValidity())) {
                throw new BrandBusinessException(String.format("序号:%s 银行卡有效期为空", data.getSequenceNo()));
            }
            // 校验银行卡有效期
            if (!ParamsCheckHelper.checkCardValidity(data.getCardValidity())) {
                throw new BrandBusinessException(String.format("序号:%s 银行卡有效期格式不正确", data.getSequenceNo()));
            }
        } else {
            if (WosaiStringUtils.isEmpty(data.getCardValidity())) {
                throw new BrandBusinessException(String.format("序号:%s 银行卡有效期为空", data.getSequenceNo()));
            }
            // 校验银行卡有效期
            if (!ParamsCheckHelper.checkCardValidity(data.getCardValidity())) {
                throw new BrandBusinessException(String.format("序号:%s 银行卡有效期格式不正确", data.getSequenceNo()));
            }
        }
    }

    @Override
    protected void doOpenBusiness(BusinessOpenAddNoMicroMerchantOpData data, BusinessOpenAddMerchantContext context, File photoFile) {
        BrandBatchAddMerchantsDTO brandBatchAddMerchantsDTO = context.getBrandBatchAddMerchantsDTO();
        String merchantId = WosaiStringUtils.isNotEmpty(data.getMerchantId()) ? data.getMerchantId() : UUID.randomUUID().toString();
        data.setMerchantId(merchantId);
        MerchantPhotoModel merchantPhotoModel = assemblePhotoInfo(data, photoFile, brandBatchAddMerchantsDTO.getLaterSupplyPhoto());
        BusinessOpenBasicModel businessOpenBasicModel = assembleBasicInfo(merchantId, data, context, merchantPhotoModel);
        if (brandBatchAddMerchantsDTO.getOpenIndirect()) {
            // 将该商户的品牌ID_支付模式写入到redis中，这样进件服务在选择入网规则组的时候可以查到支付模式
            redisClusterTemplate.opsForValue().set("BRAND_MERCHANT_PAYMENT_MODE:" + merchantId, context.getBrandModule().getBrandId() + "_" + brandBatchAddMerchantsDTO.getPaymentModeDetail().getCode(), 20, TimeUnit.SECONDS);
            BusinessOpenResult businessOpenResult = openIndirect(businessOpenBasicModel, merchantPhotoModel, data, context);
            if (businessOpenResult.isSuccess()) {
                data.setIndirectResult("成功");
                data.setMerchantSn(businessOpenResult.getMerchantSn());
                saveOrUpdateBrandMerchant(context.getBrandModule().getBrandId(), context.getBrandModule().getParentId(), businessOpenResult.getMerchantSn(), businessOpenBasicModel, brandBatchAddMerchantsDTO, data);
            } else {
                data.setIndirectResult("失败:" + businessOpenResult.getMessage());
            }
        }
    }

    private MerchantPhotoModel assemblePhotoInfo(BusinessOpenAddNoMicroMerchantOpData data, File photoFile, Boolean laterSupplyPhoto) {
        if (Objects.equals(true, laterSupplyPhoto)) {
            return MerchantPhotoModel.DEFAULT_IMAGE_MODEL;
        }
        if (Objects.isNull(photoFile) || !photoFile.exists() || !photoFile.isDirectory()) {
            return MerchantPhotoModel.DEFAULT_IMAGE_MODEL;
        }
        File file = FileUtils.getFile(photoFile, data.getSequenceNo());
        if (!file.exists() || !file.isDirectory()) {
            return MerchantPhotoModel.DEFAULT_IMAGE_MODEL;
        }
        MerchantPhotoModel merchantPhotoModel = new MerchantPhotoModel();
        merchantPhotoModel.setLicensePhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.LICENSE_PHOTO)).orElseThrow(() -> new BrandBusinessException("未找到营业执照照片")));
        merchantPhotoModel.setLegalPersonFrontPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.LEGAL_PERSON_FRONT_PHOTO)).orElseThrow(() -> new BrandBusinessException("未找到法人证件正面照")));
        merchantPhotoModel.setLegalPersonBackPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.LEGAL_PERSON_BACK_PHOTO)).orElseThrow(() -> new BrandBusinessException("未找到法人证件反面照")));
        if (data.isPublicSettlement()) {
            merchantPhotoModel.setPublicBankCardPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.PUBLIC_BANK_CARD_IMAGE)).orElseThrow(() -> new BrandBusinessException("未找到对公账户凭证")));
        } else {
            merchantPhotoModel.setBankCardPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.BANK_CARD_NAME)).orElseThrow(() -> new BrandBusinessException("未找到银行卡照片")));
            if (data.isNoLegalSettlement()) {
                merchantPhotoModel.setHolderIdFrontPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.HOLDER_ID_FRONT_PHOTO)).orElseThrow(() -> new BrandBusinessException("未找到身份证正面照")));
                merchantPhotoModel.setHolderIdBackPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.HOLDER_ID_BACK_PHOTO)).orElseThrow(() -> new BrandBusinessException("未找到身份证反面照")));
                merchantPhotoModel.setLetterOfAuthorization(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.LETTER_OF_AUTHORIZATION)).orElseThrow(() -> new BrandBusinessException("未找到授权函")));
            }
        }
        merchantPhotoModel.setHandLetterOfAuthorizationPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrls(file, MerchantPhotoModel.HAND_LETTER_OF_AUTHORIZATION)).orElse(new ArrayList<>()));
        return merchantPhotoModel;
    }

    private void saveOrUpdateBrandMerchant(String brandId, String parentId, String merchantSn, BusinessOpenBasicModel businessOpenBasicModel, BrandBatchAddMerchantsDTO brandBatchAddMerchantsDTO, BusinessOpenAddNoMicroMerchantOpData data) {
        BrandMerchantModule brandMerchantInfo = brandDomainService.getBrandMerchantInfoByMerchantId(businessOpenBasicModel.getMerchantId());
        if (Objects.nonNull(brandMerchantInfo)) {
            brandMerchantInfo.setPaymentMode(brandBatchAddMerchantsDTO.getPaymentModeDetail().getCode());
            brandDomainService.updateBrandMerchant(brandMerchantInfo);
        } else {
            BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
            brandMerchantModule.setBrandId(brandId);
            brandMerchantModule.setParentBrandId(parentId);
            brandMerchantModule.setMerchantId(businessOpenBasicModel.getMerchantId());
            brandMerchantModule.setMerchantSn(merchantSn);
            brandMerchantModule.setMerchantName(WosaiMapUtils.getString(businessOpenBasicModel.getMerchant(), Merchant.NAME));
            brandMerchantModule.setMerchantType(data.getCooperation().getMerchantType());
            brandMerchantModule.setPaymentMode(brandBatchAddMerchantsDTO.getPaymentModeDetail().getCode());
            brandMerchantModule.setType(this.getBrandMerchantType(WosaiMapUtils.getInteger(businessOpenBasicModel.getLicense(), MerchantBusinessLicence.TYPE)));
            brandDomainService.createBrandMerchant(brandMerchantModule);
        }
    }

    private String getBrandMerchantType(Integer licenseType) {
        if (Objects.isNull(licenseType)) {
            return null;
        }
        switch (licenseType) {
            case 0:
                return BrandMerchantTypeEnum.PERSONAL.getType();
            case 1:
                return BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType();
            case 2:
                return BrandMerchantTypeEnum.COMPANY.getType();
            default:
                return null;
        }
    }

    private BusinessOpenBasicModel assembleBasicInfo(String merchantId, BusinessOpenAddNoMicroMerchantOpData data, BusinessOpenAddMerchantContext context, MerchantPhotoModel merchantPhotoModel) {
        // 组装参数
        DistrictInfoQueryResult districtInfoQueryResult = bankInfoClient.getDistrictInfoByCode(data.getDistrictCode());
        PoiDetailQueryResult poiDetailQueryResult = salesPoiClient.getPoiDetailByCompleteAddress(districtInfoQueryResult.getProvince() + districtInfoQueryResult.getCity() + districtInfoQueryResult.getDistrict() + data.getStreetAddress());
        IndustryInfoQueryResult industryInfoQueryResult = bankInfoClient.getIndustryInfoByCode(data.getIndustryCode());
        // 商户
        Map<String, Object> merchant = assembleMerchantInfo(data, industryInfoQueryResult, districtInfoQueryResult, poiDetailQueryResult);
        // account
        Map<String, Object> account = assembleAccountInfo(data);
        // license
        Map<String, Object> license = assembleLicenseInfo(data, merchantPhotoModel);
        // store
        Map<String, Object> store = assembleStoreInfo(data, districtInfoQueryResult, poiDetailQueryResult, context.getStoreExtAndPicturesQueryResult());
        return BusinessOpenBasicModel.builder()
                .merchantId(merchantId)
                .merchant(merchant)
                .account(account)
                .license(license)
                .store(store)
                .build();
    }

    private BusinessOpenResult openIndirect(BusinessOpenBasicModel basicModel, MerchantPhotoModel merchantPhotoModel, BusinessOpenAddNoMicroMerchantOpData data, BusinessOpenAddMerchantContext context) {
        Map<String, Object> appInfo = assembleIndirectAppInfo(data, basicModel, merchantPhotoModel, context);
        // 调用业务开通的接口
        return merchantBusinessOpenClient.openApp(new BusinessOpenRequest()
                .setMerchantInfo(basicModel.getMerchant())
                .setAccountInfo(basicModel.getAccount())
                .setLicenseInfo(basicModel.getLicense())
                .setStoreInfo(basicModel.getStore())
                .setAppInfo(appInfo)
                .setAppId(indirectAppId)
                .setMerchantId(basicModel.getMerchantId())
                .setUserId(context.getKeeperUserId()));
    }

    private Map<String, Object> assembleMerchantInfo(BusinessOpenAddNoMicroMerchantOpData data, IndustryInfoQueryResult industryInfoQueryResult, DistrictInfoQueryResult districtInfoQueryResult, PoiDetailQueryResult poiDetailQueryResult) {
        Map<String, Object> merchant = new HashMap<>();
        merchant.put(Merchant.OWNER_CELLPHONE, data.getLoginPhone());
        merchant.put(Merchant.CUSTOMER_PHONE, data.getContactPhone());
        merchant.put(Merchant.BUSINESS, data.getBusinessName());
        merchant.put(Merchant.BUSINESS_NAME, data.getBusinessName());
        merchant.put(Merchant.INDUSTRY, industryInfoQueryResult.getIndustryId());
        merchant.put(Merchant.ALIAS, data.getBusinessName());
        merchant.put(Merchant.NAME, data.getBusinessName());
        merchant.put(Merchant.OWNER_NAME, data.getContactName());
        merchant.put(Merchant.CONTACT_NAME, data.getContactName());
        merchant.put(Merchant.CONTACT_CELLPHONE, data.getContactPhone());
        merchant.put(Merchant.PROVINCE, districtInfoQueryResult.getProvince());
        merchant.put(Merchant.CITY, districtInfoQueryResult.getCity());
        merchant.put(Merchant.DISTRICT, districtInfoQueryResult.getDistrict());
        merchant.put(Merchant.STREET_ADDRESS, data.getStreetAddress());
        merchant.put(Merchant.LONGITUDE, poiDetailQueryResult.getLongitude());
        merchant.put(Merchant.LATITUDE, poiDetailQueryResult.getLatitude());
        return merchant;
    }

    private Map<String, Object> assembleAccountInfo(BusinessOpenAddNoMicroMerchantOpData data) {
        UcUserInfoQueryResult ucUserInfoQueryResult = merchantUserClient.getUcUserInfoByIdentifier(data.getLoginPhone());
        Map<String, Object> account = new HashMap<>();
        account.put("uc_user_id", Objects.nonNull(ucUserInfoQueryResult) ? ucUserInfoQueryResult.getUcUserId() : null);
        account.put("identity_type", 1);
        account.put("identifier", data.getLoginPhone());
        return account;
    }

    private Map<String, Object> assembleLicenseInfo(BusinessOpenAddNoMicroMerchantOpData data, MerchantPhotoModel merchantPhotoModel) {
        Map<String, Object> license = new HashMap<>();
        Integer licenseType = bankInfoClient.getLicenseTypeByNumber(data.getLicenseNumber());
        license.put(MerchantBusinessLicence.TYPE, licenseType);
        license.put(MerchantBusinessLicence.NAME, data.getLicenseName());
        license.put(MerchantBusinessLicence.NUMBER, data.getLicenseNumber());
        license.put(MerchantBusinessLicence.PHOTO, merchantPhotoModel.getLicensePhoto());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_NAME, data.getLegalPersonName());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER, data.getLegalPersonIdNumber());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_TYPE, data.getLegalPersonIdType().getValue());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, merchantPhotoModel.getLegalPersonFrontPhoto());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_BACK_PHOTO, merchantPhotoModel.getLegalPersonBackPhoto());
        license.put(MerchantBusinessLicence.ID_VALIDITY, data.getIdValidityStart() + "-" + ValidityTransferHelper.transferIdValidityEnd(data.getIdValidityEnd()));
        license.put(MerchantBusinessLicence.ADDRESS, data.getLicenseAddress());
        license.put(MerchantBusinessLicence.VALIDITY, data.getLicenseValidityStart() + "-" + ValidityTransferHelper.transferIdValidityEnd(data.getLicenseValidityEnd()));
        return license;
    }

    private Map<String, Object> assembleStoreInfo(BusinessOpenAddNoMicroMerchantOpData data, DistrictInfoQueryResult districtInfoQueryResult, PoiDetailQueryResult poiDetailQueryResult, StoreExtAndPicturesQueryResult storeExtAndPicturesQueryResult) {
        Map<String, Object> storeInfo = new HashMap<>();
        storeInfo.put(Store.PROVINCE, districtInfoQueryResult.getProvince());
        storeInfo.put(Store.CITY, districtInfoQueryResult.getCity());
        storeInfo.put(Store.DISTRICT, districtInfoQueryResult.getDistrict());
        storeInfo.put(Store.STREET_ADDRESS, data.getStreetAddress());
        storeInfo.put(Store.LONGITUDE, poiDetailQueryResult.getLongitude());
        storeInfo.put(Store.LATITUDE, poiDetailQueryResult.getLatitude());
        storeInfo.put(Store.CONTACT_NAME, data.getContactName());
        storeInfo.put(Store.CONTACT_CELLPHONE, data.getContactPhone());
        storeInfo.put(Store.NAME, data.getBusinessName());
        storeInfo.put("brand_photo", CollectionUtil.hashMap("url", storeExtAndPicturesQueryResult.getBrandPhoto().getUrl()));
        storeInfo.put("indoor_material_photo", CollectionUtil.hashMap("url", storeExtAndPicturesQueryResult.getIndoorMaterialPhoto().getUrl()));
        storeInfo.put("outdoor_material_photo", CollectionUtil.hashMap("url", storeExtAndPicturesQueryResult.getOutdoorMaterialPhoto().getUrl()));
        return storeInfo;
    }

    private Map<String, Object> assembleIndirectAppInfo(BusinessOpenAddNoMicroMerchantOpData data, BusinessOpenBasicModel basicModel, MerchantPhotoModel merchantPhotoModel, BusinessOpenAddMerchantContext context) {
        BankInfoQueryResult bankInfoQueryResult = bankInfoClient.getBankInfoByOpeningNumber(data.getOpeningNumber());
        if (Objects.isNull(bankInfoQueryResult)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_INFO_NOT_FIND);
        }
        String bankName = bankInfoQueryResult.getBankName();
        String branchName = bankInfoQueryResult.getBranchName();
        Map<String, Object> appInfo = assembleMerchantConfig(context, basicModel);
        // 非法人个人结算
        if (data.isNoLegalSettlement()) {
            appInfo.put("bank_account", CollectionUtil.hashMap(
                    MerchantBankAccount.HOLDER, data.getHolder(),
                    MerchantBankAccount.IDENTITY, data.getIdentity(),
                    MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, merchantPhotoModel.getHolderIdFrontPhoto(),
                    MerchantBankAccount.HOLDER_ID_BACK_PHOTO, merchantPhotoModel.getHolderIdBackPhoto(),
                    MerchantBankAccount.ID_VALIDITY, data.getHolderIdValidityStart() + "-" + ValidityTransferHelper.transferIdValidityEnd(data.getHolderIdValidityEnd()),
                    MerchantBankAccount.BANK_CARD_IMAGE, merchantPhotoModel.getBankCardPhoto(),
                    MerchantBankAccount.NUMBER, data.getNumber(),
                    MerchantBankAccount.OPENING_NUMBER, data.getOpeningNumber(),
                    MerchantBankAccount.BANK_NAME, bankName,
                    MerchantBankAccount.BRANCH_NAME, branchName,
                    MerchantBankAccount.CARD_VALIDITY, ValidityTransferHelper.transferCardValidity(data.getCardValidity()),
                    MerchantBankAccount.LETTER_OF_AUTHORIZATION, merchantPhotoModel.getLetterOfAuthorization(),
                    "pay_type", PayTypeEnum.NO_LEGAL_SETTLEMENT.getValue(),
                    MerchantBankAccount.ID_TYPE, data.getHolderIdType().getValue(),
                    MerchantBankAccount.HAND_LETTER_OF_AUTHORIZATION, String.join(",", merchantPhotoModel.getHandLetterOfAuthorizationPhoto())
            ));
        } else if (data.isPublicSettlement()) {
            appInfo.put("bank_account", CollectionUtil.hashMap(
                    MerchantBankAccount.HOLDER, data.getPublicHolder(),
                    MerchantBankAccount.IDENTITY, data.getLegalPersonIdNumber(),
                    MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, merchantPhotoModel.getLegalPersonFrontPhoto(),
                    MerchantBankAccount.HOLDER_ID_BACK_PHOTO, merchantPhotoModel.getLegalPersonBackPhoto(),
                    MerchantBankAccount.ID_VALIDITY, data.getIdValidityStart() + "-" + ValidityTransferHelper.transferIdValidityEnd(data.getIdValidityEnd()),
                    MerchantBankAccount.BANK_CARD_IMAGE, merchantPhotoModel.getPublicBankCardPhoto(),
                    MerchantBankAccount.NUMBER, data.getNumber(),
                    MerchantBankAccount.OPENING_NUMBER, data.getOpeningNumber(),
                    MerchantBankAccount.BANK_NAME, bankName,
                    MerchantBankAccount.BRANCH_NAME, branchName,
                    MerchantBankAccount.CARD_VALIDITY, ValidityTransferHelper.transferCardValidity(data.getCardValidity()),
                    "pay_type", PayTypeEnum.PUBLIC_SETTLEMENT.getValue(),
                    MerchantBankAccount.ID_TYPE, data.getLegalPersonIdType().getValue(),
                    MerchantBankAccount.HAND_LETTER_OF_AUTHORIZATION, String.join(",", merchantPhotoModel.getHandLetterOfAuthorizationPhoto())
            ));
        } else {
            appInfo.put("bank_account", CollectionUtil.hashMap(
                    MerchantBankAccount.HOLDER, data.getLegalPersonName(),
                    MerchantBankAccount.IDENTITY, data.getLegalPersonIdNumber(),
                    MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, merchantPhotoModel.getLegalPersonFrontPhoto(),
                    MerchantBankAccount.HOLDER_ID_BACK_PHOTO, merchantPhotoModel.getLegalPersonBackPhoto(),
                    MerchantBankAccount.ID_VALIDITY, data.getIdValidityStart() + "-" + ValidityTransferHelper.transferIdValidityEnd(data.getIdValidityEnd()),
                    MerchantBankAccount.BANK_CARD_IMAGE, merchantPhotoModel.getBankCardPhoto(),
                    MerchantBankAccount.NUMBER, data.getNumber(),
                    MerchantBankAccount.OPENING_NUMBER, data.getOpeningNumber(),
                    MerchantBankAccount.BANK_NAME, bankName,
                    MerchantBankAccount.BRANCH_NAME, branchName,
                    MerchantBankAccount.CARD_VALIDITY, ValidityTransferHelper.transferCardValidity(data.getCardValidity()),
                    "pay_type", PayTypeEnum.LEGAL_SETTLEMENT.getValue(),
                    MerchantBankAccount.ID_TYPE, data.getLegalPersonIdType().getValue(),
                    MerchantBankAccount.HAND_LETTER_OF_AUTHORIZATION, String.join(",", merchantPhotoModel.getHandLetterOfAuthorizationPhoto())
            ));
        }
        return appInfo;
    }
}
