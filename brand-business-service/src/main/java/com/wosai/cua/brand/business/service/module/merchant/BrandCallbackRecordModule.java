package com.wosai.cua.brand.business.service.module.merchant;

import com.lark.oapi.core.utils.Lists;
import com.wosai.cua.brand.business.service.domain.entity.BrandCallbackRecordsDO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class BrandCallbackRecordModule {
    /**
     * 主键
     */
    private Long id;

    /**
     * 资管机构code
     */
    private String fundManagementCompanyCode;

    /**
     * 交易记录编号
     */
    private String tradeNo;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 资管机构对应的id：网商为appId，富管家为merchantNo
     */
    private String appId;

    /**
     * 通知接口名称
     */
    private String function;

    /**
     * 回调内容：JSON字符串
     */
    private String content;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 回调结果：0-未成功，1-成功
     */
    private Integer result;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    public static BrandCallbackRecordsDO convert2Do(BrandCallbackRecordModule module) {
        if (module == null) {
            return null;
        }
        BrandCallbackRecordsDO brandCallbackRecordsDO = new BrandCallbackRecordsDO();
        brandCallbackRecordsDO.setId(module.getId());
        brandCallbackRecordsDO.setFundManagementCompanyCode(module.getFundManagementCompanyCode());
        brandCallbackRecordsDO.setTradeNo(module.getTradeNo());
        brandCallbackRecordsDO.setMerchantSn(module.getMerchantSn());
        brandCallbackRecordsDO.setAppId(module.getAppId());
        brandCallbackRecordsDO.setFunction(module.getFunction());
        brandCallbackRecordsDO.setContent(module.getContent());
        brandCallbackRecordsDO.setRetryTimes(module.getRetryTimes());
        brandCallbackRecordsDO.setResult(module.getResult());
        brandCallbackRecordsDO.setCtime(module.getCtime());
        brandCallbackRecordsDO.setMtime(module.getMtime());
        return brandCallbackRecordsDO;
    }

    public static BrandCallbackRecordModule convert2Module(BrandCallbackRecordsDO brandCallbackRecordsDO) {
        if (brandCallbackRecordsDO == null) {
            return null;
        }
        BrandCallbackRecordModule brandCallbackRecordModule = new BrandCallbackRecordModule();
        brandCallbackRecordModule.setFundManagementCompanyCode(brandCallbackRecordsDO.getFundManagementCompanyCode());
        brandCallbackRecordModule.setId(brandCallbackRecordsDO.getId());
        brandCallbackRecordModule.setTradeNo(brandCallbackRecordsDO.getTradeNo());
        brandCallbackRecordModule.setMerchantSn(brandCallbackRecordsDO.getMerchantSn());
        brandCallbackRecordModule.setAppId(brandCallbackRecordsDO.getAppId());
        brandCallbackRecordModule.setFunction(brandCallbackRecordsDO.getFunction());
        brandCallbackRecordModule.setContent(brandCallbackRecordsDO.getContent());
        brandCallbackRecordModule.setRetryTimes(brandCallbackRecordsDO.getRetryTimes());
        brandCallbackRecordModule.setResult(brandCallbackRecordsDO.getResult());
        brandCallbackRecordModule.setCtime(brandCallbackRecordsDO.getCtime());
        brandCallbackRecordModule.setMtime(brandCallbackRecordsDO.getMtime());
        return brandCallbackRecordModule;
    }

    public static List<BrandCallbackRecordModule> convert2Modules(List<BrandCallbackRecordsDO> brandCallbackRecords) {
        if (CollectionUtils.isEmpty(brandCallbackRecords)){
            return Lists.newArrayList();
        }
        return brandCallbackRecords.stream().map(BrandCallbackRecordModule::convert2Module).collect(Collectors.toList());
    }
}
