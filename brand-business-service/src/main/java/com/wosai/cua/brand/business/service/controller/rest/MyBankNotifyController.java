package com.wosai.cua.brand.business.service.controller.rest;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.mybank.manage.CommonNotifyHandle;
import com.wosai.cua.brand.business.service.mybank.manage.CommonRequestHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/mybank")
@Slf4j
public class MyBankNotifyController {

    private final CommonNotifyHandle commonNotifyHandle;

    private final CommonRequestHandle commonRequestHandle;

    @Autowired
    public MyBankNotifyController(CommonNotifyHandle commonNotifyHandle, CommonRequestHandle commonRequestHandle) {
        this.commonNotifyHandle = commonNotifyHandle;
        this.commonRequestHandle = commonRequestHandle;
    }


    @PostMapping(value = "/notify", produces = MediaType.APPLICATION_XML_VALUE)
    public String myBankNotifyManage(HttpServletRequest httpServletRequest) throws MybankApiException {
        //获取请求报文
        String xmlContext = commonRequestHandle.getXmlContextString(httpServletRequest);

        log.info("my_bank notify request:{}", xmlContext);

        //处理通知
        return commonNotifyHandle.notifyRequestHandle(xmlContext);

    }
}
