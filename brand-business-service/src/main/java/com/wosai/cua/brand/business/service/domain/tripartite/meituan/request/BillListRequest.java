package com.wosai.cua.brand.business.service.domain.tripartite.meituan.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BillListRequest extends CommonRequest {

    // APP方门店id，传商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
    @JsonProperty("app_poi_code")
    private String appPoiCode;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    // 分页查询偏移量，表示从第几条开始查，0为第一条。
    private int offset = 0;

    // 分页查询每页展示的数量，每次查询最大不能超过200。查询结果按订单完成时间降序排序，即最晚完成的排在最上面。
    private int limit = 200;

    public String getAppPoiCode() {
        return appPoiCode;
    }

    public void setAppPoiCode(String appPoiCode) {
        this.appPoiCode = appPoiCode;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

}
