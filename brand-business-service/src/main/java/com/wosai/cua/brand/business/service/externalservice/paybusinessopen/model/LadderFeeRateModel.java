package com.wosai.cua.brand.business.service.externalservice.paybusinessopen.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/21
 */
@Data
public class LadderFeeRateModel {

    private Integer min;
    private Integer max;
    @JsonProperty("fee_rate_min_value")
    private String feeRateMinValue;
    @JsonProperty("fee_rate_max_value")
    private String feeRateMaxValue;
    @JsonProperty("fee_rate_default_value")
    private String feeRateDefaultValue;
    @JsonProperty("fee_rate_min")
    private String feeRateMin;
    @JsonProperty("fee_rate_max")
    private String feeRateMax;
}
