package com.wosai.cua.brand.business.service.module.merchant.analyze;

import com.wosai.cua.brand.business.service.annotations.AnalyzeFieldCheck;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreateMerchantAnalyzeModule extends BaseMerchantAnalyzeModule{

    /**
     * 省
     */
    protected String province;
    /**
     * 市
     */
    protected String city;
    /**
     * 区
     */
    protected String district;
    /**
     * 详细地址
     */
    @AnalyzeFieldCheck(needRequired = true, message = "未填写详细地址！")
    protected String address;
}
