package com.wosai.cua.brand.business.service.module.config;

import lombok.Data;

import java.util.List;

@Data
public class ConfigModule {
    /**
     * 品牌id
     */
    protected String brandId;
    /**
     * 是否允许登录
     */
    protected Boolean allowLogin;
    /**
     * 是否允许转账
     */
    protected Boolean openTransfer;
    /**
     * 转账用途
     */
    protected List<String> purposeList;
    /**
     * 渠道id
     */
    protected String channelId;
    /**
     * 渠道类型
     */
    protected String channelType;
    /**
     * 是否需要创建收钱吧门店
     */
    protected Boolean needCreateStore;

    /**
     * 美团appid
     */
    protected String meiTuanAppid;

    /**
     * 美团密钥
     */
    protected String meiTuanSecret;

    /**
     * 是否需要特殊处理
     */
    protected Boolean needSpecialTreatment;
}
