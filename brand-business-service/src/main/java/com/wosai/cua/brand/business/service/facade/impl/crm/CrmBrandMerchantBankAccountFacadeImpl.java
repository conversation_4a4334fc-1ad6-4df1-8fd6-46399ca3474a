package com.wosai.cua.brand.business.service.facade.impl.crm;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.SearchBankInfoRequest;
import com.wosai.cua.brand.business.api.dto.response.BankInfoResponseDTO;
import com.wosai.cua.brand.business.api.facade.crm.CrmBrandMerchantBankAccountFacade;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vo.ApiRequestParam;

import java.util.Map;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class CrmBrandMerchantBankAccountFacadeImpl implements CrmBrandMerchantBankAccountFacade {

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    public CrmBrandMerchantBankAccountFacadeImpl(BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness) {
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
    }

    @Override
    public BankInfoResponseDTO getBankInfoByCardNumber(ApiRequestParam<SearchBankInfoRequest, Map> apiRequestParam) {
        return brandMerchantBankAccountBusiness.getBankInfoByCardNumber(apiRequestParam.getBodyParams().getCardNumber());
    }
}
