package com.wosai.cua.brand.business.service.domain.service.impl.analyze;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.model.dto.response.ClearParamRspDTO;
import com.shouqianba.service.MerchantProviderParamsService;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.PooledMerchantAnalyzeService;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.enums.PooledMerchantExcelFieldEnum;
import com.wosai.cua.brand.business.service.helper.AnalyzeHelper;
import com.wosai.cua.brand.business.service.helper.FileHelper;
import com.wosai.cua.brand.business.service.helper.ThreadLocalHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.PooledMerchantAnalyzeModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PooledMerchantAnalyzeServiceImpl implements PooledMerchantAnalyzeService {


    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    private final BrandDomainService brandDomainService;


    private final AnalyzeHelper analyzeHelper;

    /**
     * 字段列序号对应的枚举Map
     */
    private static final Map<Integer, PooledMerchantExcelFieldEnum> POOLED_MERCHANT_EXCEL_FIELD_ENUM_MAP = Maps.newConcurrentMap();

    /**
     * 字段对应的实体类属性缓存
     */
    private static final Map<PooledMerchantExcelFieldEnum, Field> FIELD_CACHE = Maps.newHashMap();

    /**
     * 字段名对应的枚举Map
     */
    private static final Map<String, PooledMerchantExcelFieldEnum> FIELD_NAME_EXCEL_FIELD_ENUM_MAP = Maps.newHashMap();

    @Autowired
    public PooledMerchantAnalyzeServiceImpl(BrandDomainService brandDomainService, AnalyzeHelper analyzeHelper) {
        this.brandDomainService = brandDomainService;
        this.analyzeHelper = analyzeHelper;
    }

    @PostConstruct
    public void initMap() {
        POOLED_MERCHANT_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(PooledMerchantExcelFieldEnum.values()).collect(Collectors.toMap(PooledMerchantExcelFieldEnum::getColumnNo, Function.identity())));
        FIELD_NAME_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(PooledMerchantExcelFieldEnum.values()).collect(Collectors.toMap(PooledMerchantExcelFieldEnum::getFieldName, Function.identity())));
        this.cacheFields();
    }

    private void cacheFields() {
        Class<?> clazz = PooledMerchantAnalyzeModule.class;
        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                String fieldName = field.getName();
                PooledMerchantExcelFieldEnum pooledMerchantExcelFieldEnum = FIELD_NAME_EXCEL_FIELD_ENUM_MAP.get(fieldName);
                if (Objects.isNull(pooledMerchantExcelFieldEnum)) {
                    continue;
                }
                FIELD_CACHE.put(pooledMerchantExcelFieldEnum, field);
            }
            clazz = clazz.getSuperclass();
        }
    }

    private Field getField(PooledMerchantExcelFieldEnum fieldEnum) throws NoSuchFieldException {
        if (FIELD_CACHE.containsKey(fieldEnum)) {
            return FIELD_CACHE.get(fieldEnum);
        }
        throw new NoSuchFieldException();
    }

    @Override
    public List<PooledMerchantAnalyzeModule> analyzeData(List<String[]> fields) {
        List<PooledMerchantAnalyzeModule> merchantAnalyzeModules = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fields)) {
            return merchantAnalyzeModules;
        }
        // 从第二行开始
        for (int row = 1; row < fields.size(); row++) {
            PooledMerchantAnalyzeModule analyze = this.analyze(row, fields.get(row));
            if (Objects.nonNull(analyze)) {
                merchantAnalyzeModules.add(analyze);
            }
        }
        return merchantAnalyzeModules;
    }

    private PooledMerchantAnalyzeModule analyze(int row, String[] fieldArray) {
        PooledMerchantAnalyzeModule module = new PooledMerchantAnalyzeModule();
        module.setRow(row);
        for (int i = 0; i < fieldArray.length; i++) {
            PooledMerchantExcelFieldEnum pooledMerchantExcelFieldEnum = POOLED_MERCHANT_EXCEL_FIELD_ENUM_MAP.get(i);
            if (Objects.isNull(pooledMerchantExcelFieldEnum)) {
                continue;
            }
            try {
                Field field = this.getField(pooledMerchantExcelFieldEnum);
                this.analyzeField(pooledMerchantExcelFieldEnum, field, module, fieldArray[i]);
            } catch (NoSuchFieldException e) {
                log.warn("解析excel未找到相应字段：{}", pooledMerchantExcelFieldEnum.getFieldName());
                return null;
            } catch (IllegalAccessException e) {
                log.warn("解析excel字段异常", e);
                return null;
            } catch (BrandBusinessException be) {
                log.warn("解析excel异常。", be);
                return null;
            }
        }
        return module;
    }

    private void analyzeField(
            PooledMerchantExcelFieldEnum pooledMerchantExcelFieldEnum,
            Field field,
            PooledMerchantAnalyzeModule merchantAnalyzeModule,
            String fieldValue
    ) throws IllegalAccessException {
        String value = fieldValue;
        if (StringUtils.isBlank(value)) {
            return;
        }
        value = StringUtils.trim(value);
        field.setAccessible(true);
        if (pooledMerchantExcelFieldEnum.equals(PooledMerchantExcelFieldEnum.BRAND_MERCHANT_TYPE)) {
            value = MerchantTypeEnum.getMerchantTypeByDesc(value);
        }
        field.set(merchantAnalyzeModule, value);
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantModuleList(String brandId, List<PooledMerchantAnalyzeModule> merchantAnalyzeModules) {
        List<BrandMerchantModule> brandMerchantModules = Lists.newArrayList();
        if (CollectionUtils.isEmpty(merchantAnalyzeModules)) {
            return brandMerchantModules;
        }
        merchantAnalyzeModules.forEach(merchantAnalyzeModule -> {
            try {
                this.addBrandMerchantModule(brandId, merchantAnalyzeModule, brandMerchantModules);
            } catch (Exception e) {
                log.error("创建商户失败", e);
                Map<Integer, String> errorMapMsg = this.getErrorMapMsg();
                if (MapUtils.isEmpty(errorMapMsg)) {
                    errorMapMsg = Maps.newHashMap();
                }
                analyzeHelper.recordErrorMsg(errorMapMsg, merchantAnalyzeModule, e.getMessage());
                ThreadLocalHelper.set(BrandImportSheetEnum.POOLED_MERCHANT.name(), errorMapMsg);
            }
        });
        return brandMerchantModules;
    }

    private void addBrandMerchantModule(String brandId, PooledMerchantAnalyzeModule merchantAnalyzeModule, List<BrandMerchantModule> brandMerchantModules) {
        if (MerchantTypeEnum.BRAND_OWNER.getMerchantType().equals(merchantAnalyzeModule.getBrandMerchantType())) {
            throw new BrandBusinessException("归集模式下不支持导入品牌商！");
        }
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantAnalyzeModule.getMerchantSn(), null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        BrandMerchantModule merchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchant.getId());
        if (Objects.nonNull(merchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_BIND_IN_BRAND);
        }
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
        if (Objects.isNull(license)) {
            throw new BrandBusinessException("商户证件信息异常！");
        }
        UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
        if (Objects.isNull(superAdmin)) {
            throw new BrandBusinessException("商户管理员信息异常！");
        }
        BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
        brandMerchantModule.setBrandId(brandId);
        brandMerchantModule.setOutMerchantNo(merchantAnalyzeModule.getOutMerchantSn());
        brandMerchantModule.setMerchantSn(merchantAnalyzeModule.getMerchantSn());
        brandMerchantModule.setMerchantId(merchant.getId());
        brandMerchantModule.setMerchantName(merchant.getName());
        brandMerchantModule.setMerchantType(merchantAnalyzeModule.getBrandMerchantType());
        brandMerchantModule.setNotifyMobile(StringUtils.isBlank(merchantAnalyzeModule.getMobile()) ? superAdmin.getUcUserInfo().getCellphone() : merchantAnalyzeModule.getMobile());
        ClearParamRspDTO acquirer = merchantProviderParamsService.getClearParamBySnAndAcquirer(merchantAnalyzeModule.getMerchantSn(), "fuyou");
        if (Objects.isNull(acquirer)) {
            throw new BrandBusinessException("未找到商户收单机构信息！");
        }
        brandMerchantModule.setMemberId(acquirer.getAcquirerMerchantId());
        switch (license.getType()) {
            case 0:
                brandMerchantModule.setType(BrandMerchantTypeEnum.PERSONAL.getType());
                break;
            case 1:
                brandMerchantModule.setType(BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType());
                break;
            case 2:
                brandMerchantModule.setType(BrandMerchantTypeEnum.COMPANY.getType());
                break;
            default:
                throw new BrandBusinessException("不允许的证件类型");
        }
        brandMerchantModule.setMerchantDockingMode(MerchantDockingModeEnum.COLLECTION.getCode());
        brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.PENDING.getStatus());
        brandMerchantModules.add(brandMerchantModule);
    }

    @Override
    public void createErrorMsgIntoExcel(String filePath) {
        Map<Integer, String> errorMapMsg = getErrorMapMsg();
        if (errorMapMsg == null) return;
        FileHelper.createExcelErrorMsg(filePath, BrandImportSheetEnum.POOLED_MERCHANT.getSheet(), BrandImportSheetEnum.POOLED_MERCHANT.getErrorMsgColNum(), errorMapMsg);
    }

    @Override
    public void checkParams(List<PooledMerchantAnalyzeModule> merchantAnalyzeModules) {
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(merchantAnalyzeModules)) {
            Iterator<PooledMerchantAnalyzeModule> iterator = merchantAnalyzeModules.iterator();
            while (iterator.hasNext()) {
                PooledMerchantAnalyzeModule analyzeModule = iterator.next();
                boolean needRemove = analyzeHelper.checkFields(analyzeModule.getClass(), analyzeModule, errorMap, BrandImportSheetEnum.POOLED_MERCHANT);
                if (needRemove) {
                    iterator.remove();
                }
            }
            ThreadLocalHelper.set(BrandImportSheetEnum.POOLED_MERCHANT.name(), errorMap);
        }
    }

    private Map<Integer, String> getErrorMapMsg() {
        Object o = ThreadLocalHelper.get(BrandImportSheetEnum.POOLED_MERCHANT.name());
        if (Objects.isNull(o)) {
            return Collections.emptyMap();
        }
        Map errorMap = JSON.parseObject(JSON.toJSONString(o), Map.class);
        HashMap<Integer, String> errorMapMsg = Maps.newHashMap();
        errorMap.forEach((key, value) -> {
            boolean flag = Objects.nonNull(key) && NumberUtils.isCreatable(key.toString()) && Objects.nonNull(value);
            if (flag) {
                errorMapMsg.put(Integer.valueOf(key.toString()), value.toString());
            }
        });
        return errorMapMsg;
    }
}
