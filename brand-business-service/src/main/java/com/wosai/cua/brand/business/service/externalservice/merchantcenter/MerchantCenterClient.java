package com.wosai.cua.brand.business.service.externalservice.merchantcenter;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantBusinessLicenseQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.PhotoInfoBO;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.StoreExtAndPicturesQueryResult;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreExtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Component
public class MerchantCenterClient {

    @Autowired
    private StoreExtService storeExtService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    public MerchantInfoQueryResult queryLatestMerchantInfoByMerchantIdOrMerchantSn(MerchantInfoQueryRequest request) {
        MerchantInfo merchantInfo;
        if (WosaiStringUtils.isNotEmpty(request.getMerchantId())) {
            merchantInfo = merchantService.getMerchantById(request.getMerchantId(), request.getDevCode());
        } else {
            merchantInfo = merchantService.getMerchantBySn(request.getMerchantSn(), request.getDevCode());
        }
        if (Objects.isNull(merchantInfo)) {
            throw new CommonInvalidParameterException("商户信息不存在");
        }
        return new MerchantInfoQueryResult()
                .setMerchantId(merchantInfo.getId())
                .setMerchantSn(merchantInfo.getSn())
                .setMerchantType(merchantInfo.getMerchant_type())
                .setName(merchantInfo.getName())
                .setOwnerCellphone(merchantInfo.getOwner_cellphone())
                .setCustomerPhone(merchantInfo.getCustomer_phone())
                .setBusinessName(merchantInfo.getBusiness_name())
                .setIndustry(merchantInfo.getIndustry())
                .setAlias(merchantInfo.getAlias())
                .setOwnerName(merchantInfo.getOwner_name())
                .setContactName(merchantInfo.getContact_name())
                .setContactCellphone(merchantInfo.getContact_cellphone())
                .setProvince(merchantInfo.getProvince())
                .setCity(merchantInfo.getCity())
                .setDistrict(merchantInfo.getDistrict())
                .setStreetAddress(merchantInfo.getStreet_address())
                .setLongitude(merchantInfo.getLongitude())
                .setLatitude(merchantInfo.getLatitude());
    }

    public StoreExtAndPicturesQueryResult findLatestStoreExtAndPicturesByMerchantId(String merchantId) {
        StotreExtInfoAndPictures lastStoreExtAndPicturesByMerchantId = storeExtService.findLastStoreExtAndPicturesByMerchantId(merchantId);
        if (Objects.isNull(lastStoreExtAndPicturesByMerchantId)) {
            throw new CommonInvalidParameterException("门店照片信息不存在");
        }
        PhotoInfo brandPhoto = lastStoreExtAndPicturesByMerchantId.getBrandPhoto();
        PhotoInfo indoorMaterialPhoto = lastStoreExtAndPicturesByMerchantId.getIndoorMaterialPhoto();
        PhotoInfo outdoorMaterialPhoto = lastStoreExtAndPicturesByMerchantId.getOutdoorMaterialPhoto();
        return new StoreExtAndPicturesQueryResult()
                .setBrandPhoto(Objects.nonNull(brandPhoto) ? new PhotoInfoBO().setUrl(lastStoreExtAndPicturesByMerchantId.getBrandPhoto().getUrl()) : null)
                .setIndoorMaterialPhoto(Objects.nonNull(indoorMaterialPhoto) ? new PhotoInfoBO().setUrl(lastStoreExtAndPicturesByMerchantId.getIndoorMaterialPhoto().getUrl()) : null)
                .setOutdoorMaterialPhoto(Objects.nonNull(outdoorMaterialPhoto) ? new PhotoInfoBO().setUrl(lastStoreExtAndPicturesByMerchantId.getOutdoorMaterialPhoto().getUrl()) : null);
    }

    public MerchantBusinessLicenseQueryResult queryLatestMerchantBusinessLicense(String merchantId) {
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(merchantId);
        if (Objects.isNull(merchantBusinessLicense)) {
            return null;
        }
        return new MerchantBusinessLicenseQueryResult()
                .setMerchantId(merchantId)
                .setType(merchantBusinessLicense.getType())
                .setName(merchantBusinessLicense.getName())
                .setPhoto(merchantBusinessLicense.getPhoto())
                .setNumber(merchantBusinessLicense.getNumber())
                .setBusinessScope(merchantBusinessLicense.getBusiness_scope())
                .setValidity(merchantBusinessLicense.getValidity())
                .setAddress(merchantBusinessLicense.getAddress())
                .setLegalPersonIdType(merchantBusinessLicense.getLegal_person_id_type())
                .setLegalPersonIdNumber(merchantBusinessLicense.getLegal_person_id_number())
                .setLegalPersonName(merchantBusinessLicense.getLegal_person_name())
                .setLegalPersonIdCardFrontPhoto(merchantBusinessLicense.getLegal_person_id_card_front_photo())
                .setLegalPersonIdCardBackPhoto(merchantBusinessLicense.getLegal_person_id_card_back_photo())
                .setIdValidity(merchantBusinessLicense.getId_validity())
                .setLegalPersonIdCardAddress(merchantBusinessLicense.getLegal_person_id_card_address())
                .setLegalPersonIdCardIssuingAuthority(merchantBusinessLicense.getLegal_person_id_card_issuing_authority());
    }

}
