package com.wosai.cua.brand.business.service.facade.impl.sp;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.sp.PagingBrandMerchantsRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.common.ListResult;
import com.wosai.cua.brand.business.api.dto.response.sp.BrandMerchantsPagingResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.sp.BrandOpenAppInfoQueryResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.sp.BrandSimpleInfoResponseDTO;
import com.wosai.cua.brand.business.api.enums.AppOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.sp.SpBrandFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.MerchantBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppInfoOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.MerchantCenterClient;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.tag.TagClient;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@AutoJsonRpcServiceImpl
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SpBrandFacadeImpl implements SpBrandFacade {

    private final BrandBusiness brandBusiness;
    private final MerchantBusinessOpenClient merchantBusinessOpenClient;
    private final MerchantCenterClient merchantCenterClient;
    private final TagClient tagClient;
    @Value("${appid.brandPaymentHub}")
    private String brandPaymentHubAppId;

    @Override
    public List<BrandOpenAppInfoQueryResponseDTO> queryBrandOpenAppInfos(QueryBrandsDTO queryBrandsDTO) {
        if (WosaiStringUtils.isEmpty(queryBrandsDTO.getBrandId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_ID_NOT_BE_NULL);
        }
        return brandBusiness.queryBrandOpenAppInfos(queryBrandsDTO.getBrandId());
    }

    @Override
    public ListResult<BrandMerchantsPagingResponseDTO> pagingBrandMerchants(PagingBrandMerchantsRequestDTO pagingBrandMerchantsRequest) {
        return brandBusiness.pagingBrandMerchants(pagingBrandMerchantsRequest);
    }

    @Override
    public Map<String, Object> queryBrandMerchantBusinessLicense(QueryBrandsDTO queryBrandsDTO) {
        if (WosaiStringUtils.isEmpty(queryBrandsDTO.getBrandId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_ID_NOT_BE_NULL);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = brandBusiness.queryBrandMerchantBusinessLicense(queryBrandsDTO.getBrandId());
        return JSON.parseObject(JSON.toJSONString(merchantBusinessLicenseInfo), Map.class);
    }

    @Override
    public AppOpenStatusEnum queryBrandPaymentHubOpenStatus(QueryBrandsDTO queryBrandsDTO) {
        if (WosaiStringUtils.isEmpty(queryBrandsDTO.getBrandId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_ID_NOT_BE_NULL);
        }
        BrandDetailInfoDTO brandDetailInfo = brandBusiness.getBrandDetailInfoByBrandId(queryBrandsDTO.getBrandId(), false);
        if (Objects.isNull(brandDetailInfo)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (WosaiStringUtils.isEmpty(brandDetailInfo.getMerchantSn())) {
            return AppOpenStatusEnum.INIT;
        }
        MerchantInfoQueryResult merchantInfoQueryResult = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(brandDetailInfo.getMerchantSn()));
        boolean hasPaymentHubTag = tagClient.hasPaymentHubTag(merchantInfoQueryResult.getMerchantId());
        if (hasPaymentHubTag) {
            return AppOpenStatusEnum.SUCCESS;
        }
        List<AppInfoOpenResult> appInfoOpenResults = merchantBusinessOpenClient.queryAppInfoOpenResultByMerchantId(merchantInfoQueryResult.getMerchantId());
        Optional<AppInfoOpenResult> paymentHubOpenResult = appInfoOpenResults.stream().filter(r -> brandPaymentHubAppId.equals(r.getAppId())).findFirst();
        if (!paymentHubOpenResult.isPresent()) {
            return AppOpenStatusEnum.INIT;
        }
        return AppOpenStatusEnum.getByCode(paymentHubOpenResult.get().getStatus().getCode());
    }

    @Override
    public BrandSimpleInfoResponseDTO queryBrandSimpleInfoByMerchantId(QueryBrandsDTO queryBrandsDTO) {
        if (StringUtils.isBlank(queryBrandsDTO.getMerchantId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_ID_NOT_BE_NULL);
        }
        List<BrandSimpleInfoDTO> brandSimpleInfoDTO = brandBusiness.getBrandInfoListByMerchantId(queryBrandsDTO.getMerchantId(), false);
        if (WosaiCollectionUtils.isEmpty(brandSimpleInfoDTO)) {
            return null;
        }
        BrandSimpleInfoResponseDTO brandSimpleInfoResponseDTO = new BrandSimpleInfoResponseDTO();
        brandSimpleInfoResponseDTO.setBrandId(brandSimpleInfoDTO.get(0).getBrandId());
        brandSimpleInfoResponseDTO.setBrandName(brandSimpleInfoDTO.get(0).getName());
        return brandSimpleInfoResponseDTO;
    }
}
