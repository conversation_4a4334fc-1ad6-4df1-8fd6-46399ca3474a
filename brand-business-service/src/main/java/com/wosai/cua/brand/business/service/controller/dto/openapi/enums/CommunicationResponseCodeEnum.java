package com.wosai.cua.brand.business.service.controller.dto.openapi.enums;

import lombok.Getter;

@Getter
public enum CommunicationResponseCodeEnum {
    SUCCESS("200", "成功"),
    FAIL("999999", "失败"),
    ;
    private final String code;

    private final String message;

    CommunicationResponseCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
