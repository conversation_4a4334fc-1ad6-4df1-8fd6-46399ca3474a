package com.wosai.cua.brand.business.service.job;

import com.alibaba.fastjson.JSON;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.ChangeBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.job.BaseJobParamDTO;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class StoreSnCompensateJobHandler {

    private final BrandDomainService brandDomainService;

    private final StoreService storeService;

    public StoreSnCompensateJobHandler(BrandDomainService brandDomainService, StoreService storeService) {
        this.brandDomainService = brandDomainService;
        this.storeService = storeService;
    }

    @XxlJob("compensateStoreSn")
    public void compensate() throws InterruptedException {
        String jobParam = XxlJobHelper.getJobParam();
        int pageSize = 1000;
        long sleepTime = 1000L;
        if (!StringUtils.isEmpty(jobParam)) {
            log.info("BrandCallbackRecordsJobHandler jobParam is {}", jobParam);
            BaseJobParamDTO jobParamDto = JSON.parseObject(jobParam, BaseJobParamDTO.class);
            if (Objects.nonNull(jobParamDto.getPageSize())) {
                pageSize = jobParamDto.getPageSize();
            }
            if (Objects.nonNull(jobParamDto.getSleepTime())) {
                sleepTime = jobParamDto.getSleepTime();
            }
        }
        long startId = 0;
        List<BrandMerchantModule> brandMerchantModules;
        do {
            LocalTime currentTime = LocalTime.now();
            brandMerchantModules = brandDomainService.pageBrandMerchantById(startId, pageSize);
            if (CollectionUtils.isEmpty(brandMerchantModules)) {
                log.info("当前时间：{}，StoreSnCompensateJobHandler执行中断：startId = {}，pageSize = {}", currentTime.format(DateTimeFormatter.ofPattern("HH:mm:ss")), startId, pageSize);
                return;
            }
            Thread.sleep(sleepTime);
            brandMerchantModules.forEach(brandMerchantModule -> {
                if (StringUtils.isNotBlank(brandMerchantModule.getAssociatedSqbStoreId())) {
                    log.info("StoreSnCompensateJobHandler 补偿数据：{}", brandMerchantModule);
                    StoreInfo store = storeService.getStoreById(brandMerchantModule.getAssociatedSqbStoreId(), null);
                    if (Objects.nonNull(store)) {
                        brandMerchantModule.setSqbStoreSn(store.getSn());
                    }
                    ChangeBrandMerchantModule changeBrandMerchantModule = new ChangeBrandMerchantModule();
                    changeBrandMerchantModule.setId(brandMerchantModule.getId());
                    changeBrandMerchantModule.setSqbStoreSn(brandMerchantModule.getSqbStoreSn());
                    brandDomainService.updateBrandMerchantByFields(changeBrandMerchantModule);
                }
            });
            startId = brandMerchantModules.get(brandMerchantModules.size() - 1).getId();
        } while (!CollectionUtils.isEmpty(brandMerchantModules));
    }
}
