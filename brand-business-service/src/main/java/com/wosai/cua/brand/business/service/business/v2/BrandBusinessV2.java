package com.wosai.cua.brand.business.service.business.v2;

import com.wosai.cua.brand.business.api.dto.brand.ModifyAccountInfoResultDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppSubmitOpenAccountRequestDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.req.UpdateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.common.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class BrandBusinessV2 {

    @Autowired
    private MerchantService merchantService;

    @Autowired 
    private BrandDomainService brandDomainService;


    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantBizBankAccountService merchantBizBankAccountService;

    /**
     * 修改开户提交参数
     *
     * @param requestDTO 开户参数
     */
    public ModifyAccountInfoResultDTO modifyAccountInfo(AppSubmitOpenAccountRequestDTO request) {
        MerchantInfo merchant = merchantService.getMerchantBySn(request.getBrandMerchantSn(), null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(merchant.getId());
        if (Objects.isNull(license)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BUSINESS_LICENSE_NOT_EXIST);
        }
        switch (request.getMerchantType()) {
            case PERSONAL:
                license.setName(request.getName());
                license.setLegal_person_name(request.getName());
                license.setLegal_person_id_number(request.getIdNumber());
                merchant.setName(request.getName());
                merchant.setContact_name(request.getName());
                merchant.setContact_cellphone(request.getMobile());
                break;
            case INDIVIDUAL_BUSINESS:
            case COMPANY:
                license.setName(request.getBusinessLicenseName());
                license.setNumber(request.getSocialCreditCode());
                license.setLegal_person_name(request.getLegalPersonName());
                license.setLegal_person_id_type(request.getLegalPersonIdType());
                license.setLegal_person_id_number(request.getLegalPersonIdNumber());
                merchant.setName(request.getBusinessLicenseName());
                merchant.setContact_cellphone(request.getSigningMobile());
                break;
        }
        try {
            UpdateMerchantBusinessLicenseReq bean = JacksonUtil.toBean(JacksonUtil.toJsonString(license), UpdateMerchantBusinessLicenseReq.class);
            merchantBusinessLicenseService.updateMerchantBusinessLicense(bean, null);
        } catch (Exception e) {
            log.error("修改商户营业执照失败", e);
        }
        try {
            UpdateMerchantReq bean = JacksonUtil.toBean(JacksonUtil.toJsonString(merchant), UpdateMerchantReq.class);
            merchantService.updateMerchant(bean, null);
        } catch (Exception e) {
            log.error("修改商户失败", e);
        }
        return ModifyAccountInfoResultDTO.builder()
                .merchantId(merchant.getId())
                .merchantSn(merchant.getSn())
                .build();
    }

    public void supplementMerchantBusinessLicenseAndContactInfo(BrandMerchantModule brandMerchantModule){
        brandMerchantModule.setMerchantBusinessLicense(merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(brandMerchantModule.getMerchantId(),null));
        MerchantInfo merchant = merchantService.getMerchantBySn(brandMerchantModule.getMerchantSn(), null);
        if(Objects.nonNull(merchant)){
            brandMerchantModule.setMerchantContactName(merchant.getContact_name());
            brandMerchantModule.setMerchantContactPhone(merchant.getContact_cellphone());
            brandMerchantModule.setMerchantContactEmail(merchant.getContact_email());
        }
    }
}
