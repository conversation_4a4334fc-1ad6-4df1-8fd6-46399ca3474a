package com.wosai.cua.brand.business.service.domain.tripartite.citic.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 无需验签的xml标签注解
 * 加在相关类上
 * <AUTHOR>
 */
@Target({ElementType.FIELD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface NoNeedForSignature {
    /**
     * 无需验签的标签名称
     */
    String[] name() default {};
}
