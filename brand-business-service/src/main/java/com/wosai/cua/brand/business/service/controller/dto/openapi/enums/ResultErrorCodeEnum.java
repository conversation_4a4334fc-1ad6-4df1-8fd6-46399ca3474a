package com.wosai.cua.brand.business.service.controller.dto.openapi.enums;

import lombok.Getter;

@Getter
public enum ResultErrorCodeEnum {
    INVALID_PARAMS("400","INVALID_PARAMS"),
    UNKNOWN_SYSTEM_ERROR("500","UNKNOWN_SYSTEM_ERROR")
    ;
    private final String code;
    private final String message;

    ResultErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
