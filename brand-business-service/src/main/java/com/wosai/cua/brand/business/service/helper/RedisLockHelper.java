package com.wosai.cua.brand.business.service.helper;

import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;

/**
 * 基于redis实现的简易分布式锁
 */
@Component
@Slf4j
public class RedisLockHelper {

    public static final String MY_BANK_NOTIFY_MERCHANT_LOCK_KEY = "mybank_notify_merchant_lock:%s";

    private static final DefaultRedisScript<Long> LOCK_LUA_SCRIPT = new DefaultRedisScript<>(
            "if redis.call('setnx',KEYS[1],ARGV[1]) == 1 then return redis.call('expire',KEYS[1],ARGV[2])  else return 0 end"
            , Long.class
    );

    private static final DefaultRedisScript<Long> UNLOCK_LUA_SCRIPT = new DefaultRedisScript<>(
            "if redis.call('get',KEYS[1]) == ARGV[1] then return redis.call('del',KEYS[1]) else return 0 end"
            , Long.class
    );

    private final StringRedisTemplate redisTemplate;

    private final ApolloConfig apolloConfig;

    public RedisLockHelper(StringRedisTemplate redisTemplate, ApolloConfig apolloConfig) {
        this.redisTemplate = redisTemplate;
        this.apolloConfig = apolloConfig;
    }

//    /**
//     * redis setNx的集群排它锁
//     *
//     * @param key
//     * @param second
//     * @return
//     * @see <a href="http://redis.io/commands/setnx">Redis Documentation: SETNX</a>
//     */
//    public boolean lock(String key, long second) {
//        key = LOCK_KEY_PREFIX + key;
//        return lock(key, String.valueOf(System.currentTimeMillis() + second * 1000), second);
//    }
//
//    public boolean lock(String key) {
//        return lock(key, timeout);
//    }

    /**
     * 加锁
     *
     * @param key
     * @param value
     * @param timeInSeconds
     * @return
     */
    public boolean lock(final String key, final String value, long timeInSeconds) {
        Long result = redisTemplate.execute(LOCK_LUA_SCRIPT, Collections.singletonList(key), value, String.valueOf(timeInSeconds));
        return Objects.nonNull(result) && result == 1;
    }

    /**
     * 加锁
     *
     * @param key
     * @param value
     * @return
     */
    public boolean lock(final String key, final String value) {
        return lock(key, value, apolloConfig.getLockExpireTime());
    }

    /**
     * 解锁
     *
     * @param key
     * @param value
     */
    public boolean unlock(String key, String value) {
        Long result = null;
        try {
            result = redisTemplate.execute(UNLOCK_LUA_SCRIPT, Collections.singletonList(key), value);
        } catch (Exception e) {
            log.error("unlock unsuccessful！",e);
        }
        return result != null && result == 1;
    }

    /**
     * 尝试加锁
     * @param key
     * @param value
     * @param timeOutSeconds 超时时间
     * @return
     */
    public boolean tryLock(String key, String value, long timeOutSeconds){
        long startTime = System.currentTimeMillis();
        while (true){
            if (System.currentTimeMillis() - startTime > timeOutSeconds * 1000){
                return false;
            }
            if (lock(key, value)){
                return true;
            }
        }
    }

}
