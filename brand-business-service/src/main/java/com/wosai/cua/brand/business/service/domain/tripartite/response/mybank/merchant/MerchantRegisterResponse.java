package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.MerchantRegisterResponseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Mybank Api: ant.mybank.merchantprod.merch.register
 *
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
@Data
public class MerchantRegisterResponse extends MybankResponse {

    private static final long serialVersionUID = 8701232510945712133L;
    @XmlElementRef
    private MerchantprodMerchRegister merchantprodMerchRegister;

    @EqualsAndHashCode(callSuper = true)
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "response")
    @Data
    public static class MerchantprodMerchRegister extends MybankObject {

        private static final long serialVersionUID = 1040816789430043248L;
        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private ResponseHead responseHead;

        /**
         * 业务应答参数（body）
         */
        @XmlElementRef
        private MerchantRegisterResponseModel merchantRegisterResponseModel;

    }
}