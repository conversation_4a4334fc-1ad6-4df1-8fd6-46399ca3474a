package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.BaseResponseBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "xml")
public class CloseAccountResponseBody extends BaseResponseBody {
    /**
     * 用户编号
     */
    @Sign
    private String accountIn;
    /**
     * 注销证明
     * Base64编码格式的图片
     */
    private String img;
}
