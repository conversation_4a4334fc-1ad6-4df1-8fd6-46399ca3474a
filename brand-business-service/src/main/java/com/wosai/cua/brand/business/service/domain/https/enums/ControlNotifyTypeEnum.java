package com.wosai.cua.brand.business.service.domain.https.enums;

import lombok.Getter;

@Getter
public enum ControlNotifyTypeEnum {
    /**
     * 管控
     */
    CONTROL("1"),
    /**
     * 解管控
     */
    RELEASE_CONTROL("2"),
    /**
     * 弱管控
     */
    WEAK_CONTROL("3"),
    /**
     * 延迟管控
     */
    DELAY_CONTROL("4");

    private final String code;

    ControlNotifyTypeEnum(String code) {
        this.code = code;
    }

    public static ControlNotifyTypeEnum getControlNotifyTypeEnum(String code) {
        for (ControlNotifyTypeEnum controlNotifyTypeEnum : ControlNotifyTypeEnum.values()) {
            if (controlNotifyTypeEnum.getCode().equals(code)) {
                return controlNotifyTypeEnum;
            }
        }
        return null;
    }

    public static String getName(String code) {
        for (ControlNotifyTypeEnum controlNotifyTypeEnum : ControlNotifyTypeEnum.values()) {
            if (controlNotifyTypeEnum.getCode().equals(code)) {
                return controlNotifyTypeEnum.name();
            }
        }
        return "";
    }

}