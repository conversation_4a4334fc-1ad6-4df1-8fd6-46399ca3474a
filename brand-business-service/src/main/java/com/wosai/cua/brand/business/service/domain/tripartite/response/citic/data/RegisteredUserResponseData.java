package com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlElement;

@EqualsAndHashCode(callSuper = true)
@Data
public class RegisteredUserResponseData extends CiticResponseData {
    @XmlElement(name = "USER_ID")
    private String userId;//用户编号
    @XmlElement(name = "PWDID")
    private String pwdId;//动态密码句柄
    @XmlElement(name = "TRANS_ID")
    private String transId;
    @XmlElement(name = "IS_NEED_CHECK")
    private String isNeedCheck;//是否需要审核
}
