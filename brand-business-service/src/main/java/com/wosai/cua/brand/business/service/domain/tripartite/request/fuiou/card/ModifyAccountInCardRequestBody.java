package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.card;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 接口说明：
 * - 调用此接口，给用户新增绑定银行卡或解绑已绑定的银行卡。一个用户最多可存在3张生效状态的银行卡；
 * - 调用此接口后，个人完成信息验证后同步返回结果，企业将通过短信或url中富友页面进行确认及身份验证，生效状态将通过TZ001异步回调通知。
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
public class ModifyAccountInCardRequestBody extends BaseRequestBody {
    /**
     * 用户编号
     */
    @Sign
    private String accountIn;
    /**
     * 业务类型
     * 必填
     * 枚举值：1绑卡 2解绑
     */
    @Sign
    private String type;
    /**
     * 银行账号类型
     * 个体工商户用户必传枚举值：01法人对私卡 02企业对公户
     */
    private String outAcntNoType;
    /**
     *  户名
     *  个体工商户用户绑定银行账号类型为01法人对私卡时必传
     */
    private String outAcntNm;
    /**
     * 身份证号
     * 个体工商户用户绑定银行账号类型为01法人对私卡时必传
     */
    private String certNo;
    /**
     * 社会信用代码
     * 原CJ001接口创建的企业/个体户用户，绑卡时必传
     */
    private String shxyNo;
    /**
     * 开户行行号
     */
    @Sign
    private String interBankNo;
    /**
     * 银行账号
     */
    @Sign
    private String outAcntNo;
    /**
     * 验证类型
     * 枚举值：1 短信模式 2 返回url 不传默认1
     */
    @Sign
    private String checkType;
    /**
     * 手机号
     * 短信模式下，企业/个体户用户绑卡时支持使用新手机号发送绑卡验证链接，未传则向手开户手机号进行发送
     */
    @Sign(isNotNoneSign = true)
    private String mobile;

    public ModifyAccountInCardRequestBody(String mchntCd) {
        super(mchntCd);
    }
}
