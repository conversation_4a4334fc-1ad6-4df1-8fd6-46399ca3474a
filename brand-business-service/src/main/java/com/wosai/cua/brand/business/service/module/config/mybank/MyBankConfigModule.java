package com.wosai.cua.brand.business.service.module.config.mybank;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.brand.mybank.MyBankConfigDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.PurposeOfTransferEnum;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
public class MyBankConfigModule extends ConfigModule {
    /**
     * isv机构id
     */
    private String isvOrgId;
    /**
     * appId
     */
    private String appId;
    /**
     * isv机构私钥
     */
    private String isvPrivateKey;
    /**
     * isv机构公钥
     */
    private String isvPublicKey;
    /**
     * app公钥
     */
    private String publicKey;

    /**
     * 是否需要创建充值账户
     */
    private Boolean openTopUpAccount;

    public static BrandConfigDTO convert(MyBankConfigModule configModule, String brandId, Boolean needGetConfig) {
        if (configModule == null) {
            return null;
        }
        BrandConfigDTO brandConfigDTO = new BrandConfigDTO();
        if (Objects.nonNull(needGetConfig) && Boolean.TRUE.equals(needGetConfig)) {
            MyBankConfigDTO dto = MyBankConfigDTO.builder()
                    .appId(configModule.getAppId())
                    .isvOrgId(configModule.getIsvOrgId())
                    .isvPublicKey(configModule.getIsvPublicKey())
                    .publicKey(configModule.getPublicKey())
                    .isvPrivateKey(configModule.getIsvPrivateKey())
                    .build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            dto.setOpenTopUpAccount(configModule.getOpenTopUpAccount());
            brandConfigDTO.setMyBankConfig(dto);
        } else {
            MyBankConfigDTO dto = MyBankConfigDTO.builder().build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            dto.setOpenTopUpAccount(configModule.getOpenTopUpAccount());
            brandConfigDTO.setMyBankConfig(dto);
        }
        brandConfigDTO.setFundManagementCompanyCode(FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
        brandConfigDTO.setBrandId(brandId);
        return brandConfigDTO;
    }

    public static MyBankConfigModule convert(MyBankConfigDTO configDTO) {
        if (configDTO == null) {
            return null;
        }
        MyBankConfigModule myBankConfigModule = new MyBankConfigModule();
        myBankConfigModule.setAllowLogin(configDTO.getAllowLogin());
        myBankConfigModule.setOpenTransfer(configDTO.getOpenTransfer());
        myBankConfigModule.setPurposeList(PurposeOfTransferEnum.getCodeListByEnumList(configDTO.getPurposeList()));
        myBankConfigModule.setAppId(configDTO.getAppId());
        myBankConfigModule.setIsvOrgId(configDTO.getIsvOrgId());
        myBankConfigModule.setIsvPrivateKey(configDTO.getIsvPrivateKey());
        myBankConfigModule.setIsvPublicKey(configDTO.getIsvPublicKey());
        myBankConfigModule.setPublicKey(configDTO.getPublicKey());
        myBankConfigModule.setOpenTopUpAccount(configDTO.getOpenTopUpAccount());
        myBankConfigModule.setNeedSpecialTreatment(configDTO.getNeedSpecialTreatment());
        return myBankConfigModule;
    }

    public static BrandConfigDTO convert(String s, String brandId, Boolean needGetConfig) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        MyBankConfigModule configModule = JSON.parseObject(s, MyBankConfigModule.class);
        BrandConfigDTO brandConfigDTO = new BrandConfigDTO();
        if (Objects.nonNull(needGetConfig) && Boolean.TRUE.equals(needGetConfig)) {
            MyBankConfigDTO dto = MyBankConfigDTO.builder()
                    .appId(configModule.getAppId())
                    .isvOrgId(configModule.getIsvOrgId())
                    .isvPublicKey(configModule.getIsvPublicKey())
                    .publicKey(configModule.getPublicKey())
                    .isvPrivateKey(configModule.getIsvPrivateKey())
                    .build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setOpenTopUpAccount(configModule.getOpenTopUpAccount());
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setMyBankConfig(dto);
        } else {
            MyBankConfigDTO dto = MyBankConfigDTO.builder().build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setOpenTopUpAccount(configModule.getOpenTopUpAccount());
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setMyBankConfig(dto);
        }
        brandConfigDTO.setFundManagementCompanyCode(FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
        brandConfigDTO.setBrandId(brandId);
        return brandConfigDTO;
    }
}
