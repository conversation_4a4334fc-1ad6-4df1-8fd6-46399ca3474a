package com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.MybankSignature;

public class DefaultSigner implements Signer {

    private String privateKey;

    public DefaultSigner(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    @Override
    public String sign(String xmlContent, String charset, String signType) throws MybankApiException {
        return MybankSignature.sign(xmlContent, this.privateKey, charset, signType, MybankConstants.REQUEST);
    }

    @Override
    public String notifyResponseSign(String xmlContent, String charset, String signType) throws MybankApiException {
        return MybankSignature.sign(xmlContent, this.privateKey, charset, signType, MybankConstants.RESPONSE);
    }

    @Override
    public String webSign(String content, String charset, String signType) throws MybankApiException {
        return MybankSignature.rsa256SignContent(content, this.privateKey, signType, charset);
    }
}