package com.wosai.cua.brand.business.service.externalservice.merchantcenter.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 营业执照查询结果
 * <AUTHOR>
 * @date 2024/9/26
 */
@Data
@Accessors(chain = true)
public class MerchantBusinessLicenseQueryResult {

    private String merchantId;
    private Integer type;
    private String name;
    private String photo;
    private String number;
    private String businessScope;
    private String validity;
    private String address;
    private Integer legalPersonIdType;
    private String legalPersonIdCardFrontPhoto;
    private String legalPersonIdCardBackPhoto;
    private String legalPersonName;
    private String legalPersonIdNumber;
    private String idValidity;
    private String legalPersonIdCardAddress;
    private String legalPersonIdCardIssuingAuthority;
}
