package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

@Getter
public enum PooledMerchantExcelFieldEnum {
    BRAND_MERCHANT_TYPE(0,"brandMerchantType","未填写或填写错误品牌商户类型！",true, false, "", ""),
    MERCHANT_SN(1,"merchantSn","未填写商户编号！",true, false, "", ""),
    OUT_MERCHANT_SN(2,"outMerchantSn","",false, false, "", ""),
    MOBILE(3,"mobile","",false, true, "^(13[0-9]|14[579]|15[012356789]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$", "手机号填写错误"),
    ;
    private final int columnNo;

    private final String fieldName;

    private final String checkMessage;

    private final boolean needRequired;

    private final boolean needFormatCheck;

    private final String regex;

    private final String formatCheckMsg;

    PooledMerchantExcelFieldEnum(int columnNo, String fieldName, String checkMessage, boolean needRequired, boolean needFormatCheck, String regex, String formatCheckMsg) {
        this.columnNo = columnNo;
        this.fieldName = fieldName;
        this.checkMessage = checkMessage;
        this.needRequired = needRequired;
        this.needFormatCheck = needFormatCheck;
        this.regex = regex;
        this.formatCheckMsg = formatCheckMsg;
    }
}
