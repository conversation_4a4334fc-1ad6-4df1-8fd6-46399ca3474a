package com.wosai.cua.brand.business.service.config.rpc;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.InvocationListener;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import net.logstash.logback.marker.Markers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolationException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志切面
 * <AUTHOR>
 */
public class JsonRpcLogAspect implements InvocationListener {
    private static final Logger logger = LoggerFactory.getLogger(JsonRpcLogAspect.class);

    static List<String> notLogMethods = new ArrayList<>();

    static {
        //添加非需要打印日志方法
        notLogMethods.add("");
    }

    @Override
    public void willInvoke(Method method, List<JsonNode> arguments) {
    }

    @Override
    public void didInvoke(Method method, List<JsonNode> arguments, Object result, Throwable t, long duration) {
        try {
            Map<String, Object> toAppendEntriesMap = new HashMap<>(10);
            toAppendEntriesMap.put("method", method.getDeclaringClass().getSimpleName() + "." + method.getName());
            toAppendEntriesMap.put("request", arguments);
            toAppendEntriesMap.put("response", result);
            toAppendEntriesMap.put("duration", duration);
            if (t != null) {
                handleException(t, toAppendEntriesMap, method, arguments);
            } else {
                logger.info(Markers.appendEntries(toAppendEntriesMap), "");
            }
        } catch (Exception e) {
        }
    }

    private void handleException(Throwable t, Map<String, Object> toAppendEntriesMap, Method method, List<JsonNode> arguments) {
        String message = WosaiStringUtils.defaultIfEmpty(t.getMessage(), t.getCause().getMessage());

        if (t instanceof InvocationTargetException) {
            t = ((InvocationTargetException) t).getTargetException();
            if (t instanceof ConstraintViolationException) {
                logWarning(t, message, toAppendEntriesMap, method, arguments);
                return;
            }
        }

        if (t instanceof BrandBusinessException) {
            logWarning(t, message, toAppendEntriesMap, method, arguments);
        } else {
            toAppendEntriesMap.put("error", message);
            logger.error(Markers.appendEntries(toAppendEntriesMap), "");
            logger.error("method: {}  request: {}", method.getName(), arguments, t);
        }
    }

    private void logWarning(Throwable t, String message, Map<String, Object> toAppendEntriesMap, Method method, List<JsonNode> arguments) {
        toAppendEntriesMap.put("warn", message);
        logger.warn(Markers.appendEntries(toAppendEntriesMap), "");
        logger.warn("method: {}  request: {}", method.getName(), arguments, t);
    }
}
