package com.wosai.cua.brand.business.service.domain.service.impl.dictionary;

import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.enums.WithdrawCycleTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.DictionaryDomainService;
import com.wosai.cua.brand.business.service.module.common.dictionary.DictionaryModule;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class WithdrawCycleTypeDictionaryDomainServiceImpl implements DictionaryDomainService {
    /**
     * 字典名称
     */
    private static final String DICTIONARY = "WITHDRAW_CYCLE_TYPE";
    @Override
    public String dictionary() {
        return DICTIONARY;
    }

    @Override
    public List<DictionaryModule> getDictionaryModules() {
        List<DictionaryModule> list = Lists.newArrayList();
        Iterator<WithdrawCycleTypeEnum> iterator = Arrays.stream(WithdrawCycleTypeEnum.values()).iterator();
        while (iterator.hasNext()) {
            WithdrawCycleTypeEnum next = iterator.next();
            list.add(new DictionaryModule(next.getWithdrawCycleType(),next.getTypeDesc()));
        }
        return list;
    }
}
