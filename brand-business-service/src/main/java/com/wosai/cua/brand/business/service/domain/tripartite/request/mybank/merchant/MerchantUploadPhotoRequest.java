package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant;


import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.enums.MybankApiExceptionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankUploadRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.photo.MerchantUploadPhotoRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantUploadPhotoResponse;
import lombok.Data;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.StringBody;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.Map;
import java.util.TreeMap;

/**
 * Mybank Api: ant.mybank.merchantprod.merchant.uploadphoto
 *
 */
@Data
public class MerchantUploadPhotoRequest implements MybankUploadRequest<MerchantUploadPhotoResponse> {

    /**
     * API接口代码
     */
    private final String apiFunction = "ant.mybank.merchantprod.merchant.uploadphoto";

    /**
     * API接口版本
     */
    private String apiVersion = "1.0.0";
    /**
     * 图片
     */
    private static final String  PICTURE = "Picture";

    /**
     * 序列号id
     */
    private static final String  SERIALVERSIONUID = "SerialVersionUID";

    /**
     * 图片上传业务请求参数
     */
    private MerchantUploadPhotoRequestModel uploadphotoRequestModel;

    public MerchantUploadPhotoRequestModel getUploadphotoRequestModel() { return uploadphotoRequestModel; }

    public void setUploadphotoRequestModel(MerchantUploadPhotoRequestModel uploadphotoRequestModel) {
        if(uploadphotoRequestModel.getFunction() == null){ uploadphotoRequestModel.setFunction(this.apiFunction);}
        if(uploadphotoRequestModel.getVersion() == null){ uploadphotoRequestModel.setVersion(this.apiVersion);}
        this.uploadphotoRequestModel = uploadphotoRequestModel;
    }

    @Override
    public String getApiFunction() { return this.apiFunction; }

    @Override
    public String getApiVersion() { return this.apiVersion; }
    @Override
    public void setApiVersion(String apiVersion) { this.apiVersion = apiVersion; }

    @Override
    public Class<MerchantUploadPhotoResponse> getResponseClass() { return MerchantUploadPhotoResponse.class; }

    @Override
    public String uploadRequestSignString(Map<String, Object> map) {
        StringBuffer stringBuffer = new StringBuffer();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String entryKey = entry.getKey();
            Object entryValue = entry.getValue();
            if(!PICTURE.equals(entryKey) && !MybankConstants.SIGNATURE.equals(entryKey) && entryValue instanceof String){
                stringBuffer.append(MybankConstants.AND_SYMBOL);
                stringBuffer.append(entryKey);
                stringBuffer.append(MybankConstants.EQUAL_SYMBOL);
                stringBuffer.append((String)entryValue);
            }
        }
        return URLEncoder.encode(stringBuffer.toString().substring(1));
    }

    @Override
    public Map<String, Object> getMapByModel() throws MybankApiException {
        TreeMap<String, Object> map = new TreeMap<String, Object>();
        if (this.uploadphotoRequestModel == null){
            return map;
        }
        Class clazz = this.uploadphotoRequestModel.getClass();
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                String s = fieldName.substring(0, 1).toUpperCase();
                String s1 = fieldName.substring(1);
                map.put(new StringBuffer(s).append(s1).toString(), field.get(this.uploadphotoRequestModel));
            }
        } catch (Exception e) {
            throw new MybankApiException(MybankApiExceptionEnum.SERVER_SYSTEM_EXCEPTION,e);
        }
        map.remove(SERIALVERSIONUID);
        return map;
    }

    @Override
    public HttpEntity entityBuilder(Map<String, Object> map) throws MybankApiException {
        MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();
        try {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String entryKey = entry.getKey();
                Object entryValue = entry.getValue();
                if (entryValue instanceof String) {
                    String string = (String) entryValue;
                    entityBuilder.addPart(entryKey, new StringBody(string));
                } else if (entryValue instanceof File) {
                    File file = (File) entryValue;
                    entityBuilder.addBinaryBody(entryKey, file, ContentType.DEFAULT_BINARY, file.getName());
                } else {
                    throw new MybankApiException(MybankApiExceptionEnum.BUILD_HTTP_ENTITY_EXCEPTION);
                }
            }
        }catch (UnsupportedEncodingException e){
            throw new MybankApiException(MybankApiExceptionEnum.SERVER_SYSTEM_EXCEPTION,e);
        }
        return entityBuilder.build();
    }

    @Override
    public FunctionEnum getFunctionEnum() {
        return null;
    }
}