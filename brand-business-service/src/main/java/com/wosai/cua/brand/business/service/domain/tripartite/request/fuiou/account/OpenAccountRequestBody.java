package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户创建接口（CJ010）
 *     接口名称：V2/openAccount.fuiou
 *     接口说明：
 *     - 此接口用于创建用户，同一商户号下，个人账户（识别身份证）/企业账户（识别企业名称）只允许创建1个用户；
 *     - 通过此接口创建的协议类型为03的用户支持发起JY001\JY002\JY003\JY004\JY005\JY006\JY007\JY008\JY009\JY010\JY017接口交易；
 *     - 通过此接口创建的协议类型为非03的用户仅支持发起JY001\JY002\JY003\JY009\JY010接口交易。
 *     - 协议类型支持商户自定义，需通过bd提交商户协议进行配置，富友提供指定协议编号，商户按要求进行接口上送；
 *     - 调用此接口后，用户将通过短信或url中富友页面进行身份确认及验证，用户生效状态将通过TZ001异步回调通知。
 */
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "xml")
@Data
public class OpenAccountRequestBody extends BaseRequestBody {

    /**
     * 账户类型
     * 必填
     * 枚举值：01个人 02企业 03个体工商户
     */
    @Sign
    private String cleanType;
    /**
     * 户名
     * 必填
     */
    @Sign
    private String outAcntNm;
    /**
     * 证件类型
     * 必填
     * 1、个人用户枚举值：0身份证1其他
     * 2、企业/个体工商户枚举值：1营业执照
     */
    @Sign
    private String certTp;

    /**
     * 证件号码
     * 必填
     * 企业/个体工商户：社会信用代码个人：身份证号码
     */
    @Sign
    private String certNo;

    /**
     * 银行账户开户行
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传枚举值：01中信百信银行02华通银行渠道105厦门国际银行09苏商银行渠道110华通银行渠道211新网银行12苏商银行渠道2 该字段会根据商户配置，商户需按指定的参数上传。
     */
    @Sign
    private String channel;

    /**
     * 主体类型
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传枚举值：1企业商户2个体工商户3政府机关事业单位4其他组织
     */
    @Sign
    private String organizationType;

    /**
     * 基本账户开户许可核准号
     * 非必填
     * 企业/个体工商户用户同步开通银行账户且channel=12时，必传
     */
    @Sign
    private String bcpNo;

    /**
     * 营业执照有效期开始日期
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传
     */
    @Sign
    private String busiLicValidateStart;
    /**
     * 营业执照有效期结束日期
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传
     */
    @Sign
    private String busiLicValidateEnd;
    /**
     * 营业执照地址
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传
     */
    @Sign
    private String busiLicAddr;

    /**
     * 营业执照照片路径
     * 非必填
     * 企业/个体工商户用户同步开通银行账户且channel=06/10/11/12时，必传
     */
    @Sign
    private String busiLicPic;
    /**
     * 法人姓名
     * 非必填
     * 1、企业/个体工商户用户同步开通银行账户必传
     * 2、个体工商户绑定法人对私卡时必传
     */
    @Sign
    private String legalName;
    /**
     * 法人手机号码
     * 非必填
     * 1、企业/个体工商户用户同步开通银行账户必传
     * 2、个体工商户用户绑定法人对私卡时必传
     */
    @Sign
    private String legalMobile;
    /**
     * 法人证件类型
     * 非必填
     * 1、企业/个体工商户用户同步开通银行账户必传
     * 2、个体工商户用户绑定法人对私卡时必传
     * 枚举值：0 身份证 1 外国护照
     */
    @Sign
    private String legalCertTp;

    /**
     * 法人证件号码
     * 非必填
     * 1、企业/个体工商户用户户同步开通银行账户必传
     * 2、个体工商户用户绑定法人对私卡时必传
     */
    @Sign
    private String legalCertNo;

    /**
     * 法人证件有效期开始日期
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传
     */
    @Sign
    private String legalValidateStart;

    /**
     * 法人证件有效期结束日期
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传长期请填20991231
     */
    @Sign
    private String legalValidateEnd;

    /**
     * 法人身份证正面照片路径
     * 非必填
     * 企业/个体工商户用户同步开通银行账户且channel=06/10/11/12时，必传
     */
    @Sign
    private String legalImagF;

    /**
     * 法人身份证反面照片路径
     * 非必填
     * 企业/个体工商户用户同步开通银行账户且channel=06/10/11/12时，必传
     */
    @Sign
    private String legalImagB;

    /**
     * 联系人手机号码
     * 必填
     * 个人用户需为开户银行卡银行预留手机号或运营商同名登记手机号
     */
    @Sign
    private String mobile;
    /**
     * 联系人姓名
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传
     */
    @Sign
    private String contactName;
    /**
     * 联系人电子邮箱
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传
     */
    @Sign
    private String contactEmail;
    /**
     * 联系人证件号码
     * 非必填
     * 企业/个体工商户用户同步开通银行账户必传
     */
    @Sign
    private String contactCertNo;
    /**
     * 账户类型
     * 非必填
     * 个体工商户用户必传枚举值：
     * 01法人对私卡
     * 02企业对公户
     */
    private String outAcntNoType;
    /**
     * 账户号
     * 非必填
     * 1、个人用户必传，支持按商户配置为非必传，需走商务流程申请；
     * 2、企业/个体工商户用户必传
     */
    @Sign
    private String outAcntNo;
    /**
     * 开户行行号
     * 银行账号为企业/个体工商户对公户时开户行行号必传
     * 另提供附件《开户行信息列表》
     */
    @Sign
    private String interBankNo;
    /**
     * 分账比例
     * 10000表示100%,500表示5%如不传默认同商户最大分账比例
     */
    @Sign
    private String allocateScale;
    /**
     * 协议类型
     * 必填
     * 枚举值：
     * 01渠道合作协议
     * 02销售合作协议
     * 03账户专用协议协议模版向运营人员获取。
     * 支持自定义协议，需提前一周提交审核及技术处理
     */
    @Sign
    private String protocolType;
    /**
     * 商户用户id
     * 必填
     * 商户端唯一
     */
    @Sign
    private String mchntCdUserId;
    /**
     * 验证类型
     * 必填
     * 枚举值：1 短信模式  2 返回url  不传默认1
     */
    @Sign
    private String checkType;

    /**
     * 扩展信息
     * 非必填
     * 送标签参与签名 签名串为json企业/个体工商户用户同步开通银行账户，且channel=10/11/12时，必传
     */
    private ExtendInfo extendInfo;

    @Data
    public static class ExtendInfo {
        /**
         * 行业类别
         * channel=10/11时，必传取值见<行业类别>
         */
        private String mcc;
        /**
         * 注册地所属省
         * channel=10时，必传取值见<地区码>
         */
        private String registerProvince;
        /**
         * 注册地所属市
         * channel=10时，必传取值见<地区码>
         */
        private String registerCity;
        /**
         * 注册地所属区
         * channel=10时，必传取值见<地区码>
         */
        private String registerDistrict;
        /**
         * 注册资本
         * 单位：分
         * 金额,单位分channel=10时，必传
         */
        private Integer registeredCapital;
        /**
         * 经营范围
         * channel=11时，必传
         */
        private String businessScope;
        /**
         * 联系人证件有效期起始时间
         * channel=12时，必传
         */
        private String contactValidDateStart;
        /**
         * 联系人证件有效期结束时间
         * channel=11时，必传长期请填20991231
         */
        private String contactValidDateEnd;
        /**
         * 联系人证件正面照片路径
         * channel=11/12时，必传
         */
        private String contactPortraitFilePath;
        /**
         * 联系人证件反面照片路径
         * channel=11/12时，必传
         */
        private String contactBadgeFilePath;
        /**
         * 受益人列表beneficiaryInfoList<item>
         */
        private List<Item> beneficiaryInfoList;

        /**
         * 股东列表，channel=10/12时，传实控人信息
         */
        private List<Item> shareholderInfoList;

        @Data
        public static class Item {

            private String name;

            private String certTp;

            private String certNo;

            private String validDateStart;

            private String validDateEnd;

            private String mobile;

            private String address;

            private String portraitFilePath;

            private String badgeFilePath;

            private String beneficiaryType;
        }
    }

    public OpenAccountRequestBody(String mchntCd) {
        super(mchntCd);
    }
}
