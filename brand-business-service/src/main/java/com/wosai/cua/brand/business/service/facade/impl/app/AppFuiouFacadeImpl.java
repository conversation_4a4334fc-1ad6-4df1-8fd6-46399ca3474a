package com.wosai.cua.brand.business.service.facade.impl.app;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.app.AppResendRequestDTO;
import com.wosai.cua.brand.business.api.facade.app.AppFuiouFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.FuiouBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class AppFuiouFacadeImpl implements AppFuiouFacade {

    private final FuiouBusiness fuiouBusiness;

    private final BrandBusiness brandBusiness;

    public AppFuiouFacadeImpl(FuiouBusiness fuiouBusiness, BrandBusiness brandBusiness) {
        this.fuiouBusiness = fuiouBusiness;
        this.brandBusiness = brandBusiness;
    }

    @Override
    public Boolean resend(AppResendRequestDTO request) {
        String brandId = brandBusiness.getBrandIdByMerchantId(request.getTokenMerchantId());
        fuiouBusiness.resend(brandId, request.getDoMerchantId(),null);
        return true;
    }
}
