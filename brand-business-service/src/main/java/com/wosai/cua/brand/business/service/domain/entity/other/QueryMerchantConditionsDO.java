package com.wosai.cua.brand.business.service.domain.entity.other;

import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryMerchantConditionsDO {

    private String merchantName;

    private String merchantSn;

    private String brandId;

    private List<String> brandIds;

    private List<String> brandSnList;

    private List<String> merchantIds;

    private List<String> types;

    private List<String> merchantTypes;

    private String merchantId;

    private String ucUserId;

    private Integer deleted;

    private List<Long> strategyIdList;

    private String sqbStoreId;

    private String meiTuanStoreSn;

    private String elmStoreSn;

    private String dyStoreSn;

    private Integer strategyNotNull;

    private String accountOpenStatus;

    /**
     * 外部商户号
     */
    private String outMerchantNo;

    /**
     * 银行卡状态
     * @see BankCardActivateStatusEnum
     */
    private String bankCardActivateStatus;

    /**
     * 收付通品牌标识
     */
    private Integer sftTag;
}
