package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify.BkcloudfundsNotifyResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify.BkmerchantSettleNotifyPayResultResponseBody;

import javax.xml.bind.annotation.XmlSeeAlso;

@XmlSeeAlso({BkcloudfundsNotifyResponseBody.class, BkmerchantSettleNotifyPayResultResponseBody.class})
public abstract class ResponseBody extends MybankObject {
    private static final long serialVersionUID = 8830108506025691316L;
}