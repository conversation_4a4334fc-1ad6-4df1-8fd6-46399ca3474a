package com.wosai.cua.brand.business.service.domain.service.impl;

import com.wosai.cua.brand.business.service.domain.dao.BrandCallbackRecordsMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandCallbackRecordsDO;
import com.wosai.cua.brand.business.service.domain.service.BrandCallbackRecordsService;
import com.wosai.cua.brand.business.service.module.merchant.BrandCallbackRecordModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【brand_callback_records(资管机构回调请求记录表)】的数据库操作Service实现
* @createDate 2024-09-12 11:51:29
*/
@Service
public class BrandCallbackRecordsServiceImpl implements BrandCallbackRecordsService {

    private final BrandCallbackRecordsMapper brandCallbackRecordsMapper;

    @Autowired
    public BrandCallbackRecordsServiceImpl(BrandCallbackRecordsMapper brandCallbackRecordsMapper) {
        this.brandCallbackRecordsMapper = brandCallbackRecordsMapper;
    }

    @Override
    public int insertBrandCallbackRecords(BrandCallbackRecordModule brandCallbackRecordModule) {
        BrandCallbackRecordsDO brandCallbackRecordsDO = BrandCallbackRecordModule.convert2Do(brandCallbackRecordModule);
        if (brandCallbackRecordsDO != null) {
            return brandCallbackRecordsMapper.insert(brandCallbackRecordsDO);
        }
        return 0;
    }

    @Override
    public int updateBrandCallbackRecords(BrandCallbackRecordModule brandCallbackRecordModule) {
        BrandCallbackRecordsDO brandCallbackRecordsDO = BrandCallbackRecordModule.convert2Do(brandCallbackRecordModule);
        if (brandCallbackRecordsDO != null) {
            return brandCallbackRecordsMapper.updateById(brandCallbackRecordsDO);
        }
        return 0;
    }

    @Override
    public List<BrandCallbackRecordModule> pageSelectBrandCallbackRecords(int pageSize, Long id) {
        return BrandCallbackRecordModule.convert2Modules(brandCallbackRecordsMapper.pageSelectBrandCallbackRecords(pageSize, id));
    }

    @Override
    public int deleteBrandCallbackRecords(Long id) {
        return brandCallbackRecordsMapper.deleteById(id);
    }

}




