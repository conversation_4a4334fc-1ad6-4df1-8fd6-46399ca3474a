package com.wosai.cua.brand.business.service.enums.third;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CardAttributeEnum {
    /**
     *
     */
    TOB("B", "对公"),
    TOC("C","对私");
    /**
     * 卡属性
     */
    private final String cardAttribute;

    private final String desc;

    CardAttributeEnum(String cardAttribute, String desc) {
        this.cardAttribute = cardAttribute;
        this.desc = desc;
    }
}
