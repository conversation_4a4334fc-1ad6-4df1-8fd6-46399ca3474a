package com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util;

import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.ResponseBodySignature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.lang.reflect.Field;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by Ricky on 2016/11/19.
 */
@Slf4j
public class SignUtils {

    /**
     * sign(RSA签名方法)
     *
     * @param srcSignPacket
     * @param privateKey
     * @return String    DOM对象
     * @Exception 异常对象
     */
    public static String sign(String srcSignPacket, String privateKey)
            throws IOException, NoSuchAlgorithmException,
            InvalidKeySpecException, InvalidKeyException, SignatureException {
        if (StringUtils.isEmpty(privateKey)){
            throw new BrandBusinessException("富友配置商户私钥为空！");
        }
        // 解密由base64编码的私钥
        byte[] bytesKey = (new BASE64Decoder()).decodeBuffer(privateKey);
        // 构造PKCS8EncodedKeySpec对象
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(bytesKey);
        // KEY_ALGORITHM 指定的加密算法
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        // 取私钥匙对象
        PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);
        // 用私钥对信息生成数字签名
        Signature signature = Signature.getInstance("MD5WithRSA");
        signature.initSign(priKey);
        signature.update(srcSignPacket.getBytes("GBK"));
        return (new BASE64Encoder()).encodeBuffer(signature.sign());
    }

    public static <T> String sign(T t, Class<T> clazz, String privateKey) throws IOException, NoSuchAlgorithmException, InvalidKeySpecException, SignatureException, InvalidKeyException, IllegalAccessException {
        List<String> excludeFieldList = new ArrayList<>();
        List<Field> allFields = ReflectUtil.getAllFields(clazz);
        for (Field field : allFields) {
            field.setAccessible(true);
            Sign annotation = field.getAnnotation(Sign.class);
            if (Objects.isNull(annotation)){
                excludeFieldList.add(field.getName());
            }
            if (Objects.nonNull(annotation) && annotation.isNotNoneSign() && Objects.isNull(field.get(t))){
                excludeFieldList.add(field.getName());
            }
        }
        String generateClearText = ClearTextUtil.generateClearText(t, excludeFieldList);
        log.info("sign generateClearText:{}", generateClearText);
        return sign(generateClearText, privateKey);
    }

    public static <T extends ResponseBodySignature> boolean verify(T t, Class<T> clazz, String publicKey) throws Exception {
        List<String> excludeFieldList = new ArrayList<>();
        List<Field> allFields = ReflectUtil.getAllFields(clazz);
        for (Field field : allFields) {
            Sign annotation = field.getAnnotation(Sign.class);
            if (Objects.isNull(annotation)){
                excludeFieldList.add(field.getName());
            }
        }
        String generateClearText = ClearTextUtil.generateClearText(t, excludeFieldList);
        return verify(generateClearText.getBytes(Constants.CHARSET), publicKey, t.getSignature());
    }

    /**
     * 校验数字签名
     *
     * @param data      加密数据
     * @param publicKey 公钥
     * @param sign      数字签名
     * @return 校验成功返回true 失败返回false
     * @throws Exception
     */
    public static boolean verify(byte[] data, String publicKey, String sign) throws Exception {

        // 解密由base64编码的公钥
        byte[] keyBytes = decryptBASE64(publicKey);

        // 构造X509EncodedKeySpec对象
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);

        // KEY_ALGORITHM 指定的加密算法
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");

        // 取公钥匙对象
        PublicKey pubKey = keyFactory.generatePublic(keySpec);

        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initVerify(pubKey);
        signature.update(data);

        // 验证签名是否正常
        byte[] bb = decryptBASE64(sign);
        return signature.verify(bb);
    }

    /**
     * BASE64加密
     *
     * @param key
     * @return
     */
    public static String encryptBASE64(byte[] key) {
        return (new BASE64Encoder()).encodeBuffer(key);
    }

    /**
     * BASE64解密
     *
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decryptBASE64(String key) throws Exception {
        return (new BASE64Decoder()).decodeBuffer(key);
    }
}
