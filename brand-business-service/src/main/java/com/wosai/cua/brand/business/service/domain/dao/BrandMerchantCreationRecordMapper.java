package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantCreationRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandMerchantCreationRecordMapper extends BaseMapper<BrandMerchantCreationRecordDO> {

    /**
     * 批量插入创建记录
     * @param list 集合
     * @return 插入条数
     */
    int batchCreateBrandMerchantCreationRecord(@Param("list") List<BrandMerchantCreationRecordDO> list);

    /**
     * 写入记录
     * @param brandMerchantCreationRecordDO 记录do对象
     * @return 写入条数
     */
    int insertCreationRecord(BrandMerchantCreationRecordDO brandMerchantCreationRecordDO);
}