package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

/**
 * 维金系统接口请求基类模型
 * <AUTHOR>
 */
@Data
public class VfinanceBaseRequest implements TripartiteSystemCallRequest {
    /**
     * 创建合作方调用服务名
     */
    protected static final String CREATE_PARTNER = "create_partner";
    /**
     * 解绑银行卡调用服务名
     */
    protected static final String UN_BIND_BANK_CARD = "unbundling_bank_card";
    /**
     * 创建会员调用服务名
     */
    protected static final String CREATE_MEMBER = "create_member";
    /**
     * 绑定提现银行卡调用服务名
     */
    protected static final String CREATE_BANK_CARD = "create_bank_card";
    /**
     * 绑定提现银行卡推进调用服务名
     */
    protected static final String CREATE_BANK_CARD_ADVANCE = "create_bank_card_advance";
    /**
     * 修改默认提现银行卡调用服务名
     */
    protected static final String CHANGE_DEFAULT_BANK_CARD = "change_default_bank_card";
    /**
     * 删除会员调用服务名
     */
    protected static final String CANCEL_MEMBER = "cancel_member";

    protected static final String REGISTER_BEHAVIOR_RECORD_INFO = "register_behavior_record_info";

    protected static final String QUERY_MEMBER_UID = "query_member_uid";

    protected static final String QUERY_ACCOUNT_INFO = "query_account_info";

    protected static final String QUERY_BANK_CARD = "query_bank_card";

    protected String service;

    protected String version;

    @JSONField(name = "partner_id")
    protected String partnerId;
    @JSONField(name = "_input_charset")
    protected String inputCharset;

    protected String memo;

    public VfinanceBaseRequest() {
        this.inputCharset = "utf-8";
        this.version = "1.0";
    }

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.PAB_FUNCTION;
    }
}
