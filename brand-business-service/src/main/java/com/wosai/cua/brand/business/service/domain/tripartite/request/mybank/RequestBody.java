package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.account.BkcloudfundsAccountOpenRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.merchant.MerchantArrangementInfoQueryRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantAppletPreRegisterRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantArrangementAuditRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantRegisterRequestModel;

import javax.xml.bind.annotation.XmlSeeAlso;

@XmlSeeAlso({
        MerchantRegisterRequestModel.class,
        MerchantAppletPreRegisterRequestModel.class,
        BkcloudfundsAccountOpenRequestModel.class,
        MerchantArrangementAuditRequestModel.class,
        MerchantArrangementInfoQueryRequestModel.class
})
public abstract class RequestBody extends MybankObject {
    private static final long serialVersionUID = 8232658324714372520L;
}