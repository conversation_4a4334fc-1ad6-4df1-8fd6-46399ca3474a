package com.wosai.cua.brand.business.service.enums.third;

import lombok.Getter;

@Getter
public enum MemberGlobalTypeEnum {
    MEMBER_GLOBAL_TYPE_COMPANY("73", -99, "统一社会信用代码"),
    MEMBER_GLOBAL_TYPE_PERSON("1", 1, "身份证"),
    MEMBER_GLOBAL_TYPE_HONGKONG_MARKARO("3", 4, "港澳台居民通行证"),
    MEMBER_GLOBAL_TYPE_TAIWAN("5", 3, "台湾居民来往大陆通行证"),
    MEMBER_GLOBAL_TYPE_PASSPORT("19", 2, "外国护照");

    private final String type;
    private final Integer sqbType;
    private final String description;


    MemberGlobalTypeEnum(String type, Integer sqbType, String description) {
        this.type = type;
        this.sqbType = sqbType;
        this.description = description;
    }

    public static String getTypeBySqbType(Integer sqbType)
    {
        for (MemberGlobalTypeEnum memberGlobalTypeEnum : MemberGlobalTypeEnum.values())
        {
            if (memberGlobalTypeEnum.getSqbType().equals(sqbType))
            {
                return memberGlobalTypeEnum.getType();
            }
        }
        return null;
    }

}
