package com.wosai.cua.brand.business.service.kafka.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class DataCenterMessageBody {
    /**
     * 判断是事件还是用户属性，以便调取不同的api进行上报数据 EVENT
     */
    @JSONField(name = "message_type")
    private String messageType;
    /**
     * 用户uuid
     */
    @JSONField(name = "user_unique_id")
    private String userUniqueId;
    /**
     * 应用id
     */
    @JSONField(name = "app_id")
    private String appId;
    /**
     * 事件名称
     */
    @JSONField(name = "event_name")
    private String eventName;
    /**
     * 业务方自己定义即可，事件参数
     */
    @JSONField(name = "event_params")
    private Object eventParams;
}
