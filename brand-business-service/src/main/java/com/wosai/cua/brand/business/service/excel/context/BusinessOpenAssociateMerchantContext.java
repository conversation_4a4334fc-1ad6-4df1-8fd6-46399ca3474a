package com.wosai.cua.brand.business.service.excel.context;

import com.alibaba.fastjson2.JSON;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.BrandBatchAssociateMerchantsDTO;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import lombok.Getter;

import java.util.Map;

/**
 * 批量关联已有商户
 *
 * <AUTHOR>
 * @date 2024/9/10
 */
@Getter
public class BusinessOpenAssociateMerchantContext extends AbstractImportContext {
    private final BrandBatchAssociateMerchantsDTO brandBatchAssociateMerchantsDTO;
    private BrandModule brandModule;
    private String mainMerchantId;
    private Map<String, String> marketingAssociateCheckResult;

    private BusinessOpenAssociateMerchantContext(BrandTaskLogModule brandTaskLogModule) {
        super(brandTaskLogModule);
        this.brandBatchAssociateMerchantsDTO = JSON.parseObject(JSON.toJSONString(getFormData()), BrandBatchAssociateMerchantsDTO.class);
    }

    public static BusinessOpenAssociateMerchantContext newInstance(BrandTaskLogModule brandTaskLogModule) {
        return new BusinessOpenAssociateMerchantContext(brandTaskLogModule);
    }

    public void bindBrandModule(BrandModule brandModule) {
        this.brandModule = brandModule;
    }
    
    public void bindMainMerchantId(String mainMerchantId) {
        this.mainMerchantId = mainMerchantId;
    }

    public void bindMarketAssociateCheckResult(Map<String, String> marketingAssociateCheckResult) {
        this.marketingAssociateCheckResult = marketingAssociateCheckResult;
    }

    public String getKeeperUserId() {
        if (WosaiStringUtils.isNotEmpty(brandBatchAssociateMerchantsDTO.getCrmUserId())) {
            return brandBatchAssociateMerchantsDTO.getCrmUserId();
        }
        return getOperatorId();
    }
}
