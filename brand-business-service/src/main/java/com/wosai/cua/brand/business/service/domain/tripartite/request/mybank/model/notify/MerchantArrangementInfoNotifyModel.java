package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.ResponseBody;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 授权/解约成功通知接口
 * <ant.mybank.merchantprod.merchant.arrangement.info.notify>
 * */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class MerchantArrangementInfoNotifyModel extends ResponseBody {


    private static final long serialVersionUID = -8850789452532833796L;

    /**
     *合作方机构号
     * */
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;


    /**
     *网商商户号
     * */
    @XmlElement(name = "MerchantId")
    private String merchantId;


    /**
     *当前合约状态
     * */
    @XmlElement(name = "ArrangementStatus")
    private String arrangementStatus;


    /**
     *合约号
     * */
    @XmlElement(name = "ArrangementNo")
    private String arrangementNo;


    /**
     *合约类型
     * */
    @XmlElement(name = "ArrangementType")
    private String arrangementType;


    public String getIsvOrgId() {
        return isvOrgId;
    }

    public void setIsvOrgId(String isvOrgId) {
        this.isvOrgId = isvOrgId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getArrangementStatus() {
        return arrangementStatus;
    }

    public void setArrangementStatus(String arrangementStatus) {
        this.arrangementStatus = arrangementStatus;
    }

    public String getArrangementNo() {
        return arrangementNo;
    }

    public void setArrangementNo(String arrangementNo) {
        this.arrangementNo = arrangementNo;
    }

    public String getArrangementType() {
        return arrangementType;
    }

    public void setArrangementType(String arrangementType) {
        this.arrangementType = arrangementType;
    }
}
