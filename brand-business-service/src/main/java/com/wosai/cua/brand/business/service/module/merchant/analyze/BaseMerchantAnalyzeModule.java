package com.wosai.cua.brand.business.service.module.merchant.analyze;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.annotations.AnalyzeFieldCheck;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import lombok.Data;

@Data
public class BaseMerchantAnalyzeModule {
    /**
     * 商户id
     */
    protected String merchantId;
    /**
     * 收钱吧门店编号
     */
//    @AnalyzeFieldCheck(needRequired = true, message = "未填写收钱吧门店编号")
    protected String storeSn;
    /**
     * 美团门店编号
     */
    protected String meiTuanStoreSn;
    /**
     * 饿了么门店编号
     */
    protected String elmStoreSn;

    /**
     * 抖音门店编号
     */
    protected String dyStoreSn;
    /**
     * 行数
     */
    protected int row;
    /**
     * 商户类型
     *
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum
     */
    protected String type;
    /**
     * 外部商户id
     */
    protected String outMerchantNo;

    /**
     * 资管机构枚举
     */
    protected FundManagementCompanyEnum fundManagementCompanyCode;

    /**
     * 开户行行号
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写开户行行号！",
            needCheckValueValidity = true,
            checkValueValidityMsg = "开户行行号不正确或未找到开户行",
            sheetEnums = {BrandImportSheetEnum.COMPANY},
            bankSearch = "OPENING_NUMBER",
            groups = {FundManagementCompanyEnum.PAB,FundManagementCompanyEnum.CITIC,FundManagementCompanyEnum.FUIOU}
    )
    protected String openingNumber;

    /**
     * 开户行名称
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写开户行名称！",
            needCheckValueValidity = true,
            checkValueValidityMsg = "开户行名称不正确或未找到对应开户行",
            groups = {FundManagementCompanyEnum.PAB,FundManagementCompanyEnum.CITIC,FundManagementCompanyEnum.FUIOU}
    )
    protected String bankName;
    /**
     * 开户行账号
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写银行账号！",
            sheetEnums = {BrandImportSheetEnum.COMPANY,BrandImportSheetEnum.PERSONAL, BrandImportSheetEnum.INDIVIDUAL_BUSINESS},
            groups = {FundManagementCompanyEnum.PAB,FundManagementCompanyEnum.CITIC,FundManagementCompanyEnum.FUIOU}
    )
    protected String bankNumber;

    /**
     * 银行预留手机号
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写银行预留手机号！",
            needFormatCheck = true,
            pattern = "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$",
            formatCheckMsg = "银行预留手机号格式不正确！",
            sheetEnums = {BrandImportSheetEnum.COMPANY,BrandImportSheetEnum.PERSONAL, BrandImportSheetEnum.INDIVIDUAL_BUSINESS},
            groups = {FundManagementCompanyEnum.PAB,FundManagementCompanyEnum.CITIC,FundManagementCompanyEnum.FUIOU}
    )
    protected String cellPhoneNumber;

    /**
     * 品牌商户类型
     */
    @AnalyzeFieldCheck(needRequired = true, message = "未填写商户类型！")
    protected String brandMerchantType;

    protected boolean needRemove;

}
