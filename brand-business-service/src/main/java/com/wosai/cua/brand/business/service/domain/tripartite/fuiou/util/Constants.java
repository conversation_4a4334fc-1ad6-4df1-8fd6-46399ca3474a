/**
 * 创建于:2019年5月8日 上午11:21:04
 * 所属项目:
 * 文件名称:Constants.java
 * 作者:jcy
 * 版权信息:
 */
package com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util;

public class Constants {

    public static final String CHARSET = "GBK";

    //机构私钥
    //public static final String INS_PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJgAzD8fEvBHQTyxUEeK963mjziMWG7nxpi+pDMdtWiakc6xVhhbaipLaHo4wVI92A2wr3ptGQ1/YsASEHm3m2wGOpT2vrb2Ln/S7lz1ShjTKaT8U6rKgCdpQNHUuLhBQlpJer2mcYEzG/nGzcyalOCgXC/6CySiJCWJmPyR45bJAgMBAAECgYBHFfBvAKBBwIEQ2jeaDbKBIFcQcgoVa81jt5xgz178WXUg/awu3emLeBKXPh2i0YtN87hM/+J8fnt3KbuMwMItCsTD72XFXLM4FgzJ4555CUCXBf5/tcKpS2xT8qV8QDr8oLKA18sQxWp8BMPrNp0epmwun/gwgxoyQrJUB5YgZQJBAOiVXHiTnc3KwvIkdOEPmlfePFnkD4zzcv2UwTlHWgCyM/L8SCAFclXmSiJfKSZZS7o0kIeJJ6xe3Mf4/HSlhdMCQQCnTow+TnlEhDTPtWa+TUgzOys83Q/VLikqKmDzkWJ7I12+WX6AbxxEHLD+THn0JGrlvzTEIZyCe0sjQy4LzQNzAkEAr2SjfVJkuGJlrNENSwPHMugmvusbRwH3/38ET7udBdVdE6poga1Z0al+0njMwVypnNwy+eLWhkhrWmpLh3OjfQJAI3BV8JS6xzKh5SVtn/3Kv19XJ0tEIUnn2lCjvLQdAixZnQpj61ydxie1rggRBQ/5vLSlvq3H8zOelNeUF1fT1QJADNo+tkHVXLY9H2kdWFoYTvuLexHAgrsnHxONOlSA5hcVLd1B3p9utOt3QeDf6x2i1lqhTH2w8gzjvsnx13tWqg==";
    //机构公钥
    //public static final String INS_PUBLIC_KEY="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGaInSaIIegcPAQbYF0WnfGIoAxpOkwaU69kdCRo0IPeGFQInMKlmUIiPPiJfH5Zxcjn0eg8rQ6X73Dx/p706LpOQTRDGtZqwjP2tbYX6cE4E1EW1GM9BsFAeX6erz30kufAX8nVMhwOcof5BG34l9beoDCFSB3VipS4qCGITi5wIDAQAB";

    //富友公钥  用于验签
    //public static final String FY_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBv9K+jiuHqXIehX81oyNSD2RfVn+KTPb7NRT5HDPFE35CjZJd7Fu40r0U2Cp7Eyhayv/mRS6ZqvBT/8tQqwpUExTQQBbdZjfk+efb9bF9a+uCnAg0RsuqxeJ2r/rRTsORzVLJy+4GKcv06/p6CcBc5BI1gqSKmyyNBlgfkxLYewIDAQAB";

    public static final String BASE_URL = "https://richOperationFront-test.fuioupay.com";//"http://************:9800";

    // 分账相关
    public static final String OPEN_ALLOCATE_ACCOUNT = BASE_URL + "/openAllocateAccount.fuiou";
    public static final String QUERY_ALLOCATE_ACCOUNT = BASE_URL + "/queryAllocateAccount.fuiou";
    public static final String ACTIVE_ALLOCATE_ACCOUNT = BASE_URL + "/activeAllocateAccount.fuiou";
    public static final String BATCH_TRADE_ALLOCATE = BASE_URL + "/batchTradeAllocate.fuiou";
    public static final String WHOLE_TRADE_ALLOCATE = BASE_URL + "/wholeTradeAllocate.fuiou";
    public static final String QUERY_TRADE_ALLOCATE = BASE_URL + "/queryTradeAllocate.fuiou";
    public static final String OPEN_TAX = BASE_URL + "/openAllocateAccountTax.fuiou";
    public static final String QUERY_ALLOCATE_ACCOUNT_TAX = BASE_URL + "/queryAllocateAccountTax.fuiou";

    public static final String INVALID_ALLOCATE_ACCOUNT = BASE_URL + "/invalidAllocateAccount.fuiou";

    public static final String MCHNT_DRAWOUT = BASE_URL + "/mchntDrawout.fuiou";

    public static final String ACCOUNTIN_DRAWOUT = BASE_URL + "/accountInDrawout.fuiou";

    public static final String QUERY_ACCOUNT_SETTLE = BASE_URL + "/queryAccountInSettle.fuiou";

    public static final String OPEN_MEMBERS_ACCOUNT = BASE_URL + "/openAllocateMember.fuiou";

    /**
     * 4.27入账方/用户解绑卡接口
     */
    public static final String MODIFY_ACCOUNTIN_CARD = BASE_URL + "/modifyAccountInCard.fuiou";

    public static final String QUERY_MCHNT_ACCOUNT_BALANCE = BASE_URL + "/queryMchntAccountBalance.fuiou";

    public static final String QUERY_ACCOUNTIN_BALANCE = BASE_URL + "/queryAccountInBalance.fuiou";

    public static final String ACCOUNTIN_TRANSFERTO_MCHNT = BASE_URL + "/accountInTransferToMchnt.fuiou";

    public static final String CREATE_SUB_ACCOUNT = BASE_URL + "/createSubAccount.fuiou";

    public static final String CANCLE_CONCENTRATE_RELATION_APPLY = BASE_URL + "/cancleConcentrateRelationApply.fuiou";

    public static final String CANCLE_CONCENTRATE_RELATION = BASE_URL + "/cancleConcentrateRelation.fuiou";

    // 加密接口
    public static final String V2 = "/V2";
    public static final String OPEN_ALLOCATE_ACCOUNT_V2_PREFIX = BASE_URL + V2 + "/openAllocateAccount.fuiou";
    public static final String QUERY_ALLOCATE_ACCOUNT_V2_PREFIX = BASE_URL + V2 + "/queryAllocateAccount.fuiou";
    public static final String OPEN_MEMBERS_ACCOUNT_V2_PREFIX = BASE_URL + V2 + "/openAllocateMember.fuiou";
    public static final String OPEN_BATCH_ALLOCATE_MEMBER = BASE_URL + V2 + "/openBatchAllocateMember.fuiou";

    /**
     * 富管家:商户子账户批量转账接口
     */
    public static final String SUB_ACCOUNT_IN_TRANSFER = BASE_URL + "/subAccountInTransfer.fuiou";

    /**
     * 4.36结算人创建接口
     */
    public static final String OPEN_SETTLER_ACCOUNT = BASE_URL + V2 + "/openSettlerAccount.fuiou";

    /**
     * 3.1.归集授权接口
     */
    public static final String ADD_CONCENTRATE_RELATION = BASE_URL + "/addConcentrateRelation.fuiou";

    public static final String BATCH_ORDER_SETTLE = BASE_URL + "/batchOrderSettle.fuiou";


    // 密钥相关
    public static final String MCHNT_CD = "0002900F0370542";
    public static final String MCHNT_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCTK/DZ0Ve73u2ORRAYrpv07FNHyuTb87xabGMTwEIQV2PhDdAYtiRIO/dAtZ45PUN1N+rtiQQxwIgyKJpYIesFpCbBZ+3YIVf3wlkl9VVSfnUSDWcteN9n0WifBqrKbzJ3gaXi4wXveCMJViqTfgDkfgTV/EC/7h5nwj5VUF6LPQIDAQAB";
    public static final String MCHNT_PRIVATE_KEY = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJMr8NnRV7ve7Y5FEBium/TsU0fK5NvzvFpsYxPAQhBXY+EN0Bi2JEg790C1njk9Q3U36u2JBDHAiDIomlgh6wWkJsFn7dghV/fCWSX1VVJ+dRINZy1432fRaJ8GqspvMneBpeLjBe94IwlWKpN+AOR+BNX8QL/uHmfCPlVQXos9AgMBAAECgYAzqbMs434m50UBMmFKKNF6kxNRGnpodBFktLO7FTybu/HF6TFp21a1PMe5IYhfk5AAsBZ6OCUOygWFhhdYZN+5W+dweF3kp1rLE4y5CjwqNlk/g22TAndf9znh/ltHFLvITToqu/eh/34tE1gyNxRbsi1olw/1wv8ZRjM3vtM9QQJBANvNwFq+CJHUyFzkXQB7+ycQFnY8wDq8Uw2Hv9ZMjgIntH7FSlJtdu5mAYPPo6f74slO5tFUMNP7EVppqsjYaNkCQQCraD6iKHo+OIlvvYIKiMXatJGD7N1GNhq5CrhUNPWLHwv/Ih2D3JJdF8IUZOPIJfUxTfM2fZYI+EVdsv6s4RcFAkAGjNYbnighOGcUJZYD6q3sVxVkRqEv3ubWs2HrH/Lna4l8caKqXCq8JfwLkod8/QugFiLYwBqIZqX4vMdjHtfZAkBsAl9dbWZCaPvpxp/4JWGPxDLhz9NLV/KU4bVvkoObq++yUHwKyGYOdVcd5MlIKOsNq5Hzp0Vw14lWVuF2bMxFAkBuNrZksvUULNIaWDKd4rQ6GVzUxXuIZW0ZE6atHYDiXPB4jVAjKRtLxZAV1qH9cr1zNJlcg+RbGYUdF9t4A9n5";
    public static final String FY_PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIaDw5Gb27ezyjrS1yAPtmwv/RZHm9tHSSuCY6SmbfK3WGAGhgAGhSGItNO3pXo3hEmD1graS70neFM0+aIZt9pShtxiz/1EszVMLD3HIkRKhBJFcq8p0gWH+l0Bpl0wGxlf4u2mTkf490YEiP+W/EyEnksQsoIdHpsn+/rZ20CnAgMBAAECgYB1r65ZJJ10+Y3DLVgdquGVgd7RsVEA5jt0H54CHcIwCoz9ZneyagHsNujOGuxiI1RP5VJNKHP/SBsT4VNOqWWaDoVn97thBr+JYCZX0SMdNznSEPMuZu6GKNSeuDl5alKZMB6CumIJmjl6JlDUeUturqbv5XwW1FaiNtrWE7x4sQJBAMEkNvj1hyKtopoLsOca5ydpR/vLOfQgPXGlKHRcOJfj45T3bSYCZ6fHc1t6IpvFUVqYElY1pLA8JYPc9PQaePMCQQCySv+mZfkAz3f2V0U8aw9nfbks4vnUi9XS3e+V593NaJ6IVX3M6sSXAHWJFgezEt7LdB+d54taMpKVeyUDpYZ9AkB/BjBZYDF2LzhHk/TOqbTpCKbdBPWihymh+ns2vAhEbQ6aRHg2jVJa2CQYP6VPSWCN8oHszO75MTWDGejIOjjdAkAKJb6bJ96eLzCyspDcOXOs/jjV1y1E7ZiD4eHK9GFpWXT8aXE5gnsh5QLLhJd3l7Fafwd1o0IJJiu1mkanCHq5AkEAwMfKw/GCAj8i+c7hHTccO0d69fC+fGDakb5zzTpzwNi/6VVIKYG6idOmnHqknGrj9h0UR3sKdE0iXvSBuHHBdg==";
    public static final String FY_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGg8ORm9u3s8o60tcgD7ZsL/0WR5vbR0krgmOkpm3yt1hgBoYABoUhiLTTt6V6N4RJg9YK2ku9J3hTNPmiGbfaUobcYs/9RLM1TCw9xyJESoQSRXKvKdIFh/pdAaZdMBsZX+Ltpk5H+PdGBIj/lvxMhJ5LELKCHR6bJ/v62dtApwIDAQAB";

}
