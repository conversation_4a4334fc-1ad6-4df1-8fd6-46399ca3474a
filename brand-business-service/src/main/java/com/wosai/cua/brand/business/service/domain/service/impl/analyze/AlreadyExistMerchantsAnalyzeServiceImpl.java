package com.wosai.cua.brand.business.service.domain.service.impl.analyze;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantsAnalyzeService;
import com.wosai.cua.brand.business.service.enums.AlreadyExistMerchantExcelFieldEnum;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.helper.AnalyzeHelper;
import com.wosai.cua.brand.business.service.helper.CryptHelper;
import com.wosai.cua.brand.business.service.helper.FileHelper;
import com.wosai.cua.brand.business.service.helper.ThreadLocalHelper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.bank.BizBankAccountModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.AlreadyExistMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.BaseMerchantAnalyzeModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.bank.model.Request;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import com.wosai.upay.bank.model.bizbankaccount.MerchantBizBankAccount;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AlreadyExistMerchantsAnalyzeServiceImpl implements BrandMerchantsAnalyzeService {

    private static final Map<Integer, AlreadyExistMerchantExcelFieldEnum> ALREADY_EXIST_MERCHANT_EXCEL_FIELD_ENUM_MAP = Maps.newConcurrentMap();

    private static final Map<String, AlreadyExistMerchantExcelFieldEnum> FIELD_NAME_EXCEL_FIELD_ENUM_MAP = Maps.newHashMap();

    private static final Map<AlreadyExistMerchantExcelFieldEnum, Field> FIELD_CACHE = Maps.newHashMap();

    private final AnalyzeHelper analyzeHelper;

    private final CryptHelper cryptHelper;

    private final BrandMapper brandMapper;

    private final BrandMerchantMapper brandMerchantMapper;

    @Autowired
    private MerchantBizBankAccountService merchantBizBankAccountService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private StoreService storeService;

    @Autowired
    private BankService bankService;

    @Value("${spring.application.biz}")
    private String biz;

    private final ApolloConfig apolloConfig;

    @Autowired
    public AlreadyExistMerchantsAnalyzeServiceImpl(AnalyzeHelper analyzeHelper, CryptHelper cryptHelper, BrandMapper brandMapper, BrandMerchantMapper brandMerchantMapper, ApolloConfig apolloConfig) {
        this.analyzeHelper = analyzeHelper;
        this.cryptHelper = cryptHelper;
        this.brandMapper = brandMapper;
        this.brandMerchantMapper = brandMerchantMapper;
        this.apolloConfig = apolloConfig;
    }


    @Override
    public BrandImportSheetEnum getSheetEnum() {
        return BrandImportSheetEnum.ALREADY_EXIST_MERCHANT;
    }

    @PostConstruct
    public void init() {
        ALREADY_EXIST_MERCHANT_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(AlreadyExistMerchantExcelFieldEnum.values()).collect(Collectors.toMap(AlreadyExistMerchantExcelFieldEnum::getColumnNo, Function.identity())));
        FIELD_NAME_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(AlreadyExistMerchantExcelFieldEnum.values()).collect(Collectors.toMap(AlreadyExistMerchantExcelFieldEnum::getFieldName, Function.identity())));
        this.cacheFields();
    }

    private void cacheFields() {
        Class<?> clazz = AlreadyExistMerchantAnalyzeModule.class;
        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                String fieldName = field.getName();
                AlreadyExistMerchantExcelFieldEnum alreadyExistMerchantExcelFieldEnum = FIELD_NAME_EXCEL_FIELD_ENUM_MAP.get(fieldName);
                if (Objects.isNull(alreadyExistMerchantExcelFieldEnum)) {
                    continue;
                }
                FIELD_CACHE.put(alreadyExistMerchantExcelFieldEnum, field);
            }
            clazz = clazz.getSuperclass();
        }
    }

    private Field getField(AlreadyExistMerchantExcelFieldEnum fieldEnum) throws NoSuchFieldException {
        if (FIELD_CACHE.containsKey(fieldEnum)) {
            return FIELD_CACHE.get(fieldEnum);
        }
        throw new NoSuchFieldException();
    }


    @Override
    public List<BaseMerchantAnalyzeModule> analyzeData(List<String[]> fields, FundManagementCompanyEnum fundManagementCompanyCode) {
        Set<String> merchantSnSet = Sets.newHashSet();
        List<BaseMerchantAnalyzeModule> alreadyExistMerchantAnalyzeModules = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fields)) {
            return alreadyExistMerchantAnalyzeModules;
        }
        // 处理数据头三行为无效数据从第三行开始
        for (int row = 4; row < fields.size(); row++) {
            AlreadyExistMerchantAnalyzeModule analyzeModule = this.analyze(row, fields.get(row));
            if (Objects.nonNull(analyzeModule) && !merchantSnSet.contains(analyzeModule.getMerchantSn())) {
                analyzeModule.setFundManagementCompanyCode(fundManagementCompanyCode);
                alreadyExistMerchantAnalyzeModules.add(analyzeModule);
                merchantSnSet.add(analyzeModule.getMerchantSn());
            }
        }
        return alreadyExistMerchantAnalyzeModules;
    }

    private AlreadyExistMerchantAnalyzeModule analyze(int row, String[] fields) {
        AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule = new AlreadyExistMerchantAnalyzeModule();
        alreadyExistMerchantAnalyzeModule.setRow(row);
        for (int i = 0; i < fields.length; i++) {
            AlreadyExistMerchantExcelFieldEnum alreadyExistMerchantExcelFieldEnum = ALREADY_EXIST_MERCHANT_EXCEL_FIELD_ENUM_MAP.get(i);
            if (Objects.isNull(alreadyExistMerchantExcelFieldEnum)) {
                continue;
            }
            try {
                Field field = this.getField(alreadyExistMerchantExcelFieldEnum);
                this.analyzeField(alreadyExistMerchantExcelFieldEnum, field, alreadyExistMerchantAnalyzeModule, fields[i]);
            } catch (NoSuchFieldException e) {
                log.warn("解析excel字段错误：{}", alreadyExistMerchantExcelFieldEnum.getFieldName(),e);
                return null;
            } catch (IllegalAccessException e) {
                log.warn("解析excel字段异常", e);
                return null;
            } catch (BrandBusinessException bx) {
                return null;
            }
        }
        return alreadyExistMerchantAnalyzeModule;
    }

    private void analyzeField(
            AlreadyExistMerchantExcelFieldEnum alreadyExistMerchantExcelFieldEnum,
            Field field,
            AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule,
            String fieldValue
    ) throws IllegalAccessException {
        String value = fieldValue;
        if (StringUtils.isBlank(value)) {
            return;
        }
        value = StringUtils.trim(value);
        field.setAccessible(true);

        if (alreadyExistMerchantExcelFieldEnum.equals(AlreadyExistMerchantExcelFieldEnum.BRAND_MERCHANT_TYPE)) {
            value = MerchantTypeEnum.getMerchantTypeByDesc(value);
        }
        field.set(alreadyExistMerchantAnalyzeModule, value);
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantModuleList(String brandId, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules, Long strategyId) {
        List<BrandMerchantModule> brandMerchantModules = Lists.newArrayList();
        BrandDO brandDO = brandMapper.selectBrandByBrandId(brandId);
        if (Objects.isNull(brandDO)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND.getCode(), "未查到品牌信息！");
        }
        merchantAnalyzeModules.forEach(baseMerchantAnalyzeModule -> {
            if (Objects.isNull(baseMerchantAnalyzeModule)) {
                return;
            }
            AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule = (AlreadyExistMerchantAnalyzeModule) baseMerchantAnalyzeModule;
            try {
                // 校验第三方门店编号是否重复
                BrandMerchantsAnalyzeService.checkThirdStoreSn(baseMerchantAnalyzeModule, brandMerchantMapper);
                MerchantInfo merchant = merchantService.getMerchantBySn(alreadyExistMerchantAnalyzeModule.getMerchantSn(), null);
                MerchantInfo adminMerchant = merchantService.getMerchantBySn(brandDO.getMerchantSn(), null);
                if (Objects.isNull(merchant) || Objects.isNull(adminMerchant)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
                }
                StoreInfo storeInfo = null;
                if (StringUtils.isNotBlank(alreadyExistMerchantAnalyzeModule.getStoreSn())){
                    storeInfo = storeService.getStoreBySn(alreadyExistMerchantAnalyzeModule.getStoreSn(), null);
                    if (Objects.isNull(storeInfo) && !apolloConfig.getStoreSnCheckWhiteList().contains(brandId)) {
                        throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_STORE);
                    }
                    if (Objects.nonNull(storeInfo) && !storeInfo.getMerchant_id().equals(adminMerchant.getId())) {
                        throw new BrandBusinessException(BrandBusinessExceptionEnum.STORE_NOT_BELONG_BRAND);
                    }
                }
                UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
                MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
                if (Objects.isNull(merchantBusinessLicense)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_INFO_NOT_INTEGRITY);
                }
                alreadyExistMerchantAnalyzeModule.setMerchantId(merchant.getId());
                alreadyExistMerchantAnalyzeModule.setLegalPersonName(merchantBusinessLicense.getLegal_person_name());
                alreadyExistMerchantAnalyzeModule.setLegalPersonLicenseNumber(merchantBusinessLicense.getLegal_person_id_number());
                if (merchantBusinessLicense.getType() == 2) {
                    alreadyExistMerchantAnalyzeModule.setMerchantType(2);
                }
                if (merchantBusinessLicense.getType() == 1 || merchantBusinessLicense.getType() == 0) {
                    alreadyExistMerchantAnalyzeModule.setMerchantType(1);
                }
                if (StringUtils.isBlank(alreadyExistMerchantAnalyzeModule.getCellPhoneNumber())){
                    alreadyExistMerchantAnalyzeModule.setCellPhoneNumber(merchant.getContact_cellphone());
                }
                BrandMerchantModule brandMerchantModule = this.getBrandMerchantModule(brandId, merchant, merchantBusinessLicense, superAdmin, alreadyExistMerchantAnalyzeModule, storeInfo, strategyId);
                if (Objects.isNull(brandMerchantModule)) {
                    return;
                }
                brandMerchantModules.add(brandMerchantModule);
            } catch (Exception e) {
                log.error("创建品牌商户失败", e);
                Map<Integer, String> errorMapMsg = this.getErrorMapMsg();
                if (MapUtils.isEmpty(errorMapMsg)) {
                    errorMapMsg = Maps.newHashMap();
                }
                analyzeHelper.recordErrorMsg(errorMapMsg, baseMerchantAnalyzeModule, e.getMessage());
                ThreadLocalHelper.set(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT.name(), errorMapMsg);
            }
        });
        return brandMerchantModules;
    }

    private BrandMerchantModule getBrandMerchantModule(String brandId, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, UcMerchantUserInfo superAdmin, AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule, StoreInfo storeInfo, Long strategyId) {
        if (StringUtils.isEmpty(brandId) || Objects.isNull(merchant) || Objects.isNull(merchantBusinessLicense) || Objects.isNull(superAdmin)) {
            log.error("创建品牌商户失败，参数缺失");
            return null;
        }
        BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
        brandMerchantModule.setMerchantId(merchant.getId());
        brandMerchantModule.setMerchantSn(merchant.getSn());
        brandMerchantModule.setMerchantName(merchant.getName());
        brandMerchantModule.setBrandId(brandId);
        brandMerchantModule.setIsAlreadyExistMerchantImport(true);
        brandMerchantModule.setMerchantType(alreadyExistMerchantAnalyzeModule.getBrandMerchantType());
        if (Objects.nonNull(storeInfo)) {
            brandMerchantModule.setAssociatedSqbStoreId(storeInfo.getId());
            brandMerchantModule.setSqbStoreSn(storeInfo.getSn());
        }
        brandMerchantModule.setAssociatedMeituanStoreSn(alreadyExistMerchantAnalyzeModule.getMeiTuanStoreSn());
        brandMerchantModule.setAssociatedElmStoreSn(alreadyExistMerchantAnalyzeModule.getElmStoreSn());
        brandMerchantModule.setDyStoreSn(alreadyExistMerchantAnalyzeModule.getDyStoreSn());
        brandMerchantModule.setOutMerchantNo(alreadyExistMerchantAnalyzeModule.getOutMerchantNo());
        brandMerchantModule.setStrategyId(strategyId);
        switch (merchantBusinessLicense.getType()) {
            case 2:
                brandMerchantModule.setType(BrandMerchantTypeEnum.COMPANY.getType());
                break;
            case 1:
                brandMerchantModule.setType(BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType());
                break;
            case 0:
                brandMerchantModule.setType(BrandMerchantTypeEnum.PERSONAL.getType());
                break;
            default:
                brandMerchantModule.setType("");
        }
        return brandMerchantModule;
    }

    @Override
    public List<BankCardModule> getBankCarModules(String brandId, FundManagementCompanyEnum fundManagementCompanyCode, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules) {
        List<BankCardModule> bankCardModules = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(merchantAnalyzeModules) && Boolean.TRUE.equals(fundManagementCompanyCode.getNeedCreateBankAccount())) {
            merchantAnalyzeModules.forEach(merchantAnalyzeModule -> {
                if (Objects.isNull(merchantAnalyzeModule) || StringUtils.isBlank(merchantAnalyzeModule.getMerchantId())) {
                    return;
                }
                AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule = (AlreadyExistMerchantAnalyzeModule) merchantAnalyzeModule;
                this.addNewCard(brandId, merchantAnalyzeModule, alreadyExistMerchantAnalyzeModule, bankCardModules);
                this.debitCard(brandId, merchantAnalyzeModule, alreadyExistMerchantAnalyzeModule, bankCardModules);
            });
        }
        return bankCardModules;
    }

    private void debitCard(String brandId, BaseMerchantAnalyzeModule merchantAnalyzeModule, AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule, List<BankCardModule> bankCardModules) {
        HashMap<String, Object> params = Maps.newHashMap();
        params.put("merchant_id", merchantAnalyzeModule.getMerchantId());
        params.put("default_status", 1);
        params.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_MSP);
        ListResult listResult = bankService.findMerchantBankAccounts(new PageInfo(1, 10), params);
        log.info("查询商户银行卡信息：{}", JSON.toJSONString(listResult));
        if (listResult.getTotal() >= 1L){
            BizBankAccountModule bizBankAccountModule = JSON.parseObject(JSON.toJSONString(listResult.getRecords().get(0)), BizBankAccountModule.class);
            BizBankAccountAddReq bizBankAccountAddReq = BizBankAccountModule.toReq(bizBankAccountModule, biz, true);
            bizBankAccountAddReq.setMerchant_id(merchantAnalyzeModule.getMerchantId());
            Map<String, String> queryParams = Maps.newHashMap();
            queryParams.put("merchant_id", bizBankAccountAddReq.getMerchant_id());
            queryParams.put("biz",biz);
            ListResult bizBankAccount = merchantBizBankAccountService.findBizBankAccount(queryParams);
            if (bizBankAccount.getTotal() > 0){
                List<MerchantBizBankAccount> merchantBizBankAccounts = JSON.parseArray(JSON.toJSONString(bizBankAccount.getRecords()), MerchantBizBankAccount.class);
                Optional<MerchantBizBankAccount> any = merchantBizBankAccounts.stream().filter(merchantBizBankAccount -> merchantBizBankAccount.getNumber().equals(bizBankAccountAddReq.getNumber())).findAny();
                if (any.isPresent()){
                    MerchantBizBankAccount merchantBizBankAccount = any.get();
                    this.addBankCardModule(brandId, merchantAnalyzeModule, alreadyExistMerchantAnalyzeModule, bankCardModules, bizBankAccountAddReq, merchantBizBankAccount);
                    return;
                }
            }
            try {
                MerchantBizBankAccount merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(bizBankAccountAddReq);
                this.addBankCardModule(brandId, merchantAnalyzeModule, alreadyExistMerchantAnalyzeModule, bankCardModules, bizBankAccountAddReq, merchantBizBankAccount);
            } catch (Exception e) {
                log.error("创建商户银行卡信息失败", e);
            }
        }
    }

    private void addBankCardModule(String brandId, BaseMerchantAnalyzeModule merchantAnalyzeModule, AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule, List<BankCardModule> bankCardModules, BizBankAccountAddReq bizBankAccountAddReq, MerchantBizBankAccount merchantBizBankAccount) {
        BankCardModule bankCardModule = new BankCardModule();
        bankCardModule.setHolder(bizBankAccountAddReq.getHolder());
        bankCardModule.setMerchantId(alreadyExistMerchantAnalyzeModule.getMerchantId());
        bankCardModule.setBrandId(brandId);
        bankCardModule.setAccountType(alreadyExistMerchantAnalyzeModule.getMerchantType());
        bankCardModule.setBankCardId(merchantBizBankAccount.getId());
        bankCardModule.setReservedMobileNumber(cryptHelper.encrypt(alreadyExistMerchantAnalyzeModule.getCellPhoneNumber()));
        bankCardModule.setIsDefault(true);
        bankCardModule.setMobile(alreadyExistMerchantAnalyzeModule.getCellPhoneNumber());
        bankCardModule.setBankCardNo(merchantBizBankAccount.getNumber());
        bankCardModule.setOpeningBankNumber(merchantBizBankAccount.getOpening_number());
        bankCardModules.add(bankCardModule);
    }

    private void addNewCard(String brandId, BaseMerchantAnalyzeModule merchantAnalyzeModule, AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule, List<BankCardModule> bankCardModules) {
        try {
            BizBankAccountAddReq req = BrandMerchantsAnalyzeService.getBizBankAccountAddReq(alreadyExistMerchantAnalyzeModule, biz, alreadyExistMerchantAnalyzeModule.getMerchantType(), 1);
            MerchantBizBankAccount merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(req);
            this.addBankCardModule(brandId, merchantAnalyzeModule, alreadyExistMerchantAnalyzeModule, bankCardModules, req, merchantBizBankAccount);
        } catch (Exception e) {
            log.error("数据不全无法添加银行卡", e);
        }
    }

    @Override
    public void createErrorMsgIntoExcel(String filePath) {
        Map<Integer, String> errorMapMsg = this.getErrorMapMsg();
        if (errorMapMsg == null) return;
        FileHelper.createExcelErrorMsg(filePath, BrandImportSheetEnum.ALREADY_EXIST_MERCHANT.getSheet(), BrandImportSheetEnum.ALREADY_EXIST_MERCHANT.getErrorMsgColNum(), errorMapMsg);
    }

    @Override
    public void checkParams(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules) {
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(createMerchantAnalyzeModules)) {
            Iterator<BaseMerchantAnalyzeModule> iterator = createMerchantAnalyzeModules.iterator();
            while (iterator.hasNext()) {
                BaseMerchantAnalyzeModule analyzeModule = iterator.next();
                Class<? extends BaseMerchantAnalyzeModule> analyzeModuleClass = analyzeModule.getClass();
                boolean needRemove = analyzeHelper.checkFields(analyzeModuleClass, analyzeModule, errorMap, BrandImportSheetEnum.ALREADY_EXIST_MERCHANT);
                if (needRemove) {
                    iterator.remove();
                }
            }
            ThreadLocalHelper.set(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT.name(), errorMap);
        }
    }

    @Override
    public void checkIsValidChineseID(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules) {

    }

    private Map<Integer, String> getErrorMapMsg() {
        Object o = ThreadLocalHelper.get(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT.name());
        if (Objects.isNull(o)) {
            return Collections.emptyMap();
        }
        Map errorMap = JSON.parseObject(JSON.toJSONString(o), Map.class);
        HashMap<Integer, String> errorMapMsg = Maps.newHashMap();
        errorMap.forEach((key, value) -> {
            boolean flag = Objects.nonNull(key) && NumberUtils.isCreatable(key.toString()) && Objects.nonNull(value);
            if (flag) {
                errorMapMsg.put(Integer.valueOf(key.toString()), value.toString());
            }
        });
        return errorMapMsg;
    }

}
