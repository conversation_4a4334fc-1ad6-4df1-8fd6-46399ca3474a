package com.wosai.cua.brand.business.service.facade.impl;

import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardAdvanceDTO;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.BaseBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.BatchActivateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.DeleteBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBankCardDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantBankCardsDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.BrandMerchantBankAccountFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.helper.ThreadPoolHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class BrandMerchantBankAccountFacadeImpl implements BrandMerchantBankAccountFacade {

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    private final BrandBusiness brandBusiness;

    @Autowired
    public BrandMerchantBankAccountFacadeImpl(BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness, BrandBusiness brandBusiness) {
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
        this.brandBusiness = brandBusiness;
    }

    @Override
    public List<BankCardResponseDTO> batchCreateBankCard(List<CreateBankCardDTO> createBankCardList) {
        if (CollectionUtils.isEmpty(createBankCardList)) {
            return Lists.newArrayList();
        }
        List<BankCardResponseDTO> list = Lists.newArrayList();
        createBankCardList.forEach(createBankCard -> {
            BankCardResponseDTO bankCard = brandMerchantBankAccountBusiness.createBankCard(createBankCard);
            list.add(bankCard);
        });
        return list;
    }

    @Override
    public BankCardResponseDTO createBankCard(CreateBankCardDTO createBankCard) {
        return brandMerchantBankAccountBusiness.createBankCard(createBankCard);
    }

    @Override
    public Boolean activateBankCard(ActivateBankCardDTO activateBankCard) {
        return brandMerchantBankAccountBusiness.activateBankCard(activateBankCard);
    }

    @Override
    public Boolean batchActivateBankCard(BatchActivateBankCardDTO batchActivateBankCard) {
        BrandDetailInfoDTO brandDetailInfo = brandBusiness.getBrandDetailInfoByBrandId(batchActivateBankCard.getBrandId(), false);
        if (!brandDetailInfo.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.CITIC)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_NOT_SUPPORT_OPERATION);
        }
        if (!CollectionUtils.isEmpty(batchActivateBankCard.getBatchActivateBankCardList())){
            batchActivateBankCard.getBatchActivateBankCardList().forEach(activateBankCard -> {
                activateBankCard.setBrandId(batchActivateBankCard.getBrandId());
                brandMerchantBankAccountBusiness.activateBankCard(activateBankCard);
            });
        }else {
            ThreadPoolHelper.execute(() -> brandMerchantBankAccountBusiness.activateBankCardByBrandId(batchActivateBankCard.getBrandId()));
        }
        return true;
    }

    @Override
    public Boolean activateBankCardAdvance(ActivateBankCardAdvanceDTO activateBankCardAdvanceDto) {
        return brandMerchantBankAccountBusiness.activateBankCardAdvance(activateBankCardAdvanceDto);
    }

    @Override
    public Boolean changeDefaultBankCard(BaseBankCardDTO bankCard) {
        brandMerchantBankAccountBusiness.changeDefaultBankCard(bankCard.getBankCardId());
        return true;
    }

    @Override
    public Boolean deleteBankCard(DeleteBankCardDTO bankCard) {
        return brandMerchantBankAccountBusiness.deleteBankCard(bankCard.getBankCardId(),false, Objects.nonNull(bankCard.getForcedDeletion()) && bankCard.getForcedDeletion());
    }

    @Override
    public PageBrandMerchantBankCardsDTO pageFindBankCardList(PageQueryBankCardDTO pageQueryBankCardDto) {
        return brandMerchantBankAccountBusiness.pageFindBankCardList(pageQueryBankCardDto);
    }

    @Override
    public BankCardResponseDTO modifyBankCard(ModifyBankCardDTO modifyBankCard) {
        return brandMerchantBankAccountBusiness.modifyBankCard(modifyBankCard);
    }

    @Override
    public BankCardDetailResponseDTO getBankCardDetail(BaseBankCardDTO bankCard) {
        return brandMerchantBankAccountBusiness.getBankCardDetail(bankCard.getBankCardId());
    }
}
