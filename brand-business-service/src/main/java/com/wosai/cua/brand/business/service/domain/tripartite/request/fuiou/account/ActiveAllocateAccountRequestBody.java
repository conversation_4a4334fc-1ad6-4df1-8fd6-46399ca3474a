package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用户激活、短信重发接口（CJ003）
 * 接口名称：/activeAllocateAccount.fuiou
 * 接口说明：创建用户时，无需单独调用本接口。此接口用于签约01失效状态后重新激活发起签约或在00状态下调用重新进行短信重发。
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
@AllArgsConstructor
@NoArgsConstructor
public class ActiveAllocateAccountRequestBody extends BaseRequestBody {
    /**
     * 用户编号
     */
    @Sign
    private String accountIn;
    /**
     * 验证类型
     * 枚举值：1 短信模式 2 返回url 不传默认1
     */
    private String checkType;

    public ActiveAllocateAccountRequestBody(String merchantNo) {
        super(merchantNo);
    }
}
