package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

@Getter
public enum RedisKeyEnum {
    FUIOU_OPEN_ACCOUNT_RECORDS("fuiou:openAccount:records", "富友开通会员成功等待激活队列"),
    IMPORT_MERCHANT_TASK_TRANSACTION("importMerchantTask:transaction:%s:%s", "导入商户任务事务"),
    ;
    private final String key;
    private final String desc;

    RedisKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
