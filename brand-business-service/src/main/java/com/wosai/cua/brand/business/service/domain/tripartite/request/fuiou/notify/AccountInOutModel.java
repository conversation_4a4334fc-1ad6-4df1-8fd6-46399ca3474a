package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.notify;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.ResponseBodySignature;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "")
public class AccountInOutModel extends ResponseBodySignature {
    /**
     * 商户号
     */
    @Sign
    private String mchntCd;
    /**
     * 业务类型：
     * 枚举值：
     * 1创建
     * 2协议修改
     * 3绑卡
     */
    @Sign
    private String type;
    /**
     * 用户编号
     */
    @Sign
    private String accountIn;
    /**
     * 户名
     */
    @Sign
    private String outAcntNm;
    /**
     * 银行卡户名
     */
    private String outAcntCardNm;
    /**
     * 手机号
     */
    @Sign
    private String mobile;
    /**
     * 银行账号
     */
    @Sign
    private String outAcntNo;
    /**
     * 开户行行号
     */
    @Sign
    private String interBankNo;
    /**
     * 状态：
     * 枚举值：
     * 01失效
     * 02生效
     */
    @Sign
    private String status;
    /**
     * 商户流水号
     */
    private String mchntTraceNo;
    /**
     * 证件号码
     */
    private String licNo;
    /**
     * 分配比例
     */
    private Integer allocateScale;
    /**
     * 银行账户编号
     */
    private String bankAcctNo;
    /**
     * 银行联行号
     */
    private String bankInterNo;
    /**
     * 银行名称
     */
    private String bankNm;
    /**
     * 错误信息
     */
    private String msg;
}
