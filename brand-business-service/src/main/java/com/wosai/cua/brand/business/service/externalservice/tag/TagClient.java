package com.wosai.cua.brand.business.service.externalservice.tag;

import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.data.crow.api.model.query.SingleTagEntityRecord;
import com.wosai.data.crow.api.service.OnlineQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/29
 */
@Slf4j
@Component
public class TagClient {

    @Autowired
    private ApolloConfig apolloConfig;
    @Autowired
    private OnlineQueryService onlineQueryService;

    /**
     * 商户改商户是否有收付通品牌标签
     *
     * @param merchantId 商户 ID
     * @return true/false 是否有收付通标签
     */
    public boolean hasPaymentHubTag(String merchantId) {
        try {
            SingleTagEntityRecord singleTag = onlineQueryService.getSingleTagEntityRecordById(apolloConfig.getBrandTagEntityId(), apolloConfig.getBrandTagId(), Arrays.asList(merchantId), true);
            Object tagValue = singleTag.getTag().getValue();
            return Objects.equals(true, tagValue);
        } catch (Exception e) {
            log.error("查询收付通标签异常 merchantId:{}", merchantId, e);
            return false;
        }
    }
}
