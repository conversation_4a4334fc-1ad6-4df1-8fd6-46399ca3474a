package com.wosai.cua.brand.business.service.domain.service.impl.fuiou;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.EnrollChannelEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantCreationRecordMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantLicensingAgreementMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantCreationRecordDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantLicensingAgreementDO;
import com.wosai.cua.brand.business.service.domain.https.enums.FuiouAddConcentrateRelationStatusEnum;
import com.wosai.cua.brand.business.service.domain.service.FuiouNotifyService;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util.SignUtils;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util.XmlConvertUtil;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.notify.AddConcentrateRelationResult;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantEnrollEvent;
import com.wosai.cua.brand.business.service.event.publisher.DefaultEventPublisher;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import com.wosai.cua.brand.business.service.module.config.convert.BrandConfigConverter;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AddConcentrateRelationResultServiceImpl implements FuiouNotifyService {

    private final BrandMerchantCreationRecordMapper brandMerchantCreationRecordMapper;

    private final BrandMerchantMapper brandMerchantMapper;

    private final BrandMerchantLicensingAgreementMapper brandMerchantLicensingAgreementMapper;

    private final BrandConfigDOMapper brandConfigDOMapper;

    private final ApolloConfig apolloConfig;

    private final DefaultEventPublisher defaultEventPublisher;

    public AddConcentrateRelationResultServiceImpl(BrandMerchantCreationRecordMapper brandMerchantCreationRecordMapper, BrandMerchantMapper brandMerchantMapper, BrandMerchantLicensingAgreementMapper brandMerchantLicensingAgreementMapper, BrandConfigDOMapper brandConfigDOMapper, ApolloConfig apolloConfig, DefaultEventPublisher defaultEventPublisher) {
        this.brandMerchantCreationRecordMapper = brandMerchantCreationRecordMapper;
        this.brandMerchantMapper = brandMerchantMapper;
        this.brandMerchantLicensingAgreementMapper = brandMerchantLicensingAgreementMapper;
        this.brandConfigDOMapper = brandConfigDOMapper;
        this.apolloConfig = apolloConfig;
        this.defaultEventPublisher = defaultEventPublisher;
    }

    @Override
    public String getNotifyType() {
        return ADD_CONCENTRATE_RELATION_RESULT;
    }

    @Override
    public String notifyHandle(String xmlContext) throws Exception {
        AddConcentrateRelationResult result = XmlConvertUtil.notifyXml2Bean(xmlContext, AddConcentrateRelationResult.class);
        log.info("返回报文：{}", xmlContext);
        if (Objects.isNull(result)){
            return "xmlContext is null";
        }
        AddConcentrateRelationResult.Result data = result.getData();
        FuiouConfigModule fuiouConfigModule = this.getConfigModuleByChannelId(data.getMchntCd());
        if (Objects.isNull(fuiouConfigModule)){
            log.info("AddConcentrateRelationResultServiceImpl not find config");
            return "not find config";
        }
        boolean verifySign = SignUtils.verify(result, AddConcentrateRelationResult.class, fuiouConfigModule.getFyPublicKey());
        if (!verifySign) {
            log.info("返回报文验签失败");
            return "xmlContext sign fail";
        }
        LambdaQueryWrapper<BrandMerchantCreationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandMerchantCreationRecordDO::getRecordId, data.getBatchNo());
        queryWrapper.orderByDesc(BrandMerchantCreationRecordDO::getCreatedTime);
        List<BrandMerchantCreationRecordDO> creationRecordList = brandMerchantCreationRecordMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(creationRecordList)) {
            BrandMerchantCreationRecordDO recordDO = creationRecordList.get(0);
            String brandId = recordDO.getBrandId();
            String merchantId = recordDO.getMerchantId();
            LambdaQueryWrapper<BrandMerchantDO> brandMerchantLambdaQueryWrapper = new LambdaQueryWrapper<>();
            brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getBrandId, brandId);
            brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getMerchantId, merchantId);
            brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getDeleted, 0);
            BrandMerchantDO brandMerchantDO = brandMerchantMapper.selectOne(brandMerchantLambdaQueryWrapper);
            if (brandMerchantDO != null) {
                FuiouAddConcentrateRelationStatusEnum fuiouAddConcentrateRelationStatusEnum = FuiouAddConcentrateRelationStatusEnum.getByCode(data.getStatus());
                brandMerchantDO.setAccountOpenStatus(Objects.isNull(fuiouAddConcentrateRelationStatusEnum) ? BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus() : fuiouAddConcentrateRelationStatusEnum.getBrandMerchantAccountOpenStatusEnum().getStatus());
                brandMerchantDO.setUpdatedTime(new Date());
                brandMerchantDO.setVersion(brandMerchantDO.getVersion() + 1);
                brandMerchantMapper.updateById(brandMerchantDO);
                if (StringUtils.isNotBlank(data.getUrl())) {
                    BrandMerchantLicensingAgreementDO brandMerchantLicensingAgreementDO = new BrandMerchantLicensingAgreementDO();
                    brandMerchantLicensingAgreementDO.setBrandId(brandId);
                    brandMerchantLicensingAgreementDO.setMerchantId(merchantId);
                    brandMerchantLicensingAgreementDO.setType("COLLECTION_AUTHORIZATION");
                    brandMerchantLicensingAgreementDO.setUrl(data.getUrl());
                    brandMerchantLicensingAgreementMapper.insert(brandMerchantLicensingAgreementDO);
                }
                if (Objects.equals(fuiouAddConcentrateRelationStatusEnum, FuiouAddConcentrateRelationStatusEnum.SUCCESS)) {
                    // 事务提交后执行的业务代码
                    defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandMerchantDO.getBrandId()).merchantSn(brandMerchantDO.getMerchantSn()).enrollChannelEnum(EnrollChannelEnum.FUIOU).build());
                }
            }
        }
        return "";
    }

    private FuiouConfigModule getConfigModuleByChannelId(String channelId) {
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandConfigDO::getChannelId, channelId);
        queryWrapper.eq(BrandConfigDO::getChannelType, FundManagementCompanyEnum.FUIOU.getFundManagementCompanyCode());
        List<BrandConfigDO> brandConfigs = brandConfigDOMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(brandConfigs)) {
            return null;
        }
        brandConfigs = brandConfigs.stream().filter(brandConfig -> StringUtils.isNotBlank(brandConfig.getConfig()) && StringUtils.isNotBlank(brandConfig.getChannelId()) && StringUtils.isNotBlank(brandConfig.getChannelType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(brandConfigs)) {
            return null;
        }
        BrandConfigModule brandConfigModule = BrandConfigConverter.convertModule(brandConfigs.get(0));
        if (StringUtils.isNotBlank(brandConfigModule.getConfig())){
            return null;
        }
        return JSON.parseObject(brandConfigModule.getConfig(), FuiouConfigModule.class);
    }
}
