package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "brand_merchant_bank_card")
public class BrandMerchantBankCardDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id
     */
    @TableField(value = "brand_id")
    private String brandId;

    /**
     * 商户id
     */
    @TableField(value = "merchant_id")
    private String merchantId;

    @TableField(value = "bank_card_id")
    private String bankCardId;

    /**
     * 开户银行：PAB-平安银行，CMBC-中国民生银行，CMB-招商银行，SPDB-浦发银行，ECITIC-中信银行，CIB-兴业银行，CGB-广发银行，HXB-华夏银行，CEB-中国光大银行，ICBC-中国工商银行，CCB-中国建设银行，ABC-中国农业银行，BOC-中国银行，BOCOM-交通银行
     */
    @TableField(value = "bank_of_deposit")
    private String bankOfDeposit;

    /**
     * 账户类型：1-个人，2-企业
     */
    @TableField(value = "account_type")
    private Integer accountType;

    /**
     * 预留手机号
     */
    @TableField(value = "reserved_mobile_number")
    private String reservedMobileNumber;

    @TableField(value = "third_bank_card_id")
    private String thirdBankCardId;

    /**
     * 三方系统会员id
     */
    @TableField(value = "member_id")
    private String memberId;

    /**
     * 是否是默认卡：0-否，1-是
     */
    @TableField(value = "is_default")
    private Boolean isDefault;

    /**
     * 是否删除，0-否、1-是
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private Date updatedTime;
    /**
     * 激活失败原因
     */
    @TableField(value = "activate_fail_reason")
    private String activateFailReason;

    /**
     * 银行卡状态：0-未激活，1-已激活
     */
    private Integer status;

    @TableField(value = "activation_time")
    private Date activationTime;
}