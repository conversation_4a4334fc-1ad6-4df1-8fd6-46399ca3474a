package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

@Getter
public enum AlreadyExistMerchantExcelFieldEnum {
    BRAND_MERCHANT_TYPE(0,"brandMerchantType","未填写或填写错误品牌商户类型！",true, false, "", ""),
    MERCHANT_SN(1,"merchantSn","未填写商户编号！",true, false, "", ""),
    STORE_NAME(2,"storeSn","收钱吧门店编号未填写",true, false, "", ""),
    MEI_TUAN_STORE_SN(3,"meiTuanStoreSn","",false, false, "", ""),
    ELM_STORE_SN(4,"elmStoreSn","",false, false, "", ""),
    DY_STORE_SN(5,"dyStoreSn","",false, false, "", ""),
    OUT_MERCHANT_ID(6,"outMerchantNo","",false, false, "", ""),
    BANK_NAME(7,"bankName","银行卡开户行未填写",true, false, "", ""),
    CORRESPONDENT_NUMBER(8,"openingNumber","银行卡开户行联行号未填写",false, false, "", ""),
    BANK_ACCOUNT_NAME(9,"bankAccountName","银行卡的对公账户名称未填写",true, false, "", ""),
    BANK_NUMBER(10,"bankNumber","银行卡卡号未填写",true, false, "", ""),
    CELL_PHONE_NUMBER(11,"cellPhoneNumber","预留手机号未填写",true, true, "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$", "预留手机号码格式不正确"),
    ;
    private final int columnNo;

    private final String fieldName;

    private final String checkMessage;

    private final boolean needRequired;

    private final boolean needFormatCheck;

    private final String regex;

    private final String formatCheckMsg;

    AlreadyExistMerchantExcelFieldEnum(int columnNo, String fieldName, String checkMessage, boolean needRequired, boolean needFormatCheck, String regex, String formatCheckMsg) {
        this.columnNo = columnNo;
        this.fieldName = fieldName;
        this.checkMessage = checkMessage;
        this.needRequired = needRequired;
        this.needFormatCheck = needFormatCheck;
        this.regex = regex;
        this.formatCheckMsg = formatCheckMsg;
    }
}
