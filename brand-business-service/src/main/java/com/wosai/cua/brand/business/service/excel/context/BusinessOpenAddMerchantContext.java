package com.wosai.cua.brand.business.service.excel.context;

import com.alibaba.fastjson2.JSON;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.BrandBatchAddMerchantsDTO;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.StoreExtAndPicturesQueryResult;
import com.wosai.cua.brand.business.service.externalservice.salessystem.model.SalesUserModel;
import com.wosai.cua.brand.business.service.helper.ZipHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import lombok.Getter;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
public class BusinessOpenAddMerchantContext extends AbstractImportContext {
    @Getter
    private final BrandBatchAddMerchantsDTO brandBatchAddMerchantsDTO;
    @Getter
    private StoreExtAndPicturesQueryResult storeExtAndPicturesQueryResult;
    @Getter
    private BrandModule brandModule;
    @Getter
    private SalesUserModel salesUser;
    private File photoFile;

    private BusinessOpenAddMerchantContext(BrandTaskLogModule brandTaskLogModule) {
        super(brandTaskLogModule);
        this.brandBatchAddMerchantsDTO = JSON.parseObject(JSON.toJSONString(getFormData()), BrandBatchAddMerchantsDTO.class);
    }

    public static BusinessOpenAddMerchantContext newInstance(BrandTaskLogModule brandTaskLogModule) {
        return new BusinessOpenAddMerchantContext(brandTaskLogModule);
    }

    public void bindBrandModule(BrandModule brandModule) {
        this.brandModule = brandModule;
    }

    @Override
    @Nullable
    public File getPhotoFile() {
        return photoFile;
    }

    public File initPhotoFile() {
        if (Objects.isNull(brandBatchAddMerchantsDTO)) {
            return null;
        }
        if (Objects.isNull(photoFile)) {
            photoFile = ZipHelper.unzip(brandBatchAddMerchantsDTO.getPhotoZip());
        }
        return photoFile;
    }

    public void bindStoreExtPicturesQueryResult(StoreExtAndPicturesQueryResult latestStoreExtAndPicturesByMerchantId) {
        this.storeExtAndPicturesQueryResult = latestStoreExtAndPicturesByMerchantId;
    }

    public String getKeeperUserId() {
        if (WosaiStringUtils.isNotEmpty(brandBatchAddMerchantsDTO.getCrmUserId())) {
            return brandBatchAddMerchantsDTO.getCrmUserId();
        }
        return getOperatorId();
    }

    public void bindOperator(SalesUserModel salesUser) {
        this.salesUser = salesUser;
    }
}
