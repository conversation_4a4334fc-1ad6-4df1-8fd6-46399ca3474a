package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class PageQueryBankCardRequest extends OpenApiBaseRequest{

    private Integer page;

    @JsonProperty("page_size")
    private Integer pageSize;

    @NotBlank(message = "商户编号不能为空")
    @JsonProperty("merchant_sn")
    private String merchantSn;
}
