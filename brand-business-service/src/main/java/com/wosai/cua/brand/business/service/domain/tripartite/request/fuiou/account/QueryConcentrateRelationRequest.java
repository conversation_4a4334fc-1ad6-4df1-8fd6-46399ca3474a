package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

@Data
public class QueryConcentrateRelationRequest implements TripartiteSystemCallRequest {

    public static final String REQUEST_URI = "/queryConcentrateRelation.fuiou";

    private QueryConcentrateRelationRequestBody body;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_QUERY_CONCENTRATE_RELATION;
    }
}
