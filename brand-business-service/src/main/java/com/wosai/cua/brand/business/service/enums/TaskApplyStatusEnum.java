package com.wosai.cua.brand.business.service.enums;


import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum TaskApplyStatusEnum {
    NEW_APPLY(0,"新申请"),
    IN_EXECUTION(1,"执行中"),
    SUCCESS(2,"执行成功！"),
    FAIL(3,"执行失败")
    ;

    private final int status;

    private final String desc;

    TaskApplyStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static String getDescByStatus(Integer status){
        if (Objects.isNull(status)){
            return null;
        }
        for (TaskApplyStatusEnum taskApplyStatusEnum : TaskApplyStatusEnum.values()){
            if (taskApplyStatusEnum.getStatus() == status){
                return taskApplyStatusEnum.getDesc();
            }
        }
        return null;
    }
}
