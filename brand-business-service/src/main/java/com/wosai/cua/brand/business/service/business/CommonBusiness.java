package com.wosai.cua.brand.business.service.business;

import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.ArrangementStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.OperationEnum;
import com.wosai.cua.brand.business.api.enums.OperationSceneEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.DictionaryDomainService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantArrangementAuditRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantArrangementAuditRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantprodMerchantArrangeMentAuditResponse;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.common.dictionary.DictionaryModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CommonBusiness {

    private final List<DictionaryDomainService> dictionaryDomainServices;

    private final BrandDomainService brandDomainService;

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private final BrandConfigDomainService brandConfigDomainService;

    private static final Map<String, DictionaryDomainService> DICTIONARY_SERVICE_MAP = Maps.newConcurrentMap();

    private static final Map<OperationSceneEnum, Function<BrandMerchantModule, Consumer<OperationEnum>>> OPERATION_MAP = Maps.newConcurrentMap();

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = Maps.newConcurrentMap();

    @Autowired
    public CommonBusiness(List<DictionaryDomainService> dictionaryDomainServices, BrandDomainService brandDomainService, List<TripartiteSystemCallService> tripartiteSystemCallServices, BrandConfigDomainService brandConfigDomainService) {
        this.dictionaryDomainServices = dictionaryDomainServices;
        this.brandDomainService = brandDomainService;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.brandConfigDomainService = brandConfigDomainService;
    }

    @PostConstruct
    public void initDictionaryServiceMap() {
        DICTIONARY_SERVICE_MAP.putAll(dictionaryDomainServices.stream().collect(Collectors.toMap(DictionaryDomainService::dictionary, Function.identity())));
        OPERATION_MAP.put(OperationSceneEnum.MY_BANK_RESCIND_AN_AGREEMENT, brandMerchantModule -> operation -> this.myBankRescindAnAgreement(brandMerchantModule, operation));
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }

    public List<DictionaryModule> getDictionaryModules(String dictionary) {
        DictionaryDomainService dictionaryDomainService = DICTIONARY_SERVICE_MAP.get(dictionary);
        if (Objects.isNull(dictionaryDomainService)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_DICTIONARY);
        }
        return dictionaryDomainService.getDictionaryModules();
    }

    public Boolean commonOperation(String brandId, String operationMerchantId, OperationEnum operation, OperationSceneEnum scene) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, operationMerchantId);
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        Function<BrandMerchantModule, Consumer<OperationEnum>> operationEnumConsumer = OPERATION_MAP.get(scene);
        if (Objects.isNull(operationEnumConsumer)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.UN_SUPPORT_SCENE);
        }
        operationEnumConsumer.apply(brandMerchantModule).accept(operation);
        return true;
    }

    /**
     * 是否撤销网商代扣协议
     *
     * @param operation 操作类型
     */
    public void myBankRescindAnAgreement(BrandMerchantModule brandMerchantModule, OperationEnum operation) {
        MyBankConfigModule myBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandMerchantModule.getBrandId(), MyBankConfigModule.class);
        if (Objects.isNull(myBankConfigModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_CONFIG);
        }
        if (Objects.isNull(brandMerchantModule.getExtra()) || Objects.isNull(brandMerchantModule.getExtra().getMyBankMerchantExtraModule())){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MY_BANK_ARRANGEMENT);
        }
        MerchantArrangementAuditRequest merchantArrangementAuditRequest = this.getMerchantArrangementAuditRequest(brandMerchantModule, operation, myBankConfigModule);
        MerchantprodMerchantArrangeMentAuditResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.MY_BANK).call(merchantArrangementAuditRequest, MerchantprodMerchantArrangeMentAuditResponse.class, myBankConfigModule);
        if (Objects.isNull(response)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_CONFIG);
        }
        MerchantprodMerchantArrangeMentAuditResponse.MerchantprodMerchantArrangeMentAudit merchantprodMerchantArrangeMentAudit = response.getMerchantprodMerchantArrangeMentAudit();
        if (Objects.isNull(merchantprodMerchantArrangeMentAudit)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MY_BANK_ARRANGEMENT);
        }
        String resultStatus = merchantprodMerchantArrangeMentAudit.getMerchantprodMerchantArrangeMentAuditResponseModel().getRespInfo().getResultStatus();
        if ("S".equals(resultStatus)){
            if (operation.equals(OperationEnum.MY_BANK_AGREE_RESCIND_AN_AGREEMENT)){
                brandMerchantModule.getExtra().getMyBankMerchantExtraModule().setArrangementStatus(ArrangementStatusEnum.CANCELLED.getCode());
            }
            if (operation.equals(OperationEnum.MY_BANK_REFUSE_RESCIND_AN_AGREEMENT)){
                brandMerchantModule.getExtra().getMyBankMerchantExtraModule().setArrangementStatus(ArrangementStatusEnum.CANCELING.getCode());
            }
            brandDomainService.updateBrandMerchant(brandMerchantModule);
        }else {
            throw new BrandBusinessException(merchantprodMerchantArrangeMentAudit.getMerchantprodMerchantArrangeMentAuditResponseModel().getRespInfo().getResultMsg());
        }
    }

    private MerchantArrangementAuditRequest getMerchantArrangementAuditRequest(BrandMerchantModule brandMerchantModule, OperationEnum operation, MyBankConfigModule myBankConfigModule) {
        MerchantArrangementAuditRequest merchantArrangementAuditRequest = new MerchantArrangementAuditRequest(myBankConfigModule.getAppId());
        MerchantArrangementAuditRequestModel merchantArrangementAuditRequestModel = new MerchantArrangementAuditRequestModel();
        merchantArrangementAuditRequestModel.setArrangementNo(brandMerchantModule.getExtra().getMyBankMerchantExtraModule().getArrangementNo());
        merchantArrangementAuditRequestModel.setAuditResult(operation.getOperation());
        merchantArrangementAuditRequestModel.setIsvOrgId(myBankConfigModule.getIsvOrgId());
        merchantArrangementAuditRequestModel.setMerchantId(brandMerchantModule.getSubAccountNo());
        merchantArrangementAuditRequest.setRequestBody(merchantArrangementAuditRequestModel);
        return merchantArrangementAuditRequest;
    }
}
