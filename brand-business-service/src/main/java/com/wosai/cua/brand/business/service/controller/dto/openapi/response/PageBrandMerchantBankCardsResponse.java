package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageBrandMerchantBankCardsResponse extends BaseOpenApiResponse{
    /**
     * 总数
     */
    private Integer total;
    /**
     * 银行卡列表
     */
    private List<OpenApiBankCardDetailDTO> records;

}
