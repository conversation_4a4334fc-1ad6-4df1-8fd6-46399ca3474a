package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

@Data
public class BaseRequest<T extends BaseRequestBody> implements TripartiteSystemCallRequest {

    private T body;

    @Override
    public FunctionEnum getFunctionEnum() {
        return null;
    }
}
