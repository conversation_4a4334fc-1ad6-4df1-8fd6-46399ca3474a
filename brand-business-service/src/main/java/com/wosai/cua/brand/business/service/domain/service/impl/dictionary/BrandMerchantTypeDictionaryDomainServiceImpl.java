package com.wosai.cua.brand.business.service.domain.service.impl.dictionary;

import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.DictionaryDomainService;
import com.wosai.cua.brand.business.service.module.common.dictionary.DictionaryModule;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class BrandMerchantTypeDictionaryDomainServiceImpl implements DictionaryDomainService {

    /**
     * 字典名称
     */
    private static final String DICTIONARY = "BRAND_MERCHANT_TYPE";

    @Override
    public String dictionary() {
        return DICTIONARY;
    }

    @Override
    public List<DictionaryModule> getDictionaryModules() {
        List<DictionaryModule> list = Lists.newArrayList();
        Iterator<BrandMerchantTypeEnum> iterator = Arrays.stream(BrandMerchantTypeEnum.values()).iterator();
        while (iterator.hasNext()) {
            BrandMerchantTypeEnum next = iterator.next();
            list.add(new DictionaryModule(next.getType(), next.getDesc()));
        }
        return list;
    }
}
