package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资管机构回调请求记录表
 * @TableName brand_callback_records
 */
@TableName(value ="brand_callback_records")
@Data
public class BrandCallbackRecordsDO implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资管机构code
     */
    private String fundManagementCompanyCode;

    /**
     * 交易记录编号
     */
    private String tradeNo;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 资管机构对应的id：网商为appId，富管家为merchantNo
     */
    private String appId;

    /**
     * 回调函数：FUNCTION_REGISTER_MERCHANT_NOTIFY：商户注册通知，FUNCTION_MERCHANT_SETTLE_NOTIFY：商户结算通知
     */
    private String function;

    /**
     * 回调内容：JSON字符串
     */
    private String content;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 回调结果：0-未成功，1-成功
     */
    private Integer result;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.service.domain.entity.BrandCallbackRecordsDO");
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.service.domain.entity.BrandCallbackRecordsDO");
    }
}