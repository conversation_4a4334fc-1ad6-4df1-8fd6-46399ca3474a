package com.wosai.cua.brand.business.service.excel.context;

import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
public class ImportExcelAuditContext extends AbstractImportContext {

    @Getter
    private BrandModule brandModule;

    private ImportExcelAuditContext(BrandTaskLogModule brandTaskLogModule) {
        super(brandTaskLogModule);
    }

    public static ImportExcelAuditContext newInstance(BrandTaskLogModule brandTaskLogModule) {
        return new ImportExcelAuditContext(brandTaskLogModule);
    }

    public void bindBrandModule(BrandModule brandModule) {
        this.brandModule = brandModule;
    }

}
