package com.wosai.cua.brand.business.service.helper;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSErrorCode;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectAcl;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.ObjectPermission;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.StorageClass;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.middleware.aliyun.oss.DynamicCredentialsProvider;
import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
public class OssFileHelper {

    public static final String KEY_PREFIX = "sft/brand/";
    public static final String IMAGE_BUCKET_NAME = "wosai-images";
    public static final String BUCKET_NAME_PRIVATE_STATICS = "private-wosai-statics";
    public static final String BUCKET_NAME_PRIVATE_IMAGES = "private-wosai-images";
    public static final String PRIVATE_IMAGES_BASE_URL = "https://private-images.shouqianba.com";

    private static final String OSS_ENDPOINT = "http://oss-cn-hangzhou.aliyuncs.com";
    public static final long EXPIRE = 1000L * 60 * 15;

    public static final String STATIC_BASE_URL = "https://images.wosaimg.com";
    private static final AtomicReference<OSSClient> INSTANCE_OSS_CLIENT = new AtomicReference<>();

    static {
        Vault vault = null;
        try {
            vault = Vault.autoload();
        } catch (VaultException e) {
            log.error("初始化Vault异常！", e);
        }
        if (Objects.nonNull(vault)) {
            CredentialsProvider credentialsProvider = new DynamicCredentialsProvider(vault);
            OSSClient ossClient = new OSSClient(OSS_ENDPOINT, credentialsProvider, null);
            OSSClient oldOssClient = INSTANCE_OSS_CLIENT.get();
            INSTANCE_OSS_CLIENT.set(ossClient);
            if (oldOssClient != null) {
                oldOssClient.shutdown();
            }
        }
    }

    public static void uploadPrivateStaticsFile(String key, InputStream input, long length) {
        uploadStaticsFile(key, input, length, CannedAccessControlList.Private);
    }

    /**
     * 上传图片
     *
     * @param key
     * @param input
     * @param length
     */
    public static void uploadStaticsFile(String key, InputStream input, long length, CannedAccessControlList controlList) {
        upload(IMAGE_BUCKET_NAME, key, input, length, controlList);
    }

    /**
     * 上传文件
     *
     * @param bucketName
     * @param key
     * @param input
     * @param length
     */
    public static void upload(String bucketName, String key, InputStream input, long length, CannedAccessControlList controlList) {
        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(length);
        INSTANCE_OSS_CLIENT.get().putObject(bucketName, key, input, objectMeta);
        if (controlList != null) {
            INSTANCE_OSS_CLIENT.get().setObjectAcl(bucketName, key, controlList);
        }
    }

    public static void uploadFile(String bucketName, String key, File file) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, file);
        // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
//        ObjectMetadata metadata = new ObjectMetadata();
//         metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
//        metadata.setObjectAcl(CannedAccessControlList.Private);
//        putObjectRequest.setMetadata(metadata);
        INSTANCE_OSS_CLIENT.get().putObject(putObjectRequest);
    }

    public static String uploadPrivateImages(String key, File file) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET_NAME_PRIVATE_IMAGES, key, file);
        // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
        metadata.setObjectAcl(CannedAccessControlList.Private);
        putObjectRequest.setMetadata(metadata);
        INSTANCE_OSS_CLIENT.get().putObject(putObjectRequest);
        return PRIVATE_IMAGES_BASE_URL + "/" + key;
    }

    public static String getTargetPhotoUrl(File photoFile, String targetPhotoName) {
        if (!photoFile.exists() || !photoFile.isDirectory()) {
            return null;
        }
        File[] files = photoFile.listFiles();
        if (Objects.isNull(files) || files.length == 0) {
            return null;
        }
        Optional<File> first = Arrays.stream(files).filter(r -> r.getName().startsWith(targetPhotoName)).findFirst();
        if (!first.isPresent()) {
            return null;
        }
        File file = first.get();
        String fileName = file.getName();
        String fileType = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
        String key = "brand-business" + "/"
                + LocalDate.now() + "/"
                + UUID.randomUUID().toString().replaceAll("-", "") + fileType;
        File compressFile = null;
        try {
            compressFile = ZipHelper.zipPic(file, 2);
            return OssFileHelper.uploadPrivateImages(key, compressFile);
        } catch (Exception e) {
            log.error("图片上传失败: {} {}", photoFile.getAbsolutePath(), targetPhotoName, e);
            throw new BrandBusinessException(BrandBusinessExceptionEnum.UPLOAD_FILE_ERROR);
        } finally {
            FileUtils.deleteQuietly(compressFile);
        }
    }

    public static List<String> getTargetPhotoUrls(File photoFile, String targetPhotoName) {
        if (!photoFile.exists() || !photoFile.isDirectory()) {
            return null;
        }
        File[] files = photoFile.listFiles();
        if (Objects.isNull(files) || files.length == 0) {
            return null;
        }
        List<File> filesList = Arrays.stream(files).filter(r -> r.getName().startsWith(targetPhotoName)).collect(Collectors.toList());
        if (WosaiCollectionUtils.isNotEmpty(filesList)) {
            return null;
        }
        List<String> urls = new ArrayList<>();
        for (File file : filesList) {
            String fileName = file.getName();
            String fileType = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
            String key = "brand-business" + "/"
                    + LocalDate.now() + "/"
                    + UUID.randomUUID().toString().replaceAll("-", "") + fileType;
            File compressFile = null;
            try {
                compressFile = ZipHelper.zipPic(file, 2);
                String url = OssFileHelper.uploadPrivateImages(key, compressFile);
                urls.add(url);
            } catch (Exception e) {
                log.error("图片上传失败: {} {}", photoFile.getAbsolutePath(), targetPhotoName, e);
                throw new BrandBusinessException(BrandBusinessExceptionEnum.UPLOAD_FILE_ERROR);
            } finally {
                FileUtils.deleteQuietly(compressFile);
            }
        }
        return urls;
    }

    /**
     * 获取文件路径
     *
     * @param key
     * @return
     */
    public static String getStaticsFileUrl(String key) {
        if (key.contains("?")) {
            key = key.substring(0, key.indexOf("?"));
        }
        if (key.startsWith("/")) {
            key = key.substring(1);
        }
        if (exists(IMAGE_BUCKET_NAME, key)) {
            return bucketUrl(IMAGE_BUCKET_NAME, key);
        }
        return key;
    }

    public static String getStaticsFileUrlExpire(String key, long time) {
        if (key.contains("?")) {
            key = key.substring(0, key.indexOf("?"));
        }
        if (key.startsWith("/")) {
            key = key.substring(1);
        }
        if (exists(IMAGE_BUCKET_NAME, key)) {
            return bucketUrl(IMAGE_BUCKET_NAME, key, time);
        }
        return key;
    }

    /**
     * 获取文件路径
     *
     * @param bucketName
     * @param key
     * @return
     */
    public static String bucketUrl(String bucketName, String key, long expire) {

        ObjectAcl objectAcl = INSTANCE_OSS_CLIENT.get().getObjectAcl(bucketName, key);
        if (ObjectPermission.Private == objectAcl.getPermission()) {
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, key, HttpMethod.GET);
            request.setExpiration(new Date(System.currentTimeMillis() + expire));
            URL signedUrl = INSTANCE_OSS_CLIENT.get().generatePresignedUrl(request);
            return signedUrl.toString().replaceFirst("((http|https)+?://[\\s\\S]+?(.com)+?)", "");
        }
        return key;

    }

    /**
     * 获取文件路径
     *
     * @param bucketName
     * @param key
     * @return
     */
    public static String bucketUrl(String bucketName, String key) {
        return bucketUrl(bucketName, key, EXPIRE);
    }


    /**
     * 获取文件元信息
     *
     * @param bucketName
     * @param key
     * @return
     */
    public static ObjectMetadata getObjectMetadata(String bucketName, String key) {
        return INSTANCE_OSS_CLIENT.get().getObjectMetadata(bucketName, key);
    }

    /**
     * 判断文件是否存在
     *
     * @param bucketName
     * @param key
     * @return
     */
    public static boolean exists(String bucketName, String key) {
        try {
            getObjectMetadata(bucketName, key);
            return true;
        } catch (OSSException ex) {
            if (OSSErrorCode.NO_SUCH_KEY.equals(ex.getErrorCode()) || OSSErrorCode.NO_SUCH_BUCKET.equals(ex.getErrorCode())) {
                return false;
            } else {
                throw ex;
            }
        }
    }


    /**
     * 下载文件
     *
     * @param bucketName
     * @param key
     * @param filename
     */
    public static void downloadFile(String bucketName, String key, String filename) {
        INSTANCE_OSS_CLIENT.get().getObject(new GetObjectRequest(bucketName, key),
                new File(filename));
    }

    public static void downloadByStream(String bucketName, String key, String filePath) {
        try {
            FileOutputStream outputStream = new FileOutputStream(filePath);
            OSSObject ossObject = INSTANCE_OSS_CLIENT.get().getObject(new GetObjectRequest(bucketName, key));
            BufferedReader reader = new BufferedReader(new InputStreamReader(ossObject.getObjectContent()));
            // 创建BufferedWriter来写入文件
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));

            String line;
            // 逐行读取文本文件并写入到输出文件
            while ((line = reader.readLine()) != null) {
                writer.write(line);
                writer.newLine(); // 写入换行符
            }
            // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
            reader.close();
            // ossObject对象使用完毕后必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
            ossObject.close();
            writer.close();
        } catch (IOException e) {
            log.error("downloadByStream Exception", e);
        }
    }

    /**
     * 根据fileUrl下载文件并保存到指定路径
     *
     * @param fileUrl 文件的URL地址
     * @param downloadPath 下载文件保存的路径
     * @return 下载的文件对象
     * @throws IOException 如果下载过程中发生错误
     */
    public static File downloadFile(String fileUrl, String downloadPath) throws IOException {
        URL url = new URL(fileUrl);
        URLConnection connection = url.openConnection();
        InputStream inputStream = connection.getInputStream();

        File file = new File(downloadPath);
        try (FileOutputStream outputStream = new FileOutputStream(file)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } finally {
            inputStream.close();
        }
        return file;
    }

    public static void deleteFile(String bucketName, String filePath) {
        INSTANCE_OSS_CLIENT.get().deleteObject(bucketName, filePath);
    }
}

