package com.wosai.cua.brand.business.service.domain.tripartite.response.citic;

import com.wosai.cua.brand.business.service.domain.tripartite.citic.helper.CiticSignHelper;
import com.wosai.cua.brand.business.service.domain.tripartite.citic.helper.CiticXmlHelper;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data.CiticResponseData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.xml.bind.JAXBException;
import java.util.Objects;

@Data
@Slf4j
public class CiticResponse<T extends CiticResponseData> {
    /**
     * 外联平台应答码
     */
    protected String code;
    /**
     * 外联平台应答码描述
     */
    protected String message;
    /**
     * 业务响应数据
     */
    protected T data;//业务响应数据


    public static final String HTTP_SUCCESS = "AAAAAAA";
    public static final String BUSINESS_SUCCESS = "00000";
    // JU209:卡号已绑定
    public static final String BANK_CARD_BIND = "JU209";

    public CiticResponse() {
    }

    public CiticResponse(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public boolean verifySignNotPass(String resStr, String senderCert) {
        if (this.getData() == null) {
            return false;
        }
        String sigStr = this.getData().getSignInfo();
        return !CiticSignHelper.verifySign(CiticXmlHelper.sortSignInfo(resStr, this.data.getClass()).getBytes(), sigStr, senderCert);
    }

    public boolean isNotSuccess() {
        return !isSuccess();
    }

    public boolean isSuccess() {
        if (!Objects.equals(code, HTTP_SUCCESS)) {
            return Boolean.FALSE;
        }
        if (Objects.isNull(data)) {
            return Boolean.FALSE;
        }
        return Objects.equals(data.getRspCode(), BUSINESS_SUCCESS);
    }

    public String toXml() {
        try {
            return CiticXmlHelper.toXml(this);
        } catch (JAXBException ignore) {
            log.error("CiticResponse toXml error", ignore);
        }
        return null;
    }

}
