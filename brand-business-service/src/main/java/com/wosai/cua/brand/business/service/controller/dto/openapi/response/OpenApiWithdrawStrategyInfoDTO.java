package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wosai.cua.brand.business.api.serializer.BigDecimalToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 提现策略对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpenApiWithdrawStrategyInfoDTO {
    /**
     * 策略id
     */
    @JsonProperty("strategy_id")
    private String strategyId;

    /**
     * 提现方式：AUTO-自动提现，MANUAL_OPERATION-手动提现
     * @see com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum
     */
    @JsonProperty("withdraw_type")
    private String withdrawType;

    /**
     * 提现周期类型
     * @see com.wosai.cua.brand.business.api.enums.WithdrawCycleTypeEnum
     */
    @JsonProperty("withdraw_cycle_type")
    private String withdrawCycleType;

    /**
     * 提现周期时间
     */
    private Integer withdrawCycleTime;

    /**
     * 提现周期时间集合
     */
    @JsonProperty("withdraw_cycle_times")
    private String withdrawCycleTimes;

    /**
     * 最少单笔提现金额，单位：元
     */
    @JsonSerialize(using = BigDecimalToStringSerializer.class)
    @JsonProperty("min_withdrawal_amount")
    private BigDecimal minWithdrawalAmount;

    /**
     * 提现预留金额，单位：元
     */
    @JsonSerialize(using = BigDecimalToStringSerializer.class)
    @JsonProperty("reserved_amount")
    private BigDecimal reservedAmount;
}
