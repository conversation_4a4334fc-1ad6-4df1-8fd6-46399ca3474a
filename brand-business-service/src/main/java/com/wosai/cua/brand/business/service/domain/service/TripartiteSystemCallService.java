package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;

/**
 * 三方系统调用实现接口
 */
public interface TripartiteSystemCallService {

    /**
     * 获取三方系统枚举(资管机构)
     *
     * @return 资管机构枚举
     */
    FundManagementCompanyEnum getFundManagementCompanyEnum();

    /**
     * 三方系统调用
     *
     * @param request 三方系统调用请求
     * @return 三方系统调用响应
     */
    <T extends TripartiteSystemCallRequest, R extends TripartiteSystemCallResponse, C extends ConfigModule> R call(T request, Class<R> clazz, C configModule);
}
