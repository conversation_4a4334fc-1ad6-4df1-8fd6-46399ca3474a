package com.wosai.cua.brand.business.service.module.merchant.analyze;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.annotations.AnalyzeFieldCheck;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AlreadyExistMerchantAnalyzeModule extends BaseMerchantAnalyzeModule{
    /**
     * 商户编号
     */
    @AnalyzeFieldCheck(needRequired = true, message = "未填写收钱吧商户号")
    private String merchantSn;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人身份证号
     */
    private String legalPersonLicenseNumber;

    /**
     * 商户类型:1-个人 2-企业
     */
    private Integer merchantType;

    /**
     * 银行账户名称
     */
    @AnalyzeFieldCheck(needRequired = false,message = "未填写对公账户名称！",groups = {FundManagementCompanyEnum.PAB,FundManagementCompanyEnum.CITIC,FundManagementCompanyEnum.FUIOU})
    private String bankAccountName;
}
