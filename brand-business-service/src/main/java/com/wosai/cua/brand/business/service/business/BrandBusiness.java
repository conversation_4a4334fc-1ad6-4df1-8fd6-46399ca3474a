package com.wosai.cua.brand.business.service.business;


import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.app.dto.CreateBrandUserReq;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.BrandUserService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.SubAccountOpenDetailDTO;
import com.wosai.cua.brand.business.api.dto.brand.SubmitOpenAccountResultDTO;
import com.wosai.cua.brand.business.api.dto.request.BatchCreateBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.ChangeAcquirerCheckRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateMerchantDTO;
import com.wosai.cua.brand.business.api.dto.request.ExistBrandOpenPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.PaymentModeChangeRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.*;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.CreateBrandMerchantAssociationRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.RemoveBrandMerchantAssociationRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ModifyBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ModifyBrandMerchantOpenStatusDTO;
import com.wosai.cua.brand.business.api.dto.request.sp.PagingBrandMerchantsRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandListInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantsQueryResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandPaymentModeResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.ChangeAcquirerCheckResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateBrandMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateBrandResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.ModifyBrandResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.response.PaymentModeChangeResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.dto.response.common.CommonResult;
import com.wosai.cua.brand.business.api.dto.response.common.ListResult;
import com.wosai.cua.brand.business.api.dto.response.sp.BrandMerchantsPagingResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.sp.BrandOpenAppInfoQueryResponseDTO;
import com.wosai.cua.brand.business.api.enums.AggregationModelEnum;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.CheckAccountResultEnum;
import com.wosai.cua.brand.business.api.enums.DockingModeEnum;
import com.wosai.cua.brand.business.api.enums.EnrollChannelEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MeiTuanStoreStatusEnum;
import com.wosai.cua.brand.business.api.enums.MerchantBrandTypeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.cua.brand.business.api.enums.UseOfFundsEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.model.BrandMerchantFilterModel;
import com.wosai.cua.brand.business.api.model.PagingModel;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.service.BankCardDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandAccountDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.MerchantCenterDomainService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService;
import com.wosai.cua.brand.business.service.domain.tripartite.citic.helper.CiticCryptoHelper;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.client.MeituanClient;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.request.BillListRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.meituan.response.BaseResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.RegisteredUserRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.AddConcentrateRelationRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CancelConcentrateRelationApplyRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CancelConcentrateRelationApplyRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CancelConcentrateRelationRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CancelConcentrateRelationRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CloseAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CloseAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.InvalidAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.InvalidAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.OpenAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.account.BkCloudFundsAccountOpenRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantAppletPreRegisterRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantAppletRegisterQueryRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.account.BkcloudfundsAccountOpenRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantAppletRegisterQueryRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.CancelMemberRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.CreateMemberRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.QueryAccountInfoRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.QueryMemberUidRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.RegisteredResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.AddConcentrateRelationResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CancelConcentrateRelationApplyResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CancelConcentrateRelationResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CloseAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.InvalidAllocateAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.OpenAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.QueryAllocateAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.QueryAllocateAccountResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.account.BkCloudFundsAccountOpenResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantAppletPreRegisterResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantAppletRegisterQueryResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.CreateMemberResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.QueryAccountInfoResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.QueryMemberUidResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.VfinanceBaseResponse;
import com.wosai.cua.brand.business.service.enums.RedisKeyEnum;
import com.wosai.cua.brand.business.service.enums.VFinanceResponseCodeEnum;
import com.wosai.cua.brand.business.service.enums.third.FuiouOpenAccountStatusEnum;
import com.wosai.cua.brand.business.service.enums.third.MyBankAccountTypeEnum;
import com.wosai.cua.brand.business.service.event.model.BrandCreateEvent;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantEnrollEvent;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantStatusChangeEvent;
import com.wosai.cua.brand.business.service.event.model.MerchantOpenStatusEvent;
import com.wosai.cua.brand.business.service.event.publisher.DefaultEventPublisher;
import com.wosai.cua.brand.business.service.externalservice.contractjob.MerchantContractJobClient;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeRequest;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeResult;
import com.wosai.cua.brand.business.service.externalservice.coreb.CoreBusinessClient;
import com.wosai.cua.brand.business.service.externalservice.coreb.model.MerchantFindRequest;
import com.wosai.cua.brand.business.service.externalservice.coreb.model.MerchantQueryResult;
import com.wosai.cua.brand.business.service.externalservice.marketingprepaid.MarketingPrepaidClient;
import com.wosai.cua.brand.business.service.externalservice.marketingprepaid.model.AssociateBrandBeforeCheckRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.MerchantBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppInfoOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.MerchantCenterClient;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantBusinessLicenseQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.MerchantUserClient;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.model.SuperAdminInfoQueryResult;
import com.wosai.cua.brand.business.service.helper.BankOfDepositHelper;
import com.wosai.cua.brand.business.service.helper.CommonHelper;
import com.wosai.cua.brand.business.service.helper.IdGeneratorSnowflake;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.helper.RSAKeyGenerator;
import com.wosai.cua.brand.business.service.helper.RedisHelper;
import com.wosai.cua.brand.business.service.helper.ThreadPoolHelper;
import com.wosai.cua.brand.business.service.helper.UcUserV2Helper;
import com.wosai.cua.brand.business.service.module.account.AccountsModule;
import com.wosai.cua.brand.business.service.module.account.BrandAccountModule;
import com.wosai.cua.brand.business.service.module.account.citic.CiticAccountModule;
import com.wosai.cua.brand.business.service.module.account.convert.AccountsConfigModuleConverter;
import com.wosai.cua.brand.business.service.module.account.mybank.MyBankAccountModule;
import com.wosai.cua.brand.business.service.module.account.pab.PabAccountModule;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.bank.BizBankAccountModule;
import com.wosai.cua.brand.business.service.module.brand.BrandConditionModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantCreationRecordModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.brand.ChangeBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.MerchantBrandDetailModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandModule;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantDockingModeExtraModule;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantExtraModule;
import com.wosai.cua.brand.business.service.module.common.industry.IndustryModule;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.convert.ConfigModuleConverter;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantConditionModule;
import com.wosai.cua.brand.business.service.module.tripartite.fuiou.FuiouToBeActiveRecordModule;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.req.CreateMerchantAndStoreReq;
import com.wosai.mc.model.req.CreateStoreBusinessLicenseWithStoreReq;
import com.wosai.mc.model.req.CreateStoreReq;
import com.wosai.mc.model.req.StoreComplete;
import com.wosai.mc.model.req.UpdateMerchantBusinessLicenseReq;
import com.wosai.mc.model.resp.CreateMerchantResp;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.sharing.proxy.model.request.OpenAggregationRequest;
import com.wosai.sharing.proxy.service.AggregationService;
import com.wosai.uc.dto.CreateUcUserReq;
import com.wosai.uc.dto.UcUserInfoResp;
import com.wosai.uc.v2.service.UcUserServiceV2;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.bank.model.Request;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountRes;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountUpdateReq;
import com.wosai.upay.bank.model.bizbankaccount.MerchantBizBankAccount;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.bean.request.UpdateMerchantSFTBrandInfoRequest;
import com.wosai.upay.core.service.SnGenerator;
import com.wosai.upay.core.service.TradeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.bouncycastle.cert.CertIOException;
import org.bouncycastle.operator.OperatorCreationException;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.cert.CertificateException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 品牌业务方法
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BrandBusiness {

    private final BrandDomainService brandDomainService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantCenterClient merchantCenterClient;

    @Autowired
    private CoreBusinessClient coreBusinessClient;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private MerchantUserClient merchantUserClient;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    private final VfinanceInterfaceService vfinanceInterfaceService;

    @Autowired
    private UcUserServiceV2 ucUserServiceV2;

    @Autowired
    private SnGenerator snGenerator;

    private final BankCardDomainService bankCardDomainService;

    @Autowired
    private TagIngestService tagIngestService;

    @Autowired
    private IndustryV2Service industryService;

    @Autowired
    private DistrictsServiceV2 districtsService;
    @Qualifier("redisClusterTemplate")
    @Autowired
    private StringRedisTemplate redisClusterTemplate;
    @Autowired
    private BrandUserService brandUserService;

    @Autowired
    private StoreService storeService;
    @Autowired
    private MerchantContractJobClient merchantContractJobClient;
    @Autowired
    private MerchantBusinessOpenClient merchantBusinessOpenClient;

    @Value("${spring.application.biz}")
    private String biz;

    @Value("${fuiou.open.account.check_type}")
    private String fuiouCheckType;
    @Value("${appid.indirect}")
    private String indirectAppId;
    @Value("${appid.brandPaymentHub}")
    private String brandPaymentHubAppId;

    @Value("${aggregation.open.notify-url}")
    private String aggregationOpenNotifyUrl;

    @Autowired
    private MerchantBizBankAccountService merchantBizBankAccountService;
    @Autowired
    private MarketingPrepaidClient marketingPrepaidClient;

    @Autowired
    private AggregationService aggregationService;

    private final List<MerchantCenterDomainService> merchantCenterDomainServices;

    private final ApolloConfig apolloConfig;

    private final BrandConfigDomainService brandConfigDomainService;

    private final BrandAccountDomainService brandAccountDomainService;

    private final DefaultEventPublisher defaultEventPublisher;

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private static final Map<BrandMerchantTypeEnum, Function<CreateMerchantDTO, CreateMerchantResponseDTO>> CREATE_MERCHANT_FUNCTION_MAP = Maps.newHashMap();

    private static final Map<BrandMerchantTypeEnum, MerchantCenterDomainService> MERCHANT_CENTER_DOMAIN_SERVICE_MAP = Maps.newConcurrentMap();

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = Maps.newConcurrentMap();

    private final RedisHelper redisHelper;

    private final IdGeneratorSnowflake idGeneratorSnowflake;

    private final CommonHelper commonHelper;

    @Autowired
    private BankService bankService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    public BrandBusiness(BrandDomainService brandDomainService, VfinanceInterfaceService vfinanceInterfaceService, BankCardDomainService bankCardDomainService, List<MerchantCenterDomainService> merchantCenterDomainServices, ApolloConfig apolloConfig, BrandConfigDomainService brandConfigDomainService, BrandAccountDomainService brandAccountDomainService, DefaultEventPublisher defaultEventPublisher, List<TripartiteSystemCallService> tripartiteSystemCallServices, RedisHelper redisHelper, IdGeneratorSnowflake idGeneratorSnowflake, CommonHelper commonHelper, BankOfDepositHelper bankOfDepositHelper) {
        this.brandDomainService = brandDomainService;
        this.vfinanceInterfaceService = vfinanceInterfaceService;
        this.bankCardDomainService = bankCardDomainService;
        this.merchantCenterDomainServices = merchantCenterDomainServices;
        this.apolloConfig = apolloConfig;
        this.brandConfigDomainService = brandConfigDomainService;
        this.brandAccountDomainService = brandAccountDomainService;
        this.defaultEventPublisher = defaultEventPublisher;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.redisHelper = redisHelper;
        this.idGeneratorSnowflake = idGeneratorSnowflake;
        this.commonHelper = commonHelper;
    }

    @PostConstruct
    public void buildFunctionMap() {
        CREATE_MERCHANT_FUNCTION_MAP.put(BrandMerchantTypeEnum.COMPANY, this::createCompanyMerchant);
        CREATE_MERCHANT_FUNCTION_MAP.put(BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS, this::createIndividualBusinessMerchant);
        CREATE_MERCHANT_FUNCTION_MAP.put(BrandMerchantTypeEnum.PERSONAL, this::createPersonalMerchant);
        merchantCenterDomainServices.forEach(merchantCenterDomainService -> MERCHANT_CENTER_DOMAIN_SERVICE_MAP.put(merchantCenterDomainService.getBrandMerchantType(), merchantCenterDomainService));
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateBrandResponseDTO createBrand(CreateBrandRequestDTO createBrandRequest) {
        // 校验参数
        ParamsCheckHelper.checkFundManagementCompanyParams(createBrandRequest);
        createBrandRequest.setFundManagementCompany(createBrandRequest.getFundManagementCompanyCode().getFundManagementCompanyName());
        // ①先拿到merchantSn获取商户信息
        MerchantInfo merchant = merchantService.getMerchantBySn(
                createBrandRequest.getMerchantSn(), null
        );
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        List<MerchantBrandDetailModule> brandModuleByMerchantId = brandDomainService.getBrandModuleByMerchantId(merchant.getId());
        if (!CollectionUtils.isEmpty(brandModuleByMerchantId)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_A_BRAND);
        }
        UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
        //查询老板信息
        if (Objects.isNull(superAdmin)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_NOT_HAVE_SUPER_ADMIN);
        }
        CreateBrandResponseDTO responseDto = new CreateBrandResponseDTO();
        // 生成品牌编号
        String brandSn = snGenerator.nextBrandSn();
        BrandModule brandModule = JSON.parseObject(JSON.toJSONString(createBrandRequest), BrandModule.class);
        brandModule.setSn(brandSn);
        brandModule.setMerchantSn(merchant.getSn());
        brandModule.setBrandId(UUID.randomUUID().toString());
        brandModule.setParentId(createBrandRequest.getParentBrandId());
        brandModule.setSftTag(1);
        // 保存品牌信息
        brandDomainService.createBrand(brandModule);
        // 保存品牌配置信息
        this.saveBrandConfig(createBrandRequest.getFundManagementCompanyCode(), createBrandRequest.getConfig(), brandModule);
        // 保存品牌账户信息
        this.saveAccountsConfig(createBrandRequest.getFundManagementCompanyCode(), createBrandRequest.getAccounts(), brandModule);
        responseDto.setBrandId(brandModule.getBrandId());
        responseDto.setSn(brandModule.getSn());
        // 创建品牌用户
        brandUserService.createBrandUser(new CreateBrandUserReq()
                .setUc_user_id(superAdmin.getUcUserInfo().getUc_user_id())
                .setBrand_id(brandModule.getBrandId()));
        // 写入管理商户的数据
        BrandMerchantModule brandMerchantModule = BrandMerchantModule.builder()
                .merchantId(merchant.getId())
                .merchantSn(brandModule.getMerchantSn())
                .brandId(brandModule.getBrandId())
                .parentBrandId(createBrandRequest.getParentBrandId())
                .merchantName(merchant.getName())
                .merchantType(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())
                .merchantDockingMode(brandModule.getMerchantDockingMode())
                .memberId(brandModule.getAdminMerchantMemberId())
                .build();
        brandDomainService.createBrandMerchant(brandMerchantModule);
        // 创建品牌商
//        String brandOwnerMerchantId = this.createBrandOwnerMerchant(merchant, brandModule, superAdmin, configModule);
        try {
            tagIngestService.ingest(Lists.newArrayList(merchant.getId()), apolloConfig.getBrandTagId(), true);
//            tagIngestService.ingest(Lists.newArrayList(brandOwnerMerchantId), apolloConfig.getStoreTagId(), true);
        } catch (Exception e) {
            log.error("ingest crow error", e);
        }
        // 注册同步回调
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 事务提交后执行的业务代码
                defaultEventPublisher.publish(BrandCreateEvent.builder()
                        .brandId(brandModule.getBrandId())
                        .brandSn(brandModule.getSn())
                        .merchantSn(merchant.getSn())
                        .merchantId(merchant.getId()).build());
            }
        });
        return responseDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateBrandResponseDTO createBrandForReconciliationApply(CreateBrandForReconciliationApplyRequestDTO createBrandForReconciliationApplyRequestDTO) {
        CreateBrandResponseDTO responseDto = new CreateBrandResponseDTO();
        BrandModule brandModule = JSON.parseObject(JSON.toJSONString(createBrandForReconciliationApplyRequestDTO), BrandModule.class);
        // 创建品牌信息
        String brandSn = snGenerator.nextBrandSn();
        String brandId = UUID.randomUUID().toString();
        brandModule.setSn(brandSn);
        brandModule.setBrandId(brandId);
        brandDomainService.createBrand(brandModule);
        // 创建品牌用户
        String ucUserId = merchantUserClient.createUcUserForEmail(createBrandForReconciliationApplyRequestDTO.getContactEmail());
        brandUserService.createBrandUser(new CreateBrandUserReq()
                .setUc_user_id(ucUserId)
                .setBrand_id(brandId));
        responseDto.setBrandId(brandId);
        responseDto.setSn(brandSn);
        // 注册同步回调
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 事务提交后执行的业务代码
                defaultEventPublisher.publish(BrandCreateEvent.builder()
                        .brandId(brandModule.getBrandId())
                        .brandSn(brandModule.getSn()).build());
            }
        });
        return responseDto;

    }

    @Transactional(rollbackFor = Exception.class)
    public CreateBrandResponseDTO createBrandForOpenIndirectApply(CreateBrandForOpenIndirectApplyRequestDTO createBrandForOpenIndirectApplyRequestDTO) {
        if (StringUtils.isNotBlank(createBrandForOpenIndirectApplyRequestDTO.getParentBrandId())) {
            BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(createBrandForOpenIndirectApplyRequestDTO.getParentBrandId());
            if (Objects.isNull(brandModule)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
            }
        }
        MerchantInfoQueryResult merchantInfoQueryResult = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(createBrandForOpenIndirectApplyRequestDTO.getMainMerchantSn()));
        if (Objects.nonNull(brandDomainService.getBrandMerchantInfoByMerchantId(merchantInfoQueryResult.getMerchantId()))) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_A_BRAND);
        }
        MerchantBusinessLicenseQueryResult merchantBusinessLicense = merchantCenterClient.queryLatestMerchantBusinessLicense(merchantInfoQueryResult.getMerchantId());
        if (Objects.isNull(merchantBusinessLicense)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BUSINESS_LICENSE_NOT_EXIST);
        }
        if (BusinessLicenseTypeEnum.MICRO.getValue().equals(merchantBusinessLicense.getType())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BUSINESS_LICENSE_TYPE_NOT_MATCH);
        }
        Optional<AppInfoOpenResult> indirectAppInfo = merchantBusinessOpenClient.queryAppInfoOpenResultByMerchantId(merchantInfoQueryResult.getMerchantId()).stream().filter(r -> r.getAppId().equals(indirectAppId)).findFirst();
        if (!indirectAppInfo.isPresent() || indirectAppInfo.get().getStatus() != AppInfoStatusEnum.SUCCESS) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_OPEN_INDIRECT);
        }
        CreateBrandResponseDTO responseDto = new CreateBrandResponseDTO();
        BrandModule brandModule = JSON.parseObject(JSON.toJSONString(createBrandForOpenIndirectApplyRequestDTO), BrandModule.class);
        brandModule.setParentId(createBrandForOpenIndirectApplyRequestDTO.getParentBrandId());
        brandModule.setMerchantSn(createBrandForOpenIndirectApplyRequestDTO.getMainMerchantSn());
        String brandSn = snGenerator.nextBrandSn();
        String brandId = UUID.randomUUID().toString();
        brandModule.setSn(brandSn);
        brandModule.setBrandId(brandId);
        brandDomainService.createBrand(brandModule);
        // 品牌商户信息
        BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
        brandMerchantModule.setBrandId(brandId);
        brandMerchantModule.setParentBrandId(createBrandForOpenIndirectApplyRequestDTO.getParentBrandId());
        brandMerchantModule.setMerchantId(merchantInfoQueryResult.getMerchantId());
        brandMerchantModule.setMerchantSn(createBrandForOpenIndirectApplyRequestDTO.getMainMerchantSn());
        brandMerchantModule.setMerchantName(merchantInfoQueryResult.getName());
        brandMerchantModule.setMerchantType(MerchantTypeEnum.BRAND_ADMIN.getMerchantType());
        brandMerchantModule.setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode());
        brandDomainService.createBrandMerchant(brandMerchantModule);
        // 创建品牌用户
        SuperAdminInfoQueryResult superAdminInfo = merchantUserClient.getSuperAdminInfo(merchantInfoQueryResult.getMerchantId());
        brandUserService.createBrandUser(new CreateBrandUserReq()
                .setUc_user_id(superAdminInfo.getUcUserId())
                .setBrand_id(brandId));
        responseDto.setBrandId(brandId);
        responseDto.setSn(brandSn);
        // 注册同步回调
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 事务提交后执行的业务代码
                defaultEventPublisher.publish(BrandCreateEvent.builder()
                        .brandId(brandModule.getBrandId())
                        .brandSn(brandModule.getSn())
                        .merchantSn(merchantInfoQueryResult.getMerchantSn())
                        .merchantId(merchantInfoQueryResult.getMerchantId()).build());
            }
        });
        return responseDto;

    }

    @Transactional(rollbackFor = Exception.class)
    public void existBrandOpenPaymentHub(ExistBrandOpenPaymentHubRequestDTO existBrandOpenPaymentHubRequest) {
        // 校验品牌ID和参数
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandSn(existBrandOpenPaymentHubRequest.getBrandSn());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (Objects.nonNull(brandModule.getFundManagementCompanyCode())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PAYMENT_HUB_INFO_ALREADY_EXIST);
        }
        ParamsCheckHelper.checkFundManagementCompanyParams(existBrandOpenPaymentHubRequest.getFundManagementCompanyCode(), existBrandOpenPaymentHubRequest.getConfig(), existBrandOpenPaymentHubRequest.getAccounts());
        brandModule.setFundManagementCompanyCode(existBrandOpenPaymentHubRequest.getFundManagementCompanyCode());
        brandDomainService.modifyBrand(brandModule);
        // 保存品牌配置信息
        this.saveBrandConfig(existBrandOpenPaymentHubRequest.getFundManagementCompanyCode(), existBrandOpenPaymentHubRequest.getConfig(), brandModule);
        // 保存品牌账户信息
        this.saveAccountsConfig(existBrandOpenPaymentHubRequest.getFundManagementCompanyCode(), existBrandOpenPaymentHubRequest.getAccounts(), brandModule);
        try {
            MerchantInfoQueryResult merchantInfoQueryResult = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(brandModule.getMerchantSn()));
            tagIngestService.ingest(Lists.newArrayList(merchantInfoQueryResult.getMerchantId()), apolloConfig.getBrandTagId(), true);
        } catch (Exception e) {
            log.error("ingest crow error", e);
        }
    }

    private void saveBrandConfig(FundManagementCompanyEnum fundManagementCompanyEnum, ConfigDTO configDTO, BrandModule brandModule) {
        ConfigModule configModule = ConfigModuleConverter.convert(configDTO, fundManagementCompanyEnum);
        if (Objects.nonNull(configModule)) {
            if (Objects.isNull(configModule.getAllowLogin())) {
                configModule.setAllowLogin(true);
            }
            BrandConfigModule brandConfigModule = new BrandConfigModule();
            brandConfigModule.setBrandId(brandModule.getBrandId());
            switch (fundManagementCompanyEnum) {
                case MY_BANK:
                    brandConfigModule.setChannelId(configDTO.getMyBankConfig().getAppId());
                    brandConfigModule.setChannelType(FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
                    break;
                case PAB:
                    brandConfigModule.setChannelId(configDTO.getPabConfig().getPartnerId());
                    brandConfigModule.setChannelType(FundManagementCompanyEnum.PAB.getFundManagementCompanyCode());
                    break;
                case CITIC:
                    brandConfigModule.setChannelId(configDTO.getCiticConfig().getMerchantId());
                    brandConfigModule.setChannelType(FundManagementCompanyEnum.CITIC.getFundManagementCompanyCode());
                    break;
                case FUIOU:
                    if (Objects.nonNull(configDTO) && Objects.nonNull(configDTO.getFuiouConfig())) {
                        brandConfigModule.setChannelId(configDTO.getFuiouConfig().getMerchantNo());
                        brandModule.setAdminMerchantMemberId(configDTO.getFuiouConfig().getMerchantNo());
                        brandModule.setMerchantDockingMode(Objects.nonNull(configDTO.getFuiouConfig().getMerchantDockingMode()) ? configDTO.getFuiouConfig().getMerchantDockingMode().getCode() : null);
                    }
                    brandConfigModule.setChannelType(FundManagementCompanyEnum.FUIOU.getFundManagementCompanyCode());
                    break;
                default:
            }
            brandConfigModule.setConfig(JSON.toJSONString(configModule));
            brandConfigDomainService.insertBrandConfig(brandConfigModule);
        }
    }

    private void saveAccountsConfig(FundManagementCompanyEnum fundManagementCompanyEnum, AccountsDTO accountsDTO, BrandModule brandModule) {
        AccountsModule accounts = AccountsConfigModuleConverter.convert(fundManagementCompanyEnum, accountsDTO);
        if (Objects.nonNull(accounts)) {
            BrandAccountModule brandAccountModule = new BrandAccountModule();
            brandAccountModule.setBrandId(brandModule.getBrandId());
            brandAccountModule.setAccounts(JSON.toJSONString(accounts));
            brandAccountDomainService.insertBrandAccount(brandAccountModule);
        }
    }

    /**
     * 更新品牌账户配置信息
     *
     * @param fundManagementCompanyEnum 资金管理公司枚举
     * @param accountsDTO               账户配置信息
     * @param brandModule               品牌模块
     */
    private void updateAccountsConfig(FundManagementCompanyEnum fundManagementCompanyEnum, AccountsDTO accountsDTO, BrandModule brandModule) {
        AccountsModule accounts = AccountsConfigModuleConverter.convert(fundManagementCompanyEnum, accountsDTO);
        if (Objects.nonNull(accounts)) {
            BrandAccountModule brandAccountModule = brandAccountDomainService.getBrandAccountModuleByBrandId(brandModule.getBrandId());
            brandAccountModule.setAccounts(JSON.toJSONString(accounts));
            if (Objects.isNull(brandAccountModule.getId())) {
                brandAccountDomainService.insertBrandAccount(brandAccountModule);
            } else {
                brandAccountDomainService.updateBrandAccount(brandAccountModule);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public SubmitOpenAccountResultDTO invokePartnerService(
            MerchantInfo merchant,
            BrandModule brandModule,
            MerchantBusinessLicenseInfo merchantBusinessLicense,
            BrandMerchantModule brandMerchantModule,
            ConfigModule configModule,
            BrandMerchantCreationRecordModule recordModule) {
        SubmitOpenAccountResultDTO submitOpenAccountResultDTO = null;
        switch (brandModule.getFundManagementCompanyCode()) {
            case PAB:
                submitOpenAccountResultDTO = this.createPabMember(merchant, merchantBusinessLicense, brandMerchantModule, recordModule, configModule);
                break;
            case MY_BANK:
                submitOpenAccountResultDTO = this.createMyBankMember(merchant, merchantBusinessLicense, recordModule, configModule, brandMerchantModule);
                break;
            case CITIC:
                submitOpenAccountResultDTO = this.createCiticBankMember(merchant, merchantBusinessLicense, recordModule, configModule, brandMerchantModule);
                break;
            case FUIOU:
                if (brandMerchantModule.getMerchantDockingMode().equals(BrandMerchantDockingModeEnum.SEPARATE_ACCOUNT.getCode())) {
                    submitOpenAccountResultDTO = this.createFuiouMember(merchant, merchantBusinessLicense, recordModule, configModule, brandMerchantModule);
                } else {
                    submitOpenAccountResultDTO = this.invokeServiceHandleCollection(brandModule, brandMerchantModule, configModule, recordModule);
                }
                break;
            default:
                break;
        }
        brandDomainService.updateBrandMerchant(brandMerchantModule);
        brandDomainService.updateCreateBrandMerchantCreationRecord(recordModule);
        boolean b = BrandMerchantAccountOpenStatusEnum.OPENED.getStatus().equals(brandMerchantModule.getAccountOpenStatus()) && Boolean.TRUE.equals(Objects.nonNull(brandMerchantModule.getIsAlreadyExistMerchantImport()) && brandMerchantModule.getIsAlreadyExistMerchantImport() && Objects.nonNull(configModule) && Objects.nonNull(configModule.getNeedSpecialTreatment())) && Boolean.TRUE.equals(configModule.getNeedSpecialTreatment());
        if (b) {
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.IN_OPENING.getStatus());
        }
        if (BrandMerchantAccountOpenStatusEnum.OPENED.getStatus().equals(brandMerchantModule.getAccountOpenStatus())) {
            switch (brandModule.getFundManagementCompanyCode()) {
                case PAB:
                    defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandModule.getBrandId()).merchantSn(merchant.getSn()).enrollChannelEnum(EnrollChannelEnum.PING_AN_WEI_JIN).build());
                    break;
                case CITIC:
                    // 事务提交后执行的业务代码
                    defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandModule.getBrandId()).merchantSn(merchant.getSn()).enrollChannelEnum(EnrollChannelEnum.CITIC).build());
                    break;
                default:
                    break;
            }
        }
        return submitOpenAccountResultDTO;
    }

    private SubmitOpenAccountResultDTO invokeServiceHandleCollection(
            BrandModule brandModule,
            BrandMerchantModule brandMerchantModule,
            ConfigModule configModule,
            BrandMerchantCreationRecordModule recordModule) {
        if (!brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU)) {
            brandDomainService.updateBrandMerchant(brandMerchantModule);
            brandDomainService.updateCreateBrandMerchantCreationRecord(recordModule);
            return SubmitOpenAccountResultDTO.builder().openSuccess(true).build();
        }
        recordModule.setRecordId(brandMerchantModule.getMerchantSn() + System.currentTimeMillis());
        FuiouConfigModule fuiouConfigModule = (FuiouConfigModule) configModule;
        if (brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.getMerchantType())) {
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.EFFECTIVE.getStatus());
            brandMerchantModule.setMemberId(fuiouConfigModule.getMerchantNo());
        }
        if (!brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.getMerchantType()) || brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
            BrandMerchantDockingModeExtraModule dockingModeExtraModule = brandMerchantModule.getExtra().getDockingModeExtraModule();
            AddConcentrateRelationRequest request = new AddConcentrateRelationRequest(((FuiouConfigModule) configModule).getMerchantNo());
            request.getBody().setCheckType(fuiouCheckType);
            request.getBody().setBalanceConcentrateScale(String.valueOf(dockingModeExtraModule.getConcentrateScale() * 100));
            request.getBody().setTraceNo(recordModule.getRecordId());
            request.getBody().setUseType(UseOfFundsEnum.getFyCodeByCode(dockingModeExtraModule.getUseOfFunds()));
            request.getBody().setMchntCdConcentrate(brandMerchantModule.getMemberId());
            request.getBody().setConcentrateTypes(Lists.newArrayList(AggregationModelEnum.BALANCE_POOLING.getFyCode()));
//            request.getBody().setMobile(m);
            try {
                AddConcentrateRelationResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, AddConcentrateRelationResponse.class, fuiouConfigModule);
                if (response == null) {
                    recordModule.setStatus(0);
                    recordModule.setResult("调用富友接口申请归集授权未响应");
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("调用富友接口申请归集授权失败").build();
                }
                if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                    recordModule.setStatus(0);
                    recordModule.setResult("调用富友接口申请归集授权失败," + response.getResultMsg());
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    brandMerchantModule.setAccountOpenFailureReason(response.getResultMsg());
                    return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason(response.getResultMsg()).build();
                }
                recordModule.setRecordId(response.getBody().getBatchNo());
                if (Objects.nonNull(brandMerchantModule.getExtra()) && Objects.nonNull(brandMerchantModule.getExtra().getDockingModeExtraModule())) {
                    brandMerchantModule.getExtra().getDockingModeExtraModule().setBatchNo(response.getBody().getBatchNo());
                }
                brandMerchantModule.setAccountOpenFailureReason(response.getBody().getCheckUrl());
            } catch (Exception e) {
                recordModule.setStatus(0);
                recordModule.setResult("调用富友接口申请归集授权异常," + e.getMessage());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason(e.getMessage());
                return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("接口异常，请联系客服").build();
            }
            recordModule.setStatus(3);
            recordModule.setResult("归集授权申请请求成功，等待富友回调！");
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.PENDING.getStatus());
        }
        brandDomainService.updateBrandMerchant(brandMerchantModule);
        brandDomainService.updateCreateBrandMerchantCreationRecord(recordModule);
        return SubmitOpenAccountResultDTO.builder().openSuccess(true).build();
    }

    /**
     * 创建平安子账户
     */
    private SubmitOpenAccountResultDTO createPabMember(MerchantInfo merchant,
                                                       MerchantBusinessLicenseInfo merchantBusinessLicense,
                                                       BrandMerchantModule brandMerchantModule,
                                                       BrandMerchantCreationRecordModule recordModule,
                                                       ConfigModule configModule) {
        PabConfigModule pabConfigModule = (PabConfigModule) configModule;
        if (Objects.isNull(pabConfigModule) || StringUtils.isBlank(pabConfigModule.getPartnerId())) {
            recordModule.setResult("配置信息异常");
            recordModule.setStatus(0);
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("配置信息异常").build();
        }
        try {
            CreateMemberRequest createMemberRequest = VfinanceInterfaceService.getCreateMemberRequest(merchantBusinessLicense, merchant, pabConfigModule);
            CreateMemberResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.PAB).call(createMemberRequest, CreateMemberResponse.class, pabConfigModule);
            // 调用失败
            if (Objects.isNull(response) || VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
                recordModule.setResult(response.getErrorMessage());
                recordModule.setStatus(0);
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason(response.getErrorMessage());
                return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason(response.getErrorMessage()).build();
            }
            //调用成功回写会员memberId和子账号
            brandMerchantModule.setMemberId(response.getMemberId());
            brandMerchantModule.setSubAccountNo(response.getMerchantAccountId());
            brandMerchantModule.setAccountOpenedTime(new Date());
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
            brandMerchantModule.setAccountOpenFailureReason("");
            brandMerchantModule.setThreePartyMerchantSn(brandMerchantModule.getMerchantSn());
            recordModule.setStatus(1);
        } catch (Exception e) {
            log.error("调用维金系统异常。", e);
            recordModule.setResult("调用维金系统异常");
            recordModule.setStatus(0);
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            brandMerchantModule.setAccountOpenFailureReason("调用PAB系统异常，调用时间：" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("调用平安系统异常，请咨询客服").build();
        }
        return SubmitOpenAccountResultDTO.builder().openSuccess(true).build();
    }

    /**
     * 创建MY_BANK（网商银行）子账户
     */
    private SubmitOpenAccountResultDTO createMyBankMember(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, ConfigModule configModule, BrandMerchantModule brandMerchantModule) {
        if (Objects.isNull(configModule)) {
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("未查到配置信息").build();
        }
        MyBankConfigModule myBankConfigModule = (MyBankConfigModule) configModule;
        if (StringUtils.isBlank(myBankConfigModule.getAppId()) || StringUtils.isBlank(myBankConfigModule.getIsvOrgId()) || StringUtils.isBlank(myBankConfigModule.getIsvPrivateKey()) || StringUtils.isBlank(myBankConfigModule.getIsvPublicKey())) {
            recordModule.setResult("配置信息异常");
            recordModule.setStatus(0);
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("配置信息异常").build();
        }
        recordModule.setRecordId(UUID.randomUUID().toString().replace("-", ""));
        brandMerchantModule.setThreePartyMerchantSn(idGeneratorSnowflake.nextSFTSn());
        MerchantAppletPreRegisterRequest request = MerchantAppletPreRegisterRequest.buildRequest(myBankConfigModule, merchant, merchantBusinessLicense, recordModule, brandMerchantModule, apolloConfig.getMybankHandlerSwitch());
        try {
            MerchantAppletPreRegisterResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.MY_BANK).call(request, MerchantAppletPreRegisterResponse.class, myBankConfigModule);
            if (response == null) {
                recordModule.setStatus(0);
                recordModule.setResult("调用网商银行接口未响应");
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("调用网商银行接口未响应,请稍后重试").build();
            }
            if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                recordModule.setStatus(0);
                recordModule.setResult("调用网商银行接口失败," + response.getResultMsg());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason(response.getResultMsg());
                return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("调用网商银行接口失败,请咨询客服").build();
            }
        } catch (Exception e) {
            recordModule.setStatus(0);
            recordModule.setResult("调用网商银行接口异常," + e.getMessage());
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            brandMerchantModule.setAccountOpenFailureReason("调用网商银行接口异常。");
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("调用网商银行接口异常,请咨询客服").build();
        }
        recordModule.setStatus(3);
        recordModule.setResult("预入驻请求成功，等待网商回调！");
        brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.UNDER_REVIEW.getStatus());
        brandMerchantModule.setAccountOpenFailureReason("");
        return SubmitOpenAccountResultDTO.builder().openSuccess(true).build();
    }

    private SubmitOpenAccountResultDTO createCiticBankMember(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, ConfigModule configModule, BrandMerchantModule brandMerchantModule) {
        if (Objects.isNull(configModule)) {
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("未查到配置信息").build();
        }
        CiticBankConfigModule citicBankConfigModule = (CiticBankConfigModule) configModule;
        if (StringUtils.isBlank(citicBankConfigModule.getMerchantId()) || StringUtils.isBlank(citicBankConfigModule.getPrivateKey()) || StringUtils.isBlank(citicBankConfigModule.getPrivateKeyPassword()) || StringUtils.isBlank(citicBankConfigModule.getPublicKey())) {
            recordModule.setResult("中信配置信息异常");
            recordModule.setStatus(0);
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("中信配置信息异常").build();
        }
        brandMerchantModule.setThreePartyMerchantSn(idGeneratorSnowflake.nextSFTSn());
        RegisteredUserRequest request = RegisteredUserRequest.build(citicBankConfigModule, merchant, merchantBusinessLicense, recordModule, apolloConfig.getCiticUserTypeMap().getString(brandMerchantModule.getMerchantType()));
        try {
            RegisteredResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(request, RegisteredResponse.class, citicBankConfigModule);
            if (response == null) {
                recordModule.setStatus(0);
                recordModule.setResult("调用中信接口未响应");
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("调用中信接口未响应").build();
            }
            if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                recordModule.setStatus(0);
                recordModule.setResult("调用中信接口失败," + response.getResultMsg());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason(response.getResultMsg());
                return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason(response.getResultMsg()).build();
            }
            brandMerchantModule.setMemberId(response.getUserId());
            brandMerchantModule.setSubAccountNo(response.getUserId());
        } catch (Exception e) {
            recordModule.setStatus(0);
            recordModule.setResult("调用中信接口异常," + e.getMessage());
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            brandMerchantModule.setAccountOpenFailureReason("调用中信接口异常，调用时间：" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("调用中信接口异常，请咨询客服").build();
        }
        recordModule.setStatus(1);
        recordModule.setResult("请求成功");
        brandMerchantModule.setAccountOpenedTime(new Date());
        brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
        brandMerchantModule.setAccountOpenFailureReason("");
        return SubmitOpenAccountResultDTO.builder().openSuccess(true).build();
    }

    private SubmitOpenAccountResultDTO createFuiouMember(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, ConfigModule configModule, BrandMerchantModule brandMerchantModule) {
        if (Objects.isNull(configModule)) {
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("未查到配置信息").build();
        }
        FuiouConfigModule fuiouConfigModule = (FuiouConfigModule) configModule;
        if (StringUtils.isBlank(fuiouConfigModule.getMerchantNo()) || StringUtils.isBlank(fuiouConfigModule.getPrivateKey()) || StringUtils.isBlank(fuiouConfigModule.getPublicKey())) {
            recordModule.setResult("富友配置信息异常");
            recordModule.setStatus(0);
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("富友配置信息异常").build();
        }
        fuiouConfigModule.setCheckType(fuiouCheckType);
        recordModule.setRecordId(merchant.getSn() + System.currentTimeMillis());
        // 创建富友会员
        if (!brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.getMerchantType()) && !brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
            return this.createFuiouUser(merchant, merchantBusinessLicense, recordModule, brandMerchantModule, fuiouConfigModule);
        }
        //如果是品牌商户，开启品牌商户子账户
        if (brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.getMerchantType())) {
            return this.createFuiouSubAccount(recordModule, brandMerchantModule);
        }
        return SubmitOpenAccountResultDTO.builder().openSuccess(true).build();
    }

    private SubmitOpenAccountResultDTO createFuiouSubAccount(BrandMerchantCreationRecordModule recordModule, BrandMerchantModule brandMerchantModule) {
        recordModule.setStatus(1);
        brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
        brandMerchantModule.setAccountOpenedTime(new Date());
        brandMerchantModule.setAccountOpenFailureReason("");
        return SubmitOpenAccountResultDTO.builder().openSuccess(true).build();
    }

    private SubmitOpenAccountResultDTO createFuiouUser(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, BrandMerchantModule brandMerchantModule, FuiouConfigModule fuiouConfigModule) {
        OpenAccountRequest request = new OpenAccountRequest();
        try {
            brandMerchantModule.setThreePartyMerchantSn(idGeneratorSnowflake.nextSFTSn());
            request.setBody(OpenAccountRequest.buildBody(merchant, merchantBusinessLicense, brandMerchantModule, fuiouConfigModule, recordModule));
            OpenAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, OpenAccountResponse.class, fuiouConfigModule);
            if (response == null) {
                recordModule.setStatus(0);
                recordModule.setResult("调用富友接口未响应");
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("调用富友接口未响应").build();
            }
            if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                recordModule.setStatus(0);
                recordModule.setResult("调用富友接口失败," + response.getResultMsg());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason(response.getResultMsg());
                return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason(response.getResultMsg()).build();
            }
            brandMerchantModule.setMemberId(response.getBody().getAccountIn());
            brandMerchantModule.setSubAccountNo(response.getBody().getAccountIn());
        } catch (Exception e) {
            recordModule.setStatus(0);
            recordModule.setResult("调用富友接口异常," + e.getMessage());
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            brandMerchantModule.setAccountOpenFailureReason(e.getMessage());
            return SubmitOpenAccountResultDTO.builder().openSuccess(false).openFailReason("富友接口异常，请咨询客服").build();
        }
        recordModule.setStatus(3);
        recordModule.setResult("预入驻请求成功，等待客户激活后，富友回调！");
        brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus());
        brandMerchantModule.setAccountOpenFailureReason("");
        redisHelper.saveAllToSet(RedisKeyEnum.FUIOU_OPEN_ACCOUNT_RECORDS.getKey(), Lists.newArrayList(
                        FuiouToBeActiveRecordModule.builder()
                                .brandId(brandMerchantModule.getBrandId())
                                .merchantId(brandMerchantModule.getMerchantId())
                                .tradeNo(recordModule.getRecordId())
                                .build()
                )
        );
        return SubmitOpenAccountResultDTO.builder().openSuccess(true).build();
    }

    public List<BrandSimpleInfoDTO> getBrandInfoListByMerchantId(String merchantId, Boolean needGetConfig) {
        List<MerchantBrandDetailModule> brandModuleByMerchantId = brandDomainService.getBrandModuleByMerchantId(merchantId);
        if (CollectionUtils.isEmpty(brandModuleByMerchantId)) {
            return Lists.newArrayList();
        }
        List<BrandSimpleInfoDTO> brandSimpleInfos = JSON.parseArray(JSON.toJSONString(brandModuleByMerchantId), BrandSimpleInfoDTO.class);
        this.writeConfigIntoBrandSimpleInfos(brandSimpleInfos, needGetConfig);
        return brandSimpleInfos;
    }

    public List<BrandSimpleInfoDTO> getBrandInfoListByMerchantIds(List<String> merchantIds, Boolean needGetConfig) {
        List<MerchantBrandDetailModule> brandModuleByMerchantId = brandDomainService.getBrandModuleByMerchantIds(merchantIds);
        if (CollectionUtils.isEmpty(brandModuleByMerchantId)) {
            return Lists.newArrayList();
        }
        List<BrandSimpleInfoDTO> brandSimpleInfos = JSON.parseArray(JSON.toJSONString(brandModuleByMerchantId), BrandSimpleInfoDTO.class);
        this.writeConfigIntoBrandSimpleInfos(brandSimpleInfos, needGetConfig);
        return brandSimpleInfos;
    }

    public BrandDetailInfoDTO getBrandDetailInfoByBrandSn(String brandSn, Boolean needGetConfig) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandSn(brandSn);
        if (Objects.isNull(brandModule)) {
            return null;
        }
        return buildBrandDetailInfo(brandModule, needGetConfig);
    }


    public BrandDetailInfoDTO getBrandDetailInfoByAdminMerchantSn(String merchantSn, Boolean needGetConfig) {
        BrandModule brandModule = brandDomainService.getBrandModuleByMerchantSn(merchantSn);
        if (Objects.isNull(brandModule)) {
            return null;
        }
        return buildBrandDetailInfo(brandModule, needGetConfig);
    }

    public BrandDetailInfoDTO getBrandDetailInfoByBrandId(String brandId, Boolean needGetConfig) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            return null;
        }
        return buildBrandDetailInfo(brandModule, needGetConfig);
    }

    private BrandDetailInfoDTO buildBrandDetailInfo(BrandModule brandModule, Boolean needGetConfig) {
        brandModule.setDockingModeDesc(DockingModeEnum.getDescByCode(brandModule.getDockingMode()));
        BrandDetailInfoDTO brandDetailInfo = JSON.parseObject(JSON.toJSONString(brandModule), BrandDetailInfoDTO.class);
        brandDetailInfo.setIndustryArray(this.getIndustryArray(brandDetailInfo.getIndustry()));
        brandDetailInfo.setDistrictCodeArray(this.getDistrictCodeArray(brandDetailInfo.getProvince(), brandDetailInfo.getCity(), brandDetailInfo.getDistrict()));
        BrandConfigModule brandConfig = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        if (Objects.isNull(brandModule.getFundManagementCompanyCode())) {
            return brandDetailInfo;
        }
        switch (brandModule.getFundManagementCompanyCode()) {
            case PAB:
                brandDetailInfo.setConfig(PabConfigModule.convert(JSON.parseObject(brandConfig.getConfig(), PabConfigModule.class), brandModule.getBrandId(), needGetConfig));
                brandDetailInfo.setAccounts(PabAccountModule.convertDto(brandAccountDomainService.getAccountsByBrandId(brandModule.getBrandId(), PabAccountModule.class)));
                break;
            case MY_BANK:
                brandDetailInfo.setConfig(MyBankConfigModule.convert(JSON.parseObject(brandConfig.getConfig(), MyBankConfigModule.class), brandModule.getBrandId(), needGetConfig));
                brandDetailInfo.setAccounts(MyBankAccountModule.convertDto(brandAccountDomainService.getAccountsByBrandId(brandModule.getBrandId(), MyBankAccountModule.class)));
                break;
            case CITIC:
                brandDetailInfo.setConfig(CiticBankConfigModule.convert(JSON.parseObject(brandConfig.getConfig(), CiticBankConfigModule.class), brandModule.getBrandId(), needGetConfig));
                brandDetailInfo.setAccounts(CiticAccountModule.convertDto(brandAccountDomainService.getAccountsByBrandId(brandModule.getBrandId(), CiticAccountModule.class)));
                break;
            case FUIOU:
                brandDetailInfo.setConfig(FuiouConfigModule.convert(JSON.parseObject(brandConfig.getConfig(), FuiouConfigModule.class), brandModule.getBrandId(), needGetConfig));
                break;
            default:
                break;
        }
        return brandDetailInfo;
    }

    private List<String> getIndustryArray(String industryId) {
        if (StringUtils.isNotEmpty(industryId)) {
            try {
                Map<String, Object> industry = industryService.getIndustry(industryId);
                IndustryModule industryModule = JSON.parseObject(JSON.toJSONString(industry), IndustryModule.class);
                return Lists.newArrayList(industryModule.getLevel1(), industryModule.getLevel2());
            } catch (Exception e) {
                log.warn("获取行业接口失败。");
            }
        }
        return Lists.newArrayList();
    }

    private String getIndustryName(String industryId) {
        if (StringUtils.isNotEmpty(industryId)) {
            try {
                Map<String, Object> industry = industryService.getIndustry(industryId);
                IndustryModule industryModule = JSON.parseObject(JSON.toJSONString(industry), IndustryModule.class);
                return industryModule.getLevel1() + "/" + industryModule.getLevel2();
            } catch (Exception e) {
                log.warn("获取行业接口失败。");
            }
        }
        return null;
    }

    private List<String> getDistrictCodeArray(String provinceName, String cityName, String districtName) {
        StringBuilder fullName = new StringBuilder();
        if (StringUtils.isEmpty(provinceName)) {
            return Lists.newArrayList();
        }
        fullName.append(provinceName);
        if (StringUtils.isNotEmpty(cityName)) {
            fullName.append(" ");
            fullName.append(cityName);
        }
        if (StringUtils.isNotEmpty(districtName)) {
            fullName.append(" ");
            fullName.append(districtName);
        }
        try {
            District district = districtsService.getCodeByName(fullName.toString());
            if (StringUtils.isNotEmpty(cityName) && StringUtils.isEmpty(districtName) && Objects.nonNull(district)) {
                return Lists.newArrayList(district.getProvince_code(), district.getCity_code());
            }
            if (StringUtils.isNotBlank(cityName) && StringUtils.isNotBlank(districtName) && Objects.nonNull(district)) {
                return Lists.newArrayList(district.getProvince_code(), district.getCity_code(), district.getCode());
            }
        } catch (Exception e) {
            log.warn("调用获取省市区信息接口失败。", e);
        }
        return Lists.newArrayList();
    }

    public PageBrandInfoDTO pageBrandInfoListByBrandCondition(PageQueryBrandsDTO pageQueryBrandsDto) {
        BrandConditionModule brandConditionModule = JSON.parseObject(JSON.toJSONString(pageQueryBrandsDto), BrandConditionModule.class);
        MerchantConditionModule merchantConditionModule = JSON.parseObject(JSON.toJSONString(pageQueryBrandsDto), MerchantConditionModule.class);
        if (StringUtils.isNotBlank(brandConditionModule.getIdentifier())) {
            //查到ucUserId后再查询具体列表
            String ucUserId = ucUserServiceV2.getUcUserIdByIdentifier(UcUserV2Helper.identifierReq(brandConditionModule.getIdentifier()));
            merchantConditionModule.setUcUserId(StringUtils.isNotBlank(ucUserId) ? ucUserId : "");
            merchantConditionModule.setMerchantTypes(Lists.newArrayList(MerchantTypeEnum.BRAND_ADMIN.getMerchantType()));
            PageBrandModule pageBrandModule = brandDomainService.pageBrandModuleByMerchantConditions(merchantConditionModule);
            return new PageBrandInfoDTO(pageBrandModule.getTotal(), JSON.parseArray(JSON.toJSONString(pageBrandModule.getBrandList()), BrandListInfoDTO.class));
        }
        if (StringUtils.isNotBlank(brandConditionModule.getBrandName()) || StringUtils.isNotBlank(brandConditionModule.getBrandSn())) {
            PageBrandModule pageBrandModule = brandDomainService.pageBrandModuleByBrandConditions(brandConditionModule);
            return new PageBrandInfoDTO(pageBrandModule.getTotal(), JSON.parseArray(JSON.toJSONString(pageBrandModule.getBrandList()), BrandListInfoDTO.class));
        }
        if (StringUtils.isNotBlank(merchantConditionModule.getMerchantName()) || StringUtils.isNotBlank(merchantConditionModule.getMerchantSn())) {
            PageBrandModule pageBrandModule = brandDomainService.pageBrandModuleByMerchantConditions(merchantConditionModule);
            return new PageBrandInfoDTO(pageBrandModule.getTotal(), JSON.parseArray(JSON.toJSONString(pageBrandModule.getBrandList()), BrandListInfoDTO.class));
        }
        PageBrandModule pageBrandModule = brandDomainService.pageBrandModuleByBrandConditions(brandConditionModule);
        return new PageBrandInfoDTO(pageBrandModule.getTotal(), JSON.parseArray(JSON.toJSONString(pageBrandModule.getBrandList()), BrandListInfoDTO.class));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteBrand(String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            return;
        }
        List<BrandMerchantModule> brandMerchants = brandDomainService.getBrandMerchantByBrandId(brandId);
        List<BrandMerchantModule> brandMerchantModules = brandMerchants.stream().filter(
                brandMerchantModule ->
                        brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.name())
                                || brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.SERVICE_PROVIDER_SQB.name())
        ).collect(Collectors.toList());
        brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        // 会员销户
        brandMerchantModules.forEach(brandMerchantModule -> this.cancelMember(brandModule, brandMerchantModule));
        brandDomainService.deleteBrand(brandId);
        List<String> merchantIdList = brandMerchantModules.stream().map(BrandMerchantModule::getMerchantId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(merchantIdList)) {
            merchantIdList.forEach(merchantId -> merchantService.deleteMerchantByMerchantId(merchantId));
        }
        BrandMerchantModule brandMainMerchant = brandMerchants.stream().filter(r -> MerchantTypeEnum.BRAND_ADMIN.name().equals(r.getMerchantType())).findFirst().orElse(new BrandMerchantModule());
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                defaultEventPublisher.publish(BrandMerchantStatusChangeEvent.builder()
                        .brandId(brandId)
                        .mainMerchantId(brandMainMerchant.getMerchantId())
                        .merchantIds(brandMerchants.stream().map(BrandMerchantModule::getMerchantId).collect(Collectors.toList()))
                        .status(0)
                        .deleteBrand(true)
                        .build());

            }
        });
    }

    private void cancelMember(BrandModule brandModule, BrandMerchantModule brandMerchantModule) {
        if (StringUtils.isEmpty(brandMerchantModule.getMemberId())) {
            return;
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.PAB)) {
            PabConfigModule pabConfigModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), PabConfigModule.class);
            if (Objects.isNull(pabConfigModule)) {
                return;
            }
            CancelMemberRequest cancelMemberRequest = new CancelMemberRequest(pabConfigModule.getPartnerId());
            cancelMemberRequest.setMemberId(brandMerchantModule.getMemberId());
            log.info("调用维金接口cancelMember，入参为：{}", JSON.toJSONString(cancelMemberRequest));
            String s = vfinanceInterfaceService.invokeService(cancelMemberRequest);
            VfinanceBaseResponse response = JSON.parseObject(s, VfinanceBaseResponse.class);
            if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
                log.warn("【商户: + {} + 】删除失败，维金系统注销会员接口调用失败，失败原因：{}.", brandMerchantModule.getMerchantSn(), response.getErrorMessage());
            }
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU) && StringUtils.isNotBlank(brandMerchantModule.getMemberId())) {
            this.handleFuiouCase(brandModule, brandMerchantModule);
        }
    }

    private void handleFuiouCase(BrandModule brandModule, BrandMerchantModule brandMerchantModule) {
        FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), FuiouConfigModule.class);
        if (Objects.isNull(configModule)) {
            return;
        }
        if (brandMerchantModule.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus())) {
            CloseAccountRequest closeAccountRequest = new CloseAccountRequest();
            CloseAccountRequestBody body = new CloseAccountRequestBody(configModule.getMerchantNo());
            body.setAccountIn(brandMerchantModule.getMemberId());
            body.setTraceNo(brandMerchantModule.getMerchantSn() + System.currentTimeMillis());
            closeAccountRequest.setBody(body);
            CloseAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(closeAccountRequest, CloseAccountResponse.class, configModule);
            if (Objects.isNull(response) || !response.isSuccess()) {
                log.warn("【商户: {}】删除失败，会员销户接口调用失败，失败原因：{}.", brandMerchantModule.getMerchantSn(), response.getResultMsg());
                throw new BrandBusinessException(BrandBusinessExceptionEnum.CLOSE_ACCOUNT_FAIL, "商户：" + brandMerchantModule.getMerchantSn() + " 会员销户接口调用失败：" + response.getResultMsg());
            }
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.CLOSED_ACCOUNT.getStatus());
            brandMerchantModule.setExtra(BrandMerchantExtraModule.builder().extra(response.getBody().getImg()).build());
            brandDomainService.updateBrandMerchant(brandMerchantModule);
        } else {
            InvalidAllocateAccountRequest request = new InvalidAllocateAccountRequest();
            InvalidAllocateAccountRequestBody body = new InvalidAllocateAccountRequestBody(configModule.getMerchantNo());
            body.setAccountIn(brandMerchantModule.getMemberId());
            body.setTraceNo(brandMerchantModule.getMerchantSn() + System.currentTimeMillis());
            request.setBody(body);
            InvalidAllocateAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, InvalidAllocateAccountResponse.class, configModule);
            if (Objects.isNull(response) || !response.isSuccess()) {
                log.warn("【商户: {}】删除失败，会员删除接口调用失败，失败原因：{}.", brandMerchantModule.getMerchantSn(), response.getResultMsg());
                throw new BrandBusinessException(BrandBusinessExceptionEnum.CLOSE_ACCOUNT_FAIL, "商户：" + brandMerchantModule.getMerchantSn() + " 会员删除接口调用失败：" + response.getResultMsg());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteBrandMerchant(String brandId, List<String> merchantIds, Boolean needDeleteMerchant) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        List<BrandMerchantModule> brandMerchantModules = brandDomainService.getBrandMerchantByBrandIdAndMerchantIds(brandId, merchantIds);
        if (CollectionUtils.isEmpty(brandMerchantModules)) {
            return 0;
        }
        List<BrandMerchantModule> brandMerchantList = brandMerchantModules.stream().filter(
                brandMerchantDo ->
                        brandMerchantDo.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.name())
                                || brandMerchantDo.getMerchantType().equals(MerchantTypeEnum.SERVICE_PROVIDER_SQB.name())
        ).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(brandMerchantList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_CANT_DELETE);
        }
        bankCardDomainService.deleteBankCardByMerchantIds(brandId, merchantIds);
        brandMerchantModules.forEach(brandMerchantModule -> {
            if (brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.name())
                    || brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.SERVICE_PROVIDER_SQB.name())) {
                return;
            }
            // 会员销户
            this.cancelMember(brandModule, brandMerchantModule);
            tagIngestService.ingest(Lists.newArrayList(brandMerchantModule.getMerchantId()), apolloConfig.getStoreTagId(), false);
            if (Objects.nonNull(needDeleteMerchant) && Boolean.TRUE.equals(needDeleteMerchant)) {
                merchantService.deleteMerchantByMerchantId(brandMerchantModule.getMerchantId());
                merchantBusinessLicenseService.delMcMerchantBusinessLicense(brandMerchantModule.getMerchantId());
                QueryMerchantUserReq req = new QueryMerchantUserReq();
                req.setMerchant_id(brandMerchantModule.getMerchantId());
                List<UcMerchantUserSimpleInfo> merchantUserSimpleInfo = merchantUserServiceV2.getMerchantUserSimpleInfo(req);
                if (CollectionUtils.isNotEmpty(merchantUserSimpleInfo)) {
                    merchantUserSimpleInfo.forEach(merchantUser -> merchantUserServiceV2.deleteMerchantUser(merchantUser.getMerchant_user_id()));
                }
            }
        });
        return brandDomainService.deleteBrandMerchant(brandId, merchantIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateBrandMerchantResponseDTO createBrandMerchantForBusinessOpen(CreateBrandMerchantRequestDTO createBrandMerchant) {
        MerchantInfo merchant = merchantService.getMerchantBySn(createBrandMerchant.getMerchantSn(), null);
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(createBrandMerchant.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        ParamsCheckHelper.checkFundManagementCompanyMerchantParams(brandModule.getFundManagementCompanyCode(), createBrandMerchant);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        BrandMerchantModule merchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(createBrandMerchant.getBrandId(), merchant.getId());
        if (Objects.nonNull(merchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_BIND_IN_BRAND);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
        if (Objects.isNull(merchantBusinessLicense)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_INFO_NOT_INTEGRITY);
        }
        UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
        if (Objects.isNull(superAdmin)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_NOT_HAVE_SUPER_ADMIN);
        }
        if (StringUtils.isNotBlank(createBrandMerchant.getSqbStoreSn())) {
            StoreInfo storeBySn = storeService.getStoreBySn(createBrandMerchant.getSqbStoreSn(), null);
            createBrandMerchant.setAssociatedSqbStoreId(Objects.nonNull(storeBySn) && StringUtils.isNotBlank(storeBySn.getId()) ? String.valueOf(storeBySn.getId()) : null);
        }
        BrandMerchantModule brandMerchantModule = JSON.parseObject(JSON.toJSONString(createBrandMerchant), BrandMerchantModule.class);
        if (Objects.isNull(brandMerchantModule)) {
            brandMerchantModule = new BrandMerchantModule();
        }
        brandMerchantModule.setMerchantId(merchant.getId());
        brandMerchantModule.setMerchantName(merchant.getName());
        brandMerchantModule.setParentBrandId(brandModule.getParentId());
        brandMerchantModule.setMerchantDockingMode(createBrandMerchant.getMerchantDockingMode().getCode());
        this.getBrandMerchantType(merchantBusinessLicense, brandMerchantModule);
        if (StringUtils.isEmpty(createBrandMerchant.getAssociatedSqbStoreId()) && Objects.nonNull(createBrandMerchant.getNeedCreateBrandStore()) && Boolean.TRUE.equals(createBrandMerchant.getNeedCreateBrandStore())) {
            brandMerchantModule.setAssociatedSqbStoreId(this.createBrandAdminMerchantStore(brandModule.getBrandId(), merchant, merchantBusinessLicense));
        }
        if (StringUtils.isNotEmpty(createBrandMerchant.getAssociatedSqbStoreId())) {
            StoreInfo storeById = storeService.getStoreById(createBrandMerchant.getAssociatedSqbStoreId(), null);
            if (Objects.nonNull(storeById)) {
                brandMerchantModule.setSqbStoreSn(storeById.getSn());
            }
        }
        if (MerchantDockingModeEnum.COLLECTION.equals(createBrandMerchant.getMerchantDockingMode())) {
            BrandMerchantExtraModule brandMerchantExtraModule = BrandMerchantExtraModule.builder()
                    .dockingModeExtraModule(BrandMerchantDockingModeExtraModule.builder()
                            .aggregationModel(createBrandMerchant.getAggregationModel().getCode())
                            .concentrateScale(createBrandMerchant.getConcentrateScale())
                            .useOfFunds(createBrandMerchant.getUseOfFunds().getCode())
                            .build())
                    .build();
            brandMerchantModule.setExtra(brandMerchantExtraModule);
            brandMerchantModule.setMemberId(createBrandMerchant.getConcentrateMerchantNo());
        }
        brandDomainService.createBrandMerchant(brandMerchantModule);
        // 富友的需要处理银行卡
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU)) {
            this.dealWithBankCard(createBrandMerchant, merchant, brandModule, brandMerchantModule);
        }
        // 记录创建记录
        BrandMerchantCreationRecordModule recordModule = brandDomainService.createBrandMerchantCreationRecord(brandModule.getBrandId(), brandMerchantModule.getMerchantId(), brandMerchantModule.getMerchantSn());
        BrandConfigModule brandConfig = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        // 获取配置
        ConfigModule configModule = ConfigModuleConverter.convert(brandModule.getFundManagementCompanyCode(), brandConfig.getConfig());
        String tagId;
        if (MerchantDockingModeEnum.SEPARATE_ACCOUNT.equals(createBrandMerchant.getMerchantDockingMode())) {
            invokePartnerService(merchant, brandModule, merchantBusinessLicense, brandMerchantModule, configModule, recordModule);
            tagId = apolloConfig.getStoreTagId();
        } else {
            // 归集
            this.invokeServiceHandleCollection(brandModule, brandMerchantModule, configModule, recordModule);
            tagId = apolloConfig.getCollectionModeMerchantTagId();
        }
        try {
            tagIngestService.ingest(Lists.newArrayList(merchant.getId()), tagId, true);
            defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchantModule.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandModule, brandMerchantModule)).build());
        } catch (Exception e) {
            log.error("com.wosai.cua.brand.business.service.business.BrandBusiness.createBrandMerchant ingest crow error merchantId = {}", merchant.getId(), e);
        }
        return JSON.parseObject(JSON.toJSONString(brandMerchantModule), CreateBrandMerchantResponseDTO.class);
    }

    public CreateBrandMerchantResponseDTO createBrandMerchant(CreateBrandMerchantRequestDTO createBrandMerchant) {
        if (StringUtils.isNotBlank(createBrandMerchant.getSqbStoreSn())) {
            StoreInfo storeBySn = storeService.getStoreBySn(createBrandMerchant.getSqbStoreSn(), null);
            createBrandMerchant.setAssociatedSqbStoreId(Objects.nonNull(storeBySn) && StringUtils.isNotBlank(storeBySn.getId()) ? storeBySn.getId() : null);
        }
        if (!apolloConfig.getStoreSnCheckWhiteList().contains(createBrandMerchant.getBrandId()) && StringUtils.isBlank(createBrandMerchant.getAssociatedSqbStoreId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.ASSOCIATED_SQB_STORE_ID_NOT_BE_NULL);
        }
        return createBrandMerchantForBusinessOpen(createBrandMerchant);
    }

    private void dealWithBankCard(CreateBrandMerchantRequestDTO createBrandMerchant, MerchantInfo merchant, BrandModule brandModule, BrandMerchantModule brandMerchantModule) {
        BizBankAccountAddReq bizBankAccountAddReq = this.getBizBankAccountAddReq(createBrandMerchant, merchant);
        MerchantBizBankAccount merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(bizBankAccountAddReq);
        BankCardModule bankCardModule = new BankCardModule();
        bankCardModule.setBankCardId(merchantBizBankAccount.getId());
        bankCardModule.setBrandId(brandModule.getBrandId());
        bankCardModule.setMerchantId(merchant.getId());
        bankCardModule.setAccountType(createBrandMerchant.getBankCard().getType());
        bankCardModule.setReservedMobileNumber(createBrandMerchant.getBankCard().getReservedMobileNumber());
        bankCardModule.setStatus(0);
        bankCardModule.setIsDefault(true);
        bankCardDomainService.createBankCard(bankCardModule);
        brandMerchantModule.setBankCardInfo(new BrandMerchantModule.BankCardInfo());
        brandMerchantModule.getBankCardInfo().setBankCardNo(createBrandMerchant.getBankCard().getBankCardNo());
        brandMerchantModule.getBankCardInfo().setMobile(createBrandMerchant.getBankCard().getReservedMobileNumber());
        brandMerchantModule.getBankCardInfo().setOpeningBankNumber(createBrandMerchant.getBankCard().getOpeningNumber());
        brandMerchantModule.getBankCardInfo().setHolder(createBrandMerchant.getBankCard().getHolder());
    }

    private BizBankAccountAddReq getBizBankAccountAddReq(CreateBrandMerchantRequestDTO createBrandMerchant, MerchantInfo merchant) {
        BizBankAccountAddReq bizBankAccountAddReq = new BizBankAccountAddReq();
        bizBankAccountAddReq.setHolder(createBrandMerchant.getBankCard().getHolder());
        bizBankAccountAddReq.setNumber(createBrandMerchant.getBankCard().getBankCardNo());
        bizBankAccountAddReq.setCity(createBrandMerchant.getBankCard().getCity());
        bizBankAccountAddReq.setType(createBrandMerchant.getBankCard().getType());
        bizBankAccountAddReq.setOpening_number(createBrandMerchant.getBankCard().getOpeningNumber());
        bizBankAccountAddReq.setMerchant_id(merchant.getId());
        bizBankAccountAddReq.setBiz(biz);
        bizBankAccountAddReq.setSet_default(true);
        return bizBankAccountAddReq;
    }

    private void getBrandMerchantType(MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantModule brandMerchantModule) {
        if (Objects.isNull(merchantBusinessLicense) || Objects.isNull(merchantBusinessLicense.getType())) {
            return;
        }
        switch (merchantBusinessLicense.getType()) {
            case 0:
                brandMerchantModule.setType(BrandMerchantTypeEnum.PERSONAL.getType());
                break;
            case 1:
                brandMerchantModule.setType(BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType());
                break;
            case 2:
                brandMerchantModule.setType(BrandMerchantTypeEnum.COMPANY.getType());
                break;
            default:
                brandMerchantModule.setType("");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchCreateBrandMerchants(BatchCreateBrandMerchantRequestDTO batchCreateBrandMerchantRequest) {
        List<MerchantInfo> merchants = Lists.newArrayList();
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(batchCreateBrandMerchantRequest.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandConfigModule brandConfig = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        // 获取配置
        ConfigModule configModule = ConfigModuleConverter.convert(brandModule.getFundManagementCompanyCode(), brandConfig.getConfig());
        Map<String, MerchantBusinessLicenseInfo> merchantBusinessLicenseInfoMap = Maps.newHashMap();
        Map<String, UcMerchantUserInfo> superAdminMap = Maps.newHashMap();
        batchCreateBrandMerchantRequest.getCreateBrandMerchantRequestList().forEach(createBrandMerchant -> {
            MerchantInfo merchant = merchantService.getMerchantBySn(createBrandMerchant.getMerchantSn(), null);
            if (Objects.isNull(merchant)) {
                this.recordCreateBrandMerchantResult(createBrandMerchant, "商户：%s，未查询到记录。");
                return;
            }
            MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
            if (Objects.isNull(merchantBusinessLicense)) {
                this.recordCreateBrandMerchantResult(createBrandMerchant, "商户：%s，未查询到企业证件信息记录。");
                return;
            }
            UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
            if (Objects.isNull(superAdmin)) {
                this.recordCreateBrandMerchantResult(createBrandMerchant, "商户：%s，未查询到超管账号记录。");
                return;
            }
            merchants.add(merchant);
            merchantBusinessLicenseInfoMap.put(merchant.getId(), merchantBusinessLicense);
            superAdminMap.put(merchant.getId(), superAdmin);
        });
        if (CollectionUtils.isEmpty(merchants)) {
            return 0;
        }
        List<BrandMerchantModule> brandMerchantModules = Lists.newArrayList();
        List<BrandMerchantCreationRecordModule> brandMerchantCreationRecordModules = Lists.newArrayList();
        Map<String, CreateBrandMerchantRequestDTO> brandMerchantRequestMap = batchCreateBrandMerchantRequest.getCreateBrandMerchantRequestList().stream().collect(Collectors.toMap(CreateBrandMerchantRequestDTO::getMerchantSn, Function.identity()));
        Map<String, MerchantInfo> merchantInfoMap = merchants.stream().collect(Collectors.toMap(MerchantInfo::getId, Function.identity()));
        merchants.forEach(merchantInfo -> {
            CreateBrandMerchantRequestDTO createBrandMerchant = brandMerchantRequestMap.get(merchantInfo.getSn());
            if (Objects.isNull(createBrandMerchant)) {
                return;
            }
            BrandMerchantModule brandMerchantModule = JSON.parseObject(JSON.toJSONString(createBrandMerchant), BrandMerchantModule.class);
            brandMerchantModule.setMerchantId(merchantInfo.getId());
            brandMerchantModule.setMerchantName(merchantInfo.getName());
            brandMerchantModule.setParentBrandId(brandModule.getParentId());
            if (StringUtils.isBlank(brandMerchantModule.getMerchantType())) {
                brandMerchantModule.setMerchantType(MerchantTypeEnum.FRANCHISEE.getMerchantType());
            }
            this.getBrandMerchantType(merchantBusinessLicenseInfoMap.get(merchantInfo.getId()), brandMerchantModule);
            brandMerchantModules.add(brandMerchantModule);
            BrandMerchantCreationRecordModule recordModule = new BrandMerchantCreationRecordModule();
            recordModule.setMerchantSn(merchantInfo.getSn());
            recordModule.setMerchantId(merchantInfo.getId());
            recordModule.setBrandId(brandModule.getBrandId());
            recordModule.setNeedRetry(1);
            recordModule.setStatus(0);
            brandMerchantCreationRecordModules.add(recordModule);
        });
        int num = brandDomainService.bathCreateBrandMerchant(brandMerchantModules);
        brandDomainService.batchCreateBrandMerchantCreationRecord(brandMerchantCreationRecordModules);
        brandMerchantModules.forEach(brandMerchantModule -> {
            BrandMerchantCreationRecordModule recordModule = brandDomainService.createBrandMerchantCreationRecord(brandModule.getBrandId(), brandMerchantModule.getMerchantId(), brandMerchantModule.getMerchantSn());
            invokePartnerService(
                    merchantInfoMap.get(brandMerchantModule.getMerchantId()),
                    brandModule,
                    merchantBusinessLicenseInfoMap.get(brandMerchantModule.getMerchantId()),
                    brandMerchantModule,
                    configModule,
                    recordModule
            );
            defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchantModule.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandModule, brandMerchantModule)).build());
        });
        try {
            List<String> merchantIds = brandMerchantModules.stream().map(BrandMerchantModule::getMerchantId).collect(Collectors.toList());
            merchantIds.forEach(merchantId -> {
                tagIngestService.ingest(Lists.newArrayList(merchantId), apolloConfig.getStoreTagId(), true);
            });
        } catch (Exception e) {
            log.error("com.wosai.cua.brand.business.service.business.BrandBusiness.batchCreateBrandMerchants ingest crow error ", e);
        }
        return num;
    }

    private void recordCreateBrandMerchantResult(CreateBrandMerchantRequestDTO createBrandMerchant, String message) {
        BrandMerchantCreationRecordModule recordModule = new BrandMerchantCreationRecordModule();
        recordModule.setResult(String.format(message, createBrandMerchant.getMerchantSn()));
        recordModule.setNeedRetry(0);
        recordModule.setStatus(2);
        recordModule.setBrandId(createBrandMerchant.getBrandId());
        recordModule.setMerchantSn(createBrandMerchant.getMerchantSn());
        brandDomainService.createBrandMerchantCreationRecord(recordModule);
    }

    public PageBrandMerchantsDTO pageQueryBrandMerchants(PageQueryBrandMerchantsDTO pageQueryBrandMerchantsDto) {
        PageBrandMerchantsDTO pageBrandMerchants = pageQuerySimpleBrandMerchants(pageQueryBrandMerchantsDto);
        List<BrandMerchantDTO> records = pageBrandMerchants.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return pageBrandMerchants;
        }
        List<String> merchantIds = records.stream().map(BrandMerchantDTO::getMerchantId).collect(Collectors.toList());
        List<MerchantInfo> merchantInfoList = merchantService.getMerchantListByMerchantIds(merchantIds);
        if (CollectionUtils.isEmpty(merchantInfoList)) {
            return pageBrandMerchants;
        }
        Map<String, MerchantInfo> merchantInfoMap = merchantInfoList.stream().collect(Collectors.toMap(MerchantInfo::getId, Function.identity()));
        records.forEach(brandMerchantDto -> {
            MerchantInfo merchant = merchantInfoMap.get(brandMerchantDto.getMerchantId());
            if (Objects.nonNull(merchant)) {
                brandMerchantDto.setMerchantContactName(merchant.getContact_name());
                brandMerchantDto.setMerchantContactPhone(merchant.getContact_cellphone());
            }
        });
        return pageBrandMerchants;
    }

    public PageBrandMerchantsDTO pageQuerySimpleBrandMerchants(PageQueryBrandMerchantsDTO pageQueryBrandMerchantsDto) {
        if (Objects.isNull(pageQueryBrandMerchantsDto.getPage())) {
            pageQueryBrandMerchantsDto.setPage(apolloConfig.getDefaultPage());
        }
        if (Objects.isNull(pageQueryBrandMerchantsDto.getPageSize())) {
            pageQueryBrandMerchantsDto.setPageSize(apolloConfig.getDefaultPageSize());
        }
        PageBrandMerchantsDTO pageBrandMerchants = new PageBrandMerchantsDTO();
        List<BrandModule> brandModules = brandDomainService.getBrandModuleByBrandIdsOrBrandSnList(pageQueryBrandMerchantsDto.getBrandIds(), pageQueryBrandMerchantsDto.getBrandSnList());
        if (CollectionUtils.isEmpty(brandModules)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        List<String> brandIds = brandModules.stream().map(BrandModule::getBrandId).collect(Collectors.toList());
        MerchantConditionModule merchantConditionModule = JSON.parseObject(JSON.toJSONString(pageQueryBrandMerchantsDto), MerchantConditionModule.class);
        merchantConditionModule.setBrandIds(brandIds);
        PageBrandMerchantModule pageBrandMerchantModule = brandDomainService.pageQueryBrandMerchantModule(merchantConditionModule);
        List<BrandMerchantModule> brandMerchantModuleList = pageBrandMerchantModule.getBrandMerchantModuleList();
        pageBrandMerchants.setTotal(pageBrandMerchantModule.getTotal());
        if (CollectionUtils.isEmpty(brandMerchantModuleList)) {
            pageBrandMerchants.setRecords(Lists.newArrayList());
            return pageBrandMerchants;
        }
        Map<String, List<BrandMerchantModule>> brandIdMerchantModuleListMap = brandMerchantModuleList.stream().collect(Collectors.groupingBy(BrandMerchantModule::getBrandId));
        Map<String, BankCardModule> bankCardModuleMap = Maps.newHashMap();
        brandIdMerchantModuleListMap.forEach((brandId, brandMerchantModules) -> {
            if (CollectionUtils.isEmpty(brandMerchantModules)) {
                return;
            }
            List<BankCardModule> defaultBankCardModules = bankCardDomainService.getDefaultBankCardModules(brandId, brandMerchantModules.stream().map(BrandMerchantModule::getMerchantId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(defaultBankCardModules)) {
                return;
            }
            bankCardModuleMap.putAll(defaultBankCardModules.stream().collect(Collectors.toMap(BankCardModule::getMerchantId, Function.identity(), (existing, replacement) -> replacement)));
        });
        pageBrandMerchants.setRecords(Lists.newArrayList());
        Map<String, BrandModule> brandModuleMap = brandModules.stream().collect(Collectors.toMap(BrandModule::getBrandId, Function.identity()));
        List<BrandConfigModule> brandConfigModules = brandConfigDomainService.getBrandConfigByBrandIds(pageQueryBrandMerchantsDto.getBrandIds());
        Map<String, BrandConfigModule> brandConfigModuleMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(brandConfigModules)) {
            brandConfigModuleMap.putAll(brandConfigModules.stream().collect(Collectors.toMap(BrandConfigModule::getBrandId, Function.identity())));
        }
        brandMerchantModuleList.forEach(brandMerchantModule -> {
            BrandMerchantDTO brandMerchantDto = JSON.parseObject(JSON.toJSONString(brandMerchantModule), BrandMerchantDTO.class);
            brandMerchantDto.setBankCardActivateStatus(BankCardActivateStatusEnum.NOT_BOUND.getActivateStatus());
            BrandModule brandModule = brandModuleMap.get(brandMerchantModule.getBrandId());
            if (Objects.isNull(brandModule)) {
                return;
            }
            brandMerchantDto.setBrandName(brandModule.getName());
            BankCardModule bankCardModule = bankCardModuleMap.get(brandMerchantDto.getMerchantId());
            if (Objects.nonNull(bankCardModule)) {
                brandMerchantDto.setBankCardActivateStatus(bankCardModule.getStatus() == 0 ? BankCardActivateStatusEnum.UNACTIVATED.getActivateStatus() : BankCardActivateStatusEnum.ACTIVATED.getActivateStatus());
            }
            brandMerchantDto.setBankCardActivateStatusDesc(BankCardActivateStatusEnum.getDescByActivateStatus(brandMerchantDto.getBankCardActivateStatus()));
            brandMerchantDto.setTypeDesc(BrandMerchantTypeEnum.getMerchantTypeDescByType(brandMerchantDto.getType()));
            brandMerchantDto.setAccountOpenStatusDesc(BrandMerchantAccountOpenStatusEnum.getStatusDescription(brandMerchantDto.getAccountOpenStatus()));
            if (Objects.nonNull(brandModule.getFundManagementCompanyCode())) {
                brandMerchantDto.setFundManagementCompany(brandModule.getFundManagementCompany());
                brandMerchantDto.setFundManagementCompanyCode(brandModule.getFundManagementCompanyCode().getFundManagementCompanyCode());
            }
            BrandConfigModule brandConfigModule = brandConfigModuleMap.get(brandMerchantModule.getBrandId());
            if (StringUtils.isNotBlank(brandMerchantModule.getAccountOpenStatus())
                    && BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus().equals(brandMerchantModule.getAccountOpenStatus())
                    && brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.MY_BANK)
                    && Objects.nonNull(brandConfigModule)
                    && StringUtils.isNotBlank(brandConfigModule.getConfig())
            ) {
                MyBankConfigModule myBankConfigModule = JSON.parseObject(brandConfigModule.getConfig(), MyBankConfigModule.class);
                String myBankQrCodeUrl = CommonHelper.getMyBankQrCodeUrl(apolloConfig.getMyBankActivateQrcodeUrl(), myBankConfigModule.getAppId(), brandMerchantModule.getMerchantSn(), myBankConfigModule.getIsvOrgId());
                brandMerchantDto.setActivationUrl(myBankQrCodeUrl);
                brandMerchantDto.setActivationShortUrl(commonHelper.getShortUrl(myBankQrCodeUrl));
            }
            if (Objects.nonNull(brandMerchantModule.getExtra())) {
                brandMerchantDto.setExtra(BrandMerchantExtraModule.convertToExtra(brandMerchantModule.getExtra()));
            }
            pageBrandMerchants.getRecords().add(brandMerchantDto);
        });
        return pageBrandMerchants;
    }

    public List<BrandMerchantDTO> getAllBrandMerchantsByBrandId(String brandId) {
        List<BrandMerchantModule> allBrandMerchants = brandDomainService.getAllBrandMerchantsByBrandId(brandId);
        return allBrandMerchants.stream().map(r -> JSON.parseObject(JSON.toJSONString(r), BrandMerchantDTO.class)).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public ModifyBrandResponseDTO modifyBrand(ModifyBrandRequestDTO modifyBrandRequestDto) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(modifyBrandRequestDto.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        // 优化设置属性的过程
        this.setIfNotBlank(modifyBrandRequestDto.getName(), brandModule::setName);
        this.setIfNotBlank(modifyBrandRequestDto.getProvince(), brandModule::setProvince);
        this.setIfNotBlank(modifyBrandRequestDto.getCity(), brandModule::setCity);
        this.setIfNotBlank(modifyBrandRequestDto.getAddress(), brandModule::setAddress);
        this.setIfNotBlank(modifyBrandRequestDto.getIndustry(), brandModule::setIndustry);
        this.setIfNotBlank(modifyBrandRequestDto.getContactName(), brandModule::setContactName);
        this.setIfNotBlank(modifyBrandRequestDto.getAlias(), brandModule::setAlias);
        this.setContactEmail(modifyBrandRequestDto, brandModule);
        this.setIfNotBlank(modifyBrandRequestDto.getContactPhone(), brandModule::setContactPhone);


        BrandConfigModule brandConfigModule = new BrandConfigModule();
        brandConfigModule.setBrandId(brandModule.getBrandId());

        // 处理config
        ConfigDTO config = modifyBrandRequestDto.getConfig();
        if (Objects.nonNull(config)) {
            this.setConfig(brandModule, config, brandConfigModule);
        }

        // 处理accounts
        if (Objects.nonNull(modifyBrandRequestDto.getAccounts())) {
            this.updateAccountsConfig(brandModule.getFundManagementCompanyCode(), modifyBrandRequestDto.getAccounts(), brandModule);
        }

        brandModule.setDistrict(modifyBrandRequestDto.getDistrict());
        if (Objects.nonNull(modifyBrandRequestDto.getSftTag())) {
            brandModule.setSftTag(modifyBrandRequestDto.getSftTag());
        }
        brandDomainService.modifyBrand(brandModule);
        if (Objects.nonNull(modifyBrandRequestDto.getSftTag())) {
            MerchantInfo merchant = merchantService.getMerchantBySn(brandModule.getMerchantSn(), null);
            tagIngestService.ingest(Lists.newArrayList(merchant.getId()), apolloConfig.getBrandTagId(), false);
        }
        return ModifyBrandResponseDTO.builder().brandId(brandModule.getBrandId()).sn(brandModule.getSn()).build();
    }

    private void setConfig(BrandModule brandModule, ConfigDTO config, BrandConfigModule brandConfigModule) {
        if (Objects.isNull(config) || Objects.isNull(brandModule.getFundManagementCompanyCode())) {
            return;
        }
        switch (brandModule.getFundManagementCompanyCode()) {
            case PAB:
                if (Objects.isNull(config.getPabConfig())) {
                    break;
                }
                PabConfigModule pabConfigModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), PabConfigModule.class);
                if (Objects.isNull(pabConfigModule)) {
                    pabConfigModule = new PabConfigModule();
                }
                pabConfigModule.setAllowLogin(!Objects.nonNull(config.getPabConfig().getAllowLogin()) || config.getPabConfig().getAllowLogin());
                if (StringUtils.isNotBlank(config.getPabConfig().getPartnerId())) {
                    pabConfigModule.setPartnerId(config.getPabConfig().getPartnerId());
                    brandConfigModule.setChannelId(config.getPabConfig().getPartnerId());
                }
                brandConfigModule.setChannelType(FundManagementCompanyEnum.PAB.getFundManagementCompanyCode());
                // 保存品牌配置信息
                brandConfigModule.setConfig(JSON.toJSONString(pabConfigModule));
                brandConfigDomainService.updateBrandConfig(brandConfigModule);
                break;
            case MY_BANK:
                if (Objects.isNull(config.getMyBankConfig())) {
                    break;
                }
                MyBankConfigModule myBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), MyBankConfigModule.class);
                if (Objects.isNull(myBankConfigModule)) {
                    myBankConfigModule = new MyBankConfigModule();
                }
                myBankConfigModule.setAllowLogin(!Objects.nonNull(config.getMyBankConfig().getAllowLogin()) || config.getMyBankConfig().getAllowLogin());
                if (Objects.nonNull(config.getMyBankConfig().getOpenTopUpAccount())) {
                    myBankConfigModule.setOpenTopUpAccount(config.getMyBankConfig().getOpenTopUpAccount());
                }
                brandConfigModule.setChannelType(FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
                // 保存品牌配置信息
                brandConfigModule.setConfig(JSON.toJSONString(myBankConfigModule));
                brandConfigDomainService.updateBrandConfig(brandConfigModule);
                break;
            case FUIOU:
                if (Objects.isNull(config.getFuiouConfig())) {
                    break;
                }
                FuiouConfigModule fuiouConfigModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), FuiouConfigModule.class);
                if (Objects.isNull(fuiouConfigModule)) {
                    fuiouConfigModule = new FuiouConfigModule();
                }
                fuiouConfigModule.setAllowLogin(!Objects.nonNull(config.getFuiouConfig().getAllowLogin()) || config.getFuiouConfig().getAllowLogin());
                if (Objects.nonNull(config.getFuiouConfig().getDepositStrategy())) {
                    fuiouConfigModule.setDepositStrategy(config.getFuiouConfig().getDepositStrategy().getDepositStrategy());
                }
                fuiouConfigModule.setChannelId(FundManagementCompanyEnum.FUIOU.getFundManagementCompanyCode());
                // 保存品牌配置信息
                brandConfigModule.setConfig(JSON.toJSONString(fuiouConfigModule));
                brandConfigDomainService.updateBrandConfig(brandConfigModule);
                break;
            default:
        }
    }

    private void setIfNotBlank(String value, Consumer<String> consumer) {
        if (StringUtils.isNotBlank(value)) {
            consumer.accept(value);
        }
    }

    private void setContactEmail(ModifyBrandRequestDTO modifyBrandRequestDto, BrandModule brandModule) {
        if (StringUtils.isNotBlank(modifyBrandRequestDto.getContactEmail())) {
            brandModule.setContactEmail(modifyBrandRequestDto.getContactEmail());
        } else {
            brandModule.setContactEmail(""); // 考虑是否允许为null或有更合理的默认值
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void modifyBrandMerchant(ModifyBrandMerchantDTO modifyBrandMerchantRequest) {
        if (StringUtils.isBlank(modifyBrandMerchantRequest.getBrandId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_ID_NOT_BE_NULL);
        }
        if (StringUtils.isBlank(modifyBrandMerchantRequest.getMerchantId()) && StringUtils.isBlank(modifyBrandMerchantRequest.getMerchantSn())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_ID_NOT_BE_NULL);
        }
        if (StringUtils.isBlank(modifyBrandMerchantRequest.getMerchantId())) {
            MerchantInfo merchant = merchantService.getMerchantBySn(modifyBrandMerchantRequest.getMerchantSn(), null);
            if (Objects.isNull(merchant)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
            }
            modifyBrandMerchantRequest.setMerchantId(merchant.getId());
        }
        brandDomainService.updateBrandMerchant(this.getModifyBrandMerchantModule(modifyBrandMerchantRequest));
    }

    public void updateMerchantOpenStatus(ModifyBrandMerchantOpenStatusDTO modifyBrandMerchantOpenStatusDto) {
        brandDomainService.updateMerchantAccountOpenStatus(modifyBrandMerchantOpenStatusDto.getBrandId(), modifyBrandMerchantOpenStatusDto.getMerchantSn(), modifyBrandMerchantOpenStatusDto.getAccountOpenStatus(), modifyBrandMerchantOpenStatusDto.getOpenFailReason());
    }

    private BrandMerchantModule getModifyBrandMerchantModule(ModifyBrandMerchantDTO modifyBrandMerchantRequest) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(modifyBrandMerchantRequest.getBrandId(), modifyBrandMerchantRequest.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        this.handleSecretField(modifyBrandMerchantRequest, brandMerchantModule);
        if (Objects.nonNull(modifyBrandMerchantRequest.getStrategyId())) {
            brandMerchantModule.setStrategyId(modifyBrandMerchantRequest.getStrategyId());
        }
        if (StringUtils.isNotBlank(modifyBrandMerchantRequest.getMeiTuanStoreSn()) && !StringUtils.equals(brandMerchantModule.getAssociatedMeituanStoreSn(), modifyBrandMerchantRequest.getMeiTuanStoreSn())) {
            MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
            merchantConditionModule.setMeiTuanStoreSn(modifyBrandMerchantRequest.getMeiTuanStoreSn());
            merchantConditionModule.setDeleted(0);
            List<BrandMerchantModule> brandMerchantByConditions = brandDomainService.getBrandMerchantByConditions(merchantConditionModule);
            if (CollectionUtils.isNotEmpty(brandMerchantByConditions)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.MEITUAN_STORE_SN_IS_EXIST);
            }
        }

        if (StringUtils.isNotBlank(modifyBrandMerchantRequest.getElmStoreSn()) && !StringUtils.equals(brandMerchantModule.getAssociatedElmStoreSn(), modifyBrandMerchantRequest.getElmStoreSn())) {
            MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
            merchantConditionModule.setElmStoreSn(modifyBrandMerchantRequest.getElmStoreSn());
            merchantConditionModule.setDeleted(0);
            List<BrandMerchantModule> brandMerchantByConditions = brandDomainService.getBrandMerchantByConditions(merchantConditionModule);
            if (CollectionUtils.isNotEmpty(brandMerchantByConditions)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.ELM_STORE_SN_IS_EXIST);
            }
        }

        if (StringUtils.isNotBlank(modifyBrandMerchantRequest.getDyStoreSn()) && !StringUtils.equals(brandMerchantModule.getDyStoreSn(), modifyBrandMerchantRequest.getDyStoreSn())) {
            MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
            merchantConditionModule.setDyStoreSn(modifyBrandMerchantRequest.getDyStoreSn());
            merchantConditionModule.setDeleted(0);
            List<BrandMerchantModule> brandMerchantByConditions = brandDomainService.getBrandMerchantByConditions(merchantConditionModule);
            if (CollectionUtils.isNotEmpty(brandMerchantByConditions)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.DY_STORE_SN_IS_EXIST);
            }
        }
        if (StringUtils.isNotBlank(modifyBrandMerchantRequest.getOutMerchantNo()) && !StringUtils.equals(brandMerchantModule.getOutMerchantNo(), modifyBrandMerchantRequest.getOutMerchantNo())) {
            MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
            merchantConditionModule.setOutMerchantNo(modifyBrandMerchantRequest.getOutMerchantNo());
            merchantConditionModule.setDeleted(0);
            List<BrandMerchantModule> brandMerchantByConditions = brandDomainService.getBrandMerchantByConditions(merchantConditionModule);
            if (CollectionUtils.isNotEmpty(brandMerchantByConditions)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.OUT_MERCHANT_NO_IS_EXIST);
            }
        }
        brandMerchantModule.setOutMerchantNo(StringUtils.isBlank(modifyBrandMerchantRequest.getOutMerchantNo()) ? "" : modifyBrandMerchantRequest.getOutMerchantNo());
        brandMerchantModule.setAssociatedMeituanStoreSn(StringUtils.isBlank(modifyBrandMerchantRequest.getMeiTuanStoreSn()) ? "" : modifyBrandMerchantRequest.getMeiTuanStoreSn());
        brandMerchantModule.setAssociatedElmStoreSn(StringUtils.isBlank(modifyBrandMerchantRequest.getElmStoreSn()) ? "" : modifyBrandMerchantRequest.getElmStoreSn());
        brandMerchantModule.setDyStoreSn(StringUtils.isBlank(modifyBrandMerchantRequest.getDyStoreSn()) ? "" : modifyBrandMerchantRequest.getDyStoreSn());
        return brandMerchantModule;
    }

    private void handleSecretField(ModifyBrandMerchantDTO modifyBrandMerchantRequest, BrandMerchantModule brandMerchantModule) {
        if (brandMerchantModule.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.HAVE_NOT_OPENED.getStatus()) || brandMerchantModule.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus())) {
            MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(modifyBrandMerchantRequest.getMerchantId(), null);
            UpdateMerchantBusinessLicenseReq updateMerchantBusinessLicenseReq = new UpdateMerchantBusinessLicenseReq();
            updateMerchantBusinessLicenseReq.setMerchantId(modifyBrandMerchantRequest.getMerchantId());
            if (StringUtils.isNotBlank(modifyBrandMerchantRequest.getLegalPersonNumber()) && Objects.nonNull(merchantBusinessLicense)) {
                updateMerchantBusinessLicenseReq.setLegalPersonIdNumber(modifyBrandMerchantRequest.getLegalPersonNumber());
            }
            BankCardModule defaultBankCardModule = bankCardDomainService.getDefaultBankCardModule(modifyBrandMerchantRequest.getBrandId(), modifyBrandMerchantRequest.getMerchantId());
            if (StringUtils.isNotBlank(modifyBrandMerchantRequest.getBankCarNumber()) && Objects.nonNull(defaultBankCardModule)) {
                BizBankAccountRes bankAccountById = merchantBizBankAccountService.getBankAccountById(defaultBankCardModule.getBankCardId());
                BizBankAccountUpdateReq bankAccountUpdateReq = JSON.parseObject(JSON.toJSONString(bankAccountById), BizBankAccountUpdateReq.class);
                bankAccountUpdateReq.setNumber(modifyBrandMerchantRequest.getBankCarNumber());
                merchantBizBankAccountService.updateBizBankAccountWithoutApply(bankAccountUpdateReq);
            }
            if (StringUtils.isNotBlank(modifyBrandMerchantRequest.getReservedMobileNumber()) && Objects.nonNull(defaultBankCardModule)) {
                defaultBankCardModule.setReservedMobileNumber(modifyBrandMerchantRequest.getReservedMobileNumber());
                bankCardDomainService.updateBankCard(defaultBankCardModule);
            }

            if (StringUtils.isNotBlank(modifyBrandMerchantRequest.getUnifyTheSocialCreditCode()) && Objects.nonNull(merchantBusinessLicense) && Objects.nonNull(merchantBusinessLicense.getType()) && merchantBusinessLicense.getType() != 0) {
                updateMerchantBusinessLicenseReq.setNumber(modifyBrandMerchantRequest.getUnifyTheSocialCreditCode());
            }

            merchantBusinessLicenseService.updateMerchantBusinessLicense(updateMerchantBusinessLicenseReq, null);
        }
    }

    public List<BrandMerchantModule> getBrandMerchantByConditions(QueryBrandMerchantInfoDTO queryBrandMerchantInfo) {
        List<BrandMerchantModule> brandMerchantByConditions = brandDomainService.getBrandMerchantByConditions(JSON.parseObject(JSON.toJSONString(queryBrandMerchantInfo), MerchantConditionModule.class));
        if (CollectionUtils.isEmpty(brandMerchantByConditions)) {
            return Lists.newArrayList();
        }
        List<String> brandIds = brandMerchantByConditions.stream().map(BrandMerchantModule::getBrandId).distinct().collect(Collectors.toList());
        List<BrandConfigModule> brandConfigByBrandIds = brandConfigDomainService.getBrandConfigByBrandIds(brandIds);
        Map<String, BrandConfigModule> brandConfigModuleMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(brandConfigByBrandIds)) {
            brandConfigModuleMap.putAll(brandConfigByBrandIds.stream().collect(Collectors.toMap(BrandConfigModule::getBrandId, Function.identity())));
        }
        brandMerchantByConditions.forEach(brandMerchantModule -> {
            BrandConfigModule brandConfigModule = brandConfigModuleMap.get(brandMerchantModule.getBrandId());
            if (StringUtils.isNotBlank(brandMerchantModule.getAccountOpenStatus())
                    && BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus().equals(brandMerchantModule.getAccountOpenStatus())
                    && Objects.nonNull(brandConfigModule)
                    && StringUtils.isNotBlank(brandConfigModule.getConfig())
            ) {
                MyBankConfigModule myBankConfigModule = JSON.parseObject(brandConfigModule.getConfig(), MyBankConfigModule.class);
                String myBankQrCodeUrl = CommonHelper.getMyBankQrCodeUrl(apolloConfig.getMyBankActivateQrcodeUrl(), myBankConfigModule.getAppId(), brandMerchantModule.getMerchantSn(), myBankConfigModule.getIsvOrgId());
                brandMerchantModule.setActivationUrl(
                        Boolean.TRUE.equals(apolloConfig.getUsedShortUrl()) ?
                                commonHelper.getShortUrl(myBankQrCodeUrl) : myBankQrCodeUrl
                );
            }

        });
        return brandMerchantByConditions;
    }

    public void supplementaryBrandMerchantParameter(BrandMerchantModule brandMerchantModule) {
        MerchantInfo merchant = merchantService.getMerchantById(brandMerchantModule.getMerchantId(), null);
        if (Objects.isNull(merchant)) {
            return;
        }
        brandMerchantModule.setMerchantBusinessLicense(merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null));
        brandMerchantModule
                .setMerchantContactName(merchant.getContact_name())
                .setMerchantContactPhone(merchant.getContact_cellphone())
                .setMerchantContactEmail(merchant.getContact_email())
                .setStreetAddress(merchant.getStreet_address())
                .setMerchantTypeDesc(MerchantTypeEnum.getDescByMerchantType(brandMerchantModule.getMerchantType()))
                .setCity(merchant.getCity())
                .setIndustry(merchant.getIndustry())
                .setIndustryName(this.getIndustryName(merchant.getIndustry()))
                .setMerchantBusinessName(merchant.getBusiness_name())
        ;
    }

    public List<BrandModule> getBrandByBrandIds(List<String> brandIds) {
        return brandDomainService.getBrandModuleByBrandIdsOrBrandSnList(brandIds, null);
    }

    public String getBrandIdByMerchantId(String merchantId) {
        List<MerchantBrandDetailModule> brandModules = brandDomainService.getBrandModuleByMerchantId(merchantId);
        if (CollectionUtils.isEmpty(brandModules)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        return brandModules.get(0).getBrandId();
    }

    public BrandMerchantModule getBrandMerchantModuleByBrandIdAndMerchantId(String brandId, String merchantId) {
        return brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
    }

    public Boolean createBrandMerchantChildAccount(String merchantSn, String brandId) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
        if (Objects.isNull(merchantBusinessLicense)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_INFO_NOT_INTEGRITY);
        }
        UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
        if (Objects.isNull(superAdmin)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_NOT_HAVE_SUPER_ADMIN);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchant.getId());
        BrandConfigModule brandConfig = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        // 获取配置
        ConfigModule configModule = ConfigModuleConverter.convert(brandModule.getFundManagementCompanyCode(), brandConfig.getConfig());
        // 记录创建记录，通过job跑创建子账号的逻辑
        BrandMerchantCreationRecordModule recordModule = brandDomainService.createBrandMerchantCreationRecord(brandModule.getBrandId(), brandMerchantModule.getMerchantId(), brandMerchantModule.getMerchantSn());
        invokePartnerService(merchant, brandModule, merchantBusinessLicense, brandMerchantModule, configModule, recordModule);
        return true;
    }

    public BrandMerchantInfoDTO getBrandMerchantInfoByMerchantId(String merchantId) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            return null;
        }
        return BrandMerchantModule.convertToBrandMerchantInfo(brandMerchantModule);
    }

    public BrandMerchantInfoDTO getBrandMerchantInfoByMemberId(String memberId) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMemberId(memberId);
        if (Objects.isNull(brandMerchantModule)) {
            return null;
        }
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandMerchantModule.getBrandId());
        if (Objects.isNull(brandModule)) {
            log.warn("method getBrandMerchantInfoByMemberId brand is null, brandId:{}", brandMerchantModule.getBrandId());
            return null;
        }
        this.getSubAccountInfoAndTopUpAccountInfo(brandMerchantModule, brandModule.getFundManagementCompanyCode());
        return BrandMerchantModule.convertToBrandMerchantInfo(brandMerchantModule);
    }

    public BrandMerchantInfoDTO getBrandMerchantInfoByOutMerchantNo(String brandId, String outMerchantNo) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByOutMerchantNo(brandId, outMerchantNo);
        if (Objects.isNull(brandMerchantModule)) {
            return null;
        }
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandMerchantModule.getBrandId());
        if (Objects.isNull(brandModule)) {
            log.warn("method getBrandMerchantInfoByOutMerchantNo brand is null, brandId:{}", brandMerchantModule.getBrandId());
            return null;
        }
        this.getSubAccountInfoAndTopUpAccountInfo(brandMerchantModule, brandModule.getFundManagementCompanyCode());
        BrandMerchantInfoDTO brandMerchantInfo = BrandMerchantModule.convertToBrandMerchantInfo(brandMerchantModule);
        brandMerchantInfo.setBrandSn(brandModule.getSn());
        brandMerchantInfo.setBrandName(brandModule.getName());
        return brandMerchantInfo;
    }

    public int relevanceBrandWithdrawStrategy(Long strategyId, String brandId, List<String> merchantSnList) {
        return brandDomainService.relevanceBrandWithdrawStrategy(strategyId, brandId, merchantSnList);
    }

    public Boolean registerBehaviorRecord(String brandId, String merchantId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        if (StringUtils.isEmpty(brandMerchantModule.getMemberId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_CREATE_SUB_ACCOUNT);
        }
        return brandDomainService.pabRegister(brandModule, brandMerchantModule);
    }

    public String replaceOwnerMerchantId(String brandId, String merchantId) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
            BrandMerchantModule brandOwnerMerchant = brandDomainService.getBrandOwnerMerchant(brandId);
            if (Objects.nonNull(brandOwnerMerchant)) {
                merchantId = brandOwnerMerchant.getMerchantId();
            }
        }
        return merchantId;
    }

    public CreateMerchantResponseDTO createMerchant(CreateMerchantDTO createMerchantDto) {
        return CREATE_MERCHANT_FUNCTION_MAP.get(createMerchantDto.getBrandMerchantType()).apply(createMerchantDto);
    }

    private CreateMerchantResponseDTO createCompanyMerchant(CreateMerchantDTO createMerchantDto) {
        if (Objects.isNull(createMerchantDto.getCompanyMerchant())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), "companyMerchant不能为空！");
        }
        CreateMerchantResponseDTO createMerchantResponse = new CreateMerchantResponseDTO();
        MerchantCenterDomainService merchantCenterDomainService = MERCHANT_CENTER_DOMAIN_SERVICE_MAP.get(BrandMerchantTypeEnum.COMPANY);
        if (Objects.isNull(merchantCenterDomainService)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_SERVICE);
        }
        String ucUserId = this.createUcUser(createMerchantDto.getCompanyMerchant().getCellPhoneNumber());
        CreateMerchantAndStoreReq createMerchantAndStoreReq = merchantCenterDomainService.getCreateMerchantAndStoreReq(createMerchantDto.getCompanyMerchant(), ucUserId);
        createMerchantAndStoreReq.setNeedSendSms(createMerchantDto.getNeedSendSms());
        CreateMerchantResp merchantAndStore = merchantService.createMerchantAndStore(createMerchantAndStoreReq);
        createMerchantResponse.setMerchantId(merchantAndStore.getMerchantId());
        createMerchantResponse.setMerchantSn(merchantAndStore.getMerchantSn());
        return createMerchantResponse;
    }

    private CreateMerchantResponseDTO createIndividualBusinessMerchant(CreateMerchantDTO createMerchantDto) {
        if (Objects.isNull(createMerchantDto.getIndividualBusinessMerchant())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), "individualBusinessMerchant不能为空！");
        }
        CreateMerchantResponseDTO createMerchantResponse = new CreateMerchantResponseDTO();
        MerchantCenterDomainService merchantCenterDomainService = MERCHANT_CENTER_DOMAIN_SERVICE_MAP.get(BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS);
        if (Objects.isNull(merchantCenterDomainService)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_SERVICE);
        }
        String ucUserId = this.createUcUser(createMerchantDto.getIndividualBusinessMerchant().getCellPhoneNumber());
        CreateMerchantAndStoreReq createMerchantAndStoreReq = merchantCenterDomainService.getCreateMerchantAndStoreReq(createMerchantDto.getIndividualBusinessMerchant(), ucUserId);
        createMerchantAndStoreReq.setNeedSendSms(createMerchantDto.getNeedSendSms());
        CreateMerchantResp merchantAndStore = merchantService.createMerchantAndStore(createMerchantAndStoreReq);
        createMerchantResponse.setMerchantId(merchantAndStore.getMerchantId());
        createMerchantResponse.setMerchantSn(merchantAndStore.getMerchantSn());
        return createMerchantResponse;
    }

    private CreateMerchantResponseDTO createPersonalMerchant(CreateMerchantDTO createMerchantDto) {
        if (Objects.isNull(createMerchantDto.getPersonalMerchant())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR.getCode(), "personalMerchant不能为空！");
        }
        CreateMerchantResponseDTO createMerchantResponse = new CreateMerchantResponseDTO();
        MerchantCenterDomainService merchantCenterDomainService = MERCHANT_CENTER_DOMAIN_SERVICE_MAP.get(BrandMerchantTypeEnum.PERSONAL);
        if (Objects.isNull(merchantCenterDomainService)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_SERVICE);
        }
        String ucUserId = this.createUcUser(createMerchantDto.getPersonalMerchant().getCellPhoneNumber());
        CreateMerchantAndStoreReq createMerchantAndStoreReq = merchantCenterDomainService.getCreateMerchantAndStoreReq(createMerchantDto.getPersonalMerchant(), ucUserId);
        createMerchantAndStoreReq.setNeedSendSms(createMerchantDto.getNeedSendSms());
        CreateMerchantResp merchantAndStore = merchantService.createMerchantAndStore(createMerchantAndStoreReq);
        createMerchantResponse.setMerchantId(merchantAndStore.getMerchantId());
        createMerchantResponse.setMerchantSn(merchantAndStore.getMerchantSn());
        return createMerchantResponse;
    }

    private String createUcUser(String phoneNumber) {
        String ucUserId = ucUserServiceV2.getUcUserIdByIdentifier(UcUserV2Helper.identifierReq(phoneNumber));
        // 如果没有账号的话先创建ucUser
        if (StringUtils.isEmpty(ucUserId)) {
            CreateUcUserReq createUcUserReq = new CreateUcUserReq();
            createUcUserReq.setIdentifier(phoneNumber);
            createUcUserReq.setIdentity_type(1);
            createUcUserReq.setApp("trade");
            createUcUserReq.setPassword("123456");
            createUcUserReq.setStatus(-1);
            UcUserInfoResp ucUser = ucUserServiceV2.createUcUser(UcUserV2Helper.build(createUcUserReq));
            ucUserId = ucUser.getId();
        }
        return ucUserId;
    }

    public void signInMember(String merchantId, String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule) || !brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.PAB)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
        if (Objects.isNull(superAdmin)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_NOT_HAVE_SUPER_ADMIN);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
        if (Objects.isNull(merchantBusinessLicense)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_INFO_NOT_INTEGRITY);
        }
        BrandConfigModule brandConfig = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        // 获取配置
        ConfigModule configModule = ConfigModuleConverter.convert(brandModule.getFundManagementCompanyCode(), brandConfig.getConfig());
        if (Objects.isNull(configModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_CONFIG);
        }
        BrandMerchantCreationRecordModule recordModule = brandDomainService.createBrandMerchantCreationRecord(brandMerchantModule.getBrandId(), merchantId, brandMerchantModule.getMerchantSn());
        // 传了合作方id才调用维金系统
        BrandBusiness self = (BrandBusiness) AopContext.currentProxy();
        self.invokePartnerService(merchant, brandModule, merchantBusinessLicense, brandMerchantModule, configModule, recordModule);
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateMerchantResponseDTO registerMemberInfo(String brandId, String merchantId) {
        CreateMerchantResponseDTO response = new CreateMerchantResponseDTO();
        response.setMerchantId(merchantId);
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        SubmitOpenAccountResultDTO submitOpenAccountResult = invokeSubAccountOpen(brandMerchantModule, brandModule);
        response.setMemberId(brandMerchantModule.getMemberId());
        response.setMerchantSn(brandMerchantModule.getMerchantSn());
        response.setSubmitOpenAccountResult(submitOpenAccountResult);
        defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchantModule.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandModule, brandMerchantModule)).build());
        return response;
    }

    public SubmitOpenAccountResultDTO invokeSubAccountOpen(BrandMerchantModule brandMerchantModule, BrandModule brandModule) {
        MerchantInfo merchant = merchantService.getMerchantBySn(brandMerchantModule.getMerchantSn(), null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
        if (Objects.isNull(merchantBusinessLicense)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_INFO_NOT_INTEGRITY);
        }
        UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
        if (Objects.isNull(superAdmin)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_NOT_HAVE_SUPER_ADMIN);
        }
        BrandMerchantCreationRecordModule recordModule = brandDomainService.getBrandMerchantCreationRecordByBrandIdAndMerchantIdAndSn(brandModule.getBrandId(), brandMerchantModule.getMerchantId(), brandMerchantModule.getMerchantSn());
        if (Objects.isNull(recordModule)) {
            recordModule = brandDomainService.createBrandMerchantCreationRecord(brandModule.getBrandId(), brandMerchantModule.getMerchantId(), brandMerchantModule.getMerchantSn());
        }
        BankCardModule defaultBankCardModule = bankCardDomainService.getDefaultBankCardModule(brandModule.getBrandId(), brandMerchantModule.getMerchantId());
        if (Objects.nonNull(defaultBankCardModule)) {
            BizBankAccountRes bankAccountById = merchantBizBankAccountService.getBankAccountById(defaultBankCardModule.getBankCardId());
            if (Objects.nonNull(bankAccountById)) {
                brandMerchantModule.setBankCardInfo(new BrandMerchantModule.BankCardInfo());
                brandMerchantModule.getBankCardInfo().setBankCardNo(bankAccountById.getNumber());
                brandMerchantModule.getBankCardInfo().setOpeningBankNumber(bankAccountById.getOpeningNumber());
                brandMerchantModule.getBankCardInfo().setMobile(defaultBankCardModule.getReservedMobileNumber());
                brandMerchantModule.getBankCardInfo().setHolder(bankAccountById.getHolder());
            }
        }
        BrandConfigModule brandConfig = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        // 获取配置
        ConfigModule configModule = ConfigModuleConverter.convert(brandModule.getFundManagementCompanyCode(), brandConfig.getConfig());
        if (Objects.isNull(configModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_CONFIG);
        }
        return invokePartnerService(merchant, brandModule, merchantBusinessLicense, brandMerchantModule, configModule, recordModule);
    }

    @Transactional(rollbackFor = Exception.class)
    public void reGetMemberId(String brandId, String merchantId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule) || !brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.PAB)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        BrandConfigModule brandConfig = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        // 获取配置
        ConfigModule configModule = ConfigModuleConverter.convert(brandModule.getFundManagementCompanyCode(), brandConfig.getConfig());
        if (Objects.isNull(configModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_CONFIG);
        }
        PabConfigModule pabConfigModule = (PabConfigModule) configModule;
        QueryMemberUidRequest request = new QueryMemberUidRequest(pabConfigModule.getPartnerId());
        request.setUid(merchant.getSn());
        String s = vfinanceInterfaceService.invokeService(request);
        QueryMemberUidResponse response = JSON.parseObject(s, QueryMemberUidResponse.class);
        if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
            log.error("调用维金接口失败。{}", response.getErrorMessage());
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), response.getErrorMessage());
        }
        QueryAccountInfoRequest queryAccountInfoRequest = new QueryAccountInfoRequest(brandModule.getPartnerId());
        queryAccountInfoRequest.setQueryFlag("2");
        queryAccountInfoRequest.setMemberId(response.getMemberId());
        queryAccountInfoRequest.setPageNum("1");
        String queryAccountInfoResponseStr = vfinanceInterfaceService.invokeService(queryAccountInfoRequest);
        log.info("调用维金接口查询子账号信息返回：{}", queryAccountInfoResponseStr);
        String subAccountNo = "";
        QueryAccountInfoResponse queryAccountInfoResponse = JSON.parseObject(queryAccountInfoResponseStr, QueryAccountInfoResponse.class);
        if (StringUtils.isNotBlank(queryAccountInfoResponse.getResultNum()) && Integer.parseInt(queryAccountInfoResponse.getResultNum()) > 0) {
            QueryAccountInfoResponse.AccountListBean accountListBean = queryAccountInfoResponse.getAccountList().get(0);
            subAccountNo = accountListBean.getAccountCode();
        }
        brandMerchantModule.setSubAccountNo(subAccountNo);
        brandMerchantModule.setMemberId(response.getMemberId());
        brandDomainService.updateBrandMerchant(brandMerchantModule);
        // 注册同步回调
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 事务提交后执行的业务代码
                defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandModule.getBrandId()).merchantSn(merchant.getSn()).enrollChannelEnum(EnrollChannelEnum.PING_AN_WEI_JIN).build());
            }
        });
    }

    private void writeConfigIntoBrandSimpleInfos(List<BrandSimpleInfoDTO> brandSimpleInfos, Boolean needGetConfig) {
        if (CollectionUtils.isEmpty(brandSimpleInfos)) {
            return;
        }
        List<String> brandIds = brandSimpleInfos.stream().map(BrandSimpleInfoDTO::getBrandId).collect(Collectors.toList());
        List<BrandConfigModule> brandConfigModules = brandConfigDomainService.getBrandConfigByBrandIds(brandIds);
        Map<String, BrandConfigModule> brandConfigModuleMap = brandConfigModules.stream().collect(Collectors.toMap(BrandConfigModule::getBrandId, brandConfigModule -> brandConfigModule));
        brandSimpleInfos.forEach(brandSimpleInfo -> {
            BrandConfigModule brandConfigModule = brandConfigModuleMap.get(brandSimpleInfo.getBrandId());
            if (Objects.nonNull(brandConfigModule)) {
                switch (brandSimpleInfo.getFundManagementCompanyCode()) {
                    case PAB:
                        brandSimpleInfo.setConfig(PabConfigModule.convert(brandConfigModule.getConfig(), brandSimpleInfo.getBrandId(), needGetConfig));
                        break;
                    case MY_BANK:
                        brandSimpleInfo.setConfig(MyBankConfigModule.convert(brandConfigModule.getConfig(), brandSimpleInfo.getBrandId(), needGetConfig));
                        break;
                    case CITIC:
                        brandSimpleInfo.setConfig(CiticBankConfigModule.convert(brandConfigModule.getConfig(), brandSimpleInfo.getBrandId(), needGetConfig));
                        break;
                    case FUIOU:
                        brandSimpleInfo.setConfig(FuiouConfigModule.convert(brandConfigModule.getConfig(), brandSimpleInfo.getBrandId(), needGetConfig));
                        break;
                    default:
                        break;
                }
            }
        });

    }

    public BrandConfigDTO getBrandConfig(BrandModule brandModule, Boolean needGetConfig) {
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        if (Objects.isNull(brandConfigModule)) {
            return null;
        }
        switch (brandModule.getFundManagementCompanyCode()) {
            case PAB:
                return PabConfigModule.convert(brandConfigModule.getConfig(), brandModule.getBrandId(), needGetConfig);
            case MY_BANK:
                return MyBankConfigModule.convert(brandConfigModule.getConfig(), brandModule.getBrandId(), needGetConfig);
            case CITIC:
                return CiticBankConfigModule.convert(brandConfigModule.getConfig(), brandModule.getBrandId(), needGetConfig);
            case FUIOU:
                return FuiouConfigModule.convert(brandConfigModule.getConfig(), brandModule.getBrandId(), needGetConfig);
            default:
                return null;
        }
    }

    public BrandConfigDTO getBrandConfigByChannelId(String channelId, FundManagementCompanyEnum channelType) {
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByChannelIdAndChannelType(channelId, channelType.getFundManagementCompanyCode());
        if (Objects.isNull(brandConfigModule)) {
            return null;
        }
        switch (channelType) {
            case PAB:
                return PabConfigModule.convert(brandConfigModule.getConfig(), brandConfigModule.getBrandId(), true);
            case MY_BANK:
                return MyBankConfigModule.convert(brandConfigModule.getConfig(), brandConfigModule.getBrandId(), true);
            case CITIC:
                return CiticBankConfigModule.convert(brandConfigModule.getConfig(), brandConfigModule.getBrandId(), true);
            case FUIOU:
                return FuiouConfigModule.convert(brandConfigModule.getConfig(), brandConfigModule.getBrandId(), true);
            default:
                return null;
        }
    }

    public String createBrandAdminMerchantStore(String brandId, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense) {
        BrandMerchantModule brandAdminMerchant = brandDomainService.getBrandAdminMerchant(brandId);
        if (Objects.nonNull(brandAdminMerchant)) {
            StoreComplete storeComplete = new StoreComplete();
            CreateStoreReq createStoreReq = new CreateStoreReq();
            CreateStoreBusinessLicenseWithStoreReq createStoreBusinessLicenseWithStoreReq = new CreateStoreBusinessLicenseWithStoreReq();
            createStoreReq.setId(UUID.randomUUID().toString());
            createStoreReq.setMerchantId(brandAdminMerchant.getMerchantId());
            createStoreReq.setName(merchant.getName());
            createStoreReq.setContactCellphone(merchant.getContact_cellphone());
            createStoreReq.setContactName(merchant.getContact_name());
            createStoreReq.setContactPhone(merchant.getContact_phone());
            createStoreReq.setContactEmail(merchant.getContact_email());
            createStoreReq.setDistrict(merchant.getDistrict());
            createStoreReq.setProvince(merchant.getProvince());
            createStoreReq.setCity(merchant.getCity());
            createStoreReq.setStreetAddress(merchant.getStreet_address());
            storeComplete.setCreateStoreReq(createStoreReq);
            createStoreBusinessLicenseWithStoreReq.setMerchantId(merchantBusinessLicense.getMerchant_id());
            createStoreBusinessLicenseWithStoreReq.setType(merchantBusinessLicense.getType());
            createStoreBusinessLicenseWithStoreReq.setNumber(merchantBusinessLicense.getNumber());
            createStoreBusinessLicenseWithStoreReq.setLegalPersonName(merchantBusinessLicense.getLegal_person_name());
            createStoreBusinessLicenseWithStoreReq.setLegalPersonIdType(merchantBusinessLicense.getLegal_person_id_type());
            createStoreBusinessLicenseWithStoreReq.setLegalPersonIdNumber(merchantBusinessLicense.getLegal_person_id_number());
            createStoreBusinessLicenseWithStoreReq.setStoreId(createStoreReq.getId());
            storeComplete.setStoreBusinessLicenseReq(createStoreBusinessLicenseWithStoreReq);
            storeService.createStoreComplete(storeComplete);
            return createStoreReq.getId();
        }
        return null;
    }

    @Deprecated
    public void dealPabConfig() {
        brandConfigDomainService.dealPabConfig();
        brandAccountDomainService.dealPabAccount();
    }

    public void getSubAccountInfoAndTopUpAccountInfo(BrandMerchantModule brandMerchantModule, FundManagementCompanyEnum fundManagementCompanyEnum) {
        if (Objects.isNull(brandMerchantModule) || StringUtils.isBlank(brandMerchantModule.getBrandId()) || StringUtils.isBlank(brandMerchantModule.getMerchantId())) {
            return;
        }
        if (Objects.isNull(fundManagementCompanyEnum)) {
            return;
        }
        switch (fundManagementCompanyEnum) {
            case MY_BANK:
                this.getMyBankAccountInfo(brandMerchantModule);
                break;
            case CITIC:
                // 获取子账号信息
                this.getCiticAccountInfo(brandMerchantModule);
                break;
            default:
        }
    }

    private void getCiticAccountInfo(BrandMerchantModule brandMerchantModule) {
        String type = brandMerchantModule.getType();
        if (BrandMerchantTypeEnum.COMPANY.getType().equals(type) || BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType().equals(type)) {
            brandMerchantModule.setSubAccountName(brandMerchantModule.getMerchantName());
        }
        if (BrandMerchantTypeEnum.PERSONAL.getType().equals(type)) {
            MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(brandMerchantModule.getMerchantId());
            if (Objects.nonNull(license)) {
                brandMerchantModule.setSubAccountName(license.getLegal_person_name());
            }
        }
    }

    private void getMyBankAccountInfo(BrandMerchantModule brandMerchantModule) {
        if (StringUtils.isNotBlank(brandMerchantModule.getSubAccountNo())) {
            if (brandMerchantModule.getType().equals(BrandMerchantTypeEnum.PERSONAL.getType())) {
                MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(brandMerchantModule.getMerchantId());
                if (Objects.nonNull(license)) {
                    brandMerchantModule.setSubAccountName("商户_" + license.getLegal_person_name());
                }
            } else {
                brandMerchantModule.setSubAccountName(brandMerchantModule.getMerchantName());
            }
        }
    }

    private void getTopUpAccountName(BrandMerchantModule brandMerchantModule) {
        MyBankConfigModule myBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandMerchantModule.getBrandId(), MyBankConfigModule.class);
        if (Objects.nonNull(myBankConfigModule)) {
            BkCloudFundsAccountOpenRequest request = new BkCloudFundsAccountOpenRequest(myBankConfigModule.getAppId());
            BkcloudfundsAccountOpenRequestModel accountOpenRequestModel = new BkcloudfundsAccountOpenRequestModel();
            accountOpenRequestModel.setIsvOrgId(myBankConfigModule.getIsvOrgId());
            accountOpenRequestModel.setMerchantId(brandMerchantModule.getSubAccountNo());
            accountOpenRequestModel.setAcctType(MyBankAccountTypeEnum.TRADE_DEPOSIT.getCode());
            accountOpenRequestModel.setOutTradeNo(UUID.randomUUID().toString().replace(MybankConstants.CONNECT_SYMBOL_STRING, MybankConstants.NULL_STRING));
            accountOpenRequestModel.setExtInfo("");
            request.setRequestBody(accountOpenRequestModel);
            try {
                BkCloudFundsAccountOpenResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.MY_BANK).call(request, BkCloudFundsAccountOpenResponse.class, myBankConfigModule);
                if (Objects.nonNull(response) && Objects.nonNull(response.getBkcloudfundsAccountOpen()) && Objects.nonNull(response.getBkcloudfundsAccountOpen().getBkcloudfundsAccountOpenResponseModel())) {
                    brandMerchantModule.setTopUpAccountName(response.getBkcloudfundsAccountOpen().getBkcloudfundsAccountOpenResponseModel().getBankCertName());
                    brandMerchantModule.setTopUpAccountNo(response.getBkcloudfundsAccountOpen().getBkcloudfundsAccountOpenResponseModel().getBankCardNo());
                }
            } catch (Exception e) {
                log.error("获取网商银行账户信息失败", e);
            }
        }
    }

    public void compensateTopUpAccountName(String brandId, int pageSize, long sleepTime) {
        long startId = 0;
        List<BrandMerchantModule> brandMerchantModules = null;
        do {
            try {
                LocalTime currentTime = LocalTime.now();
                brandMerchantModules = brandDomainService.pageGetBrandMerchantByBrandIdAndId(brandId, startId, pageSize);
                if (CollectionUtils.isEmpty(brandMerchantModules)) {
                    log.info("当前时间：{}，compensateTopUpAccountName执行中断：startId = {}，pageSize = {}", currentTime.format(DateTimeFormatter.ofPattern("HH:mm:ss")), startId, pageSize);
                    return;
                }
                Thread.sleep(sleepTime);
                brandMerchantModules.forEach(brandMerchantModule -> {
                    this.getTopUpAccountName(brandMerchantModule);
                    ChangeBrandMerchantModule changeBrandMerchantModule = new ChangeBrandMerchantModule();
                    changeBrandMerchantModule.setTopUpAccountName(brandMerchantModule.getTopUpAccountName());
                    changeBrandMerchantModule.setTopUpAccountNo(brandMerchantModule.getTopUpAccountNo());
                    changeBrandMerchantModule.setId(brandMerchantModule.getId());
                    brandDomainService.updateBrandMerchantByFields(changeBrandMerchantModule);
                });
                startId = brandMerchantModules.get(brandMerchantModules.size() - 1).getId();
            } catch (InterruptedException e) {
                log.warn("执行中断!");
                Thread.currentThread().interrupt();
            }
        } while (!CollectionUtils.isEmpty(brandMerchantModules));
    }

    public MerchantBrandTypeEnum queryMerchantBrandType(String merchantId) {
        MerchantInfo merchantInfo = merchantService.getMerchantById(merchantId, null);
        if (Objects.isNull(merchantInfo)) {
            return MerchantBrandTypeEnum.NO_BRAND_MERCHANT;
        }
        List<MerchantBrandDetailModule> merchantBrandDetailModules = brandDomainService.getBrandModuleByMerchantId(merchantId);
        if (WosaiCollectionUtils.isNotEmpty(merchantBrandDetailModules)) {
            if (merchantBrandDetailModules.get(0).getMerchantType().equals(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
                return MerchantBrandTypeEnum.MAIN_BRAND_MERCHANT;
            } else {
                return MerchantBrandTypeEnum.SUB_BRAND_MERCHANT;
            }
        }
        return MerchantBrandTypeEnum.NO_BRAND_MERCHANT;
    }

    public ChangeAcquirerCheckResponseDTO checkChangeAcquirer(ChangeAcquirerCheckRequestDTO changeAcquirerCheckRequestDTO) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(changeAcquirerCheckRequestDTO.getMerchantId());
        if (Objects.nonNull(brandMerchantModule)) {
            return ChangeAcquirerCheckResponseDTO.notAllow("品牌商户不允许切换收单机构");
        }
        return ChangeAcquirerCheckResponseDTO.allow();
    }

    public PaymentModeChangeResponseDTO changePaymentMode(PaymentModeChangeRequestDTO paymentModeChangeRequestDTO) {
        // 校验品牌商户是否存在
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(paymentModeChangeRequestDTO.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            return PaymentModeChangeResponseDTO.fail("品牌子商户不存在");
        }
        // 校验支付模式是否没有变化
        if (Objects.equals(brandMerchantModule.getPaymentMode(), paymentModeChangeRequestDTO.getTargetPaymentMode())) {
            return PaymentModeChangeResponseDTO.fail("支付模式未发生变化");
        }
        // 调用进件接口
        PaymentModeChangeResult changeResult = merchantContractJobClient.changePaymentMode(new PaymentModeChangeRequest().setMerchantId(brandMerchantModule.getMerchantId()).setTargetPaymentMode(paymentModeChangeRequestDTO.getTargetPaymentMode()));
        if (!changeResult.isSuccess()) {
            return PaymentModeChangeResponseDTO.fail(changeResult.getMsg());
        }
        // 更改数据库商户支付模式
        brandMerchantModule.setPaymentMode(paymentModeChangeRequestDTO.getTargetPaymentMode());
        brandDomainService.updateBrandMerchant(brandMerchantModule);
        return PaymentModeChangeResponseDTO.success();
    }

    public List<BrandOpenAppInfoQueryResponseDTO> queryBrandOpenAppInfos(String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (WosaiStringUtils.isEmpty(brandModule.getMerchantSn())) {
            return new ArrayList<>();
        }
        MerchantInfoQueryResult merchantInfoQueryResult = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(brandModule.getMerchantSn()));
        List<AppInfoOpenResult> appInfoOpenResults = merchantBusinessOpenClient.queryAppInfoOpenResultByMerchantId(merchantInfoQueryResult.getMerchantId());
        return appInfoOpenResults.stream().map(appInfoOpenResult ->
                new BrandOpenAppInfoQueryResponseDTO().setAppName(appInfoOpenResult.getAppName())
                        .setStatus(appInfoOpenResult.getStatus().getDesc())).collect(Collectors.toList());
    }

    public MerchantBusinessLicenseInfo queryBrandMerchantBusinessLicense(String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (WosaiStringUtils.isEmpty(brandModule.getMerchantSn())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_FIND);
        }
        return merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(merchantService.getMerchantBySn(brandModule.getMerchantSn(), null).getId());
    }

    public ListResult<BrandMerchantsPagingResponseDTO> pagingBrandMerchants(PagingBrandMerchantsRequestDTO pagingBrandMerchantsRequest) {
        BrandMerchantFilterModel filter = pagingBrandMerchantsRequest.getFilter();
        PagingModel paging = pagingBrandMerchantsRequest.getPaging();
        // 检查是否有过滤条件。如果没有商户名、经营名、联系电话 或者 有商户号 则可以先去查brand_merchant表的数据
        boolean hasFilterConditions = WosaiStringUtils.isNotEmpty(filter.getMerchantName())
                || WosaiStringUtils.isNotEmpty(filter.getMerchantBusinessName())
                || WosaiStringUtils.isNotEmpty(filter.getContactCellphone());
        if (!hasFilterConditions) {
            // 没有过滤条件时，先分页查询brand_module数据，然后再根据查询结果获取商户信息
            return handleNoFilterCase(filter, paging);
        } else {
            return handleHasFilterCase(filter, paging);
        }
    }

    private ListResult<BrandMerchantsPagingResponseDTO> handleNoFilterCase(BrandMerchantFilterModel filter, PagingModel paging) {
        MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
        merchantConditionModule.setBrandIds(Lists.newArrayList(filter.getBrandId()));
        merchantConditionModule.setMerchantSn(filter.getMerchantSn());
        merchantConditionModule.setPage(paging.getPage());
        merchantConditionModule.setPageSize(paging.getPageSize());
        PageBrandMerchantModule pageBrandMerchantModule = brandDomainService.pageQueryBrandMerchantModule(merchantConditionModule);
        if (pageBrandMerchantModule.getTotal() == 0 || CollectionUtils.isEmpty(pageBrandMerchantModule.getBrandMerchantModuleList())) {
            return ListResult.emptyListResult();
        }

        List<String> merchantIds = pageBrandMerchantModule.getBrandMerchantModuleList().stream()
                .map(BrandMerchantModule::getMerchantId)
                .collect(Collectors.toList());
        MerchantFindRequest merchantFindRequest = new MerchantFindRequest();
        if (WosaiStringUtils.isNotEmpty(filter.getMerchantSn())) {
            merchantFindRequest.setMerchantSn(filter.getMerchantSn());
        } else {
            merchantFindRequest.setMerchantIds(merchantIds);
        }
        // 调用外部接口查询
        List<MerchantQueryResult> merchants = coreBusinessClient.findMerchants(merchantFindRequest);
        if (CollectionUtils.isEmpty(merchants)) {
            return ListResult.emptyListResult();
        }

        return buildPagingBrandMerchantsResult(merchants, pageBrandMerchantModule);
    }

    private ListResult<BrandMerchantsPagingResponseDTO> handleHasFilterCase(BrandMerchantFilterModel filter, PagingModel paging) {
        MerchantFindRequest merchantFindRequest = new MerchantFindRequest()
                .setMerchantName(filter.getMerchantName())
                .setMerchantBusinessName(filter.getMerchantBusinessName())
                .setContactCellphone(filter.getContactCellphone());
        if (WosaiStringUtils.isNotEmpty(filter.getMerchantSn())) {
            merchantFindRequest.setMerchantSn(filter.getMerchantSn());
        } else {
            List<BrandMerchantModule> brandMerchantModuleList = brandDomainService.getBrandMerchantByBrandId(filter.getBrandId());
            // 如果没有的话就返回空
            if (WosaiCollectionUtils.isEmpty(brandMerchantModuleList)) {
                return ListResult.emptyListResult();
            }
            merchantFindRequest.setMerchantIds(brandMerchantModuleList.stream().map(BrandMerchantModule::getMerchantId).collect(Collectors.toList()));
        }
        // 有过滤条件时，直接调用外部接口查询
        List<MerchantQueryResult> merchants = coreBusinessClient.findMerchants(merchantFindRequest);
        if (WosaiCollectionUtils.isEmpty(merchants)) {
            return ListResult.emptyListResult();
        }
        // 构建查询条件，查询分页数据
        MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
        merchantConditionModule.setBrandIds(Collections.singletonList(filter.getBrandId()));
        merchantConditionModule.setMerchantSn(filter.getMerchantSn());
        merchantConditionModule.setMerchantIds(merchants.stream().map(MerchantQueryResult::getMerchantId).collect(Collectors.toList()));
        merchantConditionModule.setPage(paging.getPage());
        merchantConditionModule.setPageSize(paging.getPageSize());

        PageBrandMerchantModule pageBrandMerchantModule = brandDomainService.pageQueryBrandMerchantModule(merchantConditionModule);

        // 构建返回结果
        return buildPagingBrandMerchantsResult(merchants, pageBrandMerchantModule);
    }

    private ListResult<BrandMerchantsPagingResponseDTO> buildPagingBrandMerchantsResult(List<MerchantQueryResult> merchants, PageBrandMerchantModule pageBrandMerchantModule) {
        Map<String, MerchantQueryResult> merchantsMap = merchants.stream()
                .collect(Collectors.toMap(MerchantQueryResult::getMerchantId, r -> r, (r1, r2) -> r1));
        return new ListResult<>(pageBrandMerchantModule.getTotal(), pageBrandMerchantModule.getBrandMerchantModuleList().stream().map(r -> {
            MerchantQueryResult merchantInfo = merchantsMap.get(r.getMerchantId());
            if (merchantInfo == null) {
                return null;
            }
            return new BrandMerchantsPagingResponseDTO()
                    .setMerchantId(r.getMerchantId())
                    .setMerchantSn(merchantInfo.getMerchantSn())
                    .setMerchantName(merchantInfo.getName())
                    .setMerchantBusinessName(merchantInfo.getBusinessName())
                    .setContactName(merchantInfo.getContactName())
                    .setContactCellphone(merchantInfo.getContactCellphone())
                    .setPaymentMode(r.getPaymentMode())
                    .setAssociatedTime(r.getAssociatedTime());
        }).filter(Objects::nonNull).collect(Collectors.toList()));
    }

    public BrandPaymentModeResponseDTO queryBrandPaymentModeForContract(String merchantId) {
        String brandIdAndPaymentModeInfo = redisClusterTemplate.opsForValue().get("BRAND_MERCHANT_PAYMENT_MODE:" + merchantId);
        if (WosaiStringUtils.isNotEmpty(brandIdAndPaymentModeInfo)) {
            String[] brandIdAndPaymentMode = brandIdAndPaymentModeInfo.split("_");
            return new BrandPaymentModeResponseDTO().setBrandId(brandIdAndPaymentMode[0]).setPaymentMode(PaymentModeEnum.getPaymentModeEnum(Integer.valueOf(brandIdAndPaymentMode[1])));
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(merchantId);
        if (Objects.nonNull(brandMerchantModule)) {
            return new BrandPaymentModeResponseDTO().setBrandId(brandMerchantModule.getBrandId()).setPaymentMode(PaymentModeEnum.getPaymentModeEnum(brandMerchantModule.getPaymentMode()));
        }
        return null;
    }

    public Boolean cancelEnter(String brandId, String merchantId, String cancelType) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        // 归集
        if (MerchantDockingModeEnum.COLLECTION.getCode().equals(brandMerchantModule.getMerchantDockingMode())) {
            switch (cancelType) {
                case CancelEnterRequestDTO.CANCEL_TYPE_REVOKE:
                    // 撤销
                    revoke(brandMerchantModule);
                    break;
                case CancelEnterRequestDTO.CANCEL_TYPE_CANCEL:
                    // 取消
                    cancel(brandMerchantModule);
                    break;
                default:
            }
        }
        return true;
    }

    private void revoke(BrandMerchantModule brandMerchantModule) {
        FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandMerchantModule.getBrandId(), FuiouConfigModule.class);
        CancelConcentrateRelationApplyRequest request = new CancelConcentrateRelationApplyRequest();
        CancelConcentrateRelationApplyRequestBody body = new CancelConcentrateRelationApplyRequestBody(configModule.getMerchantNo());
        body.setTraceNo(brandMerchantModule.getMerchantSn() + System.currentTimeMillis());
        body.setMchntCdConcentrate(brandMerchantModule.getMemberId());
        body.setBatchNo(brandMerchantModule.getExtra().getDockingModeExtraModule().getBatchNo());
        request.setBody(body);
        CancelConcentrateRelationApplyResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, CancelConcentrateRelationApplyResponse.class, configModule);
        if (Objects.nonNull(response) && response.isSuccess()) {
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.INVALID.getStatus());
            brandDomainService.updateBrandMerchant(brandMerchantModule);
        }
    }

    private void cancel(BrandMerchantModule brandMerchantModule) {
        FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandMerchantModule.getBrandId(), FuiouConfigModule.class);
        CancelConcentrateRelationRequest request = new CancelConcentrateRelationRequest();
        CancelConcentrateRelationRequestBody body = new CancelConcentrateRelationRequestBody(configModule.getMerchantNo());
        body.setTraceNo(brandMerchantModule.getMerchantSn() + System.currentTimeMillis());
        body.setMchntCdConcentrate(brandMerchantModule.getMemberId());
        request.setBody(body);
        CancelConcentrateRelationResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, CancelConcentrateRelationResponse.class, configModule);
        if (Objects.nonNull(response) && response.isSuccess()) {
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.REMOVED.getStatus());
            brandDomainService.updateBrandMerchant(brandMerchantModule);
        }
    }

    public BrandMerchantsQueryResponseDTO queryBrandMerchants(String merchantId) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            return null;
        }
        List<BrandMerchantModule> brandMerchantModules = brandDomainService.getBrandMerchantByBrandId(brandMerchantModule.getBrandId());
        BrandMerchantsQueryResponseDTO merchantsQueryResponseDTO = new BrandMerchantsQueryResponseDTO();
        List<BrandMerchantsQueryResponseDTO> subMerchants = new ArrayList<>();
        for (BrandMerchantModule merchantModule : brandMerchantModules) {
            if (MerchantTypeEnum.BRAND_ADMIN.getMerchantType().equals(merchantModule.getMerchantType())) {
                merchantsQueryResponseDTO.setMerchantSn(merchantModule.getMerchantSn());
                merchantsQueryResponseDTO.setMerchantId(merchantModule.getMerchantId());
                merchantsQueryResponseDTO.setMerchantName(merchantModule.getMerchantName());
            } else {
                BrandMerchantsQueryResponseDTO subMerchant = new BrandMerchantsQueryResponseDTO();
                subMerchant.setMerchantSn(merchantModule.getMerchantSn());
                subMerchant.setMerchantId(merchantModule.getMerchantId());
                subMerchant.setMerchantName(merchantModule.getMerchantName());
                subMerchants.add(subMerchant);
            }
        }
        merchantsQueryResponseDTO.setActiveMerchants(subMerchants);
        return merchantsQueryResponseDTO;
    }

    public CreateBrandResponseDTO openBrandPaymentHubForAudit(BrandPaymentHubOpenRequestDTO brandPaymentHubOpenRequest) {
        MerchantInfo merchant = checkOpenBrandPaymentHubForAudit(brandPaymentHubOpenRequest);
        // 组装参数调用业务开通接口
        BusinessOpenRequest businessOpenRequest = new BusinessOpenRequest();
        businessOpenRequest.setMerchantId(merchant.getId());
        businessOpenRequest.setUserId(brandPaymentHubOpenRequest.getOperatorId());
        businessOpenRequest.setAppId(brandPaymentHubAppId);
        businessOpenRequest.setAppInfo(JSON.parseObject(JSON.toJSONString(brandPaymentHubOpenRequest), Map.class));
        BusinessOpenResult businessOpenResult = merchantBusinessOpenClient.openApp(businessOpenRequest);
        if (businessOpenResult.isSuccess()) {
            BrandMerchantModule brandMerchantInfoByMerchantId = brandDomainService.getBrandMerchantInfoByMerchantId(merchant.getId());
            if (Objects.isNull(brandMerchantInfoByMerchantId)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
            }
            BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandMerchantInfoByMerchantId.getBrandId());
            CreateBrandResponseDTO createBrandResponseDTO = new CreateBrandResponseDTO();
            createBrandResponseDTO.setBrandId(brandModule.getBrandId());
            createBrandResponseDTO.setSn(brandModule.getSn());
            return createBrandResponseDTO;
        } else {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, businessOpenResult.getMessage());
        }
    }

    private MerchantInfo checkOpenBrandPaymentHubForAudit(BrandPaymentHubOpenRequestDTO brandPaymentHubOpenRequest) {
        MerchantInfo merchant;
        // 存量品牌开通收付通
        if (WosaiStringUtils.isNotEmpty(brandPaymentHubOpenRequest.getBrandSn())) {
            BrandModule brandModule = brandDomainService.getBrandModuleByBrandSn(brandPaymentHubOpenRequest.getBrandSn());
            if (Objects.isNull(brandModule)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
            }
            if (WosaiStringUtils.isEmpty(brandModule.getMerchantSn())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_FIND);
            }
            merchant = merchantService.getMerchantBySn(brandModule.getMerchantSn(), null);
        } else {
            // 新增品牌开通收付通
            if (WosaiStringUtils.isEmpty(brandPaymentHubOpenRequest.getMerchantSn())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_FIND);
            }
            merchant = merchantService.getMerchantBySn(brandPaymentHubOpenRequest.getMerchantSn(), null);
            if (Objects.isNull(merchant)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
            }
            // 校验主商户是不是已经在其他品牌下了
            BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(merchant.getId());
            if (Objects.nonNull(brandMerchantModule)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_A_BRAND);
            }
        }
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        ParamsCheckHelper.checkFundManagementCompanyParams(brandPaymentHubOpenRequest.getFundManagementCompanyCode(), brandPaymentHubOpenRequest.getConfig(), brandPaymentHubOpenRequest.getAccounts());
        return merchant;
    }


    public void fuiouOpenAccountCompensate() {
        List<FuiouToBeActiveRecordModule> toBeActiveRecordModules = redisHelper.getAllFromSet(RedisKeyEnum.FUIOU_OPEN_ACCOUNT_RECORDS.getKey(), FuiouToBeActiveRecordModule.class);
        if (CollectionUtils.isEmpty(toBeActiveRecordModules)) {
            return;
        }
        List<FuiouToBeActiveRecordModule> notHandleModules = Lists.newArrayList();
        toBeActiveRecordModules.forEach(toBeActiveRecordModule -> {
            try {
                FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(toBeActiveRecordModule.getBrandId(), FuiouConfigModule.class);
                if (configModule == null) {
                    log.error("【fuiouOpenAccountCompensate】品牌配置不存在，品牌ID：{}", toBeActiveRecordModule.getBrandId());
                    notHandleModules.add(toBeActiveRecordModule);
                    return;
                }
                BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(toBeActiveRecordModule.getBrandId(), toBeActiveRecordModule.getMerchantId());
                if (Objects.isNull(brandMerchantModule)) {
                    log.error("【fuiouOpenAccountCompensate】品牌商户不存在，品牌ID：{}，商户ID：{}", toBeActiveRecordModule.getBrandId(), toBeActiveRecordModule.getMerchantId());
                    return;
                }
                String traceNo = String.valueOf(idGeneratorSnowflake.nextId());
                QueryAllocateAccountRequest request = new QueryAllocateAccountRequest();
                QueryAllocateAccountRequestBody body = new QueryAllocateAccountRequestBody(configModule.getMerchantNo());
                body.setTraceNo(traceNo);
                body.setAccountIn(brandMerchantModule.getMemberId());
                request.setBody(body);
                log.info("【fuiouOpenAccountCompensate】富友开通会员补偿请求流水:{}，参数:{}", traceNo, JSON.toJSONString(body));
                QueryAllocateAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, QueryAllocateAccountResponse.class, configModule);
                if (response == null || !response.isSuccess()) {
                    log.error("【fuiouOpenAccountCompensate】富友开通会员补偿失败，响应错误，记录为：{}", toBeActiveRecordModule);
                    notHandleModules.add(toBeActiveRecordModule);
                    return;
                }
                List<QueryAllocateAccountResponseBody.Account> accountInlist = response.getBody().getAccountInlist();
                if (CollectionUtils.isEmpty(accountInlist)) {
                    log.info("【fuiouOpenAccountCompensate】富友开通会员补偿失败，未获取到会员信息，记录为：{}", toBeActiveRecordModule);
                    notHandleModules.add(toBeActiveRecordModule);
                    return;
                }
                Optional<QueryAllocateAccountResponseBody.Account> optionalAccount = accountInlist.stream().filter(account -> account.getAccountIn().equals(brandMerchantModule.getMemberId())).findAny();
                this.handleQueryResult(toBeActiveRecordModule, optionalAccount, notHandleModules);
                log.info("【fuiouOpenAccountCompensate】富友开通会员补偿成功，记录为：{}", toBeActiveRecordModule);
            } catch (Exception e) {
                log.error("补偿开通会员失败，失败记录为：{}", toBeActiveRecordModule, e);
                notHandleModules.add(toBeActiveRecordModule);
            }
        });
        // 重新写入待补偿数据
        redisHelper.overwriteSet(RedisKeyEnum.FUIOU_OPEN_ACCOUNT_RECORDS.getKey(), notHandleModules);
    }

    private void handleQueryResult(FuiouToBeActiveRecordModule toBeActiveRecordModule, Optional<QueryAllocateAccountResponseBody.Account> optionalAccount, List<FuiouToBeActiveRecordModule> notHandleModules) {
        if (optionalAccount.isPresent()) {
            QueryAllocateAccountResponseBody.Account account = optionalAccount.get();
            String accountOpenStatus = this.getAccountOpenStatusByFuiouAccountQueryResponse(account);
            String accountIn = account.getAccountIn();
            BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(toBeActiveRecordModule.getBrandId(), toBeActiveRecordModule.getMerchantId());
            if (Objects.isNull(brandMerchantModule)) {
                return;
            }
            BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandMerchantModule.getBrandId());
            if (Objects.isNull(brandModule)) {
                return;
            }
            if (accountOpenStatus.equals(BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus())) {
                notHandleModules.add(toBeActiveRecordModule);
                return;
            }
            brandMerchantModule.setMemberId(accountIn);
            brandMerchantModule.setSubAccountNo(accountIn);
            brandMerchantModule.setAccountOpenStatus(accountOpenStatus);
            brandMerchantModule.setAccountOpenFailureReason("");
            List<QueryAllocateAccountResponseBody.Account.Card> cards = account.getCards();
            if (CollectionUtils.isNotEmpty(cards)) {
                cards.forEach(card -> {
                    if (card.getIsDefault().equals("01") && FuiouOpenAccountStatusEnum.SUCCESS.getCode().equals(card.getStatus())) {
                        BankCardModule defaultBankCardModule = bankCardDomainService.getDefaultBankCardModule(toBeActiveRecordModule.getBrandId(), toBeActiveRecordModule.getMerchantId());
                        defaultBankCardModule.setStatus(1);
                        defaultBankCardModule.setActivationTime(new Date());
                        bankCardDomainService.updateBankCard(defaultBankCardModule);
                        brandMerchantModule.setAccountOpenedTime(new Date());
                        brandMerchantModule.setBankCardActivateStatus(BankCardActivateStatusEnum.ACTIVATED.getActivateStatus());
                    }
                });
            }
            brandDomainService.updateBrandMerchant(brandMerchantModule);
            defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchantModule.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandModule, brandMerchantModule)).build());
            if (accountOpenStatus.equals(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus())){
                defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandMerchantModule.getBrandId()).merchantSn(brandMerchantModule.getMerchantSn()).enrollChannelEnum(EnrollChannelEnum.FUIOU).build());
            }
        } else {
            log.info("【fuiouOpenAccountCompensate】补偿失败，未获取到会员信息，记录为：{}", toBeActiveRecordModule);
            notHandleModules.add(toBeActiveRecordModule);
        }
    }

    private String getAccountOpenStatusByFuiouAccountQueryResponse(QueryAllocateAccountResponseBody.Account account) {
        String accountOpenStatus = BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus();
        if (FuiouOpenAccountStatusEnum.SUCCESS.getCode().equals(account.getStatus())) {
            accountOpenStatus = BrandMerchantAccountOpenStatusEnum.OPENED.getStatus();
        }
        if (FuiouOpenAccountStatusEnum.LOSE_EFFECTIVENESS.getCode().equals(account.getStatus())) {
            accountOpenStatus = BrandMerchantAccountOpenStatusEnum.INVALID.getStatus();
        }
        if (FuiouOpenAccountStatusEnum.FROZEN.getCode().equals(account.getStatus())) {
            accountOpenStatus = BrandMerchantAccountOpenStatusEnum.TO_BE_CONTROLLED.getStatus();
        }
        return accountOpenStatus;
    }

    public SubAccountOpenDetailDTO querySubAccountOpenStatus(String brandId, String merchantSn) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (Objects.isNull(brandModule.getFundManagementCompanyCode()) || (!FundManagementCompanyEnum.FUIOU.equals(brandModule.getFundManagementCompanyCode()) && !FundManagementCompanyEnum.MY_BANK.equals(brandModule.getFundManagementCompanyCode()))) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_FUND_MANAGEMENT_COMPANY);
        }
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchant.getId());
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_NOT_BELONG_BRAND);
        }
        switch (brandModule.getFundManagementCompanyCode()) {
            case FUIOU:
                return queryFuiouAccount(brandId, brandMerchantModule);
            case MY_BANK:
                return queryMyBankAccount(brandId, brandMerchantModule);
            default:
                return null;
        }
    }

    private SubAccountOpenDetailDTO queryFuiouAccount(String brandId, BrandMerchantModule brandMerchantModule) {
        SubAccountOpenDetailDTO subAccountOpenDetailDTO = new SubAccountOpenDetailDTO();
        subAccountOpenDetailDTO.setMerchantSn(brandMerchantModule.getMerchantSn());
        subAccountOpenDetailDTO.setStatus("99");
        if (StringUtils.isBlank(brandMerchantModule.getMemberId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MERCHANT_NO_CHECK_IN);
        }
        FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandId, FuiouConfigModule.class);
        if (configModule == null) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NO_FIND_BRAND_CONFIG);
        }
        String traceNo = String.valueOf(idGeneratorSnowflake.nextId());
        QueryAllocateAccountRequest request = new QueryAllocateAccountRequest();
        QueryAllocateAccountRequestBody body = new QueryAllocateAccountRequestBody(configModule.getMerchantNo());
        body.setTraceNo(traceNo);
        body.setAccountIn(brandMerchantModule.getMemberId());
        request.setBody(body);
        log.info("【querySubAccountOpenStatus】查询富友子账号信息结果流水:{}，参数:{}", traceNo, JSON.toJSONString(body));
        QueryAllocateAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, QueryAllocateAccountResponse.class, configModule);
        if (response == null || !response.isSuccess()) {
            return subAccountOpenDetailDTO;
        }
        List<QueryAllocateAccountResponseBody.Account> accountInlist = response.getBody().getAccountInlist();
        if (CollectionUtils.isEmpty(accountInlist)) {
            log.info("【querySubAccountOpenStatus】未获取到会员信息，商户编号为：{}", brandMerchantModule.getMerchantSn());
            return subAccountOpenDetailDTO;
        }
        Optional<QueryAllocateAccountResponseBody.Account> optionalAccount = accountInlist.stream().filter(account -> account.getAccountIn().equals(brandMerchantModule.getMemberId())).findAny();
        if (optionalAccount.isPresent()) {
            QueryAllocateAccountResponseBody.Account account = optionalAccount.get();
            subAccountOpenDetailDTO.setStatus(account.getStatus());
            subAccountOpenDetailDTO.setResponse(account);
        }
        return subAccountOpenDetailDTO;
    }

    private SubAccountOpenDetailDTO queryMyBankAccount(String brandId, BrandMerchantModule brandMerchantModule) {
        SubAccountOpenDetailDTO subAccountOpenDetailDTO = new SubAccountOpenDetailDTO();
        subAccountOpenDetailDTO.setMerchantSn(brandMerchantModule.getMerchantSn());
        subAccountOpenDetailDTO.setStatus("99");
        if (StringUtils.isBlank(brandMerchantModule.getMemberId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MERCHANT_NO_CHECK_IN);
        }
        MyBankConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandId, MyBankConfigModule.class);
        if (configModule == null) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NO_FIND_BRAND_CONFIG);
        }
        MerchantAppletRegisterQueryRequest request = new MerchantAppletRegisterQueryRequest(configModule.getAppId());
        MerchantAppletRegisterQueryRequestModel requestModel = new MerchantAppletRegisterQueryRequestModel();
        requestModel.setIsvOrgId(configModule.getIsvOrgId());
        requestModel.setOutMerchantId(brandMerchantModule.getMerchantSn());
        request.setRequestBody(requestModel);
        MerchantAppletRegisterQueryResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.MY_BANK).call(request, MerchantAppletRegisterQueryResponse.class, configModule);
        if (response != null && response.isSuccess()) {
            MerchantAppletRegisterQueryResponse.MerchantAppletRegisterQuery merchantAppletRegisterQuery = response.getMerchantAppletRegisterQuery();
            subAccountOpenDetailDTO.setStatus(StringUtils.isEmpty(merchantAppletRegisterQuery.getMerchantAppletRegisterQueryResponseModel().getRegisterStatus()) ? "12" : "1" + merchantAppletRegisterQuery.getMerchantAppletRegisterQueryResponseModel().getRegisterStatus());
            subAccountOpenDetailDTO.setMerchantSn(brandMerchantModule.getMerchantSn());
            subAccountOpenDetailDTO.setResponse(merchantAppletRegisterQuery);
            return subAccountOpenDetailDTO;
        }
        return subAccountOpenDetailDTO;
    }

    public String getMeiTuanStoreStatus(String brandId, String storeId) {
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandId);
        if (Objects.isNull(brandConfigModule)) {
            return StringUtils.EMPTY;
        }
        ConfigModule configModule = JSON.parseObject(brandConfigModule.getConfig(), ConfigModule.class);
        if (Objects.isNull(configModule)) {
            return StringUtils.EMPTY;
        }
        String appId = configModule.getMeiTuanAppid();
        String signKey = configModule.getMeiTuanSecret();
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(signKey)) {
            return MeiTuanStoreStatusEnum.NOT_FOUND_MEI_TUAN_CONFIG.getDesc();
        }
        long readTimeout = 1000;
        long connectTimeout = 1000;
        MeituanClient meituanClient = new MeituanClient(readTimeout, connectTimeout, appId, signKey);
        LocalDate start = LocalDate.now().minusDays(1);
        LocalDate currentEnd = start.plusDays(1);
        BillListRequest request = new BillListRequest();
        BaseResponse response;
        int offset = 0;
        request.setAppPoiCode(storeId);
        request.setStartDate(start.atStartOfDay().toEpochSecond(ZoneOffset.ofHours(8)));
        request.setEndDate(currentEnd.atStartOfDay().toEpochSecond(ZoneOffset.ofHours(8)) - 1);
        request.setOffset(offset);
        try {
            response = meituanClient.queryAuthorizationStatus(request);
            if (response.getResultCode() != 1) {
                return MeiTuanStoreStatusEnum.NOT_FOUND.getDesc();
            }
        } catch (Exception e) {
            return MeiTuanStoreStatusEnum.NOT_FOUND.getDesc();
        }
        return MeiTuanStoreStatusEnum.NORMAL.getDesc();
    }

    public CommonResult singleRemoveBrandMerchantAssociation(RemoveBrandMerchantAssociationRequestDTO requestDTO) {
        String brandId = requestDTO.getBrandId();
        String merchantId = requestDTO.getMerchantId();
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            return CommonResult.fail("失败：商户未关联品牌");
        }
        if (brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
            return CommonResult.fail("失败：品牌主商户不可解绑");
        }
        PaymentModeChangeResult paymentModeChangeResult = merchantContractJobClient.changePaymentMode(new PaymentModeChangeRequest().setMerchantId(brandMerchantModule.getMerchantId()).setTargetPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode()));
        if (!paymentModeChangeResult.isSuccess()) {
            return CommonResult.fail("失败：" + paymentModeChangeResult.getMsg());
        }
        brandMerchantModule.setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode());
        brandDomainService.updateBrandMerchant(brandMerchantModule);
        brandDomainService.deleteBrandMerchant(brandId, Collections.singletonList(merchantId));
        return CommonResult.success();
    }

    public CommonResult singleCreateBrandMerchantAssociation(CreateBrandMerchantAssociationRequestDTO requestDTO) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(requestDTO.getBrandId());
        if (Objects.isNull(brandModule)) {
            return CommonResult.fail("失败：品牌不存在");
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(requestDTO.getMerchantId());
        if (Objects.nonNull(brandMerchantModule)) {
            return CommonResult.fail("失败：商户已关联品牌");
        }
        MerchantInfoQueryResult mainMerchantInfo = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(brandModule.getMerchantSn()));
        Map<String, String> marketCheckResult = marketingPrepaidClient.checkBeforeAssociateBrand(AssociateBrandBeforeCheckRequest.builder()
                .mainMerchantId(mainMerchantInfo.getMerchantId())
                .subMerchantIds(Collections.singletonList(requestDTO.getMerchantId()))
                .build());
        String failReason = marketCheckResult.get(requestDTO.getMerchantId());
        if (WosaiStringUtils.isNotEmpty(failReason)) {
            return CommonResult.fail("失败：" + failReason);
        }
        MerchantInfoQueryResult merchantInfo = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantId(requestDTO.getMerchantId()));
        if (Objects.isNull(merchantInfo)) {
            return CommonResult.fail("失败：商户不存在");
        }
        brandDomainService.createBrandMerchant(new BrandMerchantModule().setBrandId(brandModule.getBrandId())
                .setParentBrandId(brandModule.getParentId())
                .setMerchantId(merchantInfo.getMerchantId())
                .setMerchantName(merchantInfo.getName())
                .setMerchantSn(merchantInfo.getMerchantSn())
                .setType(Integer.valueOf(0).equals(merchantInfo.getMerchantType()) ? BrandMerchantTypeEnum.PERSONAL.getType() : Integer.valueOf(1).equals(merchantInfo.getMerchantType()) ? BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType() : BrandMerchantTypeEnum.COMPANY.getType())
                .setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode())
                .setMerchantType(MerchantTypeEnum.getMerchantTypeByDesc(requestDTO.getMerchantType())));
        PaymentModeChangeResult changeResult = merchantContractJobClient.changePaymentMode(
                new PaymentModeChangeRequest()
                        .setMerchantId(requestDTO.getMerchantId())
                        .setTargetPaymentMode(requestDTO.getPaymentMode())
        );
        if (changeResult.isSuccess()) {
            BrandMerchantModule updateModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandModule.getBrandId(), merchantInfo.getMerchantId());
            updateModule.setPaymentMode(requestDTO.getPaymentMode());
            brandDomainService.updateBrandMerchant(updateModule);
            return CommonResult.success(changeResult.getMsg());
        } else {
            return CommonResult.fail("失败：" + changeResult.getMsg());
        }
    }


    public CommonResult specialTreatmentBrandMerchantOpenAccount(String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (Objects.isNull(brandModule.getFundManagementCompanyCode()) || !brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.CITIC)) {
            return CommonResult.fail("失败：非中信银行品牌");
        }
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandId);
        CiticBankConfigModule citicBankConfigModule = JSON.parseObject(brandConfigModule.getConfig(), CiticBankConfigModule.class);
        if (Boolean.TRUE.equals(citicBankConfigModule.getNeedSpecialTreatment())) {
            MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
            merchantConditionModule.setBrandIds(Collections.singletonList(brandId));
            merchantConditionModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.IN_OPENING.getStatus());
            List<BrandMerchantModule> brandMerchantModules = brandDomainService.queryBrandMerchantByConditions(merchantConditionModule);
            if (CollectionUtils.isNotEmpty(brandMerchantModules)) {
                brandMerchantModules.forEach(brandMerchantModule -> {
                    BankCardModule defaultBankCardModule = bankCardDomainService.getDefaultBankCardModule(brandId, brandMerchantModule.getMerchantId());
                    if (Objects.isNull(defaultBankCardModule) || defaultBankCardModule.getStatus() != 1) {
                        return;
                    }
                    OpenAggregationRequest openAggregationRequest = new OpenAggregationRequest();
                    openAggregationRequest.setAggregationMerchantSn(brandModule.getMerchantSn());
                    openAggregationRequest.setMerchantSn(brandMerchantModule.getMerchantSn());
                    openAggregationRequest.setNotifyUrl(aggregationOpenNotifyUrl);
                    aggregationService.submitOpenAggregation(openAggregationRequest);
                });
            }
        }
        return CommonResult.success();
    }

    public CommonResult handleBrandMerchantBindCard(String brandId) {
        ThreadPoolHelper.execute(() -> {
            Long startId = 0L;
            Integer pageSize = apolloConfig.getDefaultPageSize();
            List<BrandMerchantModule> brandMerchantModules;
            do {
                MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
                merchantConditionModule.setBrandId(brandId);
                brandMerchantModules = brandDomainService.pageIdBrandMerchantByConditions(startId, pageSize, merchantConditionModule);
                if (CollectionUtils.isEmpty(brandMerchantModules)) {
                    log.info("未查到数据");
                    return;
                }
                brandMerchantModules.forEach(brandMerchantModule -> {
                    MerchantInfo merchant = merchantService.getMerchantById(brandMerchantModule.getMerchantId(), null);
                    if (Objects.isNull(merchant)) {
                        return;
                    }
                    List<BankCardModule> defaultBankCardModules = bankCardDomainService.getDefaultBankCardModules(brandId, Collections.singletonList(brandMerchantModule.getMerchantId()));
                    if (CollectionUtils.isNotEmpty(defaultBankCardModules)) {
                        log.info("商户{}已绑定默认银行卡", merchant.getName());
                        return;
                    }
                    HashMap<String, Object> params = Maps.newHashMap();
                    params.put("merchant_id", brandMerchantModule.getMerchantId());
                    params.put("default_status", 1);
                    params.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_MSP);
                    com.wosai.upay.common.bean.ListResult listResult = bankService.findMerchantBankAccounts(new PageInfo(1, 10), params);
                    log.info("查询商户银行卡信息：{}", JSON.toJSONString(listResult));
                    if (listResult.getTotal() >= 1L) {
                        BizBankAccountModule bizBankAccountModule = JSON.parseObject(JSON.toJSONString(listResult.getRecords().get(0)), BizBankAccountModule.class);
                        BizBankAccountAddReq bizBankAccountAddReq = BizBankAccountModule.toReq(bizBankAccountModule, biz, true);
                        bizBankAccountAddReq.setMerchant_id(brandMerchantModule.getMerchantId());
                        Map<String, String> queryParams = Maps.newHashMap();
                        queryParams.put("merchant_id", bizBankAccountAddReq.getMerchant_id());
                        queryParams.put("biz", biz);
                        com.wosai.upay.common.bean.ListResult bizBankAccount = merchantBizBankAccountService.findBizBankAccount(queryParams);
                        if (bizBankAccount.getTotal() > 0) {
                            List<MerchantBizBankAccount> merchantBizBankAccounts = JSON.parseArray(JSON.toJSONString(bizBankAccount.getRecords()), MerchantBizBankAccount.class);
                            Optional<MerchantBizBankAccount> any = merchantBizBankAccounts.stream().filter(merchantBizBankAccount -> merchantBizBankAccount.getNumber().equals(bizBankAccountAddReq.getNumber())).findAny();
                            if (any.isPresent()) {
                                MerchantBizBankAccount merchantBizBankAccount = any.get();
                                BankCardModule bankCardModule = new BankCardModule();
                                this.saveBankCard(brandId, brandMerchantModule, bankCardModule, merchantBizBankAccount, merchant);
                                return;
                            }
                        }
                        try {
                            MerchantBizBankAccount merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(bizBankAccountAddReq);
                            BankCardModule bankCardModule = new BankCardModule();
                            this.saveBankCard(brandId, brandMerchantModule, bankCardModule, merchantBizBankAccount, merchant);
                        } catch (Exception e) {
                            log.error("创建商户银行卡信息失败", e);
                        }
                    }
                });
                startId = brandMerchantModules.get(brandMerchantModules.size() - 1).getId();
            } while (!CollectionUtils.isEmpty(brandMerchantModules));
        });
        return CommonResult.success();
    }

    private void saveBankCard(String brandId, BrandMerchantModule brandMerchantModule, BankCardModule bankCardModule, MerchantBizBankAccount merchantBizBankAccount, MerchantInfo merchant) {
        List<BankCardModule> bankCardModulesByCarIdList = bankCardDomainService.getBankCardModulesByCarIdList(brandMerchantModule.getBrandId(), brandMerchantModule.getMerchantId(), Lists.newArrayList(merchantBizBankAccount.getId()), null, null);
        if (CollectionUtils.isNotEmpty(bankCardModulesByCarIdList)) {
            return;
        }
        bankCardModule.setBankCardId(merchantBizBankAccount.getId());
        bankCardModule.setMerchantId(merchantBizBankAccount.getMerchant_id());
        bankCardModule.setReservedMobileNumber(merchant.getContact_cellphone());
        bankCardModule.setBrandId(brandId);
        bankCardModule.setAccountType(merchantBizBankAccount.getType());
        bankCardModule.setIsDefault(true);
        bankCardDomainService.createBankCard(bankCardModule);
        brandMerchantModule.setBankCardActivateStatus("UNACTIVATED");
        brandDomainService.updateBrandMerchant(brandMerchantModule);
    }

    public CommonResult batchOpenSubAccounts(String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        ThreadPoolHelper.execute(() -> {
            Long startId = 0L;
            Integer pageSize = apolloConfig.getDefaultPageSize();
            List<BrandMerchantModule> brandMerchantModules;
            do {
                MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
                merchantConditionModule.setBrandId(brandId);
                merchantConditionModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.HAVE_NOT_OPENED.getStatus());
                brandMerchantModules = brandDomainService.pageIdBrandMerchantByConditions(startId, pageSize, merchantConditionModule);
                if (CollectionUtils.isEmpty(brandMerchantModules)) {
                    log.info("batchOpenSubAccounts 未查到数据。");
                    return;
                }
                brandMerchantModules.forEach(brandMerchantModule -> {
                    try {
                        this.invokeSubAccountOpen(brandMerchantModule, brandModule);
                        defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchantModule.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandModule, brandMerchantModule)).build());
                    } catch (Exception e) {
                        log.error("batchOpenSubAccounts 调用子商户开户失败。品牌：{}，商户：{}", brandModule.getSn(), brandMerchantModule.getMerchantSn(), e);
                    }
                });
                startId = brandMerchantModules.get(brandMerchantModules.size() - 1).getId();
            } while (!CollectionUtils.isEmpty(brandMerchantModules));
        });
        return CommonResult.success();
    }

    public CheckAccountResultEnum checkMerchantAccount(String brandId, String merchantSn, String outMerchantNo, String identifyNo) {
        if (StringUtils.isBlank(merchantSn) && StringUtils.isBlank(outMerchantNo)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_SN_OR_OUT_MERCHANT_NO_NOT_BE_NULL_IN_SAME_TIME);
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByMerchantSn(brandId, merchantSn);
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(brandMerchantModule) || Objects.isNull(merchant)) {
            return CheckAccountResultEnum.FAIL_MERCHANT_NO;
        }
        CheckAccountResultEnum failMerchantNoAndSubject = getCheckAccountResultEnum(identifyNo, brandMerchantModule);
        if (!failMerchantNoAndSubject.equals(CheckAccountResultEnum.FAIL_MERCHANT_NO)) return failMerchantNoAndSubject;
        brandMerchantModule = brandDomainService.getBrandMerchantInfoByOutMerchantNo(brandId, outMerchantNo);
        failMerchantNoAndSubject = getCheckAccountResultEnum(identifyNo, brandMerchantModule);
        if (!failMerchantNoAndSubject.equals(CheckAccountResultEnum.FAIL_MERCHANT_NO)) return failMerchantNoAndSubject;
        return CheckAccountResultEnum.FAIL_MERCHANT_NO;
    }

    private CheckAccountResultEnum getCheckAccountResultEnum(String identifyNo, BrandMerchantModule brandMerchantModule) {
        if (Objects.nonNull(brandMerchantModule)) {
            MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(brandMerchantModule.getMerchantId(), null);
            if (Objects.nonNull(license)) {
                if (license.getType() == 0) {
                    return getCheckAccountResultEnum(identifyNo, license.getLegal_person_id_number());
                }
                return getCheckAccountResultEnum(identifyNo, license.getNumber());
            }
        }
        return CheckAccountResultEnum.FAIL_MERCHANT_NO;
    }

    private CheckAccountResultEnum getCheckAccountResultEnum(String identifyNo, String checkIdentityNo) {
        if (StringUtils.isNotBlank(identifyNo) && !identifyNo.equals(checkIdentityNo)) {
            return CheckAccountResultEnum.FAIL_MERCHANT_NO_AND_SUBJECT;
        }
        if (StringUtils.isNotBlank(identifyNo) && identifyNo.equals(checkIdentityNo)) {
            return CheckAccountResultEnum.SUCCESS;
        }
        return CheckAccountResultEnum.UNKNOWN;
    }

    public List<BrandMerchantDTO> getMerchantSimpleListByBrandId(String brandId) {
        List<BrandMerchantModule> modules = brandDomainService.getBrandMerchantSimpleInfoByBrandId(brandId);
        if (CollectionUtils.isEmpty(modules)) {
            return Lists.newArrayList();
        }
        List<BrandMerchantDTO> results = Lists.newArrayList();
        modules.forEach(module -> {
            BrandMerchantDTO dto = new BrandMerchantDTO();
            dto.setMerchantId(module.getMerchantId());
            dto.setMerchantSn(module.getMerchantSn());
            dto.setMerchantName(module.getMerchantName());
            results.add(dto);
        });
        return results;
    }

    public String getBrandKey(String brandId) {
        // 获取品牌模块
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (Objects.isNull(brandModule.getFundManagementCompanyCode())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_MANAGEMENT_COMPANY);
        }

        // 检查是否支持该基金公司
        FundManagementCompanyEnum fundManagementCompanyCode = brandModule.getFundManagementCompanyCode();
        if (FundManagementCompanyEnum.PAB.equals(fundManagementCompanyCode)) {
            throw new BrandBusinessException("平安银行不支持此操作。");
        }
        // 获取品牌配置模块
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandId);
        if (Objects.isNull(brandConfigModule) || Objects.isNull(brandConfigModule.getConfig())) {
            return StringUtils.EMPTY;
        }

        return this.getPublicKey(brandConfigModule, fundManagementCompanyCode);
    }

    public String generateBrandKey(String brandId, String managementPublicKey) {
        // 获取品牌模块
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (Objects.isNull(brandModule.getFundManagementCompanyCode())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_MANAGEMENT_COMPANY);
        }

        // 检查是否支持该基金公司
        FundManagementCompanyEnum fundManagementCompanyCode = brandModule.getFundManagementCompanyCode();
        if (FundManagementCompanyEnum.PAB.equals(fundManagementCompanyCode)) {
            throw new BrandBusinessException("平安银行不支持此操作。");
        }

        // 获取品牌配置模块
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandId);
        if (Objects.isNull(brandConfigModule) || Objects.isNull(brandConfigModule.getConfig())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.INVALID_BRAND_CONFIG);
        }
        String publicKey = getPublicKey(brandConfigModule, fundManagementCompanyCode);
        if (StringUtils.isNotBlank(publicKey)) {
            return publicKey;
        }
        // 根据基金公司代码生成密钥并更新配置
        return generateAndSaveKey(brandConfigModule, fundManagementCompanyCode, managementPublicKey);
    }

    private String getPublicKey(BrandConfigModule brandConfigModule, FundManagementCompanyEnum fundManagementCompanyCode) {
        switch (fundManagementCompanyCode) {
            case CITIC:
                return JSON.parseObject(brandConfigModule.getConfig(), CiticBankConfigModule.class).getSqbPublicKey();
            case MY_BANK:
                return JSON.parseObject(brandConfigModule.getConfig(), MyBankConfigModule.class).getIsvPublicKey();
            case FUIOU:
                return JSON.parseObject(brandConfigModule.getConfig(), FuiouConfigModule.class).getPublicKey();
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.UNSUPPORTED_FUND_MANAGEMENT_COMPANY);
        }
    }

    private String generateAndSaveKey(BrandConfigModule brandConfigModule, FundManagementCompanyEnum fundManagementCompanyCode, String managementPublicKey) {
        switch (fundManagementCompanyCode) {
            case CITIC:
                return handleCiticBank(brandConfigModule);
            case MY_BANK:
                return handleMyBank(brandConfigModule, managementPublicKey);
            case FUIOU:
                return handleFuiouBank(brandConfigModule, managementPublicKey);
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.UNSUPPORTED_FUND_MANAGEMENT_COMPANY);
        }
    }

    private String handleCiticBank(BrandConfigModule brandConfigModule) {
        try {
            CiticBankConfigModule citicBankConfigModule = JSON.parseObject(brandConfigModule.getConfig(), CiticBankConfigModule.class);
            if (citicBankConfigModule == null) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.INVALID_BRAND_CONFIG);
            }
            String password = CiticCryptoHelper.generateRandomPassword(apolloConfig.getCiticPasswordLength());
            CiticCryptoHelper.CiticKeyValue citicKeyValue = CiticCryptoHelper.keyGenerator(
                    apolloConfig.getCiticKeyAlgorithm(),
                    apolloConfig.getCiticLength(),
                    apolloConfig.getCiticSubject(),
                    apolloConfig.getCiticSignatureAlgorithm(),
                    apolloConfig.getCiticValidity(),
                    password
            );

            citicBankConfigModule.setPrivateKey(citicKeyValue.getPrivateKey());
            citicBankConfigModule.setPrivateKeyPassword(password);
            citicBankConfigModule.setPublicKey(apolloConfig.getCiticPublicKey());
            citicBankConfigModule.setSqbPublicKey(citicKeyValue.getPublicKey());

            brandConfigModule.setConfig(JSON.toJSONString(citicBankConfigModule));
            brandConfigDomainService.updateBrandConfig(brandConfigModule);

            return citicKeyValue.getPublicKey();
        } catch (NoSuchAlgorithmException | NoSuchProviderException | CertificateException |
                 OperatorCreationException | CertIOException e) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.GENERATE_BRAND_KEY_ERROR);
        }
    }

    private String handleMyBank(BrandConfigModule brandConfigModule, String managementPublicKey) {
        try {
            RSAKeyGenerator.RSAKeyValue rsaKeyValue = RSAKeyGenerator.generateRSA2Keys();

            MyBankConfigModule myBankConfigModule = JSON.parseObject(brandConfigModule.getConfig(), MyBankConfigModule.class);
            if (myBankConfigModule == null) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.INVALID_BRAND_CONFIG);
            }

            myBankConfigModule.setPublicKey(managementPublicKey);
            myBankConfigModule.setIsvPrivateKey(rsaKeyValue.getPrivateKey());
            myBankConfigModule.setIsvPublicKey(rsaKeyValue.getPublicKey());

            brandConfigModule.setConfig(JSON.toJSONString(myBankConfigModule));
            brandConfigDomainService.updateBrandConfig(brandConfigModule);

            return rsaKeyValue.getPublicKey();
        } catch (NoSuchAlgorithmException e) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.GENERATE_BRAND_KEY_ERROR);
        }
    }

    private String handleFuiouBank(BrandConfigModule brandConfigModule, String managementPublicKey) {
        try {
            RSAKeyGenerator.RSAKeyValue rsaKeyValue = RSAKeyGenerator.generateRSA2Keys();

            FuiouConfigModule fuiouConfigModule = JSON.parseObject(brandConfigModule.getConfig(), FuiouConfigModule.class);
            if (fuiouConfigModule == null) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.INVALID_BRAND_CONFIG);
            }

            fuiouConfigModule.setPublicKey(rsaKeyValue.getPublicKey());
            fuiouConfigModule.setPrivateKey(rsaKeyValue.getPrivateKey());
            fuiouConfigModule.setFyPublicKey(managementPublicKey);

            brandConfigModule.setConfig(JSON.toJSONString(fuiouConfigModule));
            brandConfigDomainService.updateBrandConfig(brandConfigModule);

            return rsaKeyValue.getPublicKey();
        } catch (NoSuchAlgorithmException e) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.GENERATE_BRAND_KEY_ERROR);
        }
    }

    public BrandMerchantModule getBrandMerchantBySqbStoreId(String brandId, String storeId) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantBySqbStoreId(brandId, storeId);
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        return brandMerchantModule;
    }

    /**
     * 启用收付通
     *
     * @param brandId 品牌ID
     */
    public void enableSft(String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        MerchantInfo merchant = merchantService.getMerchantBySn(brandModule.getMerchantSn(), null);
        if (Objects.isNull(merchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        // 如果是富友切换直清模式

        if (Objects.equals(brandModule.getFundManagementCompanyCode(), FundManagementCompanyEnum.FUIOU)) {
            Map<String, Object> params = new HashMap<>();
            params.put("merchant_id", merchant.getId());
            params.put("type", 16);
            params.put("status", 1);
            tradeConfigService.configCommonSwitch(params);
        }
        // 更改收付通标识
        UpdateMerchantSFTBrandInfoRequest updateMerchantSFTBrandInfoRequest = new UpdateMerchantSFTBrandInfoRequest();
        updateMerchantSFTBrandInfoRequest.setMerchantSn(merchant.getSn());
        updateMerchantSFTBrandInfoRequest.setBrandId(brandModule.getBrandId());
        tradeConfigService.updateMerchantSFTBrandInfo(updateMerchantSFTBrandInfoRequest);

        // 设置启用收付通
        brandModule.setEnableSft(1);

        // 修改品牌信息
        brandDomainService.modifyBrand(brandModule);
    }


}
