package com.wosai.cua.brand.business.service.config.datasource;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2018-08-30
 */
@Aspect
@Component
@Slf4j
public class DataSourceAop {

    @Pointcut("@annotation(com.wosai.cua.brand.business.service.config.datasource.UseSlave)")
    public void aspect() {
    }

    @Before("aspect()")
    public void before(JoinPoint point) {
        Object target = point.getTarget();
        String methodName = point.getSignature().getName();
        Class<?> clazz = target.getClass();
        Class<?>[] parameterTypes = ((MethodSignature) point.getSignature()).getMethod().getParameterTypes();
        try {
            Method method = clazz.getMethod(methodName, parameterTypes);
            boolean setDbTypeSlave = method.isAnnotationPresent(UseSlave.class);
            if (setDbTypeSlave) {
                DBContextHolder.setDbType(DataSourceConstant.SLAVE);
            }
        } catch (Exception e) {
            log.error("数据源切换切面异常", e);
        }
    }

    @After("aspect()")
    public void after() {
        DBContextHolder.clearDbType();
    }

    @AfterThrowing("aspect()")
    public void afterThrow() {
        DBContextHolder.clearDbType();
    }

}
