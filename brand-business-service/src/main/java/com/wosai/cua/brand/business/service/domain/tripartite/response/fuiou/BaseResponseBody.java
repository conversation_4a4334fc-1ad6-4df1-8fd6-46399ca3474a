package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou;

import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BaseResponseBody extends ResponseBodySignature{
    /**
     * 商户流水号
     */
    @Sign
    protected String traceNo;
    /**
     * 商户号
     */
    @Sign
    protected String mchntCd;
    /**
     * 响应码
     */
    @Sign
    protected String respCode;
    /**
     * 响应描述
     */
    @Sign
    protected String respDesc;
}
