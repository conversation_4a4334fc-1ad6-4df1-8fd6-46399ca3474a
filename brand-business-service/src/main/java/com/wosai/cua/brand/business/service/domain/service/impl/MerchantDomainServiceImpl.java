package com.wosai.cua.brand.business.service.domain.service.impl;

import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.service.MerchantDomainService;
import com.wosai.cua.brand.business.service.helper.ThreadPoolHelper;
import com.wosai.cua.brand.business.service.module.merchant.MerchantModule;
import com.wosai.cua.brand.business.service.thread.BatchGetMerchantInfoTask;
import com.wosai.mc.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 商户域服务实现类
 */
@Service
@Slf4j
public class MerchantDomainServiceImpl implements MerchantDomainService {

    @Autowired
    private MerchantService merchantService;

    private final ApolloConfig apolloConfig;

    public MerchantDomainServiceImpl(ApolloConfig apolloConfig) {
        this.apolloConfig = apolloConfig;
    }

    @Override
    public List<MerchantModule> batchGetMerchantModule(List<String> merchantIds) {
        if (CollectionUtils.isEmpty(merchantIds)) {
            return Collections.emptyList();
        }
        // 存储结果的列表
        List<MerchantModule> result = Collections.synchronizedList(new ArrayList<>());

        // 创建CountDownLatch，计数器为任务数量
        CountDownLatch latch = new CountDownLatch(merchantIds.size() / apolloConfig.getBatchProcessingMerchantCount() + (merchantIds.size() % apolloConfig.getBatchProcessingMerchantCount() == 0 ? 0 : 1));
        // 每个线程处理100个ID
        for (int i = 0; i < merchantIds.size(); i += apolloConfig.getBatchProcessingMerchantCount()) {
            int end = Math.min(i + apolloConfig.getBatchProcessingMerchantCount(), merchantIds.size());
            List<String> subList = merchantIds.subList(i, end);

            // 提交任务到线程池
            ThreadPoolHelper.submit(new BatchGetMerchantInfoTask(subList, result, latch, merchantService));
        }
        // 等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("批量获取商户信息失败", e);
            return Collections.emptyList();
        }
        return result;
    }
}
