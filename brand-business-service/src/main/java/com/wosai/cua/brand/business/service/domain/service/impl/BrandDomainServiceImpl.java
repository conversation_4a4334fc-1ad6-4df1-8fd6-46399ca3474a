package com.wosai.cua.brand.business.service.domain.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveMethod;
import com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantCreationRecordMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.*;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryBrandConditionsDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.RegisterBehaviorRecordInfoRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.RegisterBehaviorRecordInfoResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.VfinanceBaseResponse;
import com.wosai.cua.brand.business.service.enums.RegisterBehaviorStatusEnum;
import com.wosai.cua.brand.business.service.enums.VFinanceResponseCodeEnum;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantStatusChangeEvent;
import com.wosai.cua.brand.business.service.event.publisher.DefaultEventPublisher;
import com.wosai.cua.brand.business.service.helper.CommonHelper;
import com.wosai.cua.brand.business.service.module.PageModule;
import com.wosai.cua.brand.business.service.module.brand.*;
import com.wosai.cua.brand.business.service.module.brand.convert.BrandConverter;
import com.wosai.cua.brand.business.service.module.brand.convert.BrandMerchantConverter;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantExtraModule;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantConditionModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@SensitiveClass
@Slf4j
public class BrandDomainServiceImpl implements BrandDomainService {

    @Autowired
    private BrandMapper brandMapper;

    @Autowired
    private BrandMerchantMapper brandMerchantMapper;

    @Autowired
    private BrandMerchantCreationRecordMapper brandMerchantCreationRecordMapper;

    @Autowired
    private BrandConfigDOMapper brandConfigDOMapper;

    @Autowired
    private DefaultEventPublisher defaultEventPublisher;

    @Autowired
    private VfinanceInterfaceService vfinanceInterfaceService;

    @Override
    @SensitiveMethod
    public void createBrand(BrandModule brandModule) {
        BrandDO brandDO = BrandConverter.convertDO(brandModule);
        brandDO.setVersion(1);
        brandMapper.insert(brandDO);
    }

    @Override
    @SensitiveMethod
    public void createBrandMerchant(BrandMerchantModule brandMerchantModule) {
        BrandMerchantDO brandMerchantDo = JSON.parseObject(JSON.toJSONString(brandMerchantModule), BrandMerchantDO.class);
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.selectBrandMerchantByConditions(
                QueryMerchantConditionsDO.builder()
                        .brandId(brandMerchantDo.getBrandId())
                        .merchantId(brandMerchantDo.getMerchantId())
                        .build()
        );
        if (!CollectionUtils.isEmpty(brandMerchantList)) {
            List<BrandMerchantDO> validBrandMerchantList = brandMerchantList.stream().filter(brandMerchantDO -> brandMerchantDO.getDeleted() == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(validBrandMerchantList)) {
                log.info("该商户已与该品牌关联，brandId：{}，merchantId：{}", brandMerchantDo.getBrandId(), brandMerchantDo.getMerchantId());
                throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_IS_BELONG_OTHER_BRAND);
            }
            brandMerchantDo.setDeleted(0);
            brandMerchantDo.setId(brandMerchantList.get(0).getId());
            brandMerchantDo.setVersion(brandMerchantList.get(0).getVersion() + 1);
            brandMerchantDo.setAssociatedTime(new Date());
            brandMerchantMapper.updateById(brandMerchantDo);
            publishBrandMerchantStatusChangeEvent(brandMerchantModule.getBrandId(), Collections.singletonList(brandMerchantModule.getMerchantId()), 1);
            return;
        }
        brandMerchantDo.setVersion(0);
        brandMerchantMapper.insert(brandMerchantDo);
        publishBrandMerchantStatusChangeEvent(brandMerchantModule.getBrandId(), Collections.singletonList(brandMerchantModule.getMerchantId()), 1);
    }

    @Override
    public void updateBrandMerchant(BrandMerchantModule brandMerchantModule) {
        BrandMerchantDO brandMerchantDO = brandMerchantMapper.selectBrandMerchantByBrandIdAndMerchantId(brandMerchantModule.getBrandId(), brandMerchantModule.getMerchantId(), 0);
        if (Objects.isNull(brandMerchantDO)) {
            brandMerchantMapper.insert(BrandMerchantConverter.convertDO(brandMerchantModule));
            return;
        }
        brandMerchantModule.setId(brandMerchantDO.getId());
        brandMerchantMapper.updateById(BrandMerchantConverter.convertDO(brandMerchantModule));
    }

    @Override
    public void updateBrandMerchantByFields(ChangeBrandMerchantModule changeBrandMerchantModule) {
        if (Objects.isNull(changeBrandMerchantModule) || Objects.isNull(changeBrandMerchantModule.getId())) {
            return;
        }
        BrandMerchantDO brandMerchant = brandMerchantMapper.selectById(changeBrandMerchantModule.getId());
        BrandMerchantConverter.convertChangeDO(changeBrandMerchantModule, brandMerchant);
        brandMerchantMapper.updateById(brandMerchant);
    }

    @Override
    public void updateMerchantAccountOpenStatus(String brandId, String merchantSn, String accountOpenStatus, String accountOpenStatusReason) {
        if (StringUtils.isNotBlank(accountOpenStatus)) {
            LambdaUpdateWrapper<BrandMerchantDO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(BrandMerchantDO::getBrandId, brandId)
                    .eq(BrandMerchantDO::getMerchantSn, merchantSn)
                    .set(BrandMerchantDO::getAccountOpenStatus, accountOpenStatus)
                    .set(BrandMerchantDO::getAccountOpenFailureReason, StringUtils.isNotBlank(accountOpenStatusReason) ? accountOpenStatusReason : "")
                    .set(BrandMerchantDO::getAccountOpenedTime, new Date());
            brandMerchantMapper.update(wrapper);
        }
    }

    @Override
    public List<MerchantBrandDetailModule> getBrandModuleByMerchantId(String merchantId) {
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.selectBrandMerchantByMerchantId(merchantId);
        if (CollectionUtils.isEmpty(brandMerchantList)) {
            return Lists.newArrayList();
        }
        List<MerchantBrandDetailModule> merchantBrandDetailModules = Lists.newArrayList();
        List<String> brandIdList = brandMerchantList.stream().map(BrandMerchantDO::getBrandId).collect(Collectors.toList());
        List<BrandDO> brandList = brandMapper.selectBrandByBrandIdListOrBrandSnList(brandIdList, null);
        if (CollectionUtils.isEmpty(brandList)) {
            return Lists.newArrayList();
        }
        Map<String, BrandMerchantDO> brandMerchantMap = brandMerchantList.stream().collect(Collectors.toMap(BrandMerchantDO::getBrandId, Function.identity()));
        brandList.forEach(brand -> {
            MerchantBrandDetailModule module = JSON.parseObject(JSON.toJSONString(brand), MerchantBrandDetailModule.class);
            BrandMerchantDO brandMerchant = brandMerchantMap.get(brand.getBrandId());
            if (Objects.isNull(brandMerchant)) {
                return;
            }
            module.setMerchantId(brandMerchant.getMerchantId());
            module.setMerchantType(brandMerchant.getMerchantType());
            module.setMerchantTypeDesc(MerchantTypeEnum.getDescByMerchantType(brandMerchant.getMerchantType()));
            module.setPaymentMode(brandMerchant.getPaymentMode());
            module.setAssociatedElmStoreSn(brandMerchant.getAssociatedElmStoreSn());
            module.setAssociatedMeituanStoreSn(brandMerchant.getAssociatedMeituanStoreSn());
            module.setAssociatedSqbStoreId(brandMerchant.getAssociatedSqbStoreId());
            module.setMemberId(brandMerchant.getMemberId());
            module.setSubAccountNo(brandMerchant.getSubAccountNo());
            merchantBrandDetailModules.add(module);
        });
        return merchantBrandDetailModules;
    }

    @Override
    public List<MerchantBrandDetailModule> getBrandModuleByMerchantIds(List<String> merchantIds) {
        QueryMerchantConditionsDO merchantConditions = new QueryMerchantConditionsDO();
        merchantConditions.setMerchantIds(merchantIds);
        merchantConditions.setDeleted(0);
        return this.getMerchantBrandDetailModules(merchantConditions);
    }

    private @NotNull List<MerchantBrandDetailModule> getMerchantBrandDetailModules(QueryMerchantConditionsDO merchantConditions) {
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.selectBrandMerchantByConditions(merchantConditions);
        if (CollectionUtils.isEmpty(brandMerchantList)) {
            return Lists.newArrayList();
        }
        List<MerchantBrandDetailModule> merchantBrandDetailModules = Lists.newArrayList();
        List<String> brandIdList = brandMerchantList.stream().map(BrandMerchantDO::getBrandId).collect(Collectors.toList());
        List<BrandDO> brandList = brandMapper.selectBrandByBrandIdListOrBrandSnList(brandIdList, null);
        if (CollectionUtils.isEmpty(brandList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BrandConfigDO::getBrandId, brandIdList);
        List<BrandConfigDO> brandConfigList = brandConfigDOMapper.selectList(queryWrapper);
        Map<String, String> brandIdConfigMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(brandConfigList)) {
            brandIdConfigMap.putAll(brandConfigList.stream().collect(Collectors.toMap(BrandConfigDO::getBrandId, BrandConfigDO::getConfig)));
        }
        Map<String, BrandDO> brandMap = brandList.stream().collect(Collectors.toMap(BrandDO::getBrandId, Function.identity()));
        brandMerchantList.forEach(brandMerchant -> {
            BrandDO brand = brandMap.get(brandMerchant.getBrandId());
            if (Objects.isNull(brand)) {
                return;
            }
            MerchantBrandDetailModule module = JSON.parseObject(JSON.toJSONString(brand), MerchantBrandDetailModule.class);
            module.setMerchantId(brandMerchant.getMerchantId());
            module.setMerchantType(brandMerchant.getMerchantType());
            module.setMerchantTypeDesc(MerchantTypeEnum.getDescByMerchantType(brandMerchant.getMerchantType()));
            module.setPaymentMode(brandMerchant.getPaymentMode());
            module.setMerchantSn(brandMerchant.getMerchantSn());
            module.setAssociatedElmStoreSn(brandMerchant.getAssociatedElmStoreSn());
            module.setAssociatedMeituanStoreSn(brandMerchant.getAssociatedMeituanStoreSn());
            module.setAssociatedSqbStoreId(brandMerchant.getAssociatedSqbStoreId());
            module.setMemberId(brandMerchant.getMemberId());
            module.setSubAccountNo(brandMerchant.getSubAccountNo());
            this.doBrandConfig(brandIdConfigMap, brand, module);
            merchantBrandDetailModules.add(module);
        });
        return merchantBrandDetailModules;
    }

    @Override
    public List<MerchantBrandDetailModule> getMerchantBrandDetailModuleByBrandId(String brandId) {
        QueryMerchantConditionsDO merchantConditions = new QueryMerchantConditionsDO();
        merchantConditions.setBrandId(brandId);
        merchantConditions.setDeleted(0);
        return this.getMerchantBrandDetailModules(merchantConditions);
    }

    private void doBrandConfig(Map<String, String> brandIdConfigMap, BrandDO brand, MerchantBrandDetailModule module) {
        String configString = brandIdConfigMap.get(brand.getBrandId());
        if (StringUtils.isBlank(configString)) {
            module.setAllowLogin(true);
        }
        if (StringUtils.isNotBlank(configString)) {
            ConfigModule configModule = JSON.parseObject(configString, ConfigModule.class);
            module.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            module.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
        }
        if (MerchantTypeEnum.BRAND_ADMIN.getMerchantType().equals(module.getMerchantType())) {
            module.setAllowLogin(true);
        }
        if (MerchantTypeEnum.BRAND_OWNER.getMerchantType().equals(module.getMerchantType())) {
            module.setAllowLogin(false);
        }
    }

    @Override
    @SensitiveMethod
    public BrandModule getBrandModuleByBrandId(String brandId) {
        BrandDO brandDO = brandMapper.selectBrandByBrandId(brandId);
        if (Objects.isNull(brandDO)) {
            log.info("brandId = {}，未查询到数据", brandId);
            return null;
        }
        return BrandModule.convert(brandDO);
    }

    @Override
    @SensitiveMethod
    public BrandModule getBrandModuleByBrandSn(String brandSn) {
        BrandDO brandDO = brandMapper.selectBrandByBrandSn(brandSn);
        if (Objects.isNull(brandDO)) {
            log.info("brandSn = {}，未查询到数据", brandSn);
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(brandDO), BrandModule.class);
    }

    @Override
    public BrandModule getBrandModuleByMerchantSn(String merchantSn) {
        LambdaQueryWrapper<BrandDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandDO::getMerchantSn, merchantSn);
        queryWrapper.eq(BrandDO::getDeleted, 0);
        List<BrandDO> brands = brandMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(brands)) {
            return BrandModule.convert(brands.get(0));
        }
        return null;
    }

    @Override
    @SensitiveMethod
    public List<BrandModule> getBrandModuleByBrandIdsOrBrandSnList(List<String> brandIds, List<String> brandSnList) {
        if (CollectionUtils.isEmpty(brandIds) && CollectionUtils.isEmpty(brandSnList)) {
            return Lists.newArrayList();
        }
        List<BrandDO> brandList = brandMapper.selectBrandByBrandIdListOrBrandSnList(brandIds, brandSnList);
        if (CollectionUtils.isEmpty(brandList)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(JSON.toJSONString(brandList), BrandModule.class);
    }

    @Override
    @SensitiveMethod
    public PageBrandModule pageBrandModuleByBrandConditions(BrandConditionModule brandConditionModule) {
        PageBrandModule module = new PageBrandModule(0);
        module.setBrandList(Lists.newArrayList());
        QueryBrandConditionsDO conditions = JSON.parseObject(JSON.toJSONString(brandConditionModule), QueryBrandConditionsDO.class);
        this.setBrandList(module, conditions, brandConditionModule);
        return module;
    }

    @Override
    @SensitiveMethod
    public PageBrandModule pageBrandModuleByMerchantConditions(MerchantConditionModule merchantConditionModule) {
        PageBrandModule module = new PageBrandModule(0);
        module.setBrandList(Lists.newArrayList());
        QueryMerchantConditionsDO merchantConditions = JSON.parseObject(JSON.toJSONString(merchantConditionModule), QueryMerchantConditionsDO.class);
        merchantConditions.setDeleted(0);
        List<BrandMerchantDO> brandMerchantDOList = brandMerchantMapper.selectBrandMerchantByConditions(merchantConditions);
        if (CollectionUtils.isEmpty(brandMerchantDOList)) {
            return module;
        }
        List<String> brandIdList = brandMerchantDOList.stream().map(BrandMerchantDO::getBrandId).collect(Collectors.toList());
        QueryBrandConditionsDO brandConditions = new QueryBrandConditionsDO();
        brandConditions.setBrandIdList(brandIdList);
        brandConditions.setSftTag(merchantConditionModule.getSftTag());
        brandConditions.setEnableSft(merchantConditionModule.getEnableSft());
        this.setBrandList(module, brandConditions, merchantConditionModule);
        return module;
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantByConditions(MerchantConditionModule merchantConditionModule) {
        QueryMerchantConditionsDO merchantConditions = JSON.parseObject(JSON.toJSONString(merchantConditionModule), QueryMerchantConditionsDO.class);
        List<BrandMerchantDO> brandMerchantDOList = brandMerchantMapper.selectBrandMerchantByConditions(merchantConditions);
        if (CollectionUtils.isEmpty(brandMerchantDOList)) {
            return Lists.newArrayList();
        }
        List<BrandMerchantModule> brandMerchantModules = JSON.parseArray(JSON.toJSONString(brandMerchantDOList), BrandMerchantModule.class);
        brandMerchantModules.forEach(brandMerchantModule -> brandMerchantModule.setMerchantTypeDesc(brandMerchantModule.getMerchantType()));
        return brandMerchantModules;
    }

    @Override
    public int deleteBrand(String brandId) {
        int i = brandMapper.deleteBrand(brandId);
        brandMerchantMapper.deleteBrandMerchantByBrandIdAndMerchantId(brandId, null);
        return i;
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantByBrandId(String brandId) {
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.selectBrandMerchantByConditions(QueryMerchantConditionsDO.builder().brandId(brandId).deleted(0).build());
        List<BrandMerchantModule> brandMerchantModules = JSON.parseArray(JSON.toJSONString(brandMerchantList), BrandMerchantModule.class);
        if (CollectionUtils.isEmpty(brandMerchantModules)) {
            return Lists.newArrayList();
        }
        brandMerchantModules.forEach(brandMerchantModule -> brandMerchantModule.setMerchantTypeDesc(MerchantTypeEnum.getDescByMerchantType(brandMerchantModule.getMerchantTypeDesc())));
        return brandMerchantModules;
    }

    @Override
    public List<BrandMerchantModule> getAllBrandMerchantsByBrandId(String brandId) {
        List<String> brandIds = brandMapper.selectBrandByParentBrandId(brandId).stream().map(BrandDO::getBrandId).collect(Collectors.toList());
        brandIds.add(brandId);
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.selectBrandMerchantByConditions(QueryMerchantConditionsDO.builder().brandIds(brandIds).deleted(0).build());
        List<BrandMerchantModule> brandMerchantModules = JSON.parseArray(JSON.toJSONString(brandMerchantList), BrandMerchantModule.class);
        if (CollectionUtils.isEmpty(brandMerchantModules)) {
            return Lists.newArrayList();
        }
        brandMerchantModules.forEach(brandMerchantModule -> brandMerchantModule.setMerchantTypeDesc(MerchantTypeEnum.getDescByMerchantType(brandMerchantModule.getMerchantTypeDesc())));
        return brandMerchantModules;
    }

    @Override
    public int deleteBrandMerchant(String brandId, List<String> merchantIds) {
        List<BrandMerchantDO> brandMrechantDOList = brandMerchantMapper.selectBrandMerchantByBrandIdAndMerchantIds(brandId, merchantIds);
        BrandMerchantModule brandMainMerchant = Optional.ofNullable(getBrandAdminMerchant(brandId)).orElse(new BrandMerchantModule());
        int i = brandMerchantMapper.deleteBrandMerchantByBrandIdAndMerchantId(brandId, merchantIds);
        publishBrandMerchantStatusChangeEvent(brandId, brandMainMerchant.getMerchantId(), brandMrechantDOList.stream().map(BrandMerchantDO::getMerchantId).collect(Collectors.toList()), 0);
        return i;
    }

    @Override
    @SensitiveMethod
    public int bathCreateBrandMerchant(List<BrandMerchantModule> brandMerchantModuleList) {
        if (CollectionUtils.isEmpty(brandMerchantModuleList)) {
            return 0;
        }
        List<BrandMerchantDO> brandMerchantList = JSON.parseArray(JSON.toJSONString(brandMerchantModuleList), BrandMerchantDO.class);
        int i = brandMerchantMapper.batchInsertBrandMerchant(brandMerchantList);
        brandMerchantModuleList.stream()
                // 按 brandId 分组，收集 merchantId 列表
                .collect(Collectors.groupingBy(
                        BrandMerchantModule::getBrandId,
                        Collectors.mapping(
                                BrandMerchantModule::getMerchantId,
                                Collectors.toList()
                        )
                ))
                // 遍历并发布事件
                .forEach((brandId, merchantIds) ->
                        publishBrandMerchantStatusChangeEvent(brandId, merchantIds, 1)
                );
        return i;
    }

    @Override
    public BrandMerchantCreationRecordModule createBrandMerchantCreationRecord(String brandId, String merchantId, String merchantSn) {
        BrandMerchantCreationRecordDO recordDo = new BrandMerchantCreationRecordDO();
        recordDo.setRecordId(UUID.randomUUID().toString().replace("-", ""));
        recordDo.setBrandId(brandId);
        recordDo.setMerchantId(merchantId);
        recordDo.setMerchantSn(merchantSn);
        recordDo.setNeedRetry(1);
        brandMerchantCreationRecordMapper.insertCreationRecord(recordDo);
        LambdaQueryWrapper<BrandMerchantCreationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandMerchantCreationRecordDO::getId, recordDo.getId());
        BrandMerchantCreationRecordDO brandMerchantCreationRecord = brandMerchantCreationRecordMapper.selectOne(queryWrapper);
        return JSON.parseObject(JSON.toJSONString(brandMerchantCreationRecord), BrandMerchantCreationRecordModule.class);
    }

    @Override
    public BrandMerchantCreationRecordModule getBrandMerchantCreationRecordByBrandIdAndMerchantIdAndSn(String brandId, String merchantId, String merchantSn) {
        LambdaQueryWrapper<BrandMerchantCreationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandMerchantCreationRecordDO::getBrandId, brandId);
        queryWrapper.eq(BrandMerchantCreationRecordDO::getMerchantId, merchantId);
        queryWrapper.eq(BrandMerchantCreationRecordDO::getMerchantSn, merchantSn);
        queryWrapper.orderByDesc(BrandMerchantCreationRecordDO::getCreatedTime);
        List<BrandMerchantCreationRecordDO> brandMerchantCreationRecords = brandMerchantCreationRecordMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(brandMerchantCreationRecords)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(brandMerchantCreationRecords.get(0)), BrandMerchantCreationRecordModule.class);
    }

    @Override
    public void createBrandMerchantCreationRecord(BrandMerchantCreationRecordModule brandMerchantCreationRecordModule) {
        brandMerchantCreationRecordMapper.insert(JSON.parseObject(JSON.toJSONString(brandMerchantCreationRecordModule), BrandMerchantCreationRecordDO.class));
    }

    @Override
    public void batchCreateBrandMerchantCreationRecord(List<BrandMerchantCreationRecordModule> brandMerchantCreationRecordModules) {
        brandMerchantCreationRecordMapper.batchCreateBrandMerchantCreationRecord(JSON.parseArray(JSON.toJSONString(brandMerchantCreationRecordModules), BrandMerchantCreationRecordDO.class));
    }

    @Override
    public PageBrandMerchantModule pageQueryBrandMerchantModule(MerchantConditionModule merchantConditionModule) {
        PageBrandMerchantModule pageBrandMerchantModule = new PageBrandMerchantModule();
        QueryMerchantConditionsDO conditions = JSON.parseObject(JSON.toJSONString(merchantConditionModule), QueryMerchantConditionsDO.class);
        int total = brandMerchantMapper.countBrandMerchantsByConditions(conditions);
        pageBrandMerchantModule.setTotal(total);
        if (total == 0) {
            return pageBrandMerchantModule;
        }
        Integer pageSize = merchantConditionModule.getPageSize();
        Integer offset = null;
        if (Objects.nonNull(merchantConditionModule.getPage()) && Objects.nonNull(pageSize)) {
            offset = (merchantConditionModule.getPage() - 1) * pageSize;
        }
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.pageBrandMerchantsByConditions(conditions, offset, pageSize);
        if (CollectionUtils.isEmpty(brandMerchantList)) {
            return pageBrandMerchantModule;
        }
        Map<Long, BrandMerchantDO> longBrandMerchantMap = brandMerchantList.stream().collect(Collectors.toMap(BrandMerchantDO::getId, brandMerchantDO -> brandMerchantDO, (a, b) -> a));
        List<BrandMerchantModule> brandMerchantModules = JSON.parseArray(JSON.toJSONString(brandMerchantList), BrandMerchantModule.class);
        brandMerchantModules.forEach(brandMerchantModule -> {
            brandMerchantModule.setMerchantTypeDesc(MerchantTypeEnum.getDescByMerchantType(brandMerchantModule.getMerchantType()));
            if (StringUtils.isNotBlank(longBrandMerchantMap.get(brandMerchantModule.getId()).getExtra())) {
                brandMerchantModule.setExtra(JSON.parseObject(longBrandMerchantMap.get(brandMerchantModule.getId()).getExtra(), BrandMerchantExtraModule.class));
            }
        });
        pageBrandMerchantModule.setBrandMerchantModuleList(brandMerchantModules);
        return pageBrandMerchantModule;
    }

    @Override
    public List<BrandMerchantModule> queryBrandMerchantByConditions(MerchantConditionModule merchantConditionModule) {
        LambdaQueryWrapper<BrandMerchantDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(merchantConditionModule.getBrandIds())) {
            queryWrapper.in(BrandMerchantDO::getBrandId, merchantConditionModule.getBrandIds());
        }
        if (StringUtils.isNotBlank(merchantConditionModule.getMerchantSn())) {
            queryWrapper.eq(BrandMerchantDO::getMerchantSn, merchantConditionModule.getMerchantSn());
        }
        if (CollectionUtils.isNotEmpty(merchantConditionModule.getMerchantTypes())) {
            queryWrapper.in(BrandMerchantDO::getMerchantType, merchantConditionModule.getMerchantTypes());
        }
        if (StringUtils.isNotBlank(merchantConditionModule.getAccountOpenStatus())) {
            queryWrapper.eq(BrandMerchantDO::getAccountOpenStatus, merchantConditionModule.getAccountOpenStatus());
        }
        if (StringUtils.isNotBlank(merchantConditionModule.getMerchantName())) {
            queryWrapper.like(BrandMerchantDO::getMerchantName, "%" + merchantConditionModule.getMerchantName() + "%");
        }
        queryWrapper.eq(BrandMerchantDO::getDeleted, 0);
        List<BrandMerchantDO> brandMerchantDOS = brandMerchantMapper.selectList(queryWrapper);
        return BrandMerchantModule.convertByBrandMerchantList(brandMerchantDOS);
    }

    @Override
    @SensitiveMethod
    public void modifyBrand(BrandModule module) {
        BrandDO brand = JSON.parseObject(JSON.toJSONString(module), BrandDO.class);
        brandMapper.updateBrand(brand);
    }

    @Override
    public BrandMerchantModule getBrandMerchantByBrandIdAndMerchantId(String brandId, String merchantId) {
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.selectBrandMerchantByConditions(QueryMerchantConditionsDO.builder().merchantId(merchantId).brandId(brandId).deleted(0).build());
        if (CollectionUtils.isEmpty(brandMerchantList)) {
            return null;
        }

        return BrandMerchantModule.convertByBrandMerchantDO(brandMerchantList.get(0));
    }

    @Override
    public int updateCreateBrandMerchantCreationRecord(BrandMerchantCreationRecordModule module) {
        return brandMerchantCreationRecordMapper.updateById(JSON.parseObject(JSON.toJSONString(module), BrandMerchantCreationRecordDO.class));
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantByBrandIdAndMerchantIds(String brandId, List<String> merchantIds) {
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.selectBrandMerchantByBrandIdAndMerchantIds(brandId, merchantIds);
        if (CollectionUtils.isEmpty(brandMerchantList)) {
            return Lists.newArrayList();
        }
        List<BrandMerchantModule> brandMerchantModules = JSON.parseArray(JSON.toJSONString(brandMerchantList), BrandMerchantModule.class);
        brandMerchantModules.forEach(brandMerchantModule -> brandMerchantModule.setMerchantTypeDesc(brandMerchantModule.getMerchantType()));
        return brandMerchantModules;
    }

    @Override
    public int deleteBrandMerchantWithdrawStrategy(List<Long> strategyIdList) {
        if (CollectionUtils.isEmpty(strategyIdList)) {
            return 0;
        }
        return brandMerchantMapper.deleteBrandMerchantWithdrawStrategy(strategyIdList);
    }

    @Override
    public BrandMerchantModule getBrandMerchantInfoByMerchantId(String merchantId) {
        BrandMerchantDO brandMerchant = brandMerchantMapper.getBrandMerchantInfoByMerchantId(merchantId);
        if (Objects.isNull(brandMerchant)) {
            return null;
        }
        return BrandMerchantConverter.convertModule(brandMerchant);
    }

    @Override
    public BrandMerchantModule getBrandMerchantInfoByMemberId(String memberId) {
        BrandMerchantDO brandMerchant = brandMerchantMapper.getBrandMerchantInfoByMemberId(memberId);
        if (Objects.isNull(brandMerchant)) {
            return null;
        }
        return BrandMerchantConverter.convertModule(brandMerchant);
    }

    @Override
    public BrandMerchantModule getBrandMerchantInfoByOutMerchantNo(String brandId, String outMerchantNo) {
        LambdaQueryWrapper<BrandMerchantDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandMerchantDO::getBrandId, brandId);
        queryWrapper.eq(BrandMerchantDO::getOutMerchantNo, outMerchantNo);
        queryWrapper.eq(BrandMerchantDO::getDeleted, 0);
        List<BrandMerchantDO> brandMerchants = brandMerchantMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(brandMerchants)) {
            return null;
        }
        return BrandMerchantConverter.convertModule(brandMerchants.get(0));
    }

    @Override
    public int relevanceBrandWithdrawStrategy(Long strategyId, String brandId, List<String> merchantSnList) {
        return brandMerchantMapper.relevanceBrandWithdrawStrategy(strategyId, brandId, merchantSnList);
    }

    @Override
    public BrandModule getBrandByMerchantSn(String merchantSn) {
        BrandDO brandDO = brandMapper.selectBrandByMerchantSn(merchantSn);
        if (Objects.isNull(brandDO)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(brandDO), BrandModule.class);
    }

    @Override
    public BrandMerchantModule getBrandOwnerMerchant(String brandId) {
        QueryMerchantConditionsDO queryMerchantConditions = new QueryMerchantConditionsDO();
        queryMerchantConditions.setBrandId(brandId);
        queryMerchantConditions.setMerchantTypes(Lists.newArrayList(MerchantTypeEnum.BRAND_OWNER.getMerchantType()));
        List<BrandMerchantDO> merchantDOList = brandMerchantMapper.selectBrandMerchantByConditions(queryMerchantConditions);
        if (CollectionUtils.isNotEmpty(merchantDOList)) {
            return JSON.parseObject(JSON.toJSONString(merchantDOList.get(0)), BrandMerchantModule.class);
        }
        return null;
    }

    @Override
    public BrandMerchantModule getBrandAdminMerchant(String brandId) {
        QueryMerchantConditionsDO queryMerchantConditions = new QueryMerchantConditionsDO();
        queryMerchantConditions.setBrandId(brandId);
        queryMerchantConditions.setMerchantTypes(Lists.newArrayList(MerchantTypeEnum.BRAND_ADMIN.getMerchantType()));
        List<BrandMerchantDO> merchantDOList = brandMerchantMapper.selectBrandMerchantByConditions(queryMerchantConditions);
        if (CollectionUtils.isNotEmpty(merchantDOList)) {
            return JSON.parseObject(JSON.toJSONString(merchantDOList.get(0)), BrandMerchantModule.class);
        }
        return null;
    }

    @Override
    public boolean checkExistBrandName(String brandName) {
        return !Objects.isNull(brandMapper.selectBrandByName(brandName));
    }

    @Override
    public List<BrandMerchantModule> pageGetBrandMerchantByBrandIdAndId(String brandId, Long id, Integer pageSize) {
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.pageGetBrandMerchantByBrandIdAndId(brandId, id, pageSize);
        if (CollectionUtils.isNotEmpty(brandMerchantList)) {
            return BrandMerchantConverter.convertModules(brandMerchantList);
        }
        return Collections.emptyList();
    }

    @Override
    public List<BrandMerchantModule> pageBrandMerchantById(Long id, Integer pageSize) {
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.pageBrandMerchantById(id, pageSize);
        if (CollectionUtils.isNotEmpty(brandMerchantList)) {
            return BrandMerchantConverter.convertModules(brandMerchantList);
        }
        return Collections.emptyList();
    }

    @Override
    public BrandMerchantCreationRecordModule getRecordModuleByRecordId(String recordId) {
        LambdaQueryWrapper<BrandMerchantCreationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandMerchantCreationRecordDO::getRecordId, recordId);
        BrandMerchantCreationRecordDO recordDO = brandMerchantCreationRecordMapper.selectOne(queryWrapper);
        if (Objects.isNull(recordDO)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(recordDO), BrandMerchantCreationRecordModule.class);
    }

    @Override
    public List<BrandMerchantModule> pageIdBrandMerchantByConditions(Long startId, Integer pageSize, MerchantConditionModule merchantConditionModule) {
        QueryMerchantConditionsDO conditions = new QueryMerchantConditionsDO();
        conditions.setBrandId(merchantConditionModule.getBrandId());
        conditions.setBankCardActivateStatus(merchantConditionModule.getBankCardActivateStatus());
        conditions.setAccountOpenStatus(merchantConditionModule.getAccountOpenStatus());
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.pageBrandMerchantByIdAndConditions(startId, pageSize, conditions);
        if (CollectionUtils.isNotEmpty(brandMerchantList)) {
            return BrandMerchantConverter.convertModules(brandMerchantList);
        }
        return Collections.emptyList();
    }

    @Override
    public BrandMerchantModule getBrandMerchantByMerchantSn(String brandId, String merchantSn) {
        LambdaQueryWrapper<BrandMerchantDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandMerchantDO::getBrandId, brandId);
        queryWrapper.eq(BrandMerchantDO::getMerchantSn, merchantSn);
        queryWrapper.eq(BrandMerchantDO::getDeleted, 0);
        BrandMerchantDO brandMerchant = brandMerchantMapper.selectOne(queryWrapper);
        return BrandMerchantConverter.convertModule(brandMerchant);
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantSimpleInfoByBrandId(String brandId) {
        LambdaQueryWrapper<BrandMerchantDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandMerchantDO::getBrandId, brandId);
        queryWrapper.eq(BrandMerchantDO::getDeleted, 0);
        queryWrapper.select(BrandMerchantDO::getMerchantId, BrandMerchantDO::getMerchantSn, BrandMerchantDO::getMerchantName);
        List<BrandMerchantDO> brandMerchants = brandMerchantMapper.selectList(queryWrapper);
        return BrandMerchantConverter.convertModules(brandMerchants);
    }


    public void setBrandList(PageBrandModule module, QueryBrandConditionsDO brandConditions, PageModule pageModule) {
        Integer total = brandMapper.countBrandByConditions(brandConditions);
        module.setTotal(total);
        Integer pageSize = pageModule.getPageSize();
        Integer offset = null;
        if (Objects.nonNull(pageModule.getPage()) && Objects.nonNull(pageSize)) {
            offset = (pageModule.getPage() - 1) * pageSize;
        }
        List<BrandDO> brands = brandMapper.pageBrandsByConditions(brandConditions, pageSize, offset);
        module.setBrandList(JSON.parseArray(JSON.toJSONString(brands), BrandModule.class));
        if (!CollectionUtils.isEmpty(module.getBrandList())) {
            List<String> brandIds = module.getBrandList().stream().map(BrandModule::getBrandId).collect(Collectors.toList());
            List<CountBrandMerchantNumberDO> merchantNumberList = brandMerchantMapper.countMerchantNumber(brandIds);
            Map<String, Integer> merchantNumberMap = merchantNumberList.stream().collect(Collectors.toMap(CountBrandMerchantNumberDO::getBrandId, CountBrandMerchantNumberDO::getMerchantNum));
            module.getBrandList().forEach(brandModule -> brandModule.setMerchantNumber(merchantNumberMap.get(brandModule.getBrandId())));
        }
    }

    private void publishBrandMerchantStatusChangeEvent(String brandId, List<String> merchantIds, Integer status) {
        try {
            BrandMerchantModule brandMainMerchant = Optional.ofNullable(getBrandAdminMerchant(brandId)).orElse(new BrandMerchantModule());
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        defaultEventPublisher.publish(BrandMerchantStatusChangeEvent.builder()
                                .brandId(brandId)
                                .mainMerchantId(brandMainMerchant.getMerchantId())
                                .merchantIds(merchantIds)
                                .status(status)
                                .build());

                    }
                });
            } else {
                defaultEventPublisher.publish(BrandMerchantStatusChangeEvent.builder()
                        .brandId(brandId)
                        .mainMerchantId(brandMainMerchant.getMerchantId())
                        .merchantIds(merchantIds)
                        .status(status)
                        .build());
            }
        } catch (Exception e) {
            log.error("publishBrandMerchantStatusChangeEvent error {}", JSON.toJSONString(merchantIds), e);
        }
    }

    private void publishBrandMerchantStatusChangeEvent(String brandId, String mainMerchantId, List<String> merchantIds, Integer status) {
        try {
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        defaultEventPublisher.publish(BrandMerchantStatusChangeEvent.builder()
                                .brandId(brandId)
                                .mainMerchantId(mainMerchantId)
                                .merchantIds(merchantIds)
                                .status(status)
                                .build());

                    }
                });
            } else {
                defaultEventPublisher.publish(BrandMerchantStatusChangeEvent.builder()
                        .brandId(brandId)
                        .mainMerchantId(mainMerchantId)
                        .merchantIds(merchantIds)
                        .status(status)
                        .build());
            }
        } catch (Exception e) {
            log.error("publishBrandMerchantStatusChangeEvent error {}", JSON.toJSONString(merchantIds), e);
        }
    }

    public boolean pabRegister(BrandModule brandModule, BrandMerchantModule brandMerchantModule) {
        // 先查查到了不登记，查不到再登记
        RegisterBehaviorRecordInfoRequest registerBehaviorRecordInfoRequest = new RegisterBehaviorRecordInfoRequest(brandModule.getPartnerId());
        registerBehaviorRecordInfoRequest.setMemberId(brandMerchantModule.getMemberId());
        registerBehaviorRecordInfoRequest.setFunctionFlag("2");
        String s = vfinanceInterfaceService.invokeService(registerBehaviorRecordInfoRequest);
        VfinanceBaseResponse response = JSON.parseObject(s, VfinanceBaseResponse.class);
        if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
            log.warn("调用维金接口失败。{}", response.getErrorMessage());
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), response.getErrorMessage());
        }
        List<RegisterBehaviorRecordInfoResponse> behaviorRecordInfoResponses = JSON.parseArray(response.getMemo(), RegisterBehaviorRecordInfoResponse.class);
        List<RegisterBehaviorRecordInfoResponse> registerBehaviorRecordInfoResponses = behaviorRecordInfoResponses.stream().filter(registerBehaviorRecordInfoResponse -> RegisterBehaviorStatusEnum.SUCCESS.getStatus().equals(registerBehaviorRecordInfoResponse.getStatus()) && "1".equals(registerBehaviorRecordInfoResponse.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(registerBehaviorRecordInfoResponses)) {
            return true;
        }
        AtomicBoolean flag = new AtomicBoolean(false);
        // 成功之后还需要调用报备
        Map<String, String> ipMacAddressMap = CommonHelper.getIpMacAddressMap();
        if (MapUtils.isNotEmpty(ipMacAddressMap)) {
            ipMacAddressMap.forEach((ip, macAddress) -> {
                if (flag.get()) {
                    return;
                }
                if (StringUtils.isNotEmpty(macAddress)) {
                    RegisterBehaviorRecordInfoRequest request = new RegisterBehaviorRecordInfoRequest(brandModule.getPartnerId());
                    request.setFunctionFlag("1");
                    request.setIpAddress(ip);
                    request.setMemberId(brandMerchantModule.getMemberId());
                    request.setMacAddress(macAddress);
                    request.setOpClickTime(String.valueOf(System.currentTimeMillis()));
                    request.setSigningChannel("2");
                    String registerBehaviorRecordString = vfinanceInterfaceService.invokeService(request);
                    VfinanceBaseResponse registerBehaviorRecord = JSON.parseObject(registerBehaviorRecordString, VfinanceBaseResponse.class);
                    log.info("调用维金登记接口返回结果为：{}", JSON.toJSONString(registerBehaviorRecord));
                    if (VFinanceResponseCodeEnum.FAIL.getCode().equals(registerBehaviorRecord.getSuccess())) {
                        log.warn("调用维金接口失败。{}", registerBehaviorRecord.getErrorMessage());
                        throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), registerBehaviorRecord.getErrorMessage());
                    }
                    flag.set(true);
                }
            });
        }
        return false;
    }

    @Override
    public BrandMerchantModule getBrandMerchantBySqbStoreId(String brandId, String storeId) {
        LambdaQueryWrapper<BrandMerchantDO> brandMerchantLambdaQueryWrapper = new LambdaQueryWrapper<>();
        brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getBrandId, brandId);
        brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getAssociatedSqbStoreId, storeId);
        brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getDeleted, 0);
        brandMerchantLambdaQueryWrapper.orderByDesc(BrandMerchantDO::getCreatedTime);
        List<BrandMerchantDO> brandMerchants = brandMerchantMapper.selectList(brandMerchantLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(brandMerchants)) {
            return BrandMerchantModule.convertByBrandMerchantDO(brandMerchants.get(0));
        }
        return null;
    }
}
