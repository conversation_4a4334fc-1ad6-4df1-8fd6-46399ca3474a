package com.wosai.cua.brand.business.service.domain.tripartite.mybank;

public class MybankConstants {

    public static final String URL_PROTOCOL_HTTP = "http";

    public static final String URL_PROTOCOL_HTTPS = "https";

    public static final String METHOD_POST = "POST";

    public static final String SIGN_TYPE_RSA = "RSA";

    public static final String SIGN_SHA256RSA_ALGORITHMS = "SHA256WithRSA";

    public static final String LOG_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 默认时间格式
     **/
    public static final String DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    /**
     * Date默认时区
     **/
    public static final String DATE_TIMEZONE = "UTC+8";

    /**
     * UTF-8字符集
     **/
    public static final String CHARSET_UTF8 = "UTF-8";

    /**
     * XML 格式
     */
    public static final String FORMAT_XML = "XML";

    /**
     * 空字符串
     **/
    public static final String NULL_STRING = "";

    /**
     * 连接符号
     **/
    public static final String CONNECT_SYMBOL_STRING = "-";

    /**
     * 与符号
     **/
    public static final String AND_SYMBOL = "&";

    /**
     * 等于符号
     **/
    public static final String EQUAL_SYMBOL = "=";

    /**
     * 分号
     **/
    public static final String SEMI_COLON = ";";

    /**
     * 分号
     **/
    public static final String COLON = ":";

    /**
     * charset
     **/
    public static final String CHARSET = "charset";

    /**
     * xml报文头部request
     **/
    public static final String REQUEST = "request";

    /**
     * xml报文头部request
     **/
    public static final String RESPONSE = "response";

    /**
     * 签名
     **/
    public static final String SIGNATURE = "Signature";

    /**
     * Secure Sockets Layer 安全套接字协议
     **/
    public static final String SSL = "SSL";

    /**
     * 安全传输层协议
     **/
    public static final String TLS = "TLS";

    /**
     * Java Secure Socket Extension
     **/
    public static final String SUN_JSSE = "SunJSSE";

    /**
     * https
     **/
    public static final String HTTPS = "https";

    /**
     * https
     **/
    public static final int HTTPS_DEFAULT_PORT = 443;

    /**
     * Cookie
     **/
    public static final String COOKIE = "Cookie";

    /**
     * Content-Type
     **/
    public static final String CONTENT_TYPE = "Content-Type";

    /**
     * Content-Length
     **/
    public static final String CONTENT_LENGTH = "Content-Length";

    /**
     * application/xml;charset=UTF-8
     **/
    public static final String APPLICATION_XML = "application/xml;charset=UTF-8";

    /**
     * xml解析器忽略符号
     **/
    public static final String XML_IGNORE_SYMBOL = "<![CDATA[]]>";

    /**
     * xml解析器忽略符号前缀
     **/
    public static final String XML_IGNORE_SYMBOL_PREFIX = "<![CDATA[";

    /**
     * xml解析器忽略符号后缀
     **/
    public static final String XML_IGNORE_SYMBOL_SUFFIX = "]]>";

    public static final String PREPARE_TIME = "prepareTime";

    public static final String REQUEST_TIME = "requestTime";

    public static final String RSP_CONTENT = "rspContent";

    public static final String REQUEST_COST_TIME = "requestCostTime";

    public static final String HEAD = "head";

    public static final String APPID = "Appid";

    public static final String FUNCTION = "Function";

    public static final String OK = "OK";

    public static final String FAIL = "FAIL";

    public static final String APP_ID = "AppId";

    public static final String SUCCESS_STRING = "S";
}