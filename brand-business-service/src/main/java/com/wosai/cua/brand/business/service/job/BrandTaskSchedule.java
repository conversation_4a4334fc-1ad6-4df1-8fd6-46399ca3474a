package com.wosai.cua.brand.business.service.job;

import com.alibaba.fastjson2.JSON;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.constants.WorkFlowCommonConstants;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.BrandTaskLogDomainService;
import com.wosai.cua.brand.business.service.excel.handler.ImportExcelHandlerFactory;
import com.wosai.cua.brand.business.service.helper.RedisLockHelper;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.cua.brand.business.service.module.log.TaskContextModule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/8
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BrandTaskSchedule {

    private final BrandTaskLogDomainService brandTaskLogDomainService;
    private final ImportExcelHandlerFactory importExcelHandlerFactory;
    private final CallBackService callBackService;
    private final RedissonClient redissonClient;
    private static final String TASK_LOCK_KEY = "brand_task_lock:%s";
    private static final ThreadPoolExecutor TASK_EXECUTOR = new ThreadPoolExecutor(5, 5, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100), new ThreadPoolExecutor.DiscardPolicy());
    private static final List<Integer> EXCEL_TASK_TYPES = Arrays.stream(ExcelImportTypeEnum.values()).map(ExcelImportTypeEnum::getTaskType).collect(Collectors.toList());

    public void brandMerchantImportTask(Long timeLimit, int size) {
        LocalDateTime localDateTime = LocalDateTime.now().minusSeconds(timeLimit);
        List<BrandTaskLogModule> brandTaskLogModules = brandTaskLogDomainService.queryAuditImportTaskLog(EXCEL_TASK_TYPES, localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), size);
        for (BrandTaskLogModule brandTaskLogModule : brandTaskLogModules) {
            TASK_EXECUTOR.submit(() -> handleImportTask(brandTaskLogModule));
        }
    }

    private void handleImportTask(BrandTaskLogModule brandTaskLogModule) {
        String lockKey = String.format(TASK_LOCK_KEY, brandTaskLogModule.getId());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock()) {
                doHandleImportTask(brandTaskLogModule);
            } else {
                log.info("brand merchant import task is locked, taskId: {}", brandTaskLogModule.getTaskId());
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("failed to release lock for key: {}", lockKey, e);
                }
            }
        }
    }

    private void doHandleImportTask(BrandTaskLogModule brandTaskLogModule) {
        TaskContextModule taskContextModule = JSON.parseObject(brandTaskLogModule.getTaskContext(), TaskContextModule.class);
        try {
            ExcelImportTypeEnum excelImportTypeEnum = ExcelImportTypeEnum.getExcelImportTypeEnumByTaskType(brandTaskLogModule.getTaskType());
            List<BrandSubTaskModule> brandSubTaskModules = brandTaskLogDomainService.queryBrandSubTasksByTaskId(brandTaskLogModule.getTaskId());
            String resultUrl = importExcelHandlerFactory.getHandler(excelImportTypeEnum).doHandleImportTask(brandTaskLogModule, brandSubTaskModules);
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(taskContextModule.getAuditId()))
                    .resultType(Integer.parseInt(WorkFlowCommonConstants.RESULT_SUCCESS))
                    .message("处理成功：" + resultUrl)
                    .build();
            callBackService.addComment(callBackBean);
        } catch (Exception e) {
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(taskContextModule.getAuditId()))
                    .resultType(Integer.parseInt(WorkFlowCommonConstants.RESULT_FAIL))
                    .message("处理失败：" + e.getMessage())
                    .build();
            callBackService.addComment(callBackBean);
        }
    }
}


