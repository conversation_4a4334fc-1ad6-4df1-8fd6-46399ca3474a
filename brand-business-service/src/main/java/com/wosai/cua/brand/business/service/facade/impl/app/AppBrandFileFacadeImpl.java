package com.wosai.cua.brand.business.service.facade.impl.app;

import com.alibaba.fastjson2.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.PageRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppExportBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ExportBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.ImportMerchantTaskResponseDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.app.AppBrandFileFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandFileBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class AppBrandFileFacadeImpl implements AppBrandFileFacade {

    private final BrandBusiness brandBusiness;

    private final BrandFileBusiness brandFileBusiness;

    @Autowired
    public AppBrandFileFacadeImpl(BrandBusiness brandBusiness, BrandFileBusiness brandFileBusiness) {
        this.brandBusiness = brandBusiness;
        this.brandFileBusiness = brandFileBusiness;
    }

    @Override
    public Boolean exportBrandMerchant(AppExportBrandMerchantRequestDTO exportBrandMerchantRequest) {
        List<BrandSimpleInfoDTO> brandInfoList = brandBusiness.getBrandInfoListByMerchantId(exportBrandMerchantRequest.getTokenMerchantId(), false);
        if (CollectionUtils.isEmpty(brandInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        ExportBrandMerchantRequestDTO request = JSON.parseObject(JSON.toJSONString(exportBrandMerchantRequest), ExportBrandMerchantRequestDTO.class);
        request.setBrandId(brandInfoList.get(0).getBrandId());
        List<BrandMerchantDTO> exportBrandMerchants = brandFileBusiness.getExportBrandMerchants(request);
        if (!CollectionUtils.isEmpty(exportBrandMerchants)){
            exportBrandMerchants.forEach(brandMerchantDTO -> brandMerchantDTO.setMeiTuanStoreStatus(brandBusiness.getMeiTuanStoreStatus(request.getBrandId(), brandMerchantDTO.getAssociatedMeituanStoreSn())));
        }
        String fileName = "【" + brandInfoList.get(0).getName() + "】商户列表导出_" + System.currentTimeMillis();
        brandFileBusiness.exportBrandMerchantExcel(fileName, request.getBrandId(), "MSP", exportBrandMerchants);
        return true;
    }

    @Override
    public ImportMerchantTaskResponseDTO getTaskList(AppPageRequestDTO pageRequest) {
        List<BrandSimpleInfoDTO> brandInfoList = brandBusiness.getBrandInfoListByMerchantId(pageRequest.getTokenMerchantId(), false);
        if (CollectionUtils.isEmpty(brandInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        pageRequest.setPlatform("MSP");
        pageRequest.setBrandId(brandInfoList.get(0).getBrandId());
        PageRequestDTO pageRequestDTO = JSON.parseObject(JSON.toJSONString(pageRequest), PageRequestDTO.class);
        return brandFileBusiness.getImportTaskList(pageRequestDTO);
    }
}
