package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户删除接口（CJ004）
 * 接口名称：/invalidAllocateAccount.fuiou
 * 接口说明：当用户是00未生效/01失效/08银行开户失败状态，可调用此接口直接删除。
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
public class InvalidAllocateAccountRequestBody extends BaseRequestBody {
    /**
     * 用户编号
     */
    @Sign
    private String accountIn;


    public InvalidAllocateAccountRequestBody(String mchntCd) {
        super(mchntCd);
    }
}
