package com.wosai.cua.brand.business.service.externalservice.salespoi;

import com.wosai.cua.brand.business.service.externalservice.salespoi.model.PoiDetailQueryResult;
import com.wosai.sales.model.gaoDe.GetPoiDetailByAddressReq;
import com.wosai.sales.model.gaoDe.GetPoiDetailByAddressResp;
import com.wosai.sales.service.goDe.GaoDeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Component
public class SalesPoiClient {

    @Autowired
    private GaoDeService gaoDeService;

    /**
     * 根据地址获取经纬度
     *
     * @param completeAddress 完整地址
     * @return 经纬度信息
     */
    public PoiDetailQueryResult getPoiDetailByCompleteAddress(String completeAddress) {
        GetPoiDetailByAddressReq getPoiDetailByAddressReq = new GetPoiDetailByAddressReq();
        getPoiDetailByAddressReq.setComplete_address(completeAddress);
        getPoiDetailByAddressReq.setBusiness("支付业务");
        getPoiDetailByAddressReq.setScene("进件");
        GetPoiDetailByAddressResp poiDetail = gaoDeService.getPoiDetailByCompleteAddress(getPoiDetailByAddressReq);
        return new PoiDetailQueryResult()
                .setLongitude(poiDetail.getPoiDetails().get(0).getLongitude())
                .setLatitude(poiDetail.getPoiDetails().get(0).getLatitude());
    }
}
