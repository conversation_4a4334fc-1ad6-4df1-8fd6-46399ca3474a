package com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model;

import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@Data
@Accessors(chain = true)
public class AppInfoOpenResult {

    private String appName;

    private String appId;

    private AppInfoStatusEnum status;
}
