package com.wosai.cua.brand.business.service.helper;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.wosai.common.exception.CommonPubBizException;
import lombok.extern.slf4j.Slf4j;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/9/24
 */
@Slf4j
public class JsonRpcClientHelper {


    private static final Map<String, JsonRpcHttpClient> CLIENT_CACHE = new ConcurrentHashMap<>();
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    static {
        // 序列化输出不包含null的属性
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 反序列化时忽略未知属性
        OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    /**
     * 执行远程过程调用(RPC)。
     *
     * @param url        目标服务的URL地址。
     * @param methodName 要调用的方法名。
     * @param request    请求参数对象。
     * @param clazz      期望的返回类型类。
     * @param xEnvFlag   环境标识符，用于指定运行环境。
     * @param <T>        泛型标记，表示返回值类型。
     * @return 返回调用结果，类型由clazz指定。
     * @throws CommonPubBizException 如果调用过程中发生异常。
     */
    public static <T> T doRpcCall(String url, String methodName, Object request, Class<T> clazz, String xEnvFlag) {
        // 获取RPC客户端实例。
        JsonRpcHttpClient client = getClient(url);

        // 初始化请求头，添加环境标识。
        Map<String, String> headers = new HashMap<>();
        headers.put("x-env-flag", xEnvFlag);

        // 初始化参数列表，添加请求对象。
        List<Object> params = new ArrayList<>();
        params.add(request);

        try {
            // 调用远程方法，并返回结果。
            return client.invoke(methodName, params, clazz, headers);
        } catch (Throwable throwable) {
            // 捕获其他异常，并抛出业务异常。
            log.error("调用 {} 接口异常: {}", methodName, throwable.getMessage(), throwable);
            throw new CommonPubBizException(String.format("调用 %s 接口异常: %s", methodName, throwable.getMessage()));
        }
    }

    public static <T> List<T> doRpcCallList(String url, String methodName, Object request, Class<T> clazz, String xEnvFlag) {
        // 获取RPC客户端实例。
        JsonRpcHttpClient client = getClient(url);

        // 初始化请求头，添加环境标识。
        Map<String, String> headers = new HashMap<>();
        headers.put("x-env-flag", xEnvFlag);

        // 初始化参数列表，添加请求对象。
        List<Object> params = new ArrayList<>();
        params.add(request);

        try {
            // 调用远程方法，并返回结果。
            List result = client.invoke(methodName, params, List.class, headers);
            return JSON.parseArray(JSON.toJSONString(result), clazz);
        } catch (Throwable throwable) {
            // 捕获其他异常，并抛出业务异常。
            log.error("调用 {} 接口异常: {}", methodName, throwable.getMessage(), throwable);
            throw new CommonPubBizException(String.format("调用 %s 接口异常: %s", methodName, throwable.getMessage()));
        }
    }

    /**
     * 获取 JSON-RPC 客户端实例。
     *
     * @param url JSON-RPC 服务的 URL
     * @return 初始化好的 JSON-RPC 客户端
     * @throws CommonPubBizException 如果 URL 格式不正确
     */
    private static JsonRpcHttpClient getClient(String url) {
        return CLIENT_CACHE.computeIfAbsent(url, JsonRpcClientHelper::createClient);
    }

    /**
     * 创建 JSON-RPC 客户端实例。
     *
     * @param url JSON-RPC 服务的 URL
     * @return 初始化好的 JSON-RPC 客户端
     * @throws CommonPubBizException 如果 URL 格式不正确
     */
    private static JsonRpcHttpClient createClient(String url) {
        URL serviceUrl;
        try {
            serviceUrl = new URL(url);
        } catch (MalformedURLException e) {
            log.error("无效的 URL: {}", url, e);
            throw new CommonPubBizException(String.format("无效的 URL: %s", url));
        }

        JsonRpcHttpClient client = new JsonRpcHttpClient(OBJECT_MAPPER, serviceUrl, new HashMap<>());
        client.setConnectionTimeoutMillis(10000);
        client.setReadTimeoutMillis(10000);
        return client;
    }
}
