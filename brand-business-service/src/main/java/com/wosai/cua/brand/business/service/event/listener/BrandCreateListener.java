package com.wosai.cua.brand.business.service.event.listener;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.kafka.BrandCreateMsg;
import com.wosai.cua.brand.business.api.enums.MessageTypeEnum;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.Listener;
import com.wosai.cua.brand.business.service.event.model.BrandCreateEvent;
import com.wosai.cua.brand.business.service.event.type.BrandCreateEventType;
import com.wosai.cua.brand.business.service.kafka.BrandBaseKafkaProducer;
import com.wosai.cua.brand.business.service.kafka.helper.AvroBeanHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BrandCreateListener implements Listener<BrandCreateEvent> {


    private final BrandBaseKafkaProducer brandBaseKafkaProducer;

    @Autowired
    public BrandCreateListener(BrandBaseKafkaProducer brandBaseKafkaProducer) {
        this.brandBaseKafkaProducer = brandBaseKafkaProducer;
    }

    @Override
    public EventType getEventType() {
        return BrandCreateEventType.SUCCESS;
    }

    @Override
    public void handle(BrandCreateEvent event) {
        log.info("BrandCreateListener 监听到品牌创建成功的事件：{}",JSON.toJSONString(event));
        brandBaseKafkaProducer.sendMessage(
                brandBaseKafkaProducer.getTopic(),
                AvroBeanHelper.getBaseBrandMsg(
                        JSON.toJSONString(
                                BrandCreateMsg.builder()
                                        .brandId(event.getBrandId())
                                        .brandSn(event.getBrandSn())
                                        .merchantSn(event.getMerchantSn())
                                        .merchantId(event.getMerchantId())
                                        .build()
                        ), MessageTypeEnum.BRAND_CREATE
                )
        );
    }
}
