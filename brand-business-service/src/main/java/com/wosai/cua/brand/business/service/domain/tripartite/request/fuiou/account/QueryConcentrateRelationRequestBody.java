package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "xml")
public class QueryConcentrateRelationRequestBody extends BaseRequestBody {

    @Sign
    private String mchntCdConcentrate;

    @Sign
    private String batchNo;

    /**
     * 查询类型
     * 枚举值：
     * 01 待生效商户
     * 02 已生效商户
     * 03 关系解除商户
     */
    @Sign
    private String type;

    public QueryConcentrateRelationRequestBody(String mchntCd) {
        super(mchntCd);
    }
}
