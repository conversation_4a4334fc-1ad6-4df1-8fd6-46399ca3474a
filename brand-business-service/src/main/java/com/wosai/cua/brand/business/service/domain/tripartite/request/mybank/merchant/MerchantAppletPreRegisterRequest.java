package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.enums.BusinessLicenseEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.XmlUtils;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MyBankRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantAppletPreRegisterRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantDetailModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantAppletPreRegisterResponse;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantCreationRecordModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Objects;
import java.util.UUID;

/**
 * Mybank Api: ant.mybank.bkmerchantprod.merch.applet.pre.register
 */
public class MerchantAppletPreRegisterRequest implements MyBankRequest<MerchantAppletPreRegisterResponse> {

    /**
     * API接口代码
     */
    private final String apiFunction = "ant.mybank.bkmerchantprod.merch.applet.pre.register";

    /**
     * API接口版本
     */
    private String apiVersion = "1.0.0";

    /**
     * 请求是否需要加签 true/false
     * <p>
     * 默认：true
     */
    private boolean needSign = true;

    /**
     * 请求路由分组
     */
    private String cookieValue;

    /**
     * 公共请求参数（head）
     */
    private RequestHead requestHead;

    /**
     * 业务请求参数（body）
     */
    private RequestBody requestBody;

    /**
     * @param appId 应用ID 由浙江网商银行统一分配,用于识别合作伙伴应用系统，即对端系统编号。联调前线下提供
     */
    public MerchantAppletPreRegisterRequest(String appId) {
        this.requestHead = RequestHead.builder(this.apiVersion, appId, appId, this.apiFunction)
                .inputCharset(MybankConstants.CHARSET_UTF8)
                .reqMsgId(UUID.randomUUID().toString().replace(MybankConstants.CONNECT_SYMBOL_STRING, MybankConstants.NULL_STRING))
                .reqTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(MybankConstants.DATE_TIME_FORMAT)))
                .reqTimeZone(MybankConstants.DATE_TIMEZONE)
                .reserve(MybankConstants.NULL_STRING)
                .signType(MybankConstants.SIGN_TYPE_RSA)
                .build();
    }

    public MerchantAppletPreRegisterRequest(RequestHead requestHead, RequestBody requestBody) {
        this.requestHead = requestHead;
        this.requestBody = requestBody;
    }

    @Override
    public String getApiFunction() {
        return this.apiFunction;
    }

    @Override
    public String getApiVersion() {
        return this.apiVersion;
    }

    @Override
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    @Override
    public void setCookieValue(String cookieValue) {
        this.cookieValue = cookieValue;
    }

    @Override
    public String getCookieValue() {
        return this.cookieValue;
    }

    @Override
    public Class<MerchantAppletPreRegisterResponse> getResponseClass() {
        return MerchantAppletPreRegisterResponse.class;
    }

    @Override
    public boolean isNeedSign() {
        return this.needSign;
    }

    @Override
    public void setNeedSign(boolean needSign) {
        this.needSign = needSign;
    }

    @Override
    public RequestHead getRequestHead() {
        return this.requestHead;
    }

    @Override
    public void setRequestHead(RequestHead requestHead) {
        this.requestHead = requestHead;
    }

    @Override
    public RequestBody getRequestBody() {
        return this.requestBody;
    }

    @Override
    public void setRequestBody(RequestBody requestBody) {
        this.requestBody = requestBody;
    }

    @Override
    public String xmlBuild() throws MybankApiException {
        return XmlUtils.requestXmlBuild(this.requestHead, this.requestBody);
    }

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.MY_BANK_MERCHANT_APPLET_PRE_REGISTER;
    }

    public static MerchantAppletPreRegisterRequest buildRequest(MyBankConfigModule configModule, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, BrandMerchantModule brandMerchantModule, Boolean handlerSwitch) {
        MerchantAppletPreRegisterRequest merchAppletPreRegisterRequest = new MerchantAppletPreRegisterRequest(configModule.getAppId());
        MerchantAppletPreRegisterRequestModel merchAppletPreRegisterRequestModel = new MerchantAppletPreRegisterRequestModel();
        merchAppletPreRegisterRequestModel.setIsvOrgId(configModule.getIsvOrgId());
        merchAppletPreRegisterRequestModel.setOutTradeNo(recordModule.getRecordId());
        merchAppletPreRegisterRequestModel.setOutMerchantId(brandMerchantModule.getThreePartyMerchantSn());
        merchAppletPreRegisterRequestModel.setDealType("03");
        MerchantDetailModel merchantDetailModel = new MerchantDetailModel();
        merchantDetailModel.setPrincipalCertType("01");
        merchantDetailModel.setPrincipalPerson(merchantBusinessLicense.getLegal_person_name());
        merchantDetailModel.setContactName(merchant.getContact_name());
        merchantDetailModel.setContactMobile(merchant.getContact_cellphone());
        merchantDetailModel.setPrincipalCertNo(merchantBusinessLicense.getLegal_person_id_number());
        if (Objects.nonNull(brandMerchantModule.getContactInfo()) && Objects.nonNull(handlerSwitch) && Boolean.TRUE.equals(handlerSwitch)){
            merchantDetailModel.setHandlePerson(brandMerchantModule.getContactInfo().getContactName());
            merchantDetailModel.setHandlePersonMobile(brandMerchantModule.getContactInfo().getContactPhone());
            merchantDetailModel.setHandlerPersonCertType("01");
            merchantDetailModel.setHandlePersonCertNo(brandMerchantModule.getContactInfo().getContactId());
        }
        if (BusinessLicenseEnum.MICRO_MERCHANT.getType().equals(merchantBusinessLicense.getType())) {
            merchAppletPreRegisterRequestModel.setMerchantType("01");
            merchAppletPreRegisterRequestModel.setMerchantName(merchantBusinessLicense.getLegal_person_name());
            merchantDetailModel.setPrincipalMobile(merchant.getOwner_cellphone());
        }
        if (BusinessLicenseEnum.INDIVIDUAL_BUSINESS_MERCHANT.getType().equals(merchantBusinessLicense.getType())) {
            merchAppletPreRegisterRequestModel.setMerchantType("02");
            merchAppletPreRegisterRequestModel.setMerchantName(merchant.getName());
            merchantDetailModel.setPrincipalMobile(merchant.getOwner_cellphone());
            merchantDetailModel.setBussAuthType("12");
            merchantDetailModel.setBussAuthNo(merchantBusinessLicense.getNumber());

        }
        if (merchantBusinessLicense.getType().equals(BusinessLicenseEnum.ENTERPRISE_BUSINESS_LICENSE.getType())) {
            merchAppletPreRegisterRequestModel.setMerchantType("03");
            merchAppletPreRegisterRequestModel.setMerchantName(merchant.getName());
            merchantDetailModel.setBussAuthType("12");
            merchantDetailModel.setBussAuthNo(merchantBusinessLicense.getNumber());
        }
        String merchantDetail = new String(Base64.getEncoder().encode(JSON.toJSONString(merchantDetailModel).getBytes()), StandardCharsets.UTF_8);
        merchAppletPreRegisterRequestModel.setMerchantDetail(merchantDetail);
        merchAppletPreRegisterRequest.setRequestBody(merchAppletPreRegisterRequestModel);
        return merchAppletPreRegisterRequest;
    }
}