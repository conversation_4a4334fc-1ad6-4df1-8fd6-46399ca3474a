package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify.MerchantControlNotifyModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 商户管控通知
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class MerchantControlNotifyRequest extends MybankResponse {
    private static final long serialVersionUID = -439238575378547925L;

    private MerchantControlNotify merchantControlNotify;

    public MerchantControlNotify getMerchantControlNotify() {
        return merchantControlNotify;
    }

    public void setMerchantControlNotify(MerchantControlNotify merchantControlNotify) {
        this.merchantControlNotify = merchantControlNotify;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "request")
    public static class MerchantControlNotify extends MybankObject {

        private static final long serialVersionUID = 7319715518939057942L;

        /**
         * 业务应答参数（body）
         */
        @XmlElementRef
        private MerchantControlNotifyModel merchantControlNotifyModel;

        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private RequestHead requestHead;

        public RequestHead getRequestHead() {
            return requestHead;
        }

        public void setRequestHead(RequestHead requestHead) {
            this.requestHead = requestHead;
        }

        public MerchantControlNotifyModel getMerchantControlNotifyModel() {
            return merchantControlNotifyModel;
        }

        public void setMerchantControlNotifyModel(MerchantControlNotifyModel merchantControlNotifyModel) {
            this.merchantControlNotifyModel = merchantControlNotifyModel;
        }
    }
}
