package com.wosai.cua.brand.business.service.helper;

import lombok.Builder;
import lombok.Data;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;

public class RSAKeyGenerator {

    /**
     * 生成RSA2密钥对
     *
     * @return 包含公钥和私钥的字符串数组，数组第一个元素是公钥，第二个元素是私钥
     * @throws NoSuchAlgorithmException 如果不支持RSA算法
     */
    public static RSAKeyValue generateRSA2Keys() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048); // RSA2使用2048位密钥
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        return RSAKeyValue.builder()
                .publicKey(
                        Base64.getEncoder().encodeToString(
                                keyPair.getPublic().getEncoded()
                        )
                )
                .privateKey(
                        Base64.getEncoder().encodeToString(
                                keyPair.getPrivate().getEncoded()
                        )
                )
                .build();
    }

    @Data
    @Builder
    public static class RSAKeyValue {
        private String publicKey;
        private String privateKey;
    }
}