package com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlElement;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryUserResponseData extends CiticResponseData {
    /**
     * 用户编号
     */
    @XmlElement(name = "USER_ID")
    private String userId;
    /**
     * 认证失败原因
     */
    @XmlElement(name = "CHECK_FAIL_REASON")
    private String checkFailReason;
    /**
     * 认证状态
     */
    @XmlElement(name = "USER_CHECK_ST")
    private String userCheckStatus;
    /**
     * 商户编号
     */
    @XmlElement(name = "MCHNT_ID")
    private String merchantId;
    /**
     * 账号名称
     */
    @XmlElement(name = "USER_ID_TYPE")
    private String userName;
    /**
     * 账号状态
     */
    @XmlElement(name = "USER_ST")
    private String userStatus;
    /**
     * 认证级别
     */
    @XmlElement(name = "USER_REGN_LEVL")
    private String userReignLevel;
}
