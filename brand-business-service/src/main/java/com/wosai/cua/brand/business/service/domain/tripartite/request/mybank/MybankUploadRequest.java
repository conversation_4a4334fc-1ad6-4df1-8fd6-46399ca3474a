package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import org.apache.http.HttpEntity;

import java.util.Map;

public interface MybankUploadRequest<T extends MybankResponse> extends DefaultRequest<T> {


    /**
     * 组装待签名参数字符串
     *
     * @param map
     * @return 签名值
     */
    public String uploadRequestSignString(Map<String, Object> map);

    /**
     * model实体转成map
     *
     * @return 实体map
     * @throws MybankApiException
     */
    public Map<String, Object> getMapByModel() throws MybankApiException;

    /**
     * 构建httpEntity对象
     *
     * @param map
     * @return HttpEntity
     * @throws MybankApiException
     */
    public HttpEntity entityBuilder(Map<String, Object> map) throws MybankApiException;
}