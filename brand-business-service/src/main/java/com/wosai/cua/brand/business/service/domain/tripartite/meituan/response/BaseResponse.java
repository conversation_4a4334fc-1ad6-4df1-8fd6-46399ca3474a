package com.wosai.cua.brand.business.service.domain.tripartite.meituan.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class BaseResponse {

    /**
     * data : ng
     * error_list : [{"msg":"未获取有效门店id","code":701}]
     * result_code : 4
     * error : {"msg":"未获取有效门店id","code":701}
     */

    private String data;
    @JSONField(name = "result_code")
    private int resultCode;
    private ErrorBean error;
    @JSONField(name = "error_list")
    private List<ErrorBean> errorList;

    @Data
    public static class ErrorBean {
        /**
         * msg : 未获取有效门店id
         * code : 701
         */

        private String msg;
        private int code;
    }
}
