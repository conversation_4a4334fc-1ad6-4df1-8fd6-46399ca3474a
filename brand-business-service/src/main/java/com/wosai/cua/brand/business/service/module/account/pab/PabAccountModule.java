package com.wosai.cua.brand.business.service.module.account.pab;

import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.pab.PabAccountsDTO;
import com.wosai.cua.brand.business.service.module.account.AccountsModule;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PabAccountModule extends AccountsModule {
    /**
     * 资金汇总专用账名
     */
    private String fundSummaryAccountName;

    /**
     * 资金汇总专用账户
     */
    private String fundSummaryAccount;

    /**
     * 资金归集账户
     */
    private String fundGatherAccount;

    /**
     * 资金结算账户名称
     */
    private String fundSettlementAccountName;
    /**
     * 资金结算账户
     */
    private String fundSettlementAccount;

    public static PabAccountModule convert(PabAccountsDTO pabAccounts) {
        if (pabAccounts == null) {
            return null;
        }
        PabAccountModule pabAccountModule = new PabAccountModule();
        pabAccountModule.setFundSummaryAccount(pabAccounts.getFundSummaryAccount());
        pabAccountModule.setFundSummaryAccountName(pabAccounts.getFundSummaryAccountName());
        pabAccountModule.setFundGatherAccount(pabAccounts.getFundGatherAccount());
        pabAccountModule.setFundSettlementAccount(pabAccounts.getFundSettlementAccount());
        pabAccountModule.setFundSettlementAccountName(pabAccounts.getFundSettlementAccountName());
        return pabAccountModule;
    }

    public static AccountsDTO convertDto(PabAccountModule accountsModule) {
        if (accountsModule == null) {
            return null;
        }
        AccountsDTO accounts = new AccountsDTO();
        PabAccountsDTO pabAccounts = new PabAccountsDTO();
        pabAccounts.setFundSummaryAccountName(accountsModule.getFundSummaryAccountName());
        pabAccounts.setFundSummaryAccount(accountsModule.getFundSummaryAccount());
        pabAccounts.setFundGatherAccount(accountsModule.getFundGatherAccount());
        pabAccounts.setFundSettlementAccountName(accountsModule.getFundSettlementAccountName());
        pabAccounts.setFundSettlementAccount(accountsModule.getFundSettlementAccount());
        accounts.setPabAccounts(pabAccounts);
        return accounts;
    }
}
