package com.wosai.cua.brand.business.service.domain.tripartite.request.citic;

import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantCreationRecordModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@XmlRootElement(name = "ROOT")
@XmlAccessorType(XmlAccessType.FIELD)
public class RegisteredUserRequest extends BaseCiticRequest implements TripartiteSystemCallRequest {

    /**
     * 商户端用户编号
     */
    @XmlElement(name = "MCHNT_USER_ID")
    private String merchantSn;
    /**
     * 用户类型
     */
    @XmlElement(name = "USER_TYPE")
    private String userType;
    /**
     * 用户姓名
     */
    @XmlElement(name = "USER_NM")
    private String userName;
    /**
     * 用户角色
     */
    @XmlElement(name = "USER_ROLE")
    private String userRole;
    /**
     * 签约类型
     */
    @XmlElement(name = "SIGN_TYPE")
    private String signType;
    /**
     * 证件类型
     */
    @XmlElement(name = "USER_ID_TYPE")
    private String userIdType;
    /**
     * 证件号码
     */
    @XmlElement(name = "USER_ID_NO")
    private String userIdNo;
    /**
     * 用户手机号
     */
    @XmlElement(name = "USER_PHONE")
    private String userPhone;
    /**
     * 企业法人姓名
     */
    @XmlElement(name = "CORP_NM")
    private String legalPersonName;
    /**
     * 企业法人证件号码
     */
    @XmlElement(name = "CORP_ID_NO")
    private String legalPersonId;
    /**
     * 企业法人证件类型
     */
    @XmlElement(name = "CORP_ID_TYPE")
    private String legalPersonIdType;
    /**
     * 用户地址
     */
    @XmlElement(name = "USER_ADD")
    private String userAddress;

    /**
     * 签约协议号
     */
    @Setter
    @XmlElement(name = "AGRM_NUM")
    private String signingAgreementNo;

    /**
     * 签约标志
     */
    @XmlElement(name = "SIGCT_FLG")
    private String signingFlag;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.CITIC_REGISTERED_USER;
    }

    public RegisteredUserRequest() {
        super();
        super.setTransCode(REGISTERED_USER_REQUEST_TRANS_CODE);
    }

    public static RegisteredUserRequest build(CiticBankConfigModule configModule, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule,String userType) {
        RegisteredUserRequest registeredUserRequest = new RegisteredUserRequest();
        registeredUserRequest.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + merchant.getSn().substring(merchant.getSn().length() - 8));
        recordModule.setRecordId(registeredUserRequest.getReqSsn());
        registeredUserRequest.setMerchantId(configModule.getMerchantId());
        registeredUserRequest.setMerchantSn(merchant.getSn());
        switch (merchantBusinessLicense.getType()) {
            case 0:
                registeredUserRequest.setUserType("1");
                registeredUserRequest.setUserIdType("01");
                registeredUserRequest.setUserIdNo(merchantBusinessLicense.getLegal_person_id_number());
                registeredUserRequest.setUserPhone(merchant.getContact_cellphone());
                registeredUserRequest.setUserName(merchantBusinessLicense.getLegal_person_name());
                break;
            case 1:
                registeredUserRequest.setUserType("3");
                registeredUserRequest.setUserIdType("03");
                registeredUserRequest.setUserIdNo(merchantBusinessLicense.getNumber());
                registeredUserRequest.setUserPhone(merchant.getOwner_cellphone());
                registeredUserRequest.setUserName(merchantBusinessLicense.getName());
                break;
            case 2:
                registeredUserRequest.setUserType("2");
                registeredUserRequest.setUserIdType("03");
                registeredUserRequest.setUserIdNo(merchantBusinessLicense.getNumber());
                registeredUserRequest.setUserPhone(merchant.getOwner_cellphone());
                registeredUserRequest.setUserName(merchantBusinessLicense.getName());
                break;
            default:
                throw new BrandBusinessException("商户类型错误");
        }
        registeredUserRequest.setUserRole(userType);
        registeredUserRequest.setSignType("00");
        registeredUserRequest.setLegalPersonIdType("01");
        registeredUserRequest.setLegalPersonName(merchantBusinessLicense.getLegal_person_name());
        registeredUserRequest.setLegalPersonId(merchantBusinessLicense.getLegal_person_id_number());
        registeredUserRequest.setUserAddress(merchant.getStreet_address());
        registeredUserRequest.setSigningAgreementNo("A" + merchant.getSn());
        registeredUserRequest.setSigningFlag("1");
        return registeredUserRequest;
    }
}
