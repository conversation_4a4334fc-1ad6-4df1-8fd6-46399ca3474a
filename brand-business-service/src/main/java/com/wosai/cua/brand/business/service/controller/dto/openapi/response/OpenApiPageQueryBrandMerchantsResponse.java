package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class OpenApiPageQueryBrandMerchantsResponse {

    private Integer total;

    private List<QueryBrandMerchant> records;

    @Data
    public static class QueryBrandMerchant {
        @JsonProperty("merchant_sn")
        private String merchantSn;
        @JsonProperty("merchant_name")
        private String merchantName;
        @JsonProperty("out_merchant_no")
        private String outMerchantNo;

        /**
         * 商户联系人
         */
        @JsonProperty("merchant_contact_name")
        private String merchantContactName;

        /**
         * 商户联系人联系方式
         */
        @JsonProperty("merchant_contact_phone")
        private String merchantContactPhone;

        /**
         * 银行卡激活状态
         */
        @JsonProperty("bank_card_activate_status")
        private String bankCardActivateStatus;
        /**
         * 银行卡激活状态描述
         */
        @JsonProperty("bank_card_activate_status_desc")
        private String bankCardActivateStatusDesc;
        /**
         * 商户账户开通状态
         *
         * @see com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum
         */
        @JsonProperty("account_open_status")
        private String accountOpenStatus;

        /**
         * 商户账户开通状态描述
         */
        @JsonProperty("account_open_status_desc")
        private String accountOpenStatusDesc;

        /**
         * 商户账户开通失败原因
         */
        @JsonProperty("account_open_failure_reason")
        private String accountOpenFailureReason;
    }
}
