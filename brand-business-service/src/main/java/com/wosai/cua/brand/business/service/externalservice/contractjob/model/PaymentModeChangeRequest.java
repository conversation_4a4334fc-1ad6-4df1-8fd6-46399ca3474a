package com.wosai.cua.brand.business.service.externalservice.contractjob.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/24
 */
@Data
@Accessors(chain = true)
public class PaymentModeChangeRequest {

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 目标支付模式 2商家模式 3微信品牌模式 4支付宝品牌模式 5微信支付宝品牌模式
     */
    private Integer targetPaymentMode;
    /**
     * 平台类型
     */
    private String platform;
    /**
     * 操作人ID
     */
    private String operatorId;
    /**
     * 备注
     */
    private String remark;
}
