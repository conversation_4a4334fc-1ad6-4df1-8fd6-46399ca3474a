package com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.BaseResponseBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "xml")
public class AddConcentrateRelationResponseBody extends BaseResponseBody {
    /**
     * 被归集商户号
     */
    @Sign
    private String mchntCdConcentrate;

    /**
     * 申请号
     */
    @Sign
    private String batchNo;

    /**
     * 授权链接
     */
    private String checkUrl;
}
