package com.wosai.cua.brand.business.service.domain.service.impl.analyze;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.BankAccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantsAnalyzeService;
import com.wosai.cua.brand.business.service.enums.AccountIdTypeEnum;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.enums.IndividualBusinessExcelFieldEnum;
import com.wosai.cua.brand.business.service.helper.AnalyzeHelper;
import com.wosai.cua.brand.business.service.helper.CryptHelper;
import com.wosai.cua.brand.business.service.helper.FileHelper;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.helper.ThreadLocalHelper;
import com.wosai.cua.brand.business.service.helper.UcUserV2Helper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.BaseMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.CreateMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.IndividualBusinessMerchantAnalyzeModule;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.req.AccountReq;
import com.wosai.mc.model.req.CreateMerchantAndStoreReq;
import com.wosai.mc.model.req.CreateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.CreateStoreBusinessLicenseWithStoreReq;
import com.wosai.mc.model.req.CreateStoreReq;
import com.wosai.mc.model.req.StoreComplete;
import com.wosai.mc.model.resp.CreateMerchantResp;
import com.wosai.mc.model.resp.StoreCompleteResp;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.tools.service.InfoQueryService;
import com.wosai.tools.vo.BankUnionCardReq;
import com.wosai.tools.vo.BankUnionCardVo;
import com.wosai.uc.dto.CreateUcUserReq;
import com.wosai.uc.dto.UcUserInfoResp;
import com.wosai.uc.v2.service.UcUserServiceV2;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import com.wosai.upay.bank.model.bizbankaccount.MerchantBizBankAccount;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 个体工商户解析
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IndividualBusinessMerchantsAnalyzeServiceImpl implements BrandMerchantsAnalyzeService {

    private static final Map<String, Integer> LICENSE_TYPE_MAP = Maps.newConcurrentMap();
    private static final Map<String, Integer> PERSONAL_LICENSE_TYPE_MAP = Maps.newConcurrentMap();

    @Value("${spring.application.biz}")
    private String biz;

    @Autowired
    private UcUserServiceV2 ucUserServiceV2;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private MerchantBizBankAccountService merchantBizBankAccountService;

    @Autowired
    private BrandMapper brandMapper;

    private final ApolloConfig apolloConfig;

    private final BrandConfigDOMapper brandConfigMapper;

    private final CryptHelper cryptHelper;

    private final AnalyzeHelper analyzeHelper;

    private final BrandMerchantMapper brandMerchantMapper;

    @Autowired
    private InfoQueryService infoQueryService;
    /**
     * 字段列序号对应的枚举Map
     */
    private static final Map<Integer, IndividualBusinessExcelFieldEnum> INDIVIDUAL_BUSINESS_EXCEL_FIELD_ENUM_MAP = Maps.newConcurrentMap();
    /**
     * 字段对应的实体类属性缓存
     */
    private static final Map<IndividualBusinessExcelFieldEnum, Field> FIELD_CACHE = Maps.newHashMap();

    /**
     * 字段名对应的枚举Map
     */
    private static final Map<String, IndividualBusinessExcelFieldEnum> FIELD_NAME_EXCEL_FIELD_ENUM_MAP = Maps.newHashMap();

    @Autowired
    public IndividualBusinessMerchantsAnalyzeServiceImpl(ApolloConfig apolloConfig, BrandConfigDOMapper brandConfigMapper, CryptHelper cryptHelper, AnalyzeHelper analyzeHelper, BrandMerchantMapper brandMerchantMapper) {
        this.apolloConfig = apolloConfig;
        this.brandConfigMapper = brandConfigMapper;
        this.cryptHelper = cryptHelper;
        this.analyzeHelper = analyzeHelper;
        this.brandMerchantMapper = brandMerchantMapper;
    }

    @PostConstruct
    public void initMap() {
        LICENSE_TYPE_MAP.put("统一社会信用代码", 1);
        PERSONAL_LICENSE_TYPE_MAP.put("身份证", 1);
        PERSONAL_LICENSE_TYPE_MAP.put("外国护照", 2);
        PERSONAL_LICENSE_TYPE_MAP.put("港澳通行证", 4);
        PERSONAL_LICENSE_TYPE_MAP.put("台胞证", 3);
        INDIVIDUAL_BUSINESS_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(IndividualBusinessExcelFieldEnum.values()).collect(Collectors.toMap(IndividualBusinessExcelFieldEnum::getColumnNo, Function.identity())));
        FIELD_NAME_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(IndividualBusinessExcelFieldEnum.values()).collect(Collectors.toMap(IndividualBusinessExcelFieldEnum::getFieldName, Function.identity())));
        this.cacheFields();
    }

    private void cacheFields() {
        Class<?> clazz = IndividualBusinessMerchantAnalyzeModule.class;
        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                String fieldName = field.getName();
                IndividualBusinessExcelFieldEnum individualBusinessExcelFieldEnum = FIELD_NAME_EXCEL_FIELD_ENUM_MAP.get(fieldName);
                if (Objects.isNull(individualBusinessExcelFieldEnum)) {
                    continue;
                }
                FIELD_CACHE.put(individualBusinessExcelFieldEnum, field);
            }
            clazz = clazz.getSuperclass();
        }
    }

    private Field getField(IndividualBusinessExcelFieldEnum fieldEnum) throws NoSuchFieldException {
        if (FIELD_CACHE.containsKey(fieldEnum)) {
            return FIELD_CACHE.get(fieldEnum);
        }
        throw new NoSuchFieldException();
    }

    @Override
    public BrandImportSheetEnum getSheetEnum() {
        return BrandImportSheetEnum.INDIVIDUAL_BUSINESS;
    }

    @Override
    public List<BaseMerchantAnalyzeModule> analyzeData(List<String[]> fields, FundManagementCompanyEnum fundManagementCompanyCode) {
        List<BaseMerchantAnalyzeModule> individualBusinessMerchantAnalyzeModules = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fields)) {
            return individualBusinessMerchantAnalyzeModules;
        }
        // 处理数据头三行为无效数据从第三行开始
        for (int row = 4; row < fields.size(); row++) {
            CreateMerchantAnalyzeModule analyzeModule = this.analyze(row, fields.get(row));
            if (Objects.nonNull(analyzeModule)) {
                analyzeModule.setFundManagementCompanyCode(fundManagementCompanyCode);
                individualBusinessMerchantAnalyzeModules.add(analyzeModule);
            }
        }
        return individualBusinessMerchantAnalyzeModules;
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantModuleList(String brandId, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules, Long strategyId) {
        List<BrandMerchantModule> brandMerchantModules = Lists.newArrayList();
        BrandDO brandDO = brandMapper.selectBrandByBrandId(brandId);
        if (Objects.isNull(brandDO)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND.getCode(), "未查到品牌信息！");
        }
        MerchantInfo adminMerchant = merchantService.getMerchantBySn(brandDO.getMerchantSn(), null);
        if (Objects.isNull(adminMerchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT.getCode(), "未查到商户！");
        }
        BrandConfigDO brandConfig = brandConfigMapper.selectOne(BrandMerchantsAnalyzeService.getBrandIdQueryWrapper(brandId));
        boolean allowLogin = Objects.isNull(brandConfig) || StringUtils.isBlank(brandConfig.getConfig());
        boolean needCreateStore;
        if (Objects.nonNull(brandConfig) && StringUtils.isNotBlank(brandConfig.getConfig())) {
            ConfigModule configModule = JSON.parseObject(brandConfig.getConfig(), ConfigModule.class);
            allowLogin = Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin();
            needCreateStore = Objects.nonNull(configModule.getNeedCreateStore()) && configModule.getNeedCreateStore();
        } else {
            needCreateStore = false;
        }
        boolean needSendSms = allowLogin;
        // 创建商户的完整逻辑
        merchantAnalyzeModules.forEach(baseMerchantAnalyzeModule -> {
            if (Objects.isNull(baseMerchantAnalyzeModule)) {
                return;
            }
            IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule = (IndividualBusinessMerchantAnalyzeModule) baseMerchantAnalyzeModule;
            // 先判断是否已经存在该手机号的账号
            String ucUserId = ucUserServiceV2.getUcUserIdByIdentifier(UcUserV2Helper.identifierReq(individualBusinessMerchantAnalyzeModule.getContactPhone()));
            try {
                // 判断下非平安账户校验下证件号是否是身份证
                if (!FundManagementCompanyEnum.PAB.equals(baseMerchantAnalyzeModule.getFundManagementCompanyCode()) && individualBusinessMerchantAnalyzeModule.getLegalPersonLicenseType() != 1) {
                    throw new BrandBusinessException("资管机构非平安银行的个体工商户法人证件暂不支持除身份证以外的证件。");
                }
                // 校验第三方门店编号是否重复
                BrandMerchantsAnalyzeService.checkThirdStoreSn(baseMerchantAnalyzeModule, brandMerchantMapper);
                // 如果没有账号的话先创建ucUser
                if (StringUtils.isEmpty(ucUserId)) {
                    CreateUcUserReq createUcUserReq = new CreateUcUserReq();
                    createUcUserReq.setIdentifier(individualBusinessMerchantAnalyzeModule.getContactPhone());
                    createUcUserReq.setIdentity_type(1);
                    createUcUserReq.setApp("trade");
                    createUcUserReq.setPassword("123456");
                    createUcUserReq.setStatus(-1);
                    UcUserInfoResp ucUser = ucUserServiceV2.createUcUser(UcUserV2Helper.build(createUcUserReq));
                    ucUserId = ucUser.getId();
                }
                if (!needCreateStore) {
                    StoreInfo storeInfo = null;
                    if (StringUtils.isNotEmpty(individualBusinessMerchantAnalyzeModule.getStoreSn())) {
                        storeInfo = storeService.getStoreBySn(individualBusinessMerchantAnalyzeModule.getStoreSn(), null);
                        if (Objects.isNull(storeInfo) && !apolloConfig.getStoreSnCheckWhiteList().contains(brandId)) {
                            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_STORE);
                        }
                        if (Objects.nonNull(storeInfo) && !storeInfo.getMerchant_id().equals(adminMerchant.getId())) {
                            throw new BrandBusinessException(BrandBusinessExceptionEnum.STORE_NOT_BELONG_BRAND);
                        }
                    }
                    LambdaQueryWrapper<BrandMerchantDO> brandMerchantQueryWrapper = new LambdaQueryWrapper<>();
                    brandMerchantQueryWrapper.eq(BrandMerchantDO::getAssociatedSqbStoreId, storeInfo.getId());
                    brandMerchantQueryWrapper.eq(BrandMerchantDO::getDeleted, 0);
                    List<BrandMerchantDO> brandMerchants = brandMerchantMapper.selectList(brandMerchantQueryWrapper);
                    if (CollectionUtils.isNotEmpty(brandMerchants)) {
                        throw new BrandBusinessException(BrandBusinessExceptionEnum.STORE_WAS_BIND_BRAND);
                    }
                    // 创建商户&商户下门店
                    CreateMerchantAndStoreReq merchantComplete = this.getMerchantComplete(individualBusinessMerchantAnalyzeModule, ucUserId, storeInfo);
                    merchantComplete.setNeedSendSms(needSendSms);
                    CreateMerchantResp createMerchantResp = merchantService.createMerchantAndStore(merchantComplete);
                    // 创建管理商户下的门店
                    BrandMerchantModule brandMerchantModule = this.getBrandMerchantModule(brandId, ucUserId, createMerchantResp, individualBusinessMerchantAnalyzeModule, storeInfo, strategyId);
                    if (Objects.isNull(brandMerchantModule)) {
                        return;
                    }
                    individualBusinessMerchantAnalyzeModule.setMerchantId(createMerchantResp.getMerchantId());
                    brandMerchantModules.add(brandMerchantModule);
                } else {
                    StoreComplete storeComplete = this.getStoreComplete(adminMerchant.getId(), individualBusinessMerchantAnalyzeModule);
                    StoreCompleteResp storeCompleteResp = storeService.createStoreComplete(storeComplete);
                    StoreInfo storeInfo = storeService.getStoreBySn(storeCompleteResp.getStoreInfo().getSn(), null);
                    CreateMerchantAndStoreReq merchantComplete = this.getMerchantComplete(individualBusinessMerchantAnalyzeModule, ucUserId, storeInfo);
                    merchantComplete.setNeedSendSms(needSendSms);
                    CreateMerchantResp createMerchantResp = merchantService.createMerchantAndStore(merchantComplete);
                    // 创建管理商户下的门店
                    BrandMerchantModule brandMerchantModule = this.getBrandMerchantModule(brandId, ucUserId, createMerchantResp, individualBusinessMerchantAnalyzeModule, storeInfo, strategyId);
                    if (Objects.isNull(brandMerchantModule)) {
                        return;
                    }
                    individualBusinessMerchantAnalyzeModule.setMerchantId(createMerchantResp.getMerchantId());
                    brandMerchantModules.add(brandMerchantModule);
                }
            } catch (Exception e) {
                Map<Integer, String> errorMapMsg = this.getErrorMapMsg();
                if (MapUtils.isEmpty(errorMapMsg)) {
                    errorMapMsg = Maps.newHashMap();
                }
                analyzeHelper.recordErrorMsg(errorMapMsg, baseMerchantAnalyzeModule, e.getMessage());
                ThreadLocalHelper.set(BrandImportSheetEnum.INDIVIDUAL_BUSINESS.name(), errorMapMsg);
            }
        });
        return brandMerchantModules;
    }

    @Override
    public List<BankCardModule> getBankCarModules(String brandId, FundManagementCompanyEnum fundManagementCompanyCode, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules) {
        List<BankCardModule> bankCardModules = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(merchantAnalyzeModules) && Boolean.TRUE.equals(fundManagementCompanyCode.getNeedCreateBankAccount())) {
            merchantAnalyzeModules.forEach(merchantAnalyzeModule -> {
                if (Objects.isNull(merchantAnalyzeModule) || StringUtils.isBlank(merchantAnalyzeModule.getMerchantId())) {
                    return;
                }
                BankUnionCardReq bankUnionCardReq = new BankUnionCardReq();
                bankUnionCardReq.setCardNum(merchantAnalyzeModule.getBankNumber());
                bankUnionCardReq.setPlatform("trade");
                bankUnionCardReq.setBusinessCase("brand_business");
                BankUnionCardVo bankUnionCardVo = null;
                try {
                    bankUnionCardVo = infoQueryService.bankUnionCard(bankUnionCardReq);
                } catch (Exception e) {
                    log.warn("【PersonalMerchantsAnalyzeServiceImpl】获取银行卡信息失败,接口异常",e);
                }
                if (Objects.isNull(bankUnionCardVo) || StringUtils.isBlank(bankUnionCardVo.getCnaps())) {
                    log.info("【PersonalMerchantsAnalyzeServiceImpl】获取银行卡信息失败,bankUnionCardVo为空或者开户行行号为空，{}",merchantAnalyzeModule.getBankNumber());
                    Object o = ThreadLocalHelper.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS.name());
                    Map<Integer, String> errorMap = Maps.newHashMap();
                    if (Objects.nonNull(o)) {
                        errorMap.putAll((Map<Integer, String>) o);
                    }
                    analyzeHelper.recordErrorMsg(errorMap, merchantAnalyzeModule, "获取银行卡信息失败，请检查卡号是否正确");
                    ThreadLocalHelper.set(BrandImportSheetEnum.INDIVIDUAL_BUSINESS.name(), errorMap);
                    merchantAnalyzeModule.setNeedRemove(true);
                    return;
                }
                IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule = (IndividualBusinessMerchantAnalyzeModule) merchantAnalyzeModule;
                individualBusinessMerchantAnalyzeModule.setOpeningNumber(bankUnionCardVo.getCnaps());
                BizBankAccountAddReq req = BrandMerchantsAnalyzeService.getBizBankAccountAddReq(individualBusinessMerchantAnalyzeModule, biz, BankAccountTypeEnum.PERSONAL.getAccountType(), 1);
                MerchantBizBankAccount merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(req);
                BankCardModule bankCardModule = new BankCardModule();
                bankCardModule.setHolder(req.getHolder());
                bankCardModule.setMerchantId(individualBusinessMerchantAnalyzeModule.getMerchantId());
                bankCardModule.setBrandId(brandId);
                bankCardModule.setAccountType(1);
                bankCardModule.setBankCardId(merchantBizBankAccount.getId());
                bankCardModule.setReservedMobileNumber(cryptHelper.encrypt(individualBusinessMerchantAnalyzeModule.getCellPhoneNumber()));
                bankCardModule.setIsDefault(true);
                bankCardModule.setMobile(individualBusinessMerchantAnalyzeModule.getCellPhoneNumber());
                bankCardModule.setBankCardNo(merchantBizBankAccount.getNumber());
                bankCardModule.setOpeningBankNumber(merchantBizBankAccount.getOpening_number());
                bankCardModules.add(bankCardModule);
            });
        }
        return bankCardModules;
    }

    @Override
    public void createErrorMsgIntoExcel(String filePath) {
        Map<Integer, String> errorMapMsg = getErrorMapMsg();
        if (errorMapMsg == null) return;
        FileHelper.createExcelErrorMsg(filePath, BrandImportSheetEnum.INDIVIDUAL_BUSINESS.getSheet(), BrandImportSheetEnum.INDIVIDUAL_BUSINESS.getErrorMsgColNum(), errorMapMsg);
    }

    private Map<Integer, String> getErrorMapMsg() {
        Object o = ThreadLocalHelper.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS.name());
        if (Objects.isNull(o)) {
            return Collections.emptyMap();
        }
        Map errorMap = JSON.parseObject(JSON.toJSONString(o), Map.class);
        HashMap<Integer, String> errorMapMsg = Maps.newHashMap();
        errorMap.forEach((key, value) -> {
            boolean flag = Objects.nonNull(key) && NumberUtils.isCreatable(key.toString()) && Objects.nonNull(value);
            if (flag) {
                errorMapMsg.put(Integer.valueOf(key.toString()), value.toString());
            }
        });
        return errorMapMsg;
    }

    @Override
    public void checkParams(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules) {
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(createMerchantAnalyzeModules)) {
            Iterator<BaseMerchantAnalyzeModule> iterator = createMerchantAnalyzeModules.iterator();
            while (iterator.hasNext()) {
                BaseMerchantAnalyzeModule analyzeModule = iterator.next();
                Class<? extends BaseMerchantAnalyzeModule> analyzeModuleClass = analyzeModule.getClass();
                boolean needRemove = analyzeHelper.checkFields(analyzeModuleClass, analyzeModule, errorMap, BrandImportSheetEnum.INDIVIDUAL_BUSINESS);
                if (needRemove) {
                    iterator.remove();
                }
            }
            ThreadLocalHelper.set(BrandImportSheetEnum.INDIVIDUAL_BUSINESS.name(), errorMap);
        }
    }

    private CreateMerchantAnalyzeModule analyze(int row, String[] fieldArray) {
        IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule = new IndividualBusinessMerchantAnalyzeModule();
        individualBusinessMerchantAnalyzeModule.setRow(row);
        for (int i = 0; i < fieldArray.length; i++) {
            IndividualBusinessExcelFieldEnum individualBusinessExcelFieldEnum = INDIVIDUAL_BUSINESS_EXCEL_FIELD_ENUM_MAP.get(i);
            if (Objects.isNull(individualBusinessExcelFieldEnum)) {
                continue;
            }
            try {
                Field field = this.getField(individualBusinessExcelFieldEnum);
                this.analyzeField(individualBusinessExcelFieldEnum, field, individualBusinessMerchantAnalyzeModule, fieldArray[i]);
            } catch (NoSuchFieldException e) {
                log.warn("解析excel未找到相应字段：{}", individualBusinessExcelFieldEnum.getFieldName());
                return null;
            } catch (IllegalAccessException e) {
                log.warn("解析excel字段异常", e);
                return null;
            } catch (BrandBusinessException bx) {
                return null;
            }
        }
        return individualBusinessMerchantAnalyzeModule;
    }

    private void analyzeField(
            IndividualBusinessExcelFieldEnum individualBusinessExcelFieldEnum,
            Field field,
            IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule,
            String fieldArray
    ) throws IllegalAccessException {
        String value = fieldArray;
        if (StringUtils.isBlank(value)) {
            return;
        }
        value = StringUtils.trim(value);
        field.setAccessible(true);
        if (individualBusinessExcelFieldEnum.equals(IndividualBusinessExcelFieldEnum.LEGAL_PERSON_LICENSE_TYPE)) {
            field.set(individualBusinessMerchantAnalyzeModule, PERSONAL_LICENSE_TYPE_MAP.get(value));
            return;
        }
        if (individualBusinessExcelFieldEnum.equals(IndividualBusinessExcelFieldEnum.LICENSE_TYPE)) {
            field.set(individualBusinessMerchantAnalyzeModule, LICENSE_TYPE_MAP.get(value));
            return;
        }
        if (individualBusinessExcelFieldEnum.equals(IndividualBusinessExcelFieldEnum.BRAND_MERCHANT_TYPE)) {
            value = MerchantTypeEnum.getMerchantTypeByDesc(value);
        }
        field.set(individualBusinessMerchantAnalyzeModule, value);
    }

    private CreateMerchantAndStoreReq getMerchantComplete(IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule, String ucUserId, StoreInfo storeInfo) {
        CreateMerchantAndStoreReq merchantComplete = new CreateMerchantAndStoreReq();
        // 创建商户
        CreateMerchantReq merchantReq = new CreateMerchantReq();
        merchantReq.setId(UUID.randomUUID().toString());
        merchantReq.setName(individualBusinessMerchantAnalyzeModule.getMerchantName());
        merchantReq.setBusinessName(individualBusinessMerchantAnalyzeModule.getMerchantName());
        merchantReq.setContactName(individualBusinessMerchantAnalyzeModule.getContactName());
        merchantReq.setContactCellphone(individualBusinessMerchantAnalyzeModule.getContactPhone());
        merchantReq.setContactEmail(individualBusinessMerchantAnalyzeModule.getContactEmail());
        merchantReq.setStreetAddress(individualBusinessMerchantAnalyzeModule.getAddress());
        merchantReq.setProvince(individualBusinessMerchantAnalyzeModule.getProvince());
        merchantReq.setCity(individualBusinessMerchantAnalyzeModule.getCity());
        merchantReq.setDistrict(individualBusinessMerchantAnalyzeModule.getDistrict());
        merchantComplete.setMerchant(merchantReq);
        CreateMerchantBusinessLicenseReq license = this.getCreateMerchantBusinessLicenseReq(individualBusinessMerchantAnalyzeModule, merchantReq);
        merchantComplete.setLicense(license);
        AccountReq account = this.getAccountReq(individualBusinessMerchantAnalyzeModule, ucUserId);
        merchantComplete.setAccount(account);
        CreateStoreReq store = JSON.parseObject(JSON.toJSONString(storeInfo), CreateStoreReq.class);
        store.setMerchantId(merchantReq.getId());
        store.setId(UUID.randomUUID().toString());
        merchantComplete.setStore(store);
        return merchantComplete;
    }

    private AccountReq getAccountReq(IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule, String ucUserId) {
        AccountReq account = new AccountReq();
        account.setIdentifier(individualBusinessMerchantAnalyzeModule.getContactPhone());
        account.setIdentityType(1);
        account.setUcUserId(ucUserId);
        account.setName(individualBusinessMerchantAnalyzeModule.getContactName());
        account.setIdType(AccountIdTypeEnum.getAccountIdTypeByAnalyzeIdType(individualBusinessMerchantAnalyzeModule.getLegalPersonLicenseType()));
        account.setIdNumber(individualBusinessMerchantAnalyzeModule.getLegalPersonLicenseNumber());
        account.setIdCardFrontPhoto("https://images.wosaimg.com/c9/8fb1c032d51d7ed1539d6d54d014400ebb601c.png");
        account.setIdCardValidity("********-********");
        return account;
    }

    private CreateMerchantBusinessLicenseReq getCreateMerchantBusinessLicenseReq(IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule, CreateMerchantReq merchantReq) {
        CreateMerchantBusinessLicenseReq license = new CreateMerchantBusinessLicenseReq();
        license.setMerchantId(merchantReq.getId());
        license.setType(individualBusinessMerchantAnalyzeModule.getLicenseType());
        license.setNumber(individualBusinessMerchantAnalyzeModule.getLicenseNumber());
        license.setLegalPersonName(individualBusinessMerchantAnalyzeModule.getLegalPersonName());
        license.setLegalPersonIdType(individualBusinessMerchantAnalyzeModule.getLegalPersonLicenseType());
        license.setLegalPersonIdNumber(individualBusinessMerchantAnalyzeModule.getLegalPersonLicenseNumber());
        license.setName(individualBusinessMerchantAnalyzeModule.getMerchantName());
        return license;
    }

    private BrandMerchantModule getBrandMerchantModule(String brandId, String ucUserId, CreateMerchantResp createMerchantResp, IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule, StoreInfo storeInfo, Long strategyId) {
        if (StringUtils.isEmpty(brandId) || Objects.isNull(createMerchantResp) || Objects.isNull(individualBusinessMerchantAnalyzeModule)) {
            log.info("【IndividualBusinessMerchantsAnalyzeServiceImpl】创建品牌商户失败,缺少参数");
            return null;
        }
        BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
        brandMerchantModule.setMerchantSn(createMerchantResp.getMerchantSn());
        brandMerchantModule.setMerchantId(createMerchantResp.getMerchantId());
        brandMerchantModule.setMerchantName(individualBusinessMerchantAnalyzeModule.getMerchantName());
        brandMerchantModule.setBrandId(brandId);
        brandMerchantModule.setMerchantType(individualBusinessMerchantAnalyzeModule.getBrandMerchantType());
        if (Objects.nonNull(storeInfo)) {
            brandMerchantModule.setAssociatedSqbStoreId(storeInfo.getId());
            brandMerchantModule.setSqbStoreSn(storeInfo.getSn());
        }
        brandMerchantModule.setAssociatedMeituanStoreSn(individualBusinessMerchantAnalyzeModule.getMeiTuanStoreSn());
        brandMerchantModule.setAssociatedElmStoreSn(individualBusinessMerchantAnalyzeModule.getElmStoreSn());
        brandMerchantModule.setDyStoreSn(individualBusinessMerchantAnalyzeModule.getDyStoreSn());
        brandMerchantModule.setStrategyId(strategyId);
        brandMerchantModule.setType(BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType());
        brandMerchantModule.setOutMerchantNo(individualBusinessMerchantAnalyzeModule.getOutMerchantNo());
        return brandMerchantModule;
    }

    private StoreComplete getStoreComplete(String merchantId, IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule) {
        StoreComplete storeComplete = new StoreComplete();
        CreateStoreReq createStoreReq = new CreateStoreReq();
        createStoreReq.setId(UUID.randomUUID().toString());
        createStoreReq.setMerchantId(merchantId);
        createStoreReq.setName(individualBusinessMerchantAnalyzeModule.getMerchantName());
        createStoreReq.setContactCellphone(individualBusinessMerchantAnalyzeModule.getContactPhone());
        createStoreReq.setContactName(individualBusinessMerchantAnalyzeModule.getContactName());
        createStoreReq.setContactPhone(individualBusinessMerchantAnalyzeModule.getContactPhone());
        createStoreReq.setContactEmail(individualBusinessMerchantAnalyzeModule.getContactEmail());
        createStoreReq.setStreetAddress(individualBusinessMerchantAnalyzeModule.getAddress());
        CreateStoreBusinessLicenseWithStoreReq storeBusinessLicenseReq = this.getCreateStoreBusinessLicenseWithStore(merchantId, individualBusinessMerchantAnalyzeModule);
        storeComplete.setCreateStoreReq(createStoreReq);
        storeComplete.setStoreBusinessLicenseReq(storeBusinessLicenseReq);
        return storeComplete;
    }

    private CreateStoreBusinessLicenseWithStoreReq getCreateStoreBusinessLicenseWithStore(String merchantId, IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule) {
        CreateStoreBusinessLicenseWithStoreReq storeBusinessLicenseReq = new CreateStoreBusinessLicenseWithStoreReq();
        storeBusinessLicenseReq.setMerchantId(merchantId);
        storeBusinessLicenseReq.setType(individualBusinessMerchantAnalyzeModule.getLicenseType());
        storeBusinessLicenseReq.setNumber(individualBusinessMerchantAnalyzeModule.getLicenseNumber());
        storeBusinessLicenseReq.setLegalPersonName(individualBusinessMerchantAnalyzeModule.getLegalPersonName());
        storeBusinessLicenseReq.setLegalPersonIdType(individualBusinessMerchantAnalyzeModule.getLegalPersonLicenseType());
        storeBusinessLicenseReq.setLegalPersonIdNumber(individualBusinessMerchantAnalyzeModule.getLegalPersonLicenseNumber());
        return storeBusinessLicenseReq;
    }

    @Override
    public void checkIsValidChineseID(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules) {
        Object o = ThreadLocalHelper.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS.name());
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (Objects.nonNull(o)) {
            errorMap.putAll((Map<Integer, String>) o);
        }
        if (CollectionUtils.isNotEmpty(createMerchantAnalyzeModules)) {
            Iterator<BaseMerchantAnalyzeModule> iterator = createMerchantAnalyzeModules.iterator();
            while (iterator.hasNext()) {
                IndividualBusinessMerchantAnalyzeModule analyzeModule = (IndividualBusinessMerchantAnalyzeModule) iterator.next();
                boolean isValid = true;
                if (analyzeModule.getLegalPersonLicenseType() == 1) {
                    isValid = ParamsCheckHelper.isValidChineseID(analyzeModule.getLegalPersonLicenseNumber());
                    analyzeHelper.recordErrorMsg(errorMap, analyzeModule, "法人身份证号不合法");
                }
                if (!isValid) {
                    iterator.remove();
                }
            }
        }
        ThreadLocalHelper.set(BrandImportSheetEnum.INDIVIDUAL_BUSINESS.name(), errorMap);
    }
}
