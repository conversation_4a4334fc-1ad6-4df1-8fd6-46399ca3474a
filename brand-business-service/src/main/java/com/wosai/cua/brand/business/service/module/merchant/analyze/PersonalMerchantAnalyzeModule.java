package com.wosai.cua.brand.business.service.module.merchant.analyze;

import com.wosai.cua.brand.business.service.annotations.AnalyzeFieldCheck;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 个人/小微商户解析模型
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PersonalMerchantAnalyzeModule extends CreateMerchantAnalyzeModule {
    /**
     * 商户名称
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写商户名称！")
    private String merchantName;
    /**
     * 姓名
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写真实姓名！")
    private String name;

    /**
     * 证件类型
     */
    @AnalyzeFieldCheck(needRequired = true,message = "未填写证件类型")
    private Integer idType;
    /**
     * 证件号码
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写证件号码",
            needFormatCheck = true,
            pattern = "[+-]?\\d+(\\.\\d+)?[eE][+-]?\\d+",
            satisfyPattern = false,
            formatCheckMsg = "证件号格式不正确！")
    private String idNumber;
    /**
     * 手机号
     */
    @AnalyzeFieldCheck(
            needRequired = true,
            message = "未填写手机号！",
            needFormatCheck = true,
            pattern = "^(13[0-9]|14[579]|15[012356789]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$",
            formatCheckMsg = "手机号格式不正确！"
    )
    private String cellphone;

    @AnalyzeFieldCheck(
            needRequired = false,
            message = "",
            needFormatCheck = true,
            pattern = "^[0-9A-Za-z_]+([-+.][0-9A-Za-z_]+)*@[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*\\.[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*$",
            formatCheckMsg = "邮箱格式不正确！"
    )
    private String email;

}
