package com.wosai.cua.brand.business.service.module.brand.extra;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandMerchantDockingModeExtraModule {
    /**
     * 归集模式
     */
    private String aggregationModel;

    /**
     * 归集最大比例
     * 如：100% = 10000
     */
    private Integer concentrateScale;

    /**
     * 资金用途
     */
    private String useOfFunds;

    /**
     * 联系方式
     */
    private String mobile;

    /**
     * 批次号
     */
    private String batchNo;
}
