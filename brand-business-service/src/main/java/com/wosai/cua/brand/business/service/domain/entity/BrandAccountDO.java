package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 品牌相关账户汇总表
 * @TableName brand_account
 */
@TableName(value ="brand_account")
@Data
public class BrandAccountDO implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 配置参数
     */
    private String accounts;

    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Date updatedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.service.domain.entity.BrandAccountDO");
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.service.domain.entity.BrandAccountDO");
    }
}