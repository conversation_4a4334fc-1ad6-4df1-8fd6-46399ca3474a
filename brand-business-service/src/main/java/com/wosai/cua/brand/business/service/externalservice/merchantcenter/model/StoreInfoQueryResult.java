package com.wosai.cua.brand.business.service.externalservice.merchantcenter.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/8
 */
@Data
@Accessors(chain = true)
public class StoreInfoQueryResult {

    private String id;
    private String sn;
    private String name;
    private String industry;
    private Integer status;
    private String longitude;
    private String latitude;
    private String province;
    private String city;
    private String district;
    private String streetAddress;
    private String streetAddressDesc;
    private String contactName;
    private String contactPhone;
    private String contactCellphone;
    private String contactEmail;
    private String merchantId;
    private String districtCode;
}
