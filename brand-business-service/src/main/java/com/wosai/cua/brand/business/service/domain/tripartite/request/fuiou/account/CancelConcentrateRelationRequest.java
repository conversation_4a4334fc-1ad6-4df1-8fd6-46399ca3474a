package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

@Data
public class CancelConcentrateRelationRequest implements TripartiteSystemCallRequest {

    public static final String REQUEST_URI = "/cancleConcentrateRelation.fuiou";

    private CancelConcentrateRelationRequestBody body;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_CANCEL_CONCENTRATE_RELATION;
    }
}
