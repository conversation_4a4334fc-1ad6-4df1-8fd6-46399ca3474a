package com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CreateMemberResponse extends VfinanceBaseResponse{
    /**
     * 会员号
     */
    @JSONField(name = "member_id")
    private String memberId;
    /**
     * 平安银行商户子账户
     */
    @JSONField(name = "merchant_account_id")
    private String merchantAccountId;
}
