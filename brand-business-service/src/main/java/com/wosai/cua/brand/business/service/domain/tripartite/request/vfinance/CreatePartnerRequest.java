package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * 创建合作方
 * <AUTHOR>
 */
public class CreatePartnerRequest extends VfinanceBaseRequest {

    /**
     * 银行编号	平安银行PAB 固定值	不可空	PAB
     */
    @JSONField(name = "bank_code")
    private String bankCode;
    /**
     * 商户号 不可空
     */
    @JSONField(name = "mrch_code")
    private String merchantCode;
    /**
     * 商户名称
     */
    @JSONField(name = "tran_web_name")
    private String tranWebName;
    /**
     * 商户网号，不可空
     */
    @JSONField(name = "net_number")
    private String netNumber;
    /**
     *  app_id 银行提供，不可空
     */
    @JSONField(name = "app_id")
    private String appId;
    /**
     * app_secret 银行提供，不可空
     */
    @JSONField(name = "app_secret")
    private String appSecret;
    /**
     * ecif	银行提供, 不可空
     */
    @JSONField(name = "ecif")
    private String eCif;
    /**
     * 统一编号	银行提供，不可空
     */
    @JSONField(name = "org_code")
    private String orgCode;
    /**
     * 资金汇总账号	银行提供，不可空
     */
    @JSONField(name = "fund_summary_acct_no")
    private String fundSummaryAcctNo;
    /**
     * 私钥	银行提供，不可空
     */
    @JSONField(name = "private_key")
    private String privateKey;
    /**
     * 公钥	银行提供,不可空
     */
    @JSONField(name = "public_key")
    private String publicKey;

    /**
     * 扩展信息
     */
    private String extension;

    public CreatePartnerRequest() {
        super();
        super.service = CREATE_PARTNER;
    }

    public CreatePartnerRequest(String partnerId) {
        super();
        super.service = CREATE_PARTNER;
        super.partnerId = partnerId;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getTranWebName() {
        return tranWebName;
    }

    public void setTranWebName(String tranWebName) {
        this.tranWebName = tranWebName;
    }

    public String getNetNumber() {
        return netNumber;
    }

    public void setNetNumber(String netNumber) {
        this.netNumber = netNumber;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String geteCif() {
        return eCif;
    }

    public void seteCif(String eCif) {
        this.eCif = eCif;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getFundSummaryAcctNo() {
        return fundSummaryAcctNo;
    }

    public void setFundSummaryAcctNo(String fundSummaryAcctNo) {
        this.fundSummaryAcctNo = fundSummaryAcctNo;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    @Override
    public String toString() {
        return "CreatePartnerRequest{" +
                "bankCode='" + bankCode + '\'' +
                ", merchantCode='" + merchantCode + '\'' +
                ", tranWebName='" + tranWebName + '\'' +
                ", netNumber='" + netNumber + '\'' +
                ", appId='" + appId + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", eCif='" + eCif + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", fundSummaryAcctNo='" + fundSummaryAcctNo + '\'' +
                ", privateKey='" + privateKey + '\'' +
                ", publicKey='" + publicKey + '\'' +
                ", extension='" + extension + '\'' +
                '}';
    }
}
