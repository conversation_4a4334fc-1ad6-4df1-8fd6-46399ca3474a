package com.wosai.cua.brand.business.service.module.config.fuiou;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.brand.fuiou.FuiouConfigDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.DepositStrategyConfigEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.PurposeOfTransferEnum;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
public class FuiouConfigModule extends ConfigModule {
    /**
     * 公钥
     */
    private String publicKey;
    /**
     * 私钥
     */
    private String privateKey;
    /**
     * 富管家商户号
     */
    private String merchantNo;

    private String fyPrivateKey;

    private String fyPublicKey;

    /**
     * 对接模式
     */
    private String merchantDockingMode;

    private String checkType;

    /**
     * 入金策略
     */
    private String depositStrategy;

    public static BrandConfigDTO convert(FuiouConfigModule configModule, String brandId, Boolean needGetConfig) {
        if (configModule == null) {
            return null;
        }
        BrandConfigDTO brandConfigDTO = new BrandConfigDTO();
        FuiouConfigDTO dto;
        if (Objects.nonNull(needGetConfig) && Boolean.TRUE.equals(needGetConfig)) {
            dto = FuiouConfigDTO.builder()
                    .merchantNo(configModule.getMerchantNo())
                    .privateKey(configModule.getPrivateKey())
                    .publicKey(configModule.getPublicKey())
                    .fyPrivateKey(configModule.getFyPrivateKey())
                    .fyPublicKey(configModule.getFyPublicKey())
                    .merchantDockingMode(BrandMerchantDockingModeEnum.getByCode(configModule.getMerchantDockingMode()))
                    .build();
        } else {
            dto = FuiouConfigDTO.builder()
                    .merchantNo(configModule.getMerchantNo())
                    .merchantDockingMode(BrandMerchantDockingModeEnum.getByCode(configModule.getMerchantDockingMode()))
                    .build();
        }
        dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
        dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
        dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
        dto.setDepositStrategy(DepositStrategyConfigEnum.getByDepositStrategy(configModule.getDepositStrategy()));
        brandConfigDTO.setFuiouConfig(dto);
        brandConfigDTO.setFundManagementCompanyCode(FundManagementCompanyEnum.FUIOU.getFundManagementCompanyCode());
        brandConfigDTO.setBrandId(brandId);
        return brandConfigDTO;
    }

    public static FuiouConfigModule convert(FuiouConfigDTO config) {
        if (config == null) {
            return null;
        }
        FuiouConfigModule module = new FuiouConfigModule();
        module.setMerchantNo(config.getMerchantNo());
        module.setOpenTransfer(config.getOpenTransfer());
        module.setAllowLogin(config.getAllowLogin());
        module.setPublicKey(config.getPublicKey());
        module.setPrivateKey(config.getPrivateKey());
        module.setFyPublicKey(config.getFyPublicKey());
        module.setFyPrivateKey(config.getFyPrivateKey());
        module.setNeedSpecialTreatment(config.getNeedSpecialTreatment());
        if (Objects.nonNull(config.getMerchantDockingMode())) {
            module.setMerchantDockingMode(config.getMerchantDockingMode().getCode());
        } else {
            module.setMerchantDockingMode(BrandMerchantDockingModeEnum.SEPARATE_ACCOUNT.getCode());
        }
        if (Objects.nonNull(config.getDepositStrategy())){
            module.setDepositStrategy(config.getDepositStrategy().getDepositStrategy());
        }else {
            module.setDepositStrategy(DepositStrategyConfigEnum.TRADING_STORE.getDepositStrategy());
        }
        module.setPurposeList(PurposeOfTransferEnum.getCodeListByEnumList(config.getPurposeList()));
        return module;
    }

    public static BrandConfigDTO convert(String s, String brandId, Boolean needGetConfig) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        FuiouConfigModule configModule = JSON.parseObject(s, FuiouConfigModule.class);
        BrandConfigDTO brandConfigDTO = new BrandConfigDTO();
        FuiouConfigDTO dto;
        if (Objects.nonNull(needGetConfig) && Boolean.TRUE.equals(needGetConfig)) {
            dto = FuiouConfigDTO.builder()
                    .merchantNo(configModule.getMerchantNo())
                    .publicKey(configModule.getPublicKey())
                    .privateKey(configModule.getPrivateKey())
                    .fyPrivateKey(configModule.getFyPrivateKey())
                    .fyPublicKey(configModule.getFyPublicKey())
                    .merchantDockingMode(BrandMerchantDockingModeEnum.getByCode(configModule.getMerchantDockingMode()))
                    .build();
        } else {
            dto = FuiouConfigDTO.builder()
                    .merchantNo(configModule.getMerchantNo())
                    .merchantDockingMode(BrandMerchantDockingModeEnum.getByCode(configModule.getMerchantDockingMode()))
                    .build();
        }
        dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
        dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
        dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
        dto.setDepositStrategy(DepositStrategyConfigEnum.getByDepositStrategy(configModule.getDepositStrategy()));
        dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
        brandConfigDTO.setFuiouConfig(dto);
        brandConfigDTO.setFundManagementCompanyCode(FundManagementCompanyEnum.FUIOU.getFundManagementCompanyCode());
        brandConfigDTO.setBrandId(brandId);
        return brandConfigDTO;
    }
}
