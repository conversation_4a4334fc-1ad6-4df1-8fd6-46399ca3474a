package com.wosai.cua.brand.business.service.externalservice.merchantcenter.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Data
@Accessors(chain = true)
public class MerchantInfoQueryResult {

    private String merchantId;
    private String merchantSn;
    private String name;
    private Integer merchantType;
    private String ownerCellphone;
    private String customerPhone;
    private String businessName;
    private String industry;
    private String alias;
    private String ownerName;
    private String contactName;
    private String contactCellphone;
    private String province;
    private String city;
    private String district;
    private String streetAddress;
    private String longitude;
    private String latitude;

}
