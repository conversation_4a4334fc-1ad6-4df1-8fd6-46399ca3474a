package com.wosai.cua.brand.business.service.event.listener;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.account.BkCloudFundsAccountOpenRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.account.BkcloudfundsAccountOpenRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.account.BkCloudFundsAccountOpenResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.BkcloudfundsAccountOpenResponseModel;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.Listener;
import com.wosai.cua.brand.business.service.event.model.MyBankOpenAccountEvent;
import com.wosai.cua.brand.business.service.event.type.MyBankOpenAccountEventType;
import com.wosai.cua.brand.business.service.helper.RedisLockHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 网商银行开户监听
 */
@Slf4j
@Service
public class MyBankOpenAccountListener implements Listener<MyBankOpenAccountEvent> {


    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private TripartiteSystemCallService myBankCallService;

    private final BrandMerchantMapper brandMerchantMapper;

    private final RedisLockHelper redisLockHelper;

    @Autowired
    public MyBankOpenAccountListener(List<TripartiteSystemCallService> tripartiteSystemCallServices, BrandMerchantMapper brandMerchantMapper, RedisLockHelper redisLockHelper) {
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.brandMerchantMapper = brandMerchantMapper;
        this.redisLockHelper = redisLockHelper;
    }

    @PostConstruct
    public void initMyBankCallService() {
        for (TripartiteSystemCallService tripartiteSystemCallService : tripartiteSystemCallServices) {
            if (tripartiteSystemCallService.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.MY_BANK)) {
                myBankCallService = tripartiteSystemCallService;
                break;
            }
        }
    }

    @Override
    public EventType getEventType() {
        return MyBankOpenAccountEventType.SUCCESS;
    }

    @Override
    public void handle(MyBankOpenAccountEvent event) {
        log.info("============网商银行开户监听===========");
        log.info("MyBankOpenAccountListener params：{}", JSON.toJSONString(event));
        String lockKey = String.format(RedisLockHelper.MY_BANK_NOTIFY_MERCHANT_LOCK_KEY, event.getMyBankMerchantId());
        String lockValue = event.getMyBankMerchantId();
        // 尝试获取锁，增加超时时间以避免死锁
        if (!redisLockHelper.tryLock(lockKey, lockValue, 10)) { // 超时时间为10秒
            log.error("Failed to acquire lock for model: {}", JSON.toJSONString(event));
            return; // 无法获取锁则直接返回
        }
        BkCloudFundsAccountOpenRequest request = new BkCloudFundsAccountOpenRequest(event.getMyBankConfigModule().getAppId());
        BkcloudfundsAccountOpenRequestModel accountOpenRequestModel = new BkcloudfundsAccountOpenRequestModel();
        accountOpenRequestModel.setIsvOrgId(event.getMyBankConfigModule().getIsvOrgId());
        accountOpenRequestModel.setMerchantId(event.getMyBankMerchantId());
        accountOpenRequestModel.setAcctType(event.getMyBankAccountType());
        accountOpenRequestModel.setOutTradeNo(UUID.randomUUID().toString().replace(MybankConstants.CONNECT_SYMBOL_STRING, MybankConstants.NULL_STRING));
        request.setRequestBody(accountOpenRequestModel);
        BkCloudFundsAccountOpenResponse response =  myBankCallService.call(request, BkCloudFundsAccountOpenResponse.class, event.getMyBankConfigModule());
        BkcloudfundsAccountOpenResponseModel bkcloudfundsAccountOpenResponseModel = response.getBkcloudfundsAccountOpen().getBkcloudfundsAccountOpenResponseModel();
        if (bkcloudfundsAccountOpenResponseModel.getRespInfo().getResultStatus().equals(MybankConstants.SUCCESS_STRING)){
            LambdaQueryWrapper<BrandMerchantDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BrandMerchantDO::getBrandId, event.getBrandId());
            queryWrapper.eq(BrandMerchantDO::getMerchantSn, event.getMerchantSn());
            queryWrapper.eq(BrandMerchantDO::getDeleted, 0);
            BrandMerchantDO brandMerchant = brandMerchantMapper.selectOne(queryWrapper);
            brandMerchant.setTopUpAccountNo(bkcloudfundsAccountOpenResponseModel.getBankCardNo());
            brandMerchant.setTopUpAccountName(bkcloudfundsAccountOpenResponseModel.getBankCertName());
            brandMerchant.setVersion(brandMerchant.getVersion() + 1);
            brandMerchant.setUpdatedTime(new Date());
            brandMerchantMapper.updateById(brandMerchant);
        }
    }
}
