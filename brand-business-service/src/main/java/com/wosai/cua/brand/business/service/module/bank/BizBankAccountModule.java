package com.wosai.cua.brand.business.service.module.bank;

import com.alibaba.fastjson2.annotation.JSONField;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import lombok.Data;
import lombok.experimental.Accessors;
/**
 * <AUTHOR>
 * @date 2020-09-22
 */
@Data
@Accessors(chain = true)
public class BizBankAccountModule {
    @JSONField(name = "merchant_id")
    private String merchantId;
    //================  以下为银行卡信息  ============

    /**
     * 账户类型  1 个人账户   2 企业账户
     */

    private Integer type;

    /**
     * 银行卡号
     */
    private String number;

    /**
     * 开户行号
     */
    @JSONField(name = "opening_number")
    private String openingNumber;

    /**
     * 账户持有人名称
     */
    private String holder;

    public static BizBankAccountAddReq toReq(BizBankAccountModule module,String biz,boolean isDefault) {
        BizBankAccountAddReq req = new BizBankAccountAddReq();
        req.setMerchant_id(module.getMerchantId());
        req.setBiz(biz);
        req.setType(module.getType());
        req.setNumber(module.getNumber());
        req.setOpening_number(module.getOpeningNumber());
        req.setHolder(module.getHolder());
        req.setSet_default(isDefault);
        return req;
    }
}
