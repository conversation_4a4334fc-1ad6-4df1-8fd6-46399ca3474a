package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.module.account.AccountsModule;

public interface BrandFundManagementCompanyDomainService {

    /**
     * 获取资管机构
     * @return 资管机构枚举
     */
    FundManagementCompanyEnum getFundManagementCompany();

    /**
     * 获取资管机构对应的账户信息
     * @param brandId 品牌ID
     * @return 资管机构对应的账户信息
     */
    AccountsModule getBrandAccountModuleByBrandId(String brandId);
}
