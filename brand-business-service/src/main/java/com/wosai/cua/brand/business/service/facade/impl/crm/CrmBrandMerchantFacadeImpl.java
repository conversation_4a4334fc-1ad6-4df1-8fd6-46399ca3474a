package com.wosai.cua.brand.business.service.facade.impl.crm;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.crm.ModifyBrandMerchantRequest;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.MerchantAccountDTO;
import com.wosai.cua.brand.business.api.dto.response.MerchantBusinessLicenseInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.WithdrawStrategyInfoDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.crm.CrmBrandMerchantFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.business.BrandWithdrawStrategyBusiness;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.withdraw.BrandWithdrawStrategyModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vo.ApiRequestParam;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class CrmBrandMerchantFacadeImpl implements CrmBrandMerchantFacade {

    private final BrandBusiness brandBusiness;

    private final BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness;

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    public CrmBrandMerchantFacadeImpl(BrandBusiness brandBusiness, BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness, BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness) {
        this.brandBusiness = brandBusiness;
        this.brandWithdrawStrategyBusiness = brandWithdrawStrategyBusiness;
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
    }


    @Override
    public BrandMerchantInfoDTO getBrandMerchantInfoMerchantId(QueryBrandMerchantInfoDTO queryBrandMerchantInfo) {
        BrandMerchantModule brandMerchantModule = brandBusiness.getBrandMerchantModuleByBrandIdAndMerchantId(queryBrandMerchantInfo.getBrandId(), queryBrandMerchantInfo.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT);
        }
        List<BrandModule> brandByBrandIds = brandBusiness.getBrandByBrandIds(Lists.newArrayList(queryBrandMerchantInfo.getBrandId()));
        Map<String, BrandModule> brandModuleMap = brandByBrandIds.stream().collect(Collectors.toMap(BrandModule::getBrandId, Function.identity()));
        BrandModule brandModule = brandModuleMap.get(brandMerchantModule.getBrandId());
        BrandWithdrawStrategyModule brandWithdrawStrategyModule = brandWithdrawStrategyBusiness.getBrandWithdrawStrategyModuleByStrategyId(brandMerchantModule.getStrategyId());
        if (Objects.isNull(brandModule)) {
            log.warn("未查到品牌信息。");
            return null;
        }
        // 查询子账号以及充值账户信息
        brandBusiness.getSubAccountInfoAndTopUpAccountInfo(brandMerchantModule, brandModule.getFundManagementCompanyCode());
        // 补充参数
        brandBusiness.supplementaryBrandMerchantParameter(brandMerchantModule);
        // 查默认提现银行卡
        BrandMerchantInfoDTO brandMerchantInfo = JSON.parseObject(JSON.toJSONString(brandMerchantModule), BrandMerchantInfoDTO.class);
        brandMerchantInfo.setBrandSn(brandModule.getSn());
        brandMerchantInfo.setBrandName(brandModule.getName());
        brandMerchantInfo.setMerchantType(brandMerchantModule.getMerchantType());
        brandMerchantInfo.setElmStoreSn(brandMerchantModule.getAssociatedElmStoreSn());
        brandMerchantInfo.setSqbStoreId(brandMerchantModule.getAssociatedSqbStoreId());
        brandMerchantInfo.setSqbStoreSn(brandMerchantModule.getSqbStoreSn());
        brandMerchantInfo.setDyStoreSn(brandMerchantModule.getDyStoreSn());
        brandMerchantInfo.setMeiTuanStoreSn(brandMerchantModule.getAssociatedMeituanStoreSn());
        brandMerchantInfo.setMeiTuanStoreStatus(brandBusiness.getMeiTuanStoreStatus(brandModule.getBrandId(), brandMerchantModule.getAssociatedMeituanStoreSn()));
        brandMerchantInfo.setConfig(brandBusiness.getBrandConfig(brandModule, queryBrandMerchantInfo.getNeedGetConfig()));
        MerchantAccountDTO merchantAccount = new MerchantAccountDTO();
        merchantAccount.setMemberId(brandMerchantModule.getMemberId());
        merchantAccount.setSubAccountName(brandMerchantModule.getSubAccountName());
        merchantAccount.setTopUpAccountName(brandMerchantModule.getTopUpAccountName());
        merchantAccount.setSubAccount(brandMerchantModule.getSubAccountNo());
        merchantAccount.setTopUpAccountNo(brandMerchantModule.getTopUpAccountNo());
        brandMerchantInfo.setMerchantAccount(merchantAccount);
        if (Objects.nonNull(brandWithdrawStrategyModule)) {
            WithdrawStrategyInfoDTO withdrawStrategyInfo = JSON.parseObject(JSON.toJSONString(brandWithdrawStrategyModule), WithdrawStrategyInfoDTO.class);
            if (Objects.nonNull(brandWithdrawStrategyModule.getReservedAmount()) && brandWithdrawStrategyModule.getReservedAmount() > 0) {
                withdrawStrategyInfo.setReservedAmount(new BigDecimal(brandWithdrawStrategyModule.getReservedAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            if (Objects.nonNull(brandWithdrawStrategyModule.getMinWithdrawalAmount()) && brandWithdrawStrategyModule.getMinWithdrawalAmount() > 0) {
                withdrawStrategyInfo.setMinWithdrawalAmount(new BigDecimal(brandWithdrawStrategyModule.getMinWithdrawalAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            brandMerchantInfo.setWithdrawStrategyInfo(withdrawStrategyInfo);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = brandMerchantModule.getMerchantBusinessLicense();
        if (Objects.nonNull(merchantBusinessLicense) && Objects.nonNull(merchantBusinessLicense.getType())) {
            brandMerchantInfo.setMerchantBusinessLicenseInfo(
                    new MerchantBusinessLicenseInfoDTO(
                            merchantBusinessLicense.getType() == 0 ? merchantBusinessLicense.getLegal_person_name() : merchantBusinessLicense.getName(),
                            merchantBusinessLicense.getLegal_person_name(),
                            merchantBusinessLicense.getType(),
                            merchantBusinessLicense.getType() == 0 ? merchantBusinessLicense.getLegal_person_id_number() : merchantBusinessLicense.getNumber(),
                            merchantBusinessLicense.getLegal_person_id_number(),
                            merchantBusinessLicense.getLegal_person_id_type()
                    )
            );
        }
        // 获取提现银行卡
        brandMerchantInfo.setBrandMerchantBankCard(brandMerchantBankAccountBusiness.getDefaultBankCard(brandModule.getBrandId(), brandMerchantModule.getMerchantId()));
        brandMerchantInfo.setAccountOpenStatusDesc(BrandMerchantAccountOpenStatusEnum.getStatusDescription(brandMerchantInfo.getAccountOpenStatus()));
        return brandMerchantInfo;
    }

    @Override
    public void modifyBrandMerchantInfo(ApiRequestParam<ModifyBrandMerchantRequest, Map> apiRequestParam) {

    }
}
