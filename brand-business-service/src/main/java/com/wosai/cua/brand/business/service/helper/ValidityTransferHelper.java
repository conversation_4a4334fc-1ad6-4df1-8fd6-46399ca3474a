package com.wosai.cua.brand.business.service.helper;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiStringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/24
 */
public class ValidityTransferHelper {

    public static String transferIdValidityEnd(String validityEnd) {
        if ("长期有效".equals(validityEnd)) {
            return "99991231";
        }
        return validityEnd;
    }

    /**
     * 转换银行卡有效期
     * @param cardValidity 银行卡有效期
     * @return 转换之后的有效期，格式为 MM/YYYY
     */
    public static String transferCardValidity(String cardValidity) {
        if (WosaiStringUtils.isEmpty(cardValidity)) {
            return cardValidity;
        }
        if ("长期有效".equals(cardValidity)) {
            return "12/9999";
        }
        try {
            Date date = DateUtils.parseDateStrictly(cardValidity, "yyyy年MM月");
            return DateFormatUtils.format(date, "MM/yyyy");
        } catch (ParseException e) {
            throw new CommonInvalidParameterException("银行卡有效期格式错误");
        }
    }
}
