package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "brand_withdraw_strategy")
public class BrandWithdrawStrategyDO {
    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id
     */
    @TableField(value = "brand_id")
    private String brandId;

    /**
     * 策略id
     */
    @TableField(value = "strategy_id")
    private Long strategyId;


    /**
     * 提现方式：AUTO-自动提现，MANUAL_OPERATION-手动提现
     */
    @TableField(value = "withdraw_type")
    private String withdrawType;

    /**
     * 提现周期类型
     */
    @TableField(value = "withdraw_cycle_type")
    private String withdrawCycleType;

    /**
     * 提现周期时间
     */
    @TableField(value = "withdraw_cycle_times")
    private String withdrawCycleTimes;

    /**
     * 提现周期时间
     */
    @TableField(value = "withdraw_cycle_time")
    @Deprecated
    private Integer withdrawCycleTime;

    /**
     * 最少单笔提现金额
     */
    @TableField(value = "min_withdrawal_amount")
    private Integer minWithdrawalAmount;

    /**
     * 提现预留金额
     */
    @TableField(value = "reserved_amount")
    private Integer reservedAmount;

    /**
     * 适用场景
     */
    @TableField(value = "applicable_scene")
    private String applicableScene;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private Date updatedTime;
}