package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OpenApiMerchantBusinessLicenseInfoDTO {

    @JsonProperty("license_name")
    private String licenseName;

    @JsonProperty("license_number")
    private String licenseNumber;

    @JsonProperty("legal_person_id_number")
    private String legalPersonIdNumber;

    public OpenApiMerchantBusinessLicenseInfoDTO(Integer type, String licenseNumber, String legalPersonIdNumber) {
        this.licenseNumber = licenseNumber;
        switch (type){
            case 0: this.licenseName = "身份证号";break;
            case 1: this.licenseName = "个体工商户营业执照"; break;
            case 2: this.licenseName = "企业营业执照"; break;
            default:
        }
        this.legalPersonIdNumber = legalPersonIdNumber;
    }
}
