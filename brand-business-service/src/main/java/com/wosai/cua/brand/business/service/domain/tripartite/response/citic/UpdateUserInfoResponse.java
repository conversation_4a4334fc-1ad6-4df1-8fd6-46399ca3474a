package com.wosai.cua.brand.business.service.domain.tripartite.response.citic;

import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateUserInfoResponse extends TripartiteSystemCallResponse {
    /**
     * 流水号
     */
    private String serialNumber;
    /**
     * 用户编号,银行给用户分配的编号，银行保证唯一性； 后续交易均使用该编号唯一定位用户。
     */
    private String userId;
    /**
     * 动态密码句柄(未启用)
     */
    private String pwdId;
    /**
     * 交易流水号(未启用)
     */
    private String transId;
    /**
     * 是否需要审核,如果配置用户需要注册审核，则该字段返回true，其余情况该字段返回false。
     */
    private String isNeedCheck;
}
