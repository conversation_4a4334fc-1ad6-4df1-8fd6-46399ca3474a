package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 小程序商户预入驻接口<ant.mybank.bkmerchantprod.merch.applet.pre.register>
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
@Data
public class MerchantAppletPreRegisterResponseModel extends MybankObject {

    private static final long serialVersionUID = -3510703144698209575L;
    /**
     * 返回码组件
     * Result Status 返回S不代表入驻成功，只代表入驻申请已受理。需要回查
     */
    @XmlElementRef
    private RespInfo respInfo;

    /**
     * 外部商户号
     */
    @XmlElement(name = "OutMerchantId")
    private String outMerchantId;

    /**
     * 外部交易流水号
     */
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;

    /**
     * 外部平台ID
     */
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;

    /**
     * 申请单号
     */
    @XmlElement(name = "OrderNo")
    private String orderNo;
}