package com.wosai.cua.brand.business.service.externalservice.marketingprepaid;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.cua.brand.business.service.externalservice.marketingprepaid.model.AssociateBrandBeforeCheckRequest;
import com.wosai.marekting.prepaid.command.brand.BrandMerchantBelongRequest;
import com.wosai.marekting.prepaid.representation.brand.BrandMerchantBelongResp;
import com.wosai.marekting.prepaid.service.StoredBrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/12
 */
@Component
public class MarketingPrepaidClient {

    @Autowired
    private StoredBrandService storedBrandService;

    /**
     * 在关联品牌前进行检查
     * 此方法用于在关联品牌过程中，检查子商户是否属于品牌，以及是否存在关联失败的详细信息
     *
     * @param request 关联品牌前检查的请求对象，包含主商户ID和子商户ID列表
     * @return 返回一个映射，键为子商户ID，值为关联失败的原因如果子商户ID列表为空，则返回空映射
     */
    public Map<String, String> checkBeforeAssociateBrand(AssociateBrandBeforeCheckRequest request) {
        BrandMerchantBelongRequest brandMerchantBelongRequest = new BrandMerchantBelongRequest();
        brandMerchantBelongRequest.setMerchantId(request.getMainMerchantId());
        brandMerchantBelongRequest.setSubMerchantIds(request.getSubMerchantIds());
        BrandMerchantBelongResp brandMerchantBelongResp = storedBrandService.checkBeforeBelongBrand(brandMerchantBelongRequest);
        if (WosaiCollectionUtils.isEmpty(brandMerchantBelongResp.getSubMerchantIds())) {
            return new HashMap<>();
        }
        return brandMerchantBelongResp.getFailDetails().stream().collect(Collectors.toMap(BrandMerchantBelongResp.FailDetail::getSubMerchantId, BrandMerchantBelongResp.FailDetail::getFailReason, (v1, v2) -> v1));
    }
}
