package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;


import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;

public interface MyBankRequest<T extends MybankResponse> extends DefaultRequest<T> {

    /**
     * 设置请求路由分组
     *
     * @param cookieValue 路由分组值
     */
    void setCookieValue(String cookieValue);

    /**
     * 获取当前请求路由分组
     *
     * @return 路由分组值
     */
    String getCookieValue();

    /**
     * 判断是否需要加签
     *
     * @return
     */
    boolean isNeedSign();

    /**
     * 设置请求是否需要加签
     *
     * @param needSign
     */
    void setNeedSign(boolean needSign);

    /**
     * 获取公共请求参数Model
     *
     * @return 公共请求参数Model
     */
    RequestHead getRequestHead();

    /**
     * 设置公共请求参数Model
     *
     * @param requestHead
     */
    void setRequestHead(RequestHead requestHead);

    /**
     * 获取业务请求参数Model
     *
     * @return 业务请求参数Model
     */
    RequestBody getRequestBody();

    /**
     * 设置业务请求参数Model
     *
     * @param requestBody
     */
    void setRequestBody(RequestBody requestBody);

    /**
     * 构建请求XML报文内容（待签名）
     *
     * @return 请求XML报文内容（待签名）
     */
    String xmlBuild() throws MybankApiException;
}