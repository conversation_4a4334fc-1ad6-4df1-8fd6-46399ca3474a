package com.wosai.cua.brand.business.service.module.account.convert;

import com.wosai.cua.brand.business.service.domain.entity.BrandAccountDO;
import com.wosai.cua.brand.business.service.module.account.BrandAccountModule;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BrandAccountModuleConverter {

    private BrandAccountModuleConverter() {
        throw new IllegalStateException("Utility class");
    }

    public static BrandAccountDO convertBrandAccountDO(BrandAccountModule brandAccountModule) {
        if (brandAccountModule == null) {
            return null;
        }
        BrandAccountDO brandAccountDO = new BrandAccountDO();
        brandAccountDO.setId(brandAccountModule.getId());
        brandAccountDO.setBrandId(brandAccountModule.getBrandId());
        brandAccountDO.setAccounts(brandAccountModule.getAccounts());
        brandAccountDO.setCreatedTime(brandAccountModule.getCreatedTime());
        brandAccountDO.setUpdatedTime(brandAccountModule.getUpdatedTime());
        return brandAccountDO;
    }

    public static BrandAccountModule convertBrandAccountModule(BrandAccountDO brandAccountDO) {
        if (brandAccountDO == null) {
            return null;
        }
        BrandAccountModule brandAccountModule = new BrandAccountModule();
        brandAccountModule.setId(brandAccountDO.getId());
        brandAccountModule.setBrandId(brandAccountDO.getBrandId());
        brandAccountModule.setAccounts(brandAccountDO.getAccounts());
        brandAccountModule.setCreatedTime(brandAccountDO.getCreatedTime());
        brandAccountModule.setUpdatedTime(brandAccountDO.getUpdatedTime());
        return brandAccountModule;
    }
}

