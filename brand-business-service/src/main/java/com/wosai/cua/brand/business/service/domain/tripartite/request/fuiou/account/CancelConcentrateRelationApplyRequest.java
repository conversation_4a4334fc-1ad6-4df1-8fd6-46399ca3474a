package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

/**
 * 4.11归集授权申请撤销接口（CJ008）
 * 接口地址：/cancleConcentrateRelationApply.fuiou
 * 接口说明: 调用此接口，可在CJ007接口发起的授权申请未生效前，发起撤销。
 */
@Data
public class CancelConcentrateRelationApplyRequest implements TripartiteSystemCallRequest {

    public static final String REQUEST_URI = "/cancleConcentrateRelationApply.fuiou";

    private CancelConcentrateRelationApplyRequestBody body;
    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_CANCEL_CONCENTRATE_RELATION_APPLY;
    }
}
