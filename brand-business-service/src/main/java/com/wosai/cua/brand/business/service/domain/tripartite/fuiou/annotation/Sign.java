package com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 加签注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(value = {ElementType.FIELD})
@Documented
public @interface Sign {
    /**
     * 是否不为空加签字段
     *
     */
    boolean isNotNoneSign() default false;
}
