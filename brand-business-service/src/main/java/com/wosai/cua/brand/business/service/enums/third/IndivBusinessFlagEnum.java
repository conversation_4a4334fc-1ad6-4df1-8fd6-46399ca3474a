package com.wosai.cua.brand.business.service.enums.third;

import lombok.Getter;

@Getter
public enum IndivBusinessFlagEnum {
    INDIVIDUAL("1","个体工商户"),
    COMPANY_OR_PERSON("2","企业或者个人")
    ;

    private final String flag;

    private final String description;

    IndivBusinessFlagEnum(String flag, String description) {
        this.flag = flag;
        this.description = description;
    }
}
