package com.wosai.cua.brand.business.service.facade.impl.crm;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.FileUrlRequest;
import com.wosai.cua.brand.business.api.dto.request.GenerateBrandKeyRequest;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.BaseBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.GenerateKeyResponse;
import com.wosai.cua.brand.business.api.dto.response.PageBrandInfoDTO;
import com.wosai.cua.brand.business.api.facade.crm.CrmBrandFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.v2.BrandFileBusinessV2;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.ApiRequestParam;

import java.util.Map;
import java.util.Objects;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class CrmBrandFacadeImpl implements CrmBrandFacade {

    private final BrandFileBusinessV2 brandFileBusiness;

    private final BrandBusiness brandBusiness;

    private final ApolloConfig apolloConfig;

    @Autowired
    public CrmBrandFacadeImpl(BrandFileBusinessV2 brandFileBusiness, BrandBusiness brandBusiness, ApolloConfig apolloConfig) {
        this.brandFileBusiness = brandFileBusiness;
        this.brandBusiness = brandBusiness;
        this.apolloConfig = apolloConfig;
    }

    @Override
    public GenerateKeyResponse generateBrandKey(ApiRequestParam<GenerateBrandKeyRequest, Map> apiRequestParam) {
        GenerateBrandKeyRequest request = apiRequestParam.getBodyParams();
        return GenerateKeyResponse.builder().publicKey(brandBusiness.generateBrandKey(request.getBrandId(), request.getManagementPublicKey())).build();
    }

    @Override
    public GenerateKeyResponse getBrandKey(ApiRequestParam<BaseBrandRequestDTO, Map> apiRequestParam) {
        return GenerateKeyResponse.builder().publicKey(brandBusiness.getBrandKey(apiRequestParam.getBodyParams().getBrandId())).build();
    }

    @Override
    public void batchOpenMerchant(ApiRequestParam<FileUrlRequest, Map> apiRequestParam) {
        String userId = apiRequestParam.getUser().getId();
        String fileUrl = apiRequestParam.getBodyParams().getFileUrl();
        String brandId = apiRequestParam.getBodyParams().getBrandId();
        brandFileBusiness.submitOpenedMerchantInfo(brandId, fileUrl, userId, "CRM");
    }

    @Override
    public PageBrandInfoDTO pageBrandInfoList(ApiRequestParam<PageQueryBrandsDTO, Map> apiRequestParam) {
        PageQueryBrandsDTO pageQueryBrandsDto = apiRequestParam.getBodyParams();
        // 设置默认分页参数
        if (Objects.isNull(pageQueryBrandsDto.getPage())) {
            pageQueryBrandsDto.setPage(apolloConfig.getDefaultPage());
        }
        if (Objects.isNull(pageQueryBrandsDto.getPageSize())) {
            pageQueryBrandsDto.setPageSize(apolloConfig.getDefaultPageSize());
        }
        pageQueryBrandsDto.setSftTag(1);
        return brandBusiness.pageBrandInfoListByBrandCondition(pageQueryBrandsDto);
    }

    @Override
    public void enableSft(ApiRequestParam<BaseBrandRequestDTO, Map> apiRequestParam) {
        brandBusiness.enableSft(apiRequestParam.getBodyParams().getBrandId());
    }
}
