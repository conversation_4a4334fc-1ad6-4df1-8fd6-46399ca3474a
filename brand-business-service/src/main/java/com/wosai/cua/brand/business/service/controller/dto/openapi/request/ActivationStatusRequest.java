package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class ActivationStatusRequest extends OpenApiBaseRequest{

    @NotBlank(message = "商户编号merchantSn必传")
    @JsonProperty("merchant_sn")
    private String merchantSn;
}
