package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.annotation.Sign;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.BaseRequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "xml")
public class QueryAllocateAccountRequestBody extends BaseRequestBody {
    /**
     * 开户请求流水
     */
    private String mchntTraceNo;

    /**
     * 户名,创建用户时提交的开户名称
     */
    @Sign
    private String outAcntNm;

    /**
     * 手机号,创建用户时提交的手机号
     */
    @Sign
    private String mobile;

    /**
     * 银行账号
     */
    @Sign
    private String outAcntNo;

    /**
     * 用户编号
     */
    @Sign
    private String accountIn;

    /**
     * 证件号
     * 个人 身份证号码
     * 企业 社会信用代码
     */
    private String idNo;

    public QueryAllocateAccountRequestBody(String mchntCd) {
        super(mchntCd);
    }
}
