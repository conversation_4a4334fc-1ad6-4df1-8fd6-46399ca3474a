package com.wosai.cua.brand.business.service.domain.service.impl.tripartite;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util.HttpUtils;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util.SignUtils;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.ActiveAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.ActiveAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.AddConcentrateRelationRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.AddConcentrateRelationRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CancelConcentrateRelationApplyRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CancelConcentrateRelationApplyRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CancelConcentrateRelationRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CancelConcentrateRelationRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CloseAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CloseAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CreateSubAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CreateSubAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.InvalidAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.InvalidAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.OpenAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.OpenAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryConcentrateRelationRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.QueryConcentrateRelationRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.card.ModifyAccountInCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.card.ModifyAccountInCardRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.ActiveAllocateAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.ActiveAllocateAccountResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.AddConcentrateRelationResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.AddConcentrateRelationResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CancelConcentrateRelationApplyResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CancelConcentrateRelationApplyResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CancelConcentrateRelationResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CancelConcentrateRelationResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CloseAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CloseAccountResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CreateSubAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CreateSubAccountResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.InvalidAllocateAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.InvalidAllocateAccountResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.OpenAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.OpenAccountResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.QueryAllocateAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.QueryAllocateAccountResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.QueryConcentrateRelationResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.QueryConcentrateRelationResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.card.ModifyAccountInCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.card.ModifyAccountInCardResponseBody;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * 富友系统接口实现
 */
@Service
@Slf4j
public class FuiouSystemCallServiceImpl implements TripartiteSystemCallService {

    private static final String RESPONSE_NULL = "富友请求返回报文为空";

    private static final String SUCCESS_CODE = "0000";

    @Value("${fuiou-service-address}")
    private String baseUrl;

    private final ApolloConfig apolloConfig;

    @Autowired
    public FuiouSystemCallServiceImpl(ApolloConfig apolloConfig) {
        this.apolloConfig = apolloConfig;
    }

    @Override
    public FundManagementCompanyEnum getFundManagementCompanyEnum() {
        return FundManagementCompanyEnum.FUIOU;
    }

    private static final Map<FunctionEnum, Function<TripartiteSystemCallRequest, Function<FuiouConfigModule, TripartiteSystemCallResponse>>> FUNCTION_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void initFunctionMap() {
        FUNCTION_MAP.put(FunctionEnum.FUIOU_OPEN_ACCOUNT, request -> configModule -> this.openAccount(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_MODIFY_ACCOUNT_IN_CARD, request -> configModule -> this.modifyAccountInCard(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_ACTIVE_ALLOCATE_ACCOUNT, request -> configModule -> this.activeAllocateAccount(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_QUERY_ALLOCATE_ACCOUNT, request -> configModule -> this.queryAllocateAccount(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_INVALID_ALLOCATE_ACCOUNT, request -> configModule -> this.invalidAllocateAccount(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_CLOSE_ACCOUNT, request -> configModule -> this.closeAccount(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_CREATE_SUB_ACCOUNT, request -> configModule -> this.createSubAccount(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_ADD_CONCENTRATE_RELATION, request -> configModule -> this.addConcentrateRelation(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_CANCEL_CONCENTRATE_RELATION_APPLY, request -> configModule -> this.cancelConcentrateRelationApply(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_CANCEL_CONCENTRATE_RELATION, request -> configModule -> this.cancelConcentrateRelation(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.FUIOU_QUERY_CONCENTRATE_RELATION, request -> configModule -> this.queryConcentrateRelation(request, configModule));
    }

    @Override
    public <T extends TripartiteSystemCallRequest, R extends TripartiteSystemCallResponse, C extends ConfigModule> R call(T request, Class<R> clazz, C configModule) {
        try {
            if (configModule instanceof FuiouConfigModule) {
                FuiouConfigModule fuiouConfigModule = (FuiouConfigModule) configModule;
                log.info("富友配置: {}", JSON.toJSONString(fuiouConfigModule));
                Function<TripartiteSystemCallRequest, Function<FuiouConfigModule, TripartiteSystemCallResponse>> tripartiteSystemCallRequestFunctionFunction = FUNCTION_MAP.get(request.getFunctionEnum());
                if (tripartiteSystemCallRequestFunctionFunction != null) {
                    Function<FuiouConfigModule, TripartiteSystemCallResponse> tripartiteSystemCallResponseFunction = tripartiteSystemCallRequestFunctionFunction.apply(request);
                    if (tripartiteSystemCallResponseFunction != null) {
                        return handleResponse(tripartiteSystemCallResponseFunction.apply(fuiouConfigModule), clazz);
                    }
                }
            } else {
                log.error("未找到对应的富友配置或者接口, configModule: {}", configModule.getClass().getSimpleName());
                return handleError(clazz, "未找到对应的富友配置或者接口");
            }
        } catch (Exception e) {
            return handleError(clazz, e.getMessage());
        }
        return handleError(clazz, "未知异常");
    }

    private <R> R handleResponse(TripartiteSystemCallResponse response, Class<R> clazz) {
        if (response != null) {
            if (response.getResultCode().equals(TripartiteSystemCallResponse.ResultCodeEnum.FAIL)) {
                throw new BrandBusinessException(response.getResultMsg());
            }
            // 这里假设response对象具有统一的RespInfo结构来判断成功与否
            return JSON.parseObject(JSON.toJSONString(response), clazz);
        }
        log.error("响应为空");
        return handleError(clazz, RESPONSE_NULL);
    }

    private <R> R handleError(Class<R> clazz, String errorMsg) {
        // 假设定义了一个ErrorResponse对象用于表示错误情况
        TripartiteSystemCallResponse tripartiteSystemCallResponse = new TripartiteSystemCallResponse();
        tripartiteSystemCallResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
        tripartiteSystemCallResponse.setResultMsg(errorMsg);
        return JSON.parseObject(JSON.toJSONString(tripartiteSystemCallResponse), clazz);
    }


    private OpenAccountResponse openAccount(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        OpenAccountRequest openAccountRequest = (OpenAccountRequest) request;
        try {
            String sign = SignUtils.sign(openAccountRequest.getBody(), OpenAccountRequestBody.class, configModule.getPrivateKey());
            log.info("【openAccount】富友签名:{}", sign);
            openAccountRequest.getBody().setSignature(sign);
        } catch (Exception e) {
            log.error("【openAccount】富友签名失败", e);
            return null;
        }
        try {
            OpenAccountResponse response = new OpenAccountResponse();
            OpenAccountResponseBody out = HttpUtils.encipherPost(baseUrl + OpenAccountRequest.OPEN_ACCOUNT_URL,
                    openAccountRequest.getBody(),
                    OpenAccountResponseBody.class,
                    configModule.getFyPublicKey(),
                    configModule.getPrivateKey(),
                    configModule.getMerchantNo()
            );
            log.info("【openAccount】富友响应:{}", JSON.toJSONString(out));
            if (Objects.isNull(out)) {
                return handleError(OpenAccountResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, OpenAccountResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.error("【openAccount】富友验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【openAccount】富友请求失败", e);
        }
        return null;
    }

    private ModifyAccountInCardResponse modifyAccountInCard(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        ModifyAccountInCardRequest modifyAccountInCardRequest = (ModifyAccountInCardRequest) request;
        try {
            String sign = SignUtils.sign(modifyAccountInCardRequest.getBody(), ModifyAccountInCardRequestBody.class, configModule.getPrivateKey());
            log.info("【modifyAccountInCard】富友签名:{}", sign);
            modifyAccountInCardRequest.getBody().setSignature(sign);
        } catch (Exception e) {
            log.error("【modifyAccountInCard】富友签名失败", e);
            return null;
        }
        try {
            ModifyAccountInCardResponse response = new ModifyAccountInCardResponse();
            ModifyAccountInCardResponseBody out = HttpUtils.unencryptedPost(baseUrl + ModifyAccountInCardRequest.MODIFY_ACCOUNT_IN_CARD_URL, modifyAccountInCardRequest.getBody(), ModifyAccountInCardResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(ModifyAccountInCardResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, ModifyAccountInCardResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.error("【modifyAccountInCard】富友签名验证失败");
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【modifyAccountInCard】富友请求失败", e);
        }
        return null;
    }

    private ActiveAllocateAccountResponse activeAllocateAccount(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        ActiveAllocateAccountRequest activeAllocateAccountRequest = (ActiveAllocateAccountRequest) request;
        try {
            String sign = SignUtils.sign(activeAllocateAccountRequest.getBody(), ActiveAllocateAccountRequestBody.class, configModule.getPrivateKey());
            log.info("【activeAllocateAccount】富友签名:{}", sign);
            activeAllocateAccountRequest.getBody().setSignature(sign);
        } catch (Exception e) {
            log.error("【activeAllocateAccount】富友签名失败", e);
            return null;
        }
        try {
            ActiveAllocateAccountResponse response = new ActiveAllocateAccountResponse();
            ActiveAllocateAccountResponseBody out = HttpUtils.unencryptedPost(baseUrl + ActiveAllocateAccountRequest.ACTIVE_ACCOUNT_URL, activeAllocateAccountRequest.getBody(), ActiveAllocateAccountResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(ActiveAllocateAccountResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, ActiveAllocateAccountResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.error("【activeAllocateAccount】富友验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【activeAllocateAccount】富友请求失败", e);
        }
        return null;
    }

    private QueryAllocateAccountResponse queryAllocateAccount(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        QueryAllocateAccountRequest queryAllocateAccountRequest = (QueryAllocateAccountRequest) request;
        QueryAllocateAccountRequestBody body = queryAllocateAccountRequest.getBody();
        try {
            String sign = SignUtils.sign(body, QueryAllocateAccountRequestBody.class, configModule.getPrivateKey());
            log.info("【queryAllocateAccount】富友签名:{}", sign);
            body.setSignature(sign);
        } catch (Exception e) {
            log.error("【queryAllocateAccount】富友签名失败", e);
            return null;
        }
        try {
            QueryAllocateAccountResponse response = new QueryAllocateAccountResponse();
            QueryAllocateAccountResponseBody out = HttpUtils.encipherPost(baseUrl + QueryAllocateAccountRequest.REQUEST_URI,
                    body,
                    QueryAllocateAccountResponseBody.class,
                    configModule.getFyPublicKey(),
                    configModule.getPrivateKey(),
                    configModule.getMerchantNo()
            );
            if (Objects.isNull(out)) {
                return handleError(QueryAllocateAccountResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, QueryAllocateAccountResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.warn("【queryAllocateAccount】富友请求返回报文验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【queryAllocateAccount】富友请求失败", e);
        }
        return null;
    }

    private InvalidAllocateAccountResponse invalidAllocateAccount(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        InvalidAllocateAccountRequest invalidAllocateAccountRequest = (InvalidAllocateAccountRequest) request;
        InvalidAllocateAccountRequestBody body = invalidAllocateAccountRequest.getBody();
        try {
            String sign = SignUtils.sign(body, InvalidAllocateAccountRequestBody.class, configModule.getPrivateKey());
            log.info("【invalidAllocateAccount】富友签名:{}", sign);
            body.setSignature(sign);
        } catch (Exception e) {
            log.error("【invalidAllocateAccount】富友签名失败", e);
            return null;
        }
        try {
            InvalidAllocateAccountResponse response = new InvalidAllocateAccountResponse();
            InvalidAllocateAccountResponseBody out = HttpUtils.unencryptedPost(baseUrl + InvalidAllocateAccountRequest.REQUEST_URI, body, InvalidAllocateAccountResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(InvalidAllocateAccountResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, InvalidAllocateAccountResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.warn("【invalidAllocateAccount】富友请求返回报文验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【invalidAllocateAccount】富友请求失败", e);
        }
        return null;
    }

    private CloseAccountResponse closeAccount(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        CloseAccountRequest closeAccountRequest = (CloseAccountRequest) request;
        CloseAccountRequestBody body = closeAccountRequest.getBody();
        try {
            String sign = SignUtils.sign(body, CloseAccountRequestBody.class, configModule.getPrivateKey());
            log.info("【closeAccount】富友签名:{}", sign);
            body.setSignature(sign);
        } catch (Exception e) {
            log.error("【closeAccount】富友签名失败", e);
            return null;
        }
        try {
            CloseAccountResponse response = new CloseAccountResponse();
            CloseAccountResponseBody out = HttpUtils.unencryptedPost(baseUrl + CloseAccountRequest.CLOSE_ACCOUNT_URI, body, CloseAccountResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(CloseAccountResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, CloseAccountResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.warn("【closeAccount】富友请求返回报文验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【closeAccount】富友请求失败", e);
        }
        return null;
    }

    private CreateSubAccountResponse createSubAccount(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        CreateSubAccountRequest createSubAccountRequest = (CreateSubAccountRequest) request;
        CreateSubAccountRequestBody body = createSubAccountRequest.getBody();
        try {
            String sign = SignUtils.sign(body, CreateSubAccountRequestBody.class, configModule.getPrivateKey());
            log.info("【createSubAccount】富友签名:{}", sign);
            body.setSignature(sign);
        } catch (Exception e) {
            log.error("【createSubAccount】富友签名失败", e);
            return null;
        }
        try {
            CreateSubAccountResponse response = new CreateSubAccountResponse();
            CreateSubAccountResponseBody out = HttpUtils.unencryptedPost(baseUrl + CreateSubAccountRequest.REQUEST_URI, body, CreateSubAccountResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(CreateSubAccountResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, CreateSubAccountResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.warn("【createSubAccount】富友请求返回报文验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【createSubAccount】富友请求失败", e);
        }
        return null;
    }

    private AddConcentrateRelationResponse addConcentrateRelation(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        AddConcentrateRelationRequest addConcentrateRelationRequest = (AddConcentrateRelationRequest) request;
        AddConcentrateRelationRequestBody body = addConcentrateRelationRequest.getBody();
        try {
            String sign = SignUtils.sign(body, AddConcentrateRelationRequestBody.class, configModule.getPrivateKey());
            log.info("【addConcentrateRelation】富友签名:{}", sign);
            body.setSignature(sign);
        } catch (Exception e) {
            log.error("【addConcentrateRelation】富友签名失败", e);
            return null;
        }
        try {
            AddConcentrateRelationResponse response = new AddConcentrateRelationResponse();
            AddConcentrateRelationResponseBody out = HttpUtils.unencryptedPost(baseUrl + AddConcentrateRelationRequest.REQUEST_URI, body, AddConcentrateRelationResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(AddConcentrateRelationResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, AddConcentrateRelationResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.warn("【addConcentrateRelation】富友请求返回报文验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【addConcentrateRelation】富友请求失败", e);
        }
        return null;
    }

    private CancelConcentrateRelationApplyResponse cancelConcentrateRelationApply(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        CancelConcentrateRelationApplyRequest cancelConcentrateRelationApplyRequest = (CancelConcentrateRelationApplyRequest) request;
        CancelConcentrateRelationApplyRequestBody body = cancelConcentrateRelationApplyRequest.getBody();
        try {
            String sign = SignUtils.sign(body, CancelConcentrateRelationApplyRequestBody.class, configModule.getPrivateKey());
            log.info("【cancelConcentrateRelationApply】富友签名:{}", sign);
            body.setSignature(sign);
        } catch (Exception e) {
            log.error("【cancelConcentrateRelationApply】富友签名失败", e);
            return null;
        }
        try {
            CancelConcentrateRelationApplyResponse response = new CancelConcentrateRelationApplyResponse();
            CancelConcentrateRelationApplyResponseBody out = HttpUtils.unencryptedPost(baseUrl + CancelConcentrateRelationApplyRequest.REQUEST_URI, body, CancelConcentrateRelationApplyResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(CancelConcentrateRelationApplyResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, CancelConcentrateRelationApplyResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.warn("【cancelConcentrateRelationApply】富友请求返回报文验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【cancelConcentrateRelationApply】富友请求失败", e);
        }
        return null;
    }

    private CancelConcentrateRelationResponse cancelConcentrateRelation(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        CancelConcentrateRelationRequest cancelConcentrateRelationRequest = (CancelConcentrateRelationRequest) request;
        CancelConcentrateRelationRequestBody body = cancelConcentrateRelationRequest.getBody();
        try {
            String sign = SignUtils.sign(body, CancelConcentrateRelationRequestBody.class, configModule.getPrivateKey());
            log.info("【cancelConcentrateRelation】富友签名:{}", sign);
            body.setSignature(sign);
        } catch (Exception e) {
            log.error("【cancelConcentrateRelation】富友签名失败", e);
            return null;
        }
        try {
            CancelConcentrateRelationResponse response = new CancelConcentrateRelationResponse();
            CancelConcentrateRelationResponseBody out = HttpUtils.unencryptedPost(baseUrl + CancelConcentrateRelationRequest.REQUEST_URI, body, CancelConcentrateRelationResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(CancelConcentrateRelationResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, CancelConcentrateRelationResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.warn("【cancelConcentrateRelation】富友请求返回报文验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【cancelConcentrateRelation】富友请求失败", e);
        }
        return null;
    }

    private QueryConcentrateRelationResponse queryConcentrateRelation(TripartiteSystemCallRequest request, FuiouConfigModule configModule) {
        QueryConcentrateRelationRequest queryConcentrateRelationRequest = (QueryConcentrateRelationRequest) request;
        QueryConcentrateRelationRequestBody body = queryConcentrateRelationRequest.getBody();
        try {
            String sign = SignUtils.sign(body, QueryConcentrateRelationRequestBody.class, configModule.getPrivateKey());
            log.info("【queryConcentrateRelation】富友签名:{}", sign);
            body.setSignature(sign);
        } catch (Exception e) {
            log.error("【queryConcentrateRelation】富友签名失败", e);
            return null;
        }
        try {
            QueryConcentrateRelationResponse response = new QueryConcentrateRelationResponse();
            QueryConcentrateRelationResponseBody out = HttpUtils.unencryptedPost(baseUrl + QueryConcentrateRelationRequest.REQUEST_URI, body, QueryConcentrateRelationResponseBody.class);
            if (Objects.isNull(out)) {
                return handleError(QueryConcentrateRelationResponse.class, RESPONSE_NULL);
            }
            if (!out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
                response.setResultMsg(out.getRespDesc());
                return response;
            }
            boolean verifySign = SignUtils.verify(out, QueryConcentrateRelationResponseBody.class, configModule.getFyPublicKey());
            if (!verifySign) {
                log.warn("【queryConcentrateRelation】富友请求返回报文验签失败");
                return null;
            }
            response.setBody(out);
            if (out.getRespCode().equals(SUCCESS_CODE)) {
                response.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS);
                response.setResultMsg(TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.getDesc());
            }
            return response;
        } catch (Exception e) {
            log.error("【queryConcentrateRelation】富友请求失败", e);
        }
        return null;
    }
}
