package com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR> Date: 2024/08/08 Time: 3:53 PM
 * 中信银行结算响应数据对象
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CiticResponseData {

    /**
     * 发起方流水号
     */
    @XmlElement(name = "REQ_SSN")
    protected String reqSsn;

    /**
     * 电商管家应答码
     */
    @XmlElement(name = "RSP_CODE")
    protected String rspCode;

    /**
     * 电商管家应答码描述
     */
    @XmlElement(name = "RSP_MSG")
    protected String rspMsg;

    /**
     * 签名
     */
    @XmlElement(name = "SIGN_INFO")
    protected String signInfo;
}
