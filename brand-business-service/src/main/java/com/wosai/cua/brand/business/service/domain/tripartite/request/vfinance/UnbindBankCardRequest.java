package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * 解绑银行卡请求
 * <AUTHOR>
 */
public class UnbindBankCardRequest extends VfinanceBaseRequest{

    @J<PERSON><PERSON>ield(name = "member_id")
    private String memberId;

    @JSONField(name = "bank_card_id")
    private String bankCardId;

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getBankCardId() {
        return bankCardId;
    }

    public void setBankCardId(String bankCardId) {
        this.bankCardId = bankCardId;
    }

    public UnbindBankCardRequest(String partnerId) {
        super();
        super.partnerId = partnerId;
        super.service = UN_BIND_BANK_CARD;
    }

    @Override
    public String toString() {
        return "UnbindBankCardRequest{" +
                "memberId='" + memberId + '\'' +
                ", bankCardId='" + bankCardId + '\'' +
                ", service='" + service + '\'' +
                ", version='" + version + '\'' +
                ", partnerId='" + partnerId + '\'' +
                ", inputCharset='" + inputCharset + '\'' +
                ", memo='" + memo + '\'' +
                '}';
    }
}
