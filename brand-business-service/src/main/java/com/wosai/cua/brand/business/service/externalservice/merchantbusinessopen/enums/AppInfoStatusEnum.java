package com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums;

import lombok.Getter;

/**
 * app_info 开通状态的枚举
 * <AUTHOR>
 * @date 2024/9/27
 */
public enum AppInfoStatusEnum {

    /**
     * 待开通
     */
    INIT(0,"待开通"),
    /**
     * 开通中
     */
    PENDING(1,"开通中"),
    /**
     * 开通失败
     */
    FAIL(2,"开通失败"),
    /**
     * 开通成功
     */
    SUCCESS(3,"开通成功");

    @Getter
    private final int code;
    @Getter
    private final String desc;

     AppInfoStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AppInfoStatusEnum getByCode(int code) {
        for (AppInfoStatusEnum appInfoStatusEnum : AppInfoStatusEnum.values()) {
            if (appInfoStatusEnum.getCode() == code) {
                return appInfoStatusEnum;
            }
        }
        return null;
    }
}
