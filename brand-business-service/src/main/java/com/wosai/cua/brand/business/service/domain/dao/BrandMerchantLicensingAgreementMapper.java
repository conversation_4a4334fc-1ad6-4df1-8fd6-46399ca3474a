package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantLicensingAgreementDO;

/**
* <AUTHOR>
* @description 针对表【brand_merchant_licensing_agreement(品牌商户授权协议表)】的数据库操作Mapper
* @createDate 2024-09-21 17:01:11
* @Entity com.wosai.cua.brand.business.service.domain.entity.BrandMerchantLicensingAgreementDO
*/
public interface BrandMerchantLicensingAgreementMapper extends BaseMapper<BrandMerchantLicensingAgreementDO> {

}




