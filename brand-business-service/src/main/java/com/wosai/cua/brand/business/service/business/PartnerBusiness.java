package com.wosai.cua.brand.business.service.business;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.request.CreatePartnerRequestDTO;
import com.wosai.cua.brand.business.api.enums.BankOfDepositEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.CreatePartnerRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.VfinanceBaseResponse;
import com.wosai.cua.brand.business.service.enums.VFinanceResponseCodeEnum;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class PartnerBusiness {

    private final VfinanceInterfaceService vfinanceInterfaceService;

    private final BrandDomainService brandDomainService;

    public PartnerBusiness(VfinanceInterfaceService vfinanceInterfaceService, BrandDomainService brandDomainService) {
        this.vfinanceInterfaceService = vfinanceInterfaceService;
        this.brandDomainService = brandDomainService;
    }

    @Transactional(rollbackFor = Exception.class)
    public String createPartner(CreatePartnerRequestDTO createPartnerRequest) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandSn(createPartnerRequest.getBrandSn());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        CreatePartnerRequest request = JSON.parseObject(JSON.toJSONString(createPartnerRequest), CreatePartnerRequest.class);
        request.setPartnerId(createPartnerRequest.getBrandSn());
        request.setBankCode(BankOfDepositEnum.PAB.getBankOfDeposit());
        String s;
        try {
            s = vfinanceInterfaceService.invokeService(request);
        } catch (Exception e) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL);
        }
        VfinanceBaseResponse response = JSON.parseObject(s, VfinanceBaseResponse.class);
        if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(),response.getErrorMessage());
        }
        brandModule.setPartnerId(response.getPartnerId());
        brandDomainService.modifyBrand(brandModule);
        return createPartnerRequest.getBrandSn();
    }
}
