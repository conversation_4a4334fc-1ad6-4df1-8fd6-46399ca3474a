package com.wosai.cua.brand.business.service.module.brand;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BrandMerchantCreationRecordModule {

    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    private String recordId;

    /**
     * 商户id
     */
    private String merchantId;

    private String merchantSn;

    /**
     * 创建结果
     */
    private String result;

    /**
     * 创建结果状态值：1-创建成功，0-创建失败
     */
    private Integer status;

    /**
     * 是否需要重试，0-不需要，1-需要
     */
    private Integer needRetry;

    /**
     * 重试次数
     */
    private Integer retryNum;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}
