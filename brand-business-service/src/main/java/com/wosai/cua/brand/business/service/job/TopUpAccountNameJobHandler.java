package com.wosai.cua.brand.business.service.job;

import com.alibaba.fastjson.JSON;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.module.job.TopUpAccountNameJobParamDTO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
@Slf4j
public class TopUpAccountNameJobHandler {

    private final BrandBusiness brandBusiness;

    public TopUpAccountNameJobHandler(BrandBusiness brandBusiness) {
        this.brandBusiness = brandBusiness;
    }

    @XxlJob("topUpAccountNameCompensate")
    public void topUpAccountNameCompensate() {
        String jobParam = XxlJobHelper.getJobParam();
        int pageSize = 1000;
        long sleepTime = 1000L;
        String brandId = "";
        if (!StringUtils.isEmpty(jobParam)) {
            log.info("BrandCallbackRecordsJobHandler jobParam is {}", jobParam);
            TopUpAccountNameJobParamDTO jobParamDto = JSON.parseObject(jobParam, TopUpAccountNameJobParamDTO.class);
            if (Objects.nonNull(jobParamDto.getPageSize())) {
                pageSize = jobParamDto.getPageSize();
            }
            if (Objects.nonNull(jobParamDto.getSleepTime())) {
                sleepTime = jobParamDto.getSleepTime();
            }
            brandId = jobParamDto.getBrandId();
        }
        if (StringUtils.isEmpty(brandId)){
            return;
        }
        brandBusiness.compensateTopUpAccountName(brandId, pageSize, sleepTime);
    }
}
