package com.wosai.cua.brand.business.service.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.service.excel.converters.MerchantTypeEnumConverter;
import com.wosai.cua.brand.business.service.excel.groups.Default;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessOpenAssociateMerchantOpData {
    /**
     * 商户号
     */
    @ExcelProperty("商户号")
    private String merchantSn;
    @ExcelProperty(value = "合作关系", converter = MerchantTypeEnumConverter.class)
    @NotNull(message = "合作关系", groups = Default.class)
    private MerchantTypeEnum cooperation;

    @ExcelProperty("结果")
    private String result;
}
