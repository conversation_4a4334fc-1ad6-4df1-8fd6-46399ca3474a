package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantBankCardDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandMerchantBankCardMapper extends BaseMapper<BrandMerchantBankCardDO> {
    /**
     * 分页查询品牌下商户的银行卡列表
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     * @param offset     偏移量
     * @param pageSize   每页条数
     * @return 银行卡列表
     */
    List<BrandMerchantBankCardDO> pageQueryBankCardsByBrandIdAndMerchantId(@Param("brandId") String brandId, @Param("merchantId") String merchantId, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    /**
     * @param bankCardId
     * @return
     */
    BrandMerchantBankCardDO getBrandMerchantBankCardByCardId(@Param("bankCardId") String bankCardId);

    /**
     * 将品牌商户下所有的银行卡设置为非默认
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     * @return 更新条数
     */
    int updateAllBankCardIsNotDefault(@Param("brandId") String brandId, @Param("merchantId") String merchantId);

    /**
     * 获取默认银行卡
     * @param brandId 品牌id
     * @param merchantId 商户id
     * @return 默认卡
     */
    BrandMerchantBankCardDO getDefaultCard(@Param("brandId") String brandId, @Param("merchantId") String merchantId);

    /**
     * 根据商户id集合查询默认银行卡信息
     * @param brandId 品牌id
     * @param merchantIds 商户id集合
     * @return 银行卡集合
     */
    List<BrandMerchantBankCardDO> getDefaultCardsByMerchantIds(@Param("brandId") String brandId, @Param("merchantIds") List<String> merchantIds);

    /**
     * 根据银行卡id集合获取银行卡信息
     * @param brandId 品牌id
     * @param merchantId 商户id
     * @param cardIds 银行卡id集合
     * @return 银行卡集合
     */
    List<BrandMerchantBankCardDO> getBankCardModulesByCarIdList(@Param("brandId") String brandId, @Param("merchantId") String merchantId,@Param("cardIds") List<String> cardIds, @Param("status") Integer status,@Param("isDefault")Integer isDefault);

    /**
     * 批量添加银行卡
     * @param brandMerchantBankCards 银行卡DO对象
     * @return 添加条数
     */
    int batchInsertBankCards(@Param("list")List<BrandMerchantBankCardDO> brandMerchantBankCards);

    /**
     * 根据品牌id分页查询银行卡列表
     * @param brandId 品牌id
     * @param pageSize 每页条数
     * @param startId 起始id
     * @return 银行卡列表
     */
    List<BrandMerchantBankCardDO> getBankCardListByBrandId(@Param("brandId") String brandId, @Param("pageSize") Integer pageSize, @Param("startId") Long startId);
}