package com.wosai.cua.brand.business.service.business.v2;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskTypeEnum;
import com.wosai.cua.brand.business.api.enums.ExcelConvertValueEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.BankCardDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantsAnalyzeService;
import com.wosai.cua.brand.business.service.domain.service.BrandTaskLogDomainService;
import com.wosai.cua.brand.business.service.domain.service.MerchantDomainService;
import com.wosai.cua.brand.business.service.domain.service.PooledMerchantAnalyzeService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.MerchantBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenRequest;
import com.wosai.cua.brand.business.service.helper.CommonHelper;
import com.wosai.cua.brand.business.service.helper.FileHelper;
import com.wosai.cua.brand.business.service.helper.IdGeneratorSnowflake;
import com.wosai.cua.brand.business.service.helper.OssFileHelper;
import com.wosai.cua.brand.business.service.helper.RedisHelper;
import com.wosai.cua.brand.business.service.helper.ZipHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.cua.brand.business.service.module.log.TaskApplyLogResultModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.v2.OpeningAccountAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.input.BrandMerchantInputResultModule;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BrandFileBusinessV2 {

    private final BrandDomainService brandDomainService;

    private final BankCardDomainService bankCardDomainService;

    private final BrandTaskLogDomainService brandTaskLogDomainService;

    @Value(value = "${tmp.folder}")
    private String tmpFolder;

    @Value("${appid.paymentHub}")
    private String paymentHubAppId;

    private final MerchantBusinessOpenClient merchantBusinessOpenClient;

    @Value("${fuiou.open.account.check_type}")
    private String fuiouCheckType;

    @Value("${aggregation.open.notify-url}")
    private String aggregationOpenNotifyUrl;

    private final IdGeneratorSnowflake idGeneratorSnowflake;

    private final List<BrandMerchantsAnalyzeService> brandMerchantsAnalyzeServices;

    private static final Map<BrandImportSheetEnum, BrandMerchantsAnalyzeService> BRAND_MERCHANTS_ANALYZE_SERVICE_MAP = Maps.newConcurrentMap();

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = Maps.newConcurrentMap();

    private final RedisHelper redisHelper;

    private final CommonHelper commonHelper;

    private final MerchantDomainService merchantDomainService;

    private final MerchantService merchantService;


    @Autowired
    public BrandFileBusinessV2(BrandDomainService brandDomainService, BankCardDomainService bankCardDomainService, BrandTaskLogDomainService brandTaskLogDomainService, MerchantBusinessOpenClient merchantBusinessOpenClient, IdGeneratorSnowflake idGeneratorSnowflake, List<BrandMerchantsAnalyzeService> brandMerchantsAnalyzeServices, List<TripartiteSystemCallService> tripartiteSystemCallServices, PooledMerchantAnalyzeService pooledMerchantAnalyzeService, RedisHelper redisHelper, CommonHelper commonHelper, MerchantDomainService merchantDomainService, MerchantService merchantService) {
        this.brandDomainService = brandDomainService;
        this.bankCardDomainService = bankCardDomainService;
        this.brandTaskLogDomainService = brandTaskLogDomainService;
        this.merchantBusinessOpenClient = merchantBusinessOpenClient;
        this.idGeneratorSnowflake = idGeneratorSnowflake;
        this.brandMerchantsAnalyzeServices = brandMerchantsAnalyzeServices;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.redisHelper = redisHelper;
        this.commonHelper = commonHelper;
        this.merchantDomainService = merchantDomainService;
        this.merchantService = merchantService;
    }

    @PostConstruct
    public void initBrandMerchantsAnalyzeServiceMap() {
        Map<BrandImportSheetEnum, BrandMerchantsAnalyzeService> analyzeServiceMap =
                brandMerchantsAnalyzeServices.stream().collect(Collectors.toMap(BrandMerchantsAnalyzeService::getSheetEnum, Function.identity()));
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.putAll(analyzeServiceMap);
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }


    public void submitOpenedMerchantInfo(String brandId, String fileUrl, String crmUserId, String platform) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BusinessException(ErrorCodeEnum.BRAND_NOT_EXIST);
        }
        long taskId = idGeneratorSnowflake.nextId();
        BrandTaskLogModule brandTaskLogModule = new BrandTaskLogModule();
        brandTaskLogModule.setTaskId(taskId);
        brandTaskLogModule.setPlatform(platform);
        brandTaskLogModule.setTaskType(BrandTaskTypeEnum.MERCHANT_IMPORT.getTaskType());
        brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.EXECUTION.getTaskStatus());
        brandTaskLogModule.setBrandId(brandId);
        String fileName = "批量开通收付通" + "_" + taskId;
        String filePath = tmpFolder + fileName;
        brandTaskLogModule.setTaskName(fileName);
        Long id = brandTaskLogDomainService.insertBrandTaskLog(brandTaskLogModule);
        brandTaskLogModule.setId(id);
        ZipHelper.deletefile(filePath);
        try {
            OssFileHelper.downloadFile(fileUrl, filePath);
            BrandMerchantInputResultModule brandMerchantInputResultModule = submitMerchantInfo(brandId, filePath, crmUserId);
            OssFileHelper.uploadFile(OssFileHelper.IMAGE_BUCKET_NAME, OssFileHelper.KEY_PREFIX + fileName, new File(filePath));
            String url = OssFileHelper.STATIC_BASE_URL + "/" + OssFileHelper.getStaticsFileUrl(OssFileHelper.KEY_PREFIX + fileName);
            brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            brandTaskLogModule.setTaskResult(JSON.toJSONString(
                    TaskApplyLogResultModule.builder()
                            .result("导入成功：" + brandMerchantInputResultModule.getSuccess() + "条！\n导入失败：" + brandMerchantInputResultModule.getFailed() + "条")
                            .downloadUrl(url).build()));
            brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
        } catch (Exception e) {
            log.error("处理数据失败", e);
            brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
            brandTaskLogModule.setErrorLog(e.getMessage());
            brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
        } finally {
            ZipHelper.deletefile(filePath);
        }
    }

    public BrandMerchantInputResultModule submitMerchantInfo(String brandId, String filePath, String crmUserId) {
        BrandMerchantInputResultModule resultModule = new BrandMerchantInputResultModule();
        List<OpeningAccountAnalyzeModule> openingAccountAnalyzeModules = FileHelper.parseExcel(filePath, 0, 3, OpeningAccountAnalyzeModule.class);
        if (CollectionUtils.isEmpty(openingAccountAnalyzeModules)) {
            resultModule.setSuccess(0);
            resultModule.setFailed(0);
            return resultModule;
        }
        for (OpeningAccountAnalyzeModule openingAccountAnalyzeModule : openingAccountAnalyzeModules) {
            if (!openingAccountAnalyzeModule.validateParameters()) {
                log.error("导入数据有误：{}", openingAccountAnalyzeModule.getValidateErrorMsg());
                continue;
            }
            if (openingAccountAnalyzeModule.getDataType().equals(ExcelConvertValueEnum.OPEN_TYPE_MERCHANT.getExcelValue())) {
                MerchantInfo merchant = merchantService.getMerchantBySn(openingAccountAnalyzeModule.getMerchantSn(), "");
                if (Objects.isNull(merchant)){
                    log.warn("商户不存在：{}", openingAccountAnalyzeModule.getMerchantSn());
                    continue;
                }
                BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
                brandMerchantModule.setMerchantSn(openingAccountAnalyzeModule.getMerchantSn());
                brandMerchantModule.setMerchantId(merchant.getId());
                brandMerchantModule.setBrandId(brandId);
                brandMerchantModule.setMerchantName(merchant.getName());
                brandMerchantModule.setMerchantType(MerchantTypeEnum.getMerchantTypeByDesc(openingAccountAnalyzeModule.getShouFuTongRole()));
                brandMerchantModule.setOutMerchantNo(openingAccountAnalyzeModule.getOutMerchantNo());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.HAVE_NOT_OPENED.getStatus());
                brandMerchantModule.setBankCardActivateStatus(BankCardActivateStatusEnum.NOT_BOUND.getActivateStatus());
                brandMerchantModule.setDyStoreSn(openingAccountAnalyzeModule.getDyStoreId());
                brandMerchantModule.setSqbStoreSn(openingAccountAnalyzeModule.getStoreSn());
                brandMerchantModule.setAssociatedMeituanStoreSn(openingAccountAnalyzeModule.getMtStoreId());
                brandMerchantModule.setAssociatedElmStoreSn(openingAccountAnalyzeModule.getElmStoreId());
                brandDomainService.createBrandMerchant(brandMerchantModule);
            }
            if (openingAccountAnalyzeModule.getDataType().equals(ExcelConvertValueEnum.OPEN_TYPE_STORE.getExcelValue())) {
                BusinessOpenRequest request = new BusinessOpenRequest();
                request.setUserId(crmUserId);
                request.setAppId(paymentHubAppId);
                Map<String , Object> merchant = Maps.newHashMap();
                Map<String , Object> license = Maps.newHashMap();
                merchant.put("name", openingAccountAnalyzeModule.getOpeningName());
                merchant.put("business_name", openingAccountAnalyzeModule.getOpeningName());
                merchant.put("contact_name", openingAccountAnalyzeModule.getContactName());
                merchant.put("contact_cellphone", openingAccountAnalyzeModule.getContactPhoneNumber());
                request.setMerchantInfo(merchant);
                request.setLicenseInfo(license);
                if (openingAccountAnalyzeModule.getOpeningType().equals("个人")){
                    license.put("type", 0);
                    license.put("name", openingAccountAnalyzeModule.getOpeningName());
                    license.put("number", openingAccountAnalyzeModule.getIdNumber());
                    license.put("legal_person_name", openingAccountAnalyzeModule.getOpeningName());
                    license.put("legal_person_id_number", openingAccountAnalyzeModule.getIdNumber());
                    license.put("legal_person_id_type", 1);
                }
                if (openingAccountAnalyzeModule.getOpeningType().equals("个体工商户")){
                    license.put("type", 1);
                    license.put("name", openingAccountAnalyzeModule.getOpeningName());
                    license.put("number", openingAccountAnalyzeModule.getIdNumber());
                    license.put("legal_person_name", openingAccountAnalyzeModule.getLegalPersonName());
                    license.put("legal_person_id_number", openingAccountAnalyzeModule.getLegalPersonIdNumber());
                    license.put("legal_person_id_type", 1);
                }
                if (openingAccountAnalyzeModule.getOpeningType().equals("企业")){
                    license.put("type", 2);
                    license.put("name", openingAccountAnalyzeModule.getOpeningName());
                    license.put("number", openingAccountAnalyzeModule.getIdNumber());
                    license.put("legal_person_name", openingAccountAnalyzeModule.getLegalPersonName());
                    license.put("legal_person_id_number", openingAccountAnalyzeModule.getLegalPersonIdNumber());
                    license.put("legal_person_id_type", 1);
                }
            }
        }

        return resultModule;
    }
}
