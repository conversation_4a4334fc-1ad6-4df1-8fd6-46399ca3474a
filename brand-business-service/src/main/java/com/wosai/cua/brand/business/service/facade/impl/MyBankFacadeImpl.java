package com.wosai.cua.brand.business.service.facade.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.mybank.OpenAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.mybank.SynchronousProtocolStatusDTO;
import com.wosai.cua.brand.business.api.facade.MyBankFacade;
import com.wosai.cua.brand.business.service.business.MyBankBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class MyBankFacadeImpl implements MyBankFacade {

    private final MyBankBusiness myBankBusiness;

    @Autowired
    public MyBankFacadeImpl(MyBankBusiness myBankBusiness) {
        this.myBankBusiness = myBankBusiness;
    }

    @Override
    public Boolean openAccount(OpenAccountDTO openAccountDto) {
        myBankBusiness.openAccount(openAccountDto.getBrandId(), openAccountDto.getBrandMerchantId());
        return true;
    }

    @Override
    public Boolean synchronousProtocolStatus(SynchronousProtocolStatusDTO synchronousProtocolStatusDto) {
        myBankBusiness.synchronousProtocolStatus(synchronousProtocolStatusDto.getBrandId(), synchronousProtocolStatusDto.getBrandMerchantId());
        return true;
    }
}
