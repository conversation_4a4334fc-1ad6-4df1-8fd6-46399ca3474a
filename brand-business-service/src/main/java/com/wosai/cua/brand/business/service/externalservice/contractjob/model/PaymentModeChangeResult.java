package com.wosai.cua.brand.business.service.externalservice.contractjob.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 切换支付模式结果
 * <AUTHOR>
 * @date 2024/9/24
 */
@Data
@Accessors(chain = true)
public class PaymentModeChangeResult {

    private int code;
    private String msg;

    /**
     * 成功
     */
    public static final int SUCCESS = 200;
    /**
     * 业务异常
     */
    public static final int BIZ_FAIL = 400;
    /**
     * 系统异常
     */
    public static final int ERROR = 500;

    public boolean isSuccess() {
        return code == SUCCESS;
    }

    public boolean isBusFail() {
        return code >= BIZ_FAIL && code < ERROR;
    }

    public boolean isSysFail() {
        return code >= ERROR;
    }
}
