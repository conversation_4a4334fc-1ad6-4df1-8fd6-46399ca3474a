package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "response")
@XmlType(propOrder = {"responseHead", "responseBody"})
public class Response extends MybankObject {
    private static final long serialVersionUID = 1587337112317735326L;

    @XmlAttribute(name = "id")
    private String id;

    /**
     * 公共应答参数（head）
     */
    @XmlElementRef
    private ResponseHead responseHead;

    /**
     * 业务应答参数（body）
     */
    @XmlElementRef
    private ResponseBody responseBody;

    public Response() {
    }

    public Response(ResponseHead responseHead, ResponseBody responseBody) {
        this.responseHead = responseHead;
        this.responseBody = responseBody;
        this.id = MybankConstants.RESPONSE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public ResponseHead getResponseHead() {
        return responseHead;
    }

    public void setResponseHead(ResponseHead responseHead) {
        this.responseHead = responseHead;
    }

    public ResponseBody getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(ResponseBody responseBody) {
        this.responseBody = responseBody;
    }
}