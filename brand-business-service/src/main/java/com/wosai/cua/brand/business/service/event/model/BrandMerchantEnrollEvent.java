package com.wosai.cua.brand.business.service.event.model;

import com.wosai.cua.brand.business.api.enums.EnrollChannelEnum;
import com.wosai.cua.brand.business.service.event.Event;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.type.MerchantRegistrationEventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandMerchantEnrollEvent implements Event {

    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 商户编号
     */
    private String merchantSn;
    /**
     * 报备渠道
     * @see EnrollChannelEnum
     */
    private EnrollChannelEnum enrollChannelEnum;

    @Override
    public EventType getEventType() {
        return MerchantRegistrationEventType.SUCCESS;
    }
}
