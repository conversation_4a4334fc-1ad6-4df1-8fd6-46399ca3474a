package com.wosai.cua.brand.business.service.module.brand;

import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantBrandDetailModule extends BrandModule{

    private String merchantId;

    /**
     * @see MerchantTypeEnum
     * 商户类型：FRANCHISEE-加盟商、SUPPLIER-供应商、BRAND_OWNER-品牌商、BRAND_OPERATED_STORES-品牌自营门店、SERVICE_PROVIDER_SQB-服务商（收钱吧）
     */
    private String merchantType;

    private String merchantTypeDesc;

    private Integer paymentMode;

    /**
     * 子账号
     */
    private String subAccountNo;

    /**
     * 系统会员id
     */
    private String memberId;

    /**
     * 关联品牌商户门店编号
     */
    private String associatedSqbStoreId;

    /**
     * 关联美团门店号
     */
    private String associatedMeituanStoreSn;

    /**
     * 关联饿了么门店号
     */
    private String associatedElmStoreSn;

    /**
     * 是否允许登录
     */
    private Boolean allowLogin;

    /**
     * 是否允许转账
     */
    private Boolean openTransfer;

}
