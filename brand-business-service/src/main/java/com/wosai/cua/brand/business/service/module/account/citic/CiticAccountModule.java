package com.wosai.cua.brand.business.service.module.account.citic;

import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.citic.CiticAccountsDTO;
import com.wosai.cua.brand.business.service.module.account.AccountsModule;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CiticAccountModule extends AccountsModule {
    /**
     * 交易资金账户
     */
    private String tradingFundAccount;

    /**
     * 交易资金账户名称
     */
    private String tradingFundAccountName;

    /**
     * 自有资金账户
     */
    private String ownCapitalAccount;

    /**
     * 自有资金账户名称
     */
    private String ownCapitalAccountName;

    public static CiticAccountModule convert(CiticAccountsDTO accounts) {
        if (accounts == null) {
            return null;
        }
        CiticAccountModule citicAccountModule = new CiticAccountModule();
        citicAccountModule.setTradingFundAccount(accounts.getTradingFundAccount());
        citicAccountModule.setTradingFundAccountName(accounts.getTradingFundAccountName());
        citicAccountModule.setOwnCapitalAccount(accounts.getOwnCapitalAccount());
        citicAccountModule.setOwnCapitalAccountName(accounts.getOwnCapitalAccountName());
        return citicAccountModule;
    }

    public static AccountsDTO convertDto(CiticAccountModule accountsModule) {
        if (accountsModule == null) {
            return null;
        }
        AccountsDTO accounts = new AccountsDTO();
        CiticAccountsDTO citicAccounts =  new CiticAccountsDTO();
        citicAccounts.setOwnCapitalAccount(accountsModule.getOwnCapitalAccount());
        citicAccounts.setOwnCapitalAccountName(accountsModule.getOwnCapitalAccountName());
        citicAccounts.setTradingFundAccount(accountsModule.getTradingFundAccount());
        citicAccounts.setTradingFundAccountName(accountsModule.getTradingFundAccountName());
        accounts.setCiticAccounts(citicAccounts);
        return accounts;
    }
}
