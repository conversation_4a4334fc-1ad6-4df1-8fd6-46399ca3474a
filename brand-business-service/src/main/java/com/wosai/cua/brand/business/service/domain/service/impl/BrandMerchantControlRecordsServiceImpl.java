package com.wosai.cua.brand.business.service.domain.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantControlRecordsMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantControlRecordsDO;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantControlRecordsService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【brand_merchant_control_records(品牌商户管控记录表)】的数据库操作Service实现
* @createDate 2024-08-06 14:36:01
*/
@Service
public class BrandMerchantControlRecordsServiceImpl extends ServiceImpl<BrandMerchantControlRecordsMapper, BrandMerchantControlRecordsDO>
    implements BrandMerchantControlRecordsService {

}




