package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OpenApiMerchantAccountDTO {
    /**
     * 子账户名称
     */
    @JsonProperty("sub_account_name")
    private String subAccountName;
    /**
     * 子账户
     * PAB-子账号
     * MY_BANK-云资金商户号
     */
    @JsonProperty("sub_account")
    private String subAccount;

    /**
     * 会员id
     * PAB-会员id
     * MY_BANK-外部商户号
     */
    @JsonProperty("member_id")
    private String memberId;

    /**
     * 充值账户名称
     */
    @JsonProperty("top_up_account_name")
    private String topUpAccountName;

    /**
     *  充值账户
     */
    @JsonProperty("top_up_account_no")
    private String topUpAccountNo;
}
