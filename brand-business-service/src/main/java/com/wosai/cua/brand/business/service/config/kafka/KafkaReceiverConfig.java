package com.wosai.cua.brand.business.service.config.kafka;

import com.wosai.cua.brand.business.service.consumer.MyKafkaAvroDeserializer;
import io.confluent.kafka.serializers.KafkaAvroDeserializerConfig;
import io.confluent.kafka.serializers.KafkaAvroSerializerConfig;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.lang.ArrayUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


@Configuration
@EnableKafka
public class KafkaReceiverConfig {

    @Value("${brand.kafka.old.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${brand.kafka.old.schema.registry.url}")
    private String registryServers;

    @Value("${brand.kafka.schema.registry.url}")
    private String aliRegistryServers;

    @Value("${brand.kafka.bootstrap-servers}")
    private String aliBootstrapServers;

    private static final String CONSUMER_GROUP_ID = "brand-business";

    @Autowired
    Environment environment;

    /**
     * 测试环境阿里云、生产环境自建
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, GenericRecord> dataBusKafkaListenerContainerFactoryV2() {
        ConcurrentKafkaListenerContainerFactory<String, GenericRecord> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(aliKafkaConsumerConfigsV2()));
        return factory;
    }


    /**
     * 生产在自建，测试在阿里
     *
     * @return
     */
    @Bean
    public Map<String, Object> aliKafkaConsumerConfigsV2() {
        Map<String, Object> props = new HashMap<>();
        if (!ArrayUtils.isEmpty(environment.getActiveProfiles()) && Objects.equals(environment.getActiveProfiles()[0], "prod")) {//添加测试环境支付目录
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
            props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryServers);
            props.put(ConsumerConfig.GROUP_ID_CONFIG, CONSUMER_GROUP_ID);
            props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
        } else {
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, aliBootstrapServers);
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
            props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, aliRegistryServers);
            props.put(ConsumerConfig.GROUP_ID_CONFIG, CONSUMER_GROUP_ID);
            props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
        }
        return props;
    }
}
