package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【brand_config(品牌配置表)】的数据库操作Service
 * @createDate 2024-03-27 14:20:39
 */
public interface BrandConfigDomainService {

    /**
     * 插入品牌配置
     *
     * @param configModule
     */
    void insertBrandConfig(BrandConfigModule configModule);

    /**
     * 更新品牌配置
     *
     * @param configModule
     */
    void updateBrandConfig(BrandConfigModule configModule);

    /**
     * 根据品牌id查询品牌配置
     *
     * @param brandId
     * @return
     */
    BrandConfigModule getBrandConfigByBrandId(String brandId);

    /**
     * 根据品牌id查询品牌配置
     *
     * @param brandId
     * @param clazz
     * @return
     */
    <R> R getConfigByBrandId(String brandId, Class<R> clazz);

    /**
     * 根据品牌id列表查询品牌配置
     *
     * @param brandIds
     * @return
     */
    List<BrandConfigModule> getBrandConfigByBrandIds(List<String> brandIds);

    /**
     * 根据渠道id和渠道类型查询品牌配置
     *
     * @param channelId 渠道id
     * @param channelType 渠道类型
     * @return 配置模型
     */
    BrandConfigModule getBrandConfigByChannelIdAndChannelType(String channelId, String channelType);

    /**
     * 处理平安银行配置
     */
    @Deprecated
    void dealPabConfig();
}
