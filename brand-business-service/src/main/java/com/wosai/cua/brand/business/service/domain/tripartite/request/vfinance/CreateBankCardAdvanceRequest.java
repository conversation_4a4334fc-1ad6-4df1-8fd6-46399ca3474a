package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;

public class CreateBankCardAdvanceRequest extends VfinanceBaseRequest{

    /**
     * 鉴权信息(验证码)
     */
    @JSONField(name = "msg_code")
    private String msgCode;
    /**
     * 绑定提现卡接口返回的银行卡ID
     */
    @JSONField(name = "bank_card_id")
    private String bankCardId;
    /**
     * 校验方式：1:往账鉴权,3:银联鉴权(短信)
     */
    @JSONField(name = "check_mode")
    private String checkMode;
    /**
     * 银行打款金额：企业会员绑卡（往账鉴权使用）
     */
    @JSONField(name = "bank_payment_amount")
    private String bankPaymentAmount;

    public CreateBankCardAdvanceRequest(String partnerId) {
        super();
        super.service = CREATE_BANK_CARD_ADVANCE;
        super.partnerId = partnerId;
    }

    public String getMsgCode() {
        return msgCode;
    }

    public void setMsgCode(String msgCode) {
        this.msgCode = msgCode;
    }

    public String getBankCardId() {
        return bankCardId;
    }

    public void setBankCardId(String bankCardId) {
        this.bankCardId = bankCardId;
    }

    public String getCheckMode() {
        return checkMode;
    }

    public void setCheckMode(String checkMode) {
        this.checkMode = checkMode;
    }

    public String getBankPaymentAmount() {
        return bankPaymentAmount;
    }

    public void setBankPaymentAmount(String bankPaymentAmount) {
        this.bankPaymentAmount = bankPaymentAmount;
    }
}
