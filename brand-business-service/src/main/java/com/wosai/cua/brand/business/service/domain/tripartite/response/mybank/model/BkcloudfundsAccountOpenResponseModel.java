package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 商户通用开户接口<ant.mybank.bkcloudfunds.account.open>
 * 响应模型
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class BkcloudfundsAccountOpenResponseModel extends MybankObject {

    private static final long serialVersionUID = 8138966839867900163L;
    /**
     * 返回码组件
     */
    @XmlElementRef
    private RespInfo respInfo;

    /**
     * 合作方机构号（网商银行分配）
     */
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;

    /**
     * 网商商户号
     */
    @XmlElement(name = "MerchantId")
    private String merchantId;

    /**
     * 外部流水号
     */
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;

    /**
     * 开户行行号
     */
    @XmlElement(name = "BranchNo")
    private String branchNo;

    /**
     * 开户行名称
     */
    @XmlElement(name = "BranchName")
    private String branchName;

    /**
     * 网商卡号  卡号不是必然返回
     */
    @XmlElement(name = "BankCardNo")
    private String bankCardNo;

    /**
     * 证件号
     */
    @XmlElement(name = "BankCertName")
    private String bankCertName;

    public RespInfo getRespInfo() {
        return respInfo;
    }

    public void setRespInfo(RespInfo respInfo) {
        this.respInfo = respInfo;
    }

    public String getIsvOrgId() {
        return isvOrgId;
    }

    public void setIsvOrgId(String isvOrgId) {
        this.isvOrgId = isvOrgId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getBranchNo() {
        return branchNo;
    }

    public void setBranchNo(String branchNo) {
        this.branchNo = branchNo;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getBankCertName() {
        return bankCertName;
    }

    public void setBankCertName(String bankCertName) {
        this.bankCertName = bankCertName;
    }
}