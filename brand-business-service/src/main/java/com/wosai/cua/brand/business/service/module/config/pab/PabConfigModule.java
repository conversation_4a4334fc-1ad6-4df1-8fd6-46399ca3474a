package com.wosai.cua.brand.business.service.module.config.pab;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.brand.pab.PabConfigDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.PurposeOfTransferEnum;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
public class PabConfigModule extends ConfigModule {
    /**
     * 是否允许登录
     */
    private String partnerId;

    /**
     * 是否提交营业执照
     */
    private Boolean submitBusinessLicense;

    public static BrandConfigDTO convert(PabConfigModule configModule, String brandId, Boolean needGetConfig) {
        if (configModule == null) {
            return null;
        }
        BrandConfigDTO brandConfigDTO = new BrandConfigDTO();
        brandConfigDTO.setBrandId(brandId);
        brandConfigDTO.setFundManagementCompanyCode(FundManagementCompanyEnum.PAB.getFundManagementCompanyCode());
        if (Objects.nonNull(needGetConfig) && Boolean.TRUE.equals(needGetConfig)){
            PabConfigDTO dto = PabConfigDTO.builder()
                    .partnerId(configModule.getPartnerId())
                    .build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setSubmitBusinessLicense(!Objects.isNull(configModule.getSubmitBusinessLicense()) && configModule.getSubmitBusinessLicense());
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setPabConfig(dto);
        }else {
            PabConfigDTO dto = PabConfigDTO.builder().build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setSubmitBusinessLicense(!Objects.isNull(configModule.getSubmitBusinessLicense()) && configModule.getSubmitBusinessLicense());
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setPabConfig(dto);
        }
        return brandConfigDTO;
    }

    public static BrandConfigDTO convert(String s, String brandId, Boolean needGetConfig) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        PabConfigModule configModule = JSON.parseObject(s, PabConfigModule.class);
        BrandConfigDTO brandConfigDTO = new BrandConfigDTO();
        brandConfigDTO.setBrandId(brandId);
        brandConfigDTO.setFundManagementCompanyCode(FundManagementCompanyEnum.PAB.getFundManagementCompanyCode());
        if (Objects.nonNull(needGetConfig) && Boolean.TRUE.equals(needGetConfig)){
            PabConfigDTO dto = PabConfigDTO.builder()
                    .partnerId(configModule.getPartnerId())
                    .build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setSubmitBusinessLicense(!Objects.isNull(configModule.getSubmitBusinessLicense()) && configModule.getSubmitBusinessLicense());
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setPabConfig(dto);
        }else {
            PabConfigDTO dto = PabConfigDTO.builder().build();
            dto.setAllowLogin(Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin());
            dto.setOpenTransfer(Objects.isNull(configModule.getOpenTransfer()) || configModule.getOpenTransfer());
            dto.setPurposeList(PurposeOfTransferEnum.getByCodeList(configModule.getPurposeList()));
            dto.setSubmitBusinessLicense(!Objects.isNull(configModule.getSubmitBusinessLicense()) && configModule.getSubmitBusinessLicense());
            dto.setNeedSpecialTreatment(configModule.getNeedSpecialTreatment());
            brandConfigDTO.setPabConfig(dto);
        }
        return brandConfigDTO;
    }

    public static PabConfigModule convert(PabConfigDTO pabConfigDTO) {
        if (pabConfigDTO == null) {
            return null;
        }
        PabConfigModule pabConfigModule = new PabConfigModule();
        pabConfigModule.setAllowLogin(pabConfigDTO.getAllowLogin());
        pabConfigModule.setOpenTransfer(pabConfigDTO.getOpenTransfer());
        pabConfigModule.setPartnerId(pabConfigDTO.getPartnerId());
        pabConfigModule.setPurposeList(PurposeOfTransferEnum.getCodeListByEnumList(pabConfigDTO.getPurposeList()));
        pabConfigModule.setSubmitBusinessLicense(!Objects.isNull(pabConfigDTO.getSubmitBusinessLicense()) && pabConfigDTO.getSubmitBusinessLicense());
        pabConfigModule.setNeedSpecialTreatment(pabConfigDTO.getNeedSpecialTreatment());
        return pabConfigModule;
    }
}
