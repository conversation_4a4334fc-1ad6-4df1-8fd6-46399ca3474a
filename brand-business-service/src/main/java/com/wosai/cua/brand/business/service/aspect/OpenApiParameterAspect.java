package com.wosai.cua.brand.business.service.aspect;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.exception.OpenApiParamsException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.config.apollo.dto.AppIdBrandMapping;
import com.wosai.cua.brand.business.service.context.OpenApiContext;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.OpenApiBaseRequest;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Aspect
@Component
@Slf4j
public class OpenApiParameterAspect {

    private final BrandDomainService brandDomainService;

    private final ApolloConfig apolloConfig;

    @Autowired
    public OpenApiParameterAspect(BrandDomainService brandDomainService, ApolloConfig apolloConfig) {
        this.brandDomainService = brandDomainService;
        this.apolloConfig = apolloConfig;
    }

    @Around("execution(* com.wosai.cua.brand.business.service.controller.rest.OpenApiController.*(..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        // 这里可以对入参进行处理，例如打印
        for (Object arg : args) {
            if (arg instanceof OpenApiBaseRequest) {
                OpenApiBaseRequest request = JSON.parseObject(JSON.toJSONString(arg), OpenApiBaseRequest.class);
                handleRequest(request);
            }
        }
        return joinPoint.proceed(args);
    }

    /**
     * 处理参数，将appid，brandSn，brandId封装到上下文中
     *
     * @param request 入参
     */
    private void handleRequest(OpenApiBaseRequest request) {
        String appId = request.getAppId();
        if (StringUtils.isBlank(appId)) {
            throw new OpenApiParamsException(BrandBusinessExceptionEnum.OPEN_API_PARAM_ERROR.getCode().toString(),BrandBusinessExceptionEnum.OPEN_API_PARAM_ERROR.getMessage());
        }
        String brandSn = request.getBrandSn();
        if (StringUtils.isBlank(brandSn)) {
            throw new OpenApiParamsException(BrandBusinessExceptionEnum.OPEN_API_BRAND_SN_NOT_BE_NULL.getCode().toString(),BrandBusinessExceptionEnum.OPEN_API_BRAND_SN_NOT_BE_NULL.getMessage());
        }
        String clientSn = request.getClientSn();
        if (StringUtils.isBlank(clientSn)) {
            throw new OpenApiParamsException(BrandBusinessExceptionEnum.OPEN_API_REQUEST_SERIAL_NUMBER_NOT_BE_NULL.getCode().toString(),BrandBusinessExceptionEnum.OPEN_API_REQUEST_SERIAL_NUMBER_NOT_BE_NULL.getMessage());
        }
        List<AppIdBrandMapping> appIdBrandMappingList = apolloConfig.getAppIdBrandMappingList();
        if (CollectionUtils.isNotEmpty(appIdBrandMappingList)) {
            Map<String, List<String>> listMap = appIdBrandMappingList.stream().collect(Collectors.toMap(AppIdBrandMapping::getAppid, AppIdBrandMapping::getBrandSnList));
            if (!listMap.containsKey(appId) || !listMap.get(appId).contains(brandSn)){
                log.warn("appId:{} brandSn:{} is not permission",appId,brandSn);
                throw new OpenApiParamsException(BrandBusinessExceptionEnum.OPEN_API_BRAND_NOT_PERMISSION.getCode().toString(),BrandBusinessExceptionEnum.OPEN_API_BRAND_NOT_PERMISSION.getMessage());
            }
        }
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandSn(brandSn);
        if (brandModule == null) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.OPEN_API_BRAND_IS_NOT_EXIST);
        }
        OpenApiContext.Context context = new OpenApiContext.Context();
        context.setBrandId(brandModule.getBrandId());
        context.setBrandSn(brandSn);
        context.setAppId(appId);
        context.setClientSn(clientSn);
        context.setFundManagementCompanyEnum(brandModule.getFundManagementCompanyCode());
        OpenApiContext.set(context);
    }
}