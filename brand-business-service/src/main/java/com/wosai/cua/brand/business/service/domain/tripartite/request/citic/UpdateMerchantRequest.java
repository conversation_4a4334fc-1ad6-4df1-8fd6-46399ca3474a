package com.wosai.cua.brand.business.service.domain.tripartite.request.citic;

import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@XmlRootElement(name = "ROOT")
@XmlAccessorType(XmlAccessType.FIELD)
public class UpdateMerchantRequest extends BaseCiticRequest implements TripartiteSystemCallRequest {

    /**
     * 用户编号
     * 是否必填：是
     * 银行给用户分配的编号，银行保证唯一性； 后续交易均使用该编号唯一定位用户。
     */
    @XmlElement(name = "USER_ID")
    private String userId;

    /**
     * 用户角色
     * 是否必填：是
     * 联系银行业务同事获取
     */
    @XmlElement(name = "USER_ROLE")
    private String userRole;

    /**
     * 用户类型
     * 是否必填：是
     * 2-企业
     * 3-个体工商户
     */
    @XmlElement(name = "USER_TYPE")
    private String userType;

    /**
     * 用户姓名
     * 是否必填：否
     */
    @XmlElement(name = "USER_NM")
    private String userName;

    /**
     * 用户证件类型
     * 是否必填：否
     * 企业用户必填
     */
    @XmlElement(name = "USER_CARD_TP")
    private String userCardType;

    /**
     * 用户证件号码
     * 是否必填：否
     * 若用户证件类型上送，则该字段必填
     */
    @XmlElement(name = "USER_CARD_NO")
    private String userCardNo;

    /**
     * 用户手机号/电话
     * 是否必填：否
     * 个人用户必填
     */
    @XmlElement(name = "USER_PHONE")
    private String userPhone;

    /**
     * 用户地址
     * 是否必填：否
     */
    @XmlElement(name = "USER_ADD")
    private String userAddress;

    /**
     * 企业法人姓名
     * 是否必填：否
     */
    @XmlElement(name = "CORP_NM")
    private String legalPersonName;

    /**
     * 企业法人证件类型
     * 是否必填：否
     */
    @XmlElement(name = "CORP_ID_TYPE_NEW")
    private String legalPersonIdType;

    /**
     * 企业法人证件号码
     * 是否必填：否
     */
    @XmlElement(name = "CORP_ID_NUM_NEW")
    private String legalPersonId;

    /**
     * 自然人性别
     * 是否必填：否
     * 0-女, 1-男
     * 用户性质为个人非必填，其他情况忽略
     */
    @XmlElement(name = "PERSON_SEX")
    private String personSex;

    /**
     * 国家/地区
     * 是否必填：否
     * 156-中国大陆, 158-中国台湾, 344-中国香港, 446-中国澳门, 999-其他
     * 用户性质为个人非必填，其他情况忽略
     */
    @XmlElement(name = "PERSON_NATIONALITY")
    private String personNationality;

    /**
     * 自然人职业
     * 是否必填：否
     * 用户性质为个人非必填，其他情况忽略
     */
    @XmlElement(name = "PERSON_OCCUPATION")
    private String personOccupation;

    /**
     * 自然人身份证件截止日期
     * 是否必填：否
     * 用户性质为个人非必填，其他情况忽略
     */
    @XmlElement(name = "PERSON_ID_DATE")
    private String personIdDate;

    /**
     * 自然人身份证件发证日期
     * 是否必填：否
     * 用户性质为个人非必填，其他情况忽略
     */
    @XmlElement(name = "PERSON_ID_ISSUE_DATE")
    private String personIdIssueDate;

    /**
     * 企业经营范围
     * 是否必填：否
     * 子商户性质为企业/个体工商户非必填，其他情况忽略
     */
    @XmlElement(name = "COMPANY_REALADDR")
    private String companyRealAddr;

    /**
     * 企业证件有效期
     * 是否必填：否
     * 子商户性质为企业/个体工商户非必填，其他情况忽略
     */
    @XmlElement(name = "COMPANY_DATE")
    private String companyDate;

    /**
     * 企业法人身份证件有效期
     * 是否必填：否
     * 子商户性质为企业/个体工商户非必填，其他情况忽略
     */
    @XmlElement(name = "ARTIF_ID_DATE")
    private String artifIdDate;

    /**
     * 受益人姓名
     * 是否必填：否
     * 子商户性质为企业/个体工商户非必填，其他情况忽略
     */
    @XmlElement(name = "COMPANY_PER")
    private String companyPer;

    /**
     * 受益人地址
     * 是否必填：否
     * 子商户性质为企业/个体工商户非必填，其他情况忽略
     */
    @XmlElement(name = "BNF_ADDR")
    private String bnfAddr;

    /**
     * 受益人证件类型
     * 是否必填：否
     * 同用户证件类型
     * 子商户性质为企业/个体工商户非必填，其他情况忽略
     */
    @XmlElement(name = "BNF_CARD_TP")
    private String bnfCardType;

    /**
     * 受益人证件号
     * 是否必填：否
     * 子商户性质为企业/个体工商户非必填，其他情况忽略
     */
    @XmlElement(name = "BNF_CARD_NO")
    private String bnfCardNo;

    /**
     * 受益人证件有效期
     * 是否必填：否
     * 子商户性质为企业/个体工商户非必填，其他情况忽略
     */
    @XmlElement(name = "BNF_ID_DEDL_DATE")
    private String bnfIdDedlDate;

    /**
     * 签约协议号
     * 是否必填：否
     * 最大长度32
     */
    @XmlElement(name = "AGRM_NUM")
    private String signingAgreementNo;

    /**
     * 签约标志
     * 是否必填：否
     * 1:是 0:否
     */
    @XmlElement(name = "SIGCT_FLG")
    private String signingFlag;

    /**
     * 发起方保留域
     * 是否必填：否
     */
    @XmlElement(name = "REQ_RESERVED")
    private String reqReserved;


    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.CITIC_UPDATE_USER;
    }

    public UpdateMerchantRequest() {
        super();
        super.setTransCode("********");
    }

    public static UpdateMerchantRequest builder(CiticBankConfigModule configModule, BrandMerchantModule brandMerchantModule, String userRole, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense) {
        UpdateMerchantRequest request = new UpdateMerchantRequest();
        request.setMerchantId(configModule.getMerchantId());
        request.setUserId(brandMerchantModule.getMemberId());
        request.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + brandMerchantModule.getMerchantSn().substring(brandMerchantModule.getMerchantSn().length() - 8));
        request.setLaasSsn(UUID.randomUUID().toString().replace("-", ""));
        request.setUserRole(userRole);
        switch (merchantBusinessLicense.getType()) {
            case 2:
                request.setUserName(merchantBusinessLicense.getName());
                request.setUserCardType("03");
                request.setUserCardNo(merchantBusinessLicense.getNumber());
                break;
            case 0:
            case 1:
            default:
                throw new BrandBusinessException("商户类型错误");

        }
        return request;
    }
}
