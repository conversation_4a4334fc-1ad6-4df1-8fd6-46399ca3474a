package com.wosai.cua.brand.business.service.business;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.account.BkCloudFundsAccountOpenRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantArrangementInfoQueryRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.account.BkcloudfundsAccountOpenRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.merchant.MerchantArrangementInfoQueryRequestModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.account.BkCloudFundsAccountOpenResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantArrangementInfoQueryResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.BkcloudfundsAccountOpenResponseModel;
import com.wosai.cua.brand.business.service.enums.third.MyBankAccountTypeEnum;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.ChangeBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Service
public class MyBankBusiness {

    private final BrandConfigDomainService brandConfigDomainService;

    private final BrandDomainService brandDomainService;

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private TripartiteSystemCallService myBankCallService;

    @PostConstruct
    public void initMyBankCallService() {
        for (TripartiteSystemCallService tripartiteSystemCallService : tripartiteSystemCallServices) {
            if (tripartiteSystemCallService.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.MY_BANK)) {
                myBankCallService = tripartiteSystemCallService;
                break;
            }
        }
    }

    @Autowired
    public MyBankBusiness(BrandConfigDomainService brandConfigDomainService, BrandDomainService brandDomainService, List<TripartiteSystemCallService> tripartiteSystemCallServices) {
        this.brandConfigDomainService = brandConfigDomainService;
        this.brandDomainService = brandDomainService;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
    }

    public void openAccount(String brandId, String brandMerchantId) {
        MyBankConfigModule myBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandId, MyBankConfigModule.class);
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, brandMerchantId);
        BkCloudFundsAccountOpenRequest request = new BkCloudFundsAccountOpenRequest(myBankConfigModule.getAppId());
        BkcloudfundsAccountOpenRequestModel accountOpenRequestModel = new BkcloudfundsAccountOpenRequestModel();
        accountOpenRequestModel.setIsvOrgId(myBankConfigModule.getIsvOrgId());
        accountOpenRequestModel.setMerchantId(brandMerchantModule.getSubAccountNo());
        accountOpenRequestModel.setAcctType(MyBankAccountTypeEnum.TRADE_DEPOSIT.getCode());
        accountOpenRequestModel.setOutTradeNo(UUID.randomUUID().toString().replace(MybankConstants.CONNECT_SYMBOL_STRING, MybankConstants.NULL_STRING));
        accountOpenRequestModel.setExtInfo("");
        request.setRequestBody(accountOpenRequestModel);
        BkCloudFundsAccountOpenResponse response =  myBankCallService.call(request, BkCloudFundsAccountOpenResponse.class, myBankConfigModule);
        BkcloudfundsAccountOpenResponseModel bkcloudfundsAccountOpenResponseModel = response.getBkcloudfundsAccountOpen().getBkcloudfundsAccountOpenResponseModel();
        if (bkcloudfundsAccountOpenResponseModel.getRespInfo().getResultStatus().equals(MybankConstants.SUCCESS_STRING)){
            ChangeBrandMerchantModule changeBrandMerchantModule = new ChangeBrandMerchantModule();
            changeBrandMerchantModule.setId(brandMerchantModule.getId());
            changeBrandMerchantModule.setTopUpAccountNo(bkcloudfundsAccountOpenResponseModel.getBankCardNo());
            changeBrandMerchantModule.setTopUpAccountName(bkcloudfundsAccountOpenResponseModel.getBankCertName());
            brandDomainService.updateBrandMerchantByFields(changeBrandMerchantModule);
        }
    }

    public void synchronousProtocolStatus(String brandId, String brandMerchantId){
        MyBankConfigModule myBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandId, MyBankConfigModule.class);
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, brandMerchantId);
        if (Objects.nonNull(brandMerchantModule)
                && Objects.nonNull(myBankConfigModule)
                && StringUtils.isNotEmpty(myBankConfigModule.getIsvOrgId())
                && StringUtils.isNotEmpty(brandMerchantModule.getMemberId())
                && Objects.nonNull(brandMerchantModule.getExtra())
                && Objects.nonNull(brandMerchantModule.getExtra().getMyBankMerchantExtraModule())
                && StringUtils.isNotEmpty(brandMerchantModule.getExtra().getMyBankMerchantExtraModule().getArrangementType())
        ){
            MerchantArrangementInfoQueryRequest request = this.getMerchantArrangementInfoQueryRequest(myBankConfigModule, brandMerchantModule);
            MerchantArrangementInfoQueryResponse merchantArrangementInfoQueryResponse = myBankCallService.call(request, MerchantArrangementInfoQueryResponse.class, myBankConfigModule);
            String arrangementNo = merchantArrangementInfoQueryResponse.getMerchantprodMerchantArrangementInfoQuery().getMerchantArrangementInfoQueryResponseModel().getArrangementNo();
            String status = merchantArrangementInfoQueryResponse.getMerchantprodMerchantArrangementInfoQuery().getMerchantArrangementInfoQueryResponseModel().getStatus();
            if (StringUtils.isNotEmpty(arrangementNo) && StringUtils.isNotEmpty(status)){
                brandMerchantModule.getExtra().getMyBankMerchantExtraModule().setArrangementNo(arrangementNo);
                brandMerchantModule.getExtra().getMyBankMerchantExtraModule().setArrangementStatus(status);
                brandDomainService.updateBrandMerchant(brandMerchantModule);
            }else{
                brandMerchantModule.setExtra(null);
                brandDomainService.updateBrandMerchant(brandMerchantModule);
            }
        }
    }

    private MerchantArrangementInfoQueryRequest getMerchantArrangementInfoQueryRequest(MyBankConfigModule myBankConfigModule, BrandMerchantModule brandMerchantModule) {
        MerchantArrangementInfoQueryRequest request = new MerchantArrangementInfoQueryRequest(myBankConfigModule.getAppId());
        MerchantArrangementInfoQueryRequestModel merchantprodMerchantArrangementInfoQueryRequestModel = new MerchantArrangementInfoQueryRequestModel();
        merchantprodMerchantArrangementInfoQueryRequestModel.setIsvOrgId(myBankConfigModule.getIsvOrgId());
        merchantprodMerchantArrangementInfoQueryRequestModel.setMerchantId(brandMerchantModule.getMemberId());
        merchantprodMerchantArrangementInfoQueryRequestModel.setArrangementType(brandMerchantModule.getExtra().getMyBankMerchantExtraModule().getArrangementType());
        request.setRequestBody(merchantprodMerchantArrangementInfoQueryRequestModel);
        return request;
    }
}
