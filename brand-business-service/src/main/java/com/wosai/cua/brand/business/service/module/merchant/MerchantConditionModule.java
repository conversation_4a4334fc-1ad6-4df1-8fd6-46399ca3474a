package com.wosai.cua.brand.business.service.module.merchant;

import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.service.module.PageModule;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantConditionModule extends PageModule {

    private String merchantName;

    private String merchantSn;

    private String merchantId;

    private String brandId;

    private List<String> brandIds;

    private List<String> types;

    private List<String> merchantTypes;

    private String ucUserId;

    private List<Long> strategyIdList;

    private String sqbStoreId;

    private String meiTuanStoreSn;

    private String elmStoreSn;

    private String dyStoreSn;

    private List<String> merchantIds;

    private Integer strategyNotNull;

    private String accountOpenStatus;

    /**
     * 外部商户号
     */
    private String outMerchantNo;

    /**
     * 银行卡状态
     * @see BankCardActivateStatusEnum
     */
    private String bankCardActivateStatus;

    /**
     * 收付通品牌标识
     */
    private Integer sftTag;

    /**
     * 启用收付通标识
     */
    private Integer enableSft;
}
