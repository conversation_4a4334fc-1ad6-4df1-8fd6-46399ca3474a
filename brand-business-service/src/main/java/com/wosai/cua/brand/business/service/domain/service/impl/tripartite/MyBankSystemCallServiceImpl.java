package com.wosai.cua.brand.business.service.domain.service.impl.tripartite;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.DefaultMyBankClient;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MyBankClient;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.account.BkCloudFundsAccountOpenRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantAppletPreRegisterRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantAppletRegisterQueryRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantArrangementAuditRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantArrangementInfoQueryRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist.MerchantprodMerchantArrangeMentAuditResponseModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.account.BkCloudFundsAccountOpenResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantAppletPreRegisterResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantAppletRegisterQueryResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantArrangementInfoQueryResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantprodMerchantArrangeMentAuditResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.BkcloudfundsAccountOpenResponseModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.MerchantAppletPreRegisterResponseModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.MerchantAppletRegisterQueryResponseModel;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.MerchantArrangementInfoQueryResponseModel;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.function.Function;

/**
 * 网商系统接口实现
 */
@Service
@Slf4j
public class MyBankSystemCallServiceImpl implements TripartiteSystemCallService {

    @Value("${mybank-service-address}")
    private String myBankUrl;

    @Value("${mybank-fcsupergw-address}")
    private String fcsupergwUrl;

    private static final Map<FunctionEnum, Function<TripartiteSystemCallRequest, Function<MyBankConfigModule, TripartiteSystemCallResponse>>> FUNCTION_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void initFunctionMap() {
        FUNCTION_MAP.put(FunctionEnum.MY_BANK_MERCHANT_APPLET_PRE_REGISTER,request -> configModule -> this.invokeMerchantAppletPreRegister(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.MY_BANK_OPEN_FUNDS_ACCOUNT,request -> configModule -> this.invokeBkCloudFundsAccountOpen(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.MY_BANK_RESCIND_AN_AGREEMENT,request -> configModule -> this.invokeMerchantArrangementAudit(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.MY_BANK_MERCHANT_ARRANGEMENT_INFO_QUERY,request -> configModule -> this.invokeMerchantArrangementInfoQuery(request, configModule));
        FUNCTION_MAP.put(FunctionEnum.MY_BANK_APPLET_REGISTER_QUERY,request -> configModule -> this.invokeMerchantAppletRegisterQuery(request, configModule));
    }

    @Override
    public FundManagementCompanyEnum getFundManagementCompanyEnum() {
        return FundManagementCompanyEnum.MY_BANK;
    }

    @Override
    public <T extends TripartiteSystemCallRequest, R extends TripartiteSystemCallResponse, C extends ConfigModule> R call(T request, Class<R> clazz, C configModule) {
        if (configModule instanceof MyBankConfigModule) {
            MyBankConfigModule myBankConfigModule = (MyBankConfigModule) configModule;
            log.info("网商配置: {}", JSON.toJSONString(myBankConfigModule));
            Function<TripartiteSystemCallRequest, Function<MyBankConfigModule, TripartiteSystemCallResponse>> tripartiteSystemCallRequestFunctionFunction = FUNCTION_MAP.get(request.getFunctionEnum());
            if (tripartiteSystemCallRequestFunctionFunction != null) {
                Function<MyBankConfigModule, TripartiteSystemCallResponse> tripartiteSystemCallResponseFunction = tripartiteSystemCallRequestFunctionFunction.apply(request);
                if (tripartiteSystemCallResponseFunction != null) {
                    return handleResponse(tripartiteSystemCallResponseFunction.apply(myBankConfigModule), clazz);
                }
            }
        } else {
            log.error("未找到对应的网商配置或者接口, configModule: {}", configModule.getClass().getSimpleName());
            return handleError(clazz);
        }
        return handleError(clazz);
    }

    private <R> R handleResponse(TripartiteSystemCallResponse response, Class<R> clazz) {
        if (response != null) {
            if (response.getResultCode().equals(TripartiteSystemCallResponse.ResultCodeEnum.FAIL)) {
                throw new BrandBusinessException(response.getResultMsg());
            }
            // 这里假设response对象具有统一的RespInfo结构来判断成功与否
            return JSON.parseObject(JSON.toJSONString(response), clazz);
        }
        log.error("响应为空");
        return handleError(clazz);
    }

    private <R> R handleError(Class<R> clazz) {
        // 假设定义了一个ErrorResponse对象用于表示错误情况
        TripartiteSystemCallResponse tripartiteSystemCallResponse = new TripartiteSystemCallResponse();
        tripartiteSystemCallResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.FAIL);
        tripartiteSystemCallResponse.setResultMsg("调用网商预入驻接口失败");
        return JSON.parseObject(JSON.toJSONString(tripartiteSystemCallResponse), clazz);
    }

    /**
     * 调用网商预入驻接口
     *
     * @param request      请求
     * @param configModule 配置
     */
    private MerchantAppletPreRegisterResponse invokeMerchantAppletPreRegister(TripartiteSystemCallRequest request, MyBankConfigModule configModule) {
        MerchantAppletPreRegisterRequest merchantAppletPreRegisterRequest = null;
        if (request instanceof MerchantAppletPreRegisterRequest) {
            merchantAppletPreRegisterRequest = (MerchantAppletPreRegisterRequest) request;
        }
        //实例化客户端
        MyBankClient myBankClient = new DefaultMyBankClient(myBankUrl, configModule.getIsvPrivateKey(), configModule.getPublicKey());
        try {
            MerchantAppletPreRegisterResponse merchantAppletPreRegisterResponse = myBankClient.execute(merchantAppletPreRegisterRequest);
            MerchantAppletPreRegisterResponseModel merchantprodMerchAppletPreRegisterResponseModel = merchantAppletPreRegisterResponse.getMerchantprodMerchAppletPreRegister().getMerchantprodMerchAppletPreRegisterResponseModel();
            merchantAppletPreRegisterResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.getByMyBankCode(merchantprodMerchAppletPreRegisterResponseModel.getRespInfo().getResultStatus()));
            merchantAppletPreRegisterResponse.setResultMsg(merchantprodMerchAppletPreRegisterResponseModel.getRespInfo().getResultMsg());
            // 客户端请求报文
            String requestContent = merchantAppletPreRegisterResponse.getRequestContent();
            log.info("调用网商预入驻接口客户端请求报文:{}", requestContent);
            // 服务端返回报文
            String xmlContent = merchantAppletPreRegisterResponse.getXmlContent();
            log.info("调用网商预入驻接口服务端返回报文:{}", xmlContent);
            return merchantAppletPreRegisterResponse;
        } catch (MybankApiException e) {
            log.error("调用网商预入驻接口异常", e);
        }
        return null;
    }

    private BkCloudFundsAccountOpenResponse invokeBkCloudFundsAccountOpen(TripartiteSystemCallRequest request, MyBankConfigModule configModule) {
        BkCloudFundsAccountOpenRequest bkCloudFundsAccountOpenRequest = null;
        if (request instanceof BkCloudFundsAccountOpenRequest) {
            bkCloudFundsAccountOpenRequest = (BkCloudFundsAccountOpenRequest) request;
        }
        //实例化客户端
        MyBankClient myBankClient = new DefaultMyBankClient(fcsupergwUrl, configModule.getIsvPrivateKey(), configModule.getPublicKey());
        try {
            BkCloudFundsAccountOpenResponse bkcloudfundsAccountOpenResponse = myBankClient.execute(bkCloudFundsAccountOpenRequest);
            BkcloudfundsAccountOpenResponseModel bkcloudfundsAccountOpenResponseModel = bkcloudfundsAccountOpenResponse.getBkcloudfundsAccountOpen().getBkcloudfundsAccountOpenResponseModel();
            bkcloudfundsAccountOpenResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.getByMyBankCode(bkcloudfundsAccountOpenResponseModel.getRespInfo().getResultStatus()));
            bkcloudfundsAccountOpenResponse.setResultMsg(bkcloudfundsAccountOpenResponseModel.getRespInfo().getResultMsg());
            // 客户端请求报文
            String requestContent = bkcloudfundsAccountOpenResponse.getRequestContent();
            log.info("调用网商商户通用开户接口客户端请求报文:{}", requestContent);
            // 服务端返回报文
            String xmlContent = bkcloudfundsAccountOpenResponse.getXmlContent();
            log.info("调用网商商户通用开户接口服务端返回报文:{}", xmlContent);
            return bkcloudfundsAccountOpenResponse;
        } catch (MybankApiException e) {
            log.error("调用网商商户通用开户接口异常", e);
        }
        return null;
    }

    private MerchantprodMerchantArrangeMentAuditResponse invokeMerchantArrangementAudit(TripartiteSystemCallRequest request, MyBankConfigModule configModule) {
        MerchantArrangementAuditRequest merchantArrangementAuditRequest = null;
        if (request instanceof MerchantArrangementAuditRequest) {
            merchantArrangementAuditRequest = (MerchantArrangementAuditRequest) request;
        }
        //实例化客户端
        MyBankClient myBankClient = new DefaultMyBankClient(myBankUrl, configModule.getIsvPrivateKey(), configModule.getPublicKey());
        try {
            MerchantprodMerchantArrangeMentAuditResponse merchantprodMerchantArrangeMentAuditResponse = myBankClient.execute(merchantArrangementAuditRequest);
            MerchantprodMerchantArrangeMentAuditResponseModel merchantprodMerchantArrangeMentAuditResponseModel = merchantprodMerchantArrangeMentAuditResponse.getMerchantprodMerchantArrangeMentAudit().getMerchantprodMerchantArrangeMentAuditResponseModel();
            merchantprodMerchantArrangeMentAuditResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.getByMyBankCode(merchantprodMerchantArrangeMentAuditResponseModel.getRespInfo().getResultStatus()));
            merchantprodMerchantArrangeMentAuditResponse.setResultMsg(merchantprodMerchantArrangeMentAuditResponseModel.getRespInfo().getResultMsg());
            // 客户端请求报文
            String requestContent = merchantprodMerchantArrangeMentAuditResponse.getRequestContent();
            log.info("调用网商商户代扣协议解除接口客户端请求报文:{}", requestContent);
            // 服务端返回报文
            String xmlContent = merchantprodMerchantArrangeMentAuditResponse.getXmlContent();
            log.info("调用网商商户代扣协议解除接口服务端返回报文:{}", xmlContent);
            return merchantprodMerchantArrangeMentAuditResponse;
        } catch (MybankApiException e) {
            log.error("调用网商商户代扣协议解除接口异常", e);
        }
        return null;
    }

    private MerchantArrangementInfoQueryResponse invokeMerchantArrangementInfoQuery(TripartiteSystemCallRequest request, MyBankConfigModule configModule) {

        MerchantArrangementInfoQueryRequest merchantArrangementInfoQueryRequest = null;
        if (request instanceof MerchantArrangementInfoQueryRequest) {
            merchantArrangementInfoQueryRequest = (MerchantArrangementInfoQueryRequest) request;
        }
        if (merchantArrangementInfoQueryRequest != null) {
            //实例化客户端
            MyBankClient myBankClient = new DefaultMyBankClient(myBankUrl, configModule.getIsvPrivateKey(), configModule.getPublicKey());
            try {
                MerchantArrangementInfoQueryResponse merchantArrangementInfoQueryResponse = myBankClient.execute(merchantArrangementInfoQueryRequest);
                MerchantArrangementInfoQueryResponse.MerchantprodMerchantArrangementInfoQuery merchantprodMerchantArrangementInfoQuery = merchantArrangementInfoQueryResponse.getMerchantprodMerchantArrangementInfoQuery();
                MerchantArrangementInfoQueryResponseModel merchantArrangementInfoQueryModel = merchantprodMerchantArrangementInfoQuery.getMerchantArrangementInfoQueryResponseModel();
                merchantArrangementInfoQueryResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.getByMyBankCode(merchantArrangementInfoQueryModel.getRespInfo().getResultStatus()));
                merchantArrangementInfoQueryResponse.setResultMsg(merchantArrangementInfoQueryModel.getRespInfo().getResultMsg());
                // 客户端请求报文
                String requestContent = merchantArrangementInfoQueryResponse.getRequestContent();
                log.info("调用网商商户代扣协议查询接口客户端请求报文:{}", requestContent);
                // 服务端返回报文
                String xmlContent = merchantArrangementInfoQueryResponse.getXmlContent();
                log.info("调用网商商户代扣协议查询接口服务端返回报文:{}", xmlContent);
                return merchantArrangementInfoQueryResponse;
            }catch (MybankApiException e){
                log.error("调用网商商户代扣协议查询接口异常", e);
            }
        }
        return null;
    }

    private MerchantAppletRegisterQueryResponse invokeMerchantAppletRegisterQuery(TripartiteSystemCallRequest request, MyBankConfigModule configModule) {
        MerchantAppletRegisterQueryRequest merchantAppletRegisterQueryRequest = null;
        if (request instanceof MerchantAppletRegisterQueryRequest) {
            merchantAppletRegisterQueryRequest = (MerchantAppletRegisterQueryRequest) request;
        }
        //实例化客户端
        MyBankClient myBankClient = new DefaultMyBankClient(myBankUrl, configModule.getIsvPrivateKey(), configModule.getPublicKey());
        try {
            MerchantAppletRegisterQueryResponse merchantAppletRegisterQueryResponse = myBankClient.execute(merchantAppletRegisterQueryRequest);
            MerchantAppletRegisterQueryResponseModel merchantAppletRegisterQueryResponseModel = merchantAppletRegisterQueryResponse.getMerchantAppletRegisterQuery().getMerchantAppletRegisterQueryResponseModel();
            merchantAppletRegisterQueryResponse.setResultCode(TripartiteSystemCallResponse.ResultCodeEnum.getByMyBankCode(merchantAppletRegisterQueryResponseModel.getRespInfo().getResultStatus()));
            merchantAppletRegisterQueryResponse.setResultMsg(merchantAppletRegisterQueryResponseModel.getRespInfo().getResultMsg());
            // 客户端请求报文
            // 客户端请求报文
            String requestContent = merchantAppletRegisterQueryResponse.getRequestContent();
            log.info("调用网商商户注册查询接口客户端请求报文:{}", requestContent);
            // 服务端返回报文
            String xmlContent = merchantAppletRegisterQueryResponse.getXmlContent();
            log.info("调用网商商户注册查询接口服务端返回报文:{}", xmlContent);
            return merchantAppletRegisterQueryResponse;
        }catch (MybankApiException e){
            log.error("调用网商商户注册查询接口异常", e);
        }
        return null;
    }
}
