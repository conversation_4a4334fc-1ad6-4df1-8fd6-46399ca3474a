//package com.wosai.cua.brand.business.service.config.aop;
//
//
//import com.wosai.cua.brand.business.service.aspect.EncryptionAspect;
//import com.wosai.cua.brand.business.service.aspect.TransactionAspect;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.ComponentScan;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.EnableAspectJAutoProxy;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//
///**
// * <AUTHOR>
// */
//@Configuration
//@EnableTransactionManagement
//@EnableAspectJAutoProxy
//@ComponentScan("com.wosai.cua.brand.business.service")
//public class AopConfig {
//
//    @Bean
//    public TransactionAspect myTransactionAspect() {
//        return new TransactionAspect();
//    }
//
//    @Bean
//    public EncryptionAspect myOtherAspect() {
//        return new EncryptionAspect();
//    }
//}
