package com.wosai.cua.brand.business.service.module.config.convert;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class ConfigModuleConverter {

    private ConfigModuleConverter() {
        throw new IllegalStateException("Utility class");
    }

    // 配置模块与资管公司的映射关系
    // 使用Map将枚举与相应的类映射起来，以提高可维护性和扩展性
    public static final Map<FundManagementCompanyEnum, Class<? extends ConfigModule>> CONFIG_MODULE_MAP = Maps.newHashMap();

    static {
        CONFIG_MODULE_MAP.put(FundManagementCompanyEnum.MY_BANK, MyBankConfigModule.class);
        CONFIG_MODULE_MAP.put(FundManagementCompanyEnum.PAB, PabConfigModule.class);
        CONFIG_MODULE_MAP.put(FundManagementCompanyEnum.CITIC, CiticBankConfigModule.class);
        CONFIG_MODULE_MAP.put(FundManagementCompanyEnum.FUIOU, FuiouConfigModule.class);
    }

    public static ConfigModule convert(FundManagementCompanyEnum fundManagementCompanyEnum, String config) {
        // 检查输入参数的有效性
        if (fundManagementCompanyEnum == null || config == null || config.trim().isEmpty()) {
            log.warn("Invalid input parameters.");
            return null;
        }

        // 获取对应的ConfigModule类
        Class<? extends ConfigModule> configModuleClass = CONFIG_MODULE_MAP.get(fundManagementCompanyEnum);
        if (configModuleClass == null) {
            log.warn("Unsupported fund management company: {}", fundManagementCompanyEnum);
            return null;
        }

        try {
            // 使用try-catch捕获JSON解析异常
            return JSON.parseObject(config, configModuleClass);
        } catch (JSONException e) {
            // 异常处理，可以根据实际需求调整
            // 例如，记录日志、抛出自定义异常等
            log.error("JSON parse error: {}", e.getMessage());
            return null;
        }
    }

    public static <R> R convert(ConfigDTO config, Class<R> clazz) {
        // 检查输入参数的有效性
        if (config == null) {
            log.warn("Invalid input parameters.");
            return null;
        }
        try {
            // 使用try-catch捕获JSON解析异常
            return JSON.parseObject(JSON.toJSONString(config), clazz);
        } catch (JSONException e) {
            // 异常处理，可以根据实际需求调整
            // 例如，记录日志、抛出自定义异常等
            log.error("JSON parse error: {}", e.getMessage());
            return null;
        }
    }

    public static <R> R convert(ConfigModule config, Class<R> clazz) {
        // 检查输入参数的有效性
        if (config == null) {
            log.warn("Invalid input parameters.");
            return null;
        }
        try {
            // 使用try-catch捕获JSON解析异常
            return JSON.parseObject(JSON.toJSONString(config), clazz);
        } catch (JSONException e) {
            // 异常处理，可以根据实际需求调整
            // 例如，记录日志、抛出自定义异常等
            log.error("JSON parse error: {}", e.getMessage());
            return null;
        }
    }

    public static <R> R convert(String config, Class<R> clazz) {
        // 检查输入参数的有效性
        if (config == null) {
            log.warn("Invalid input parameters.");
            return null;
        }
        try {
            // 使用try-catch捕获JSON解析异常
            return JSON.parseObject(JSON.toJSONString(config), clazz);
        } catch (JSONException e) {
            // 异常处理，可以根据实际需求调整
            // 例如，记录日志、抛出自定义异常等
            log.error("JSON parse error: {}", e.getMessage());
            return null;
        }
    }

    public static ConfigModule convert(ConfigDTO config, FundManagementCompanyEnum fundManagementCompanyEnum) {
        switch (fundManagementCompanyEnum) {
            case MY_BANK:
                return MyBankConfigModule.convert(config.getMyBankConfig());
            case PAB:
                return PabConfigModule.convert(config.getPabConfig());
            case CITIC:
                return CiticBankConfigModule.convert(config.getCiticConfig());
            case FUIOU:
                return FuiouConfigModule.convert(config.getFuiouConfig());
            default:
                return null;
        }
    }
}

