package com.wosai.cua.brand.business.service.domain.tripartite.request.feishu;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class PostMessage {

    /**
     * post : {"zh_cn":{"title":"项目更新通知","content":[[{"tag":"text","text":"项目有更新: ","href":"http://www.example.com/","user_id":"ou_18eac8********17ad4f02e8bbbb"},{"tag":"a","text":"请查看","href":"http://www.example.com/"},{"tag":"at","user_id":"ou_18eac8********17ad4f02e8bbbb"}]]}}
     */

    private PostBean post;

    @Data
    public static class PostBean {
        /**
         * zh_cn : {"title":"项目更新通知","content":[[{"tag":"text","text":"项目有更新: ","href":"http://www.example.com/","user_id":"ou_18eac8********17ad4f02e8bbbb"},{"tag":"a","text":"请查看","href":"http://www.example.com/"},{"tag":"at","user_id":"ou_18eac8********17ad4f02e8bbbb"}]]}
         */
        @JSONField(name = "zh_cn")
        private ZhCnBean zhCn;

        @Data
        public static class ZhCnBean {
            /**
             * title : 项目更新通知
             * content : [[{"tag":"text","text":"项目有更新: "},{"tag":"a","text":"请查看","href":"http://www.example.com/"},{"tag":"at","user_id":"ou_18eac8********17ad4f02e8bbbb"}]]
             */

            private String title;
            private List<List<ContentBean>> content;

            @Data
            public static class ContentBean {
                /**
                 * tag : text
                 * text : 项目有更新:
                 * href : http://www.example.com/
                 * user_id : ou_18eac8********17ad4f02e8bbbb
                 */

                private String tag;
                private String text;
                private String href;
                @JSONField(name = "user_id")
                private String userId;
            }
        }
    }
}
