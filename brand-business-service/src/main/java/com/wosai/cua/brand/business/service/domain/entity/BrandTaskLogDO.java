package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "brand_task_log")
public class BrandTaskLogDO {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 品牌id
     */
    @TableField(value = "brand_id")
    private String brandId;

    @TableField(value = "task_name")
    private String taskName;

    /**
     * 任务类型：0-品牌商户导入
     */
    @TableField(value = "task_type")
    private Integer taskType;

    /**
     * 提交平台 CRM
     */
    @TableField(value = "platform")
    private String platform;

    /**
     * 操作人id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 上下文
     */
    @TableField(value = "task_context")
    private String taskContext;

    /**
     * 任务状态：0-未知，1-执行中，2-执行成功，3-执行失败
     */
    @TableField(value = "task_status")
    private Integer taskStatus;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private Date updatedTime;

    /**
     * 任务执行结果
     */
    @TableField(value = "task_result")
    private String taskResult;

    /**
     * 错误日志
     */
    @TableField(value = "error_log")
    private String errorLog;
}