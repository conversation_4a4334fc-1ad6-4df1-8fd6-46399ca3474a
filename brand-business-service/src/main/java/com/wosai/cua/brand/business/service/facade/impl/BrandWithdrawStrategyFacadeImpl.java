package com.wosai.cua.brand.business.service.facade.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.CreateWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.DeleteBrandWithdrawStrategyByStrategyIdListDTO;
import com.wosai.cua.brand.business.api.dto.request.GetBrandWithdrawStrategyByBrandIdDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.PageBrandWithdrawStrategyRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandWithdrawStrategyResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.EditWithdrawStrategyResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandWithdrawStrategyResponseDTO;
import com.wosai.cua.brand.business.api.enums.ApplicableSceneEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.WithdrawCycleTypeEnum;
import com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.BrandWithdrawStrategyFacade;
import com.wosai.cua.brand.business.service.business.BrandWithdrawStrategyBusiness;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.module.withdraw.BrandWithdrawStrategyModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class BrandWithdrawStrategyFacadeImpl implements BrandWithdrawStrategyFacade {

    private final BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness;

    @Autowired
    public BrandWithdrawStrategyFacadeImpl(BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness) {
        this.brandWithdrawStrategyBusiness = brandWithdrawStrategyBusiness;
    }

    @Override
    public EditWithdrawStrategyResponseDTO createBrandWithdrawStrategy(CreateWithdrawStrategyDTO createWithdrawStrategy) {
        if (StringUtils.isBlank(createWithdrawStrategy.getApplicableScene()) || ApplicableSceneEnum.CONFIGURATION.name().equals(createWithdrawStrategy.getApplicableScene())){
            if (createWithdrawStrategy.getWithdrawType().equals(WithdrawTypeEnum.AUTO.getWithdrawType())) {
                if (StringUtils.isEmpty(createWithdrawStrategy.getWithdrawCycleType())) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.WITHDRAW_CYCLE_TYPE_CAN_NOT_BE_EMPTY);
                }
                if (StringUtils.isEmpty(createWithdrawStrategy.getWithdrawCycleTimes())) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.WITHDRAW_CYCLE_TIME_CAN_NOT_BE_NULL);
                }
            }
            if (createWithdrawStrategy.getWithdrawType().equals(WithdrawTypeEnum.MANUAL_OPERATION.getWithdrawType())){
                createWithdrawStrategy.setWithdrawCycleType(null);
                createWithdrawStrategy.setWithdrawCycleTimes(null);
                createWithdrawStrategy.setWithdrawCycleTime(null);
            }
            if (Objects.nonNull(createWithdrawStrategy.getWithdrawCycleType()) && StringUtils.isNotEmpty(createWithdrawStrategy.getWithdrawCycleTimes())){
                String[] times = createWithdrawStrategy.getWithdrawCycleTimes().split(",");
                Lists.newArrayList(times).forEach(withdrawCycleTime -> ParamsCheckHelper.checkWithdrawCycleTime(WithdrawCycleTypeEnum.getWithdrawCycleTypeEnumByWithdrawCycleType(createWithdrawStrategy.getWithdrawCycleType()), Integer.parseInt(withdrawCycleTime)));
            }
        }
        BrandWithdrawStrategyModule brandWithdrawStrategy = brandWithdrawStrategyBusiness.createBrandWithdrawStrategy(createWithdrawStrategy);
        return JSON.parseObject(JSON.toJSONString(brandWithdrawStrategy), EditWithdrawStrategyResponseDTO.class);
    }

    @Override
    public EditWithdrawStrategyResponseDTO modifyBrandWithdrawStrategy(ModifyWithdrawStrategyDTO modifyWithdrawStrategy) {
        if (StringUtils.isBlank(modifyWithdrawStrategy.getApplicableScene()) || ApplicableSceneEnum.CONFIGURATION.name().equals(modifyWithdrawStrategy.getApplicableScene())){
            if (modifyWithdrawStrategy.getWithdrawType().equals(WithdrawTypeEnum.AUTO.getWithdrawType())) {
                if (StringUtils.isEmpty(modifyWithdrawStrategy.getWithdrawCycleType())) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.WITHDRAW_CYCLE_TYPE_CAN_NOT_BE_EMPTY);
                }
                if (StringUtils.isEmpty(modifyWithdrawStrategy.getWithdrawCycleTimes())) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.WITHDRAW_CYCLE_TIME_CAN_NOT_BE_NULL);
                }
            }
            if (modifyWithdrawStrategy.getWithdrawType().equals(WithdrawTypeEnum.MANUAL_OPERATION.getWithdrawType())){
                modifyWithdrawStrategy.setWithdrawCycleType(null);
                modifyWithdrawStrategy.setWithdrawCycleTimes(null);
                modifyWithdrawStrategy.setWithdrawCycleTime(null);
            }
            if (Objects.nonNull(modifyWithdrawStrategy.getWithdrawCycleType()) && StringUtils.isNotEmpty(modifyWithdrawStrategy.getWithdrawCycleTimes())){
                String[] times = modifyWithdrawStrategy.getWithdrawCycleTimes().split(",");
                Lists.newArrayList(times).forEach(withdrawCycleTime -> ParamsCheckHelper.checkWithdrawCycleTime(WithdrawCycleTypeEnum.getWithdrawCycleTypeEnumByWithdrawCycleType(modifyWithdrawStrategy.getWithdrawCycleType()), Integer.parseInt(withdrawCycleTime)));
            }
        }
        BrandWithdrawStrategyModule brandWithdrawStrategy = brandWithdrawStrategyBusiness.modifyBrandWithdrawStrategy(modifyWithdrawStrategy);
        return JSON.parseObject(JSON.toJSONString(brandWithdrawStrategy), EditWithdrawStrategyResponseDTO.class);
    }

    @Override
    public List<BrandWithdrawStrategyResponseDTO> getBrandWithdrawStrategyByBrandId(GetBrandWithdrawStrategyByBrandIdDTO brandIdDto) {
        List<BrandWithdrawStrategyResponseDTO> brandWithdrawStrategyResponses = JSON.parseArray(JSON.toJSONString(brandWithdrawStrategyBusiness.getBrandWithdrawStrategyModulesByBrandId(brandIdDto.getBrandId())), BrandWithdrawStrategyResponseDTO.class);
        brandWithdrawStrategyResponses.forEach(brandWithdrawStrategyResponse -> {
            if (Objects.nonNull(brandWithdrawStrategyResponse.getReservedAmount())) {
                brandWithdrawStrategyResponse.setReservedAmount(brandWithdrawStrategyResponse.getReservedAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            if (Objects.nonNull(brandWithdrawStrategyResponse.getMinWithdrawalAmount())) {
                brandWithdrawStrategyResponse.setMinWithdrawalAmount(brandWithdrawStrategyResponse.getMinWithdrawalAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
        });
        return brandWithdrawStrategyResponses;
    }

    @Override
    public int deleteBrandWithdrawStrategyByStrategyIdList(DeleteBrandWithdrawStrategyByStrategyIdListDTO deleteByStrategyIdListDto) {
        return brandWithdrawStrategyBusiness.deleteBrandWithdrawStrategyByStrategyIdList(JSON.parseArray(JSON.toJSONString(deleteByStrategyIdListDto.getStrategyIdList()), Long.class));
    }

    @Override
    public PageBrandWithdrawStrategyResponseDTO pageGetBrandWithdrawStrategy(PageBrandWithdrawStrategyRequestDTO request) {
        return brandWithdrawStrategyBusiness.pageGetBrandWithdrawStrategy(request);
    }
}
