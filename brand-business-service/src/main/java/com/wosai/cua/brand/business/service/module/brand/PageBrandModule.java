package com.wosai.cua.brand.business.service.module.brand;

import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@SensitiveClass
public class PageBrandModule {

    @SensitiveField
    private List<BrandModule> brandList;

    private Integer total;

    public PageBrandModule(Integer total) {
        this.total = total;
    }
}
