package com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CreateBankCardResponse extends VfinanceBaseResponse{
    /**
     * 银行卡ID
     * 此处返回两个ID用“,”隔开
     * 例：324,325
     */
    @JSONField(name = "bank_card_id")
    private String bankCardId;
}
