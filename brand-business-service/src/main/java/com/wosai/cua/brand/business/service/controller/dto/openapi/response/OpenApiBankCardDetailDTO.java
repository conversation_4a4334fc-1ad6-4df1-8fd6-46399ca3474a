package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OpenApiBankCardDetailDTO extends OpenApiBankCardResponse {

    /**
     * 账户名称
     */
    @JsonProperty("account_name")
    @JSONField(name = "accountName")
    private String accountName;
    /**
     * 银行卡号
     */
    @JsonProperty("bank_card_number")
    @JSONField(name = "bankCardNumber")
    private String bankCardNumber;

    /**
     * 账号类型
     */
    @JsonProperty("account_type")
    @JSONField(name = "accountType")
    private Integer accountType;

    /**
     * 账号类型描述
     */
    @JsonProperty("account_type_desc")
    @JSONField(name = "accountTypeDesc")
    private String accountTypeDesc;

    /**
     * 银行名称
     */
    @JsonProperty("bank_name")
    @JSONField(name = "bankName")
    private String bankName;
    /**
     * 支行名称
     */
    @JsonProperty("branch_name")
    @JSONField(name = "branchName")
    private String branchName;
    /**
     * 背景图
     */
    @JsonProperty("bank_back_picture")
    @JSONField(name = "bankBackPicture")
    private String bankBackPicture;
    /**
     * 银行图标
     */
    @JsonProperty("bank_icon")
    @JSONField(name = "bankIcon")
    private String bankIcon;

    /**
     * 激活状态
     */
    @JsonProperty("activate_status")
    @JSONField(name = "activateStatus")
    private Integer activateStatus;
    /**
     * 激活状态描述
     */
    @JsonProperty("activate_status_desc")
    @JSONField(name = "activateStatusDesc")
    private String activateStatusDesc;
    /**
     * 是否是默认卡
     */
    @JsonProperty("default_card")
    @JSONField(name = "defaultCard")
    private Boolean defaultCard;

    /**
     * 激活失败原因
     */
    @JsonProperty("activate_fail_reason")
    @JSONField(name = "activateFailReason")
    private String activateFailReason;

    /**
     * 预留手机号
     */
    @JsonProperty("reserved_mobile_number")
    @JSONField(name = "reservedMobileNumber")
    private String reservedMobileNumber;

    /**
     * 激活时间
     */
    @JsonProperty("activation_time")
    @JSONField(name = "activationTime")
    private Date activationTime;
}
