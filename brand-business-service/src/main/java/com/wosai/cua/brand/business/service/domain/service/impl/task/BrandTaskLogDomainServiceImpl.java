package com.wosai.cua.brand.business.service.domain.service.impl.task;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.service.domain.dao.BrandSubTaskMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandTaskLogMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandSubTaskDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandTaskLogDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryBrandTaskConditionsDO;
import com.wosai.cua.brand.business.service.domain.service.BrandTaskLogDomainService;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.cua.brand.business.service.module.log.QueryLogConditionsModule;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class BrandTaskLogDomainServiceImpl implements BrandTaskLogDomainService {

    private final BrandTaskLogMapper brandTaskLogMapper;
    private final BrandSubTaskMapper brandSubTaskMapper;

    @Autowired
    public BrandTaskLogDomainServiceImpl(BrandTaskLogMapper brandTaskLogMapper, BrandSubTaskMapper brandSubTaskMapper) {
        this.brandTaskLogMapper = brandTaskLogMapper;
        this.brandSubTaskMapper = brandSubTaskMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertBrandTaskLog(BrandTaskLogModule module) {
        BrandTaskLogDO brandTaskLog = JSON.parseObject(JSON.toJSONString(module), BrandTaskLogDO.class);
        brandTaskLogMapper.insert(brandTaskLog);
        return brandTaskLog.getId();
    }

    @Override
    public int updateBrandTaskLog(BrandTaskLogModule module) {
        BrandTaskLogDO brandTaskLogDO = brandTaskLogMapper.selectById(module.getId());
        if (brandTaskLogDO == null) {
            return brandTaskLogMapper.insert(JSON.parseObject(JSON.toJSONString(module), BrandTaskLogDO.class));
        }
        return brandTaskLogMapper.updateById(JSON.parseObject(JSON.toJSONString(module), BrandTaskLogDO.class));
    }

    @Override
    public Long countBrandTaskLog(QueryLogConditionsModule conditionsModule) {
        return brandTaskLogMapper.countBrandTaskLog(JSON.parseObject(JSON.toJSONString(conditionsModule), QueryBrandTaskConditionsDO.class));
    }

    @Override
    public List<BrandTaskLogModule> pageSearchBrandTaskLog(QueryLogConditionsModule conditionsModule, int page, int pageSize) {
        List<BrandTaskLogDO> brandTaskLogs = brandTaskLogMapper.pageBrandTaskLogList(JSON.parseObject(JSON.toJSONString(conditionsModule), QueryBrandTaskConditionsDO.class), (page - 1) * pageSize, pageSize);
        if (CollectionUtils.isEmpty(brandTaskLogs)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(JSON.toJSONString(brandTaskLogs), BrandTaskLogModule.class);
    }

    @Override
    public List<BrandTaskLogModule> queryAuditImportTaskLog(List<Integer> taskTypes, String start, int size) {
        List<BrandTaskLogDO> brandTaskLogs = brandTaskLogMapper.getAuditImportTaskLog(taskTypes, start, size);
        if (CollectionUtils.isEmpty(brandTaskLogs)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(JSON.toJSONString(brandTaskLogs), BrandTaskLogModule.class);
    }

    @Override
    public int batchInsertBrandSubTasks(List<BrandSubTaskModule> brandSubTasks) {
        if (CollectionUtils.isEmpty(brandSubTasks)) {
            return 0;
        }
        List<BrandSubTaskDO> brandSubTaskDOS = JSON.parseArray(JSON.toJSONString(brandSubTasks), BrandSubTaskDO.class);
        return brandSubTaskMapper.batchInsertSubTasks(brandSubTaskDOS);
    }

    @Override
    public List<BrandSubTaskModule> queryBrandSubTasksByTaskId(Long taskId) {
        List<BrandSubTaskDO> brandSubTaskDOS = brandSubTaskMapper.selectByTaskId(taskId);
        return JSON.parseArray(JSON.toJSONString(brandSubTaskDOS), BrandSubTaskModule.class);
    }

    @Override
    public int updateBrandSubTask(BrandSubTaskModule brandSubTaskModule) {
        return brandSubTaskMapper.updateById(JSON.parseObject(JSON.toJSONString(brandSubTaskModule), BrandSubTaskDO.class));
    }
}
