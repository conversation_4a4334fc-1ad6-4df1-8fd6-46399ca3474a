package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantActivateRequest extends OpenApiBaseRequest{

    @NotBlank(message = "商户编号必填")
    @JsonProperty("merchant_sn")
    private String merchantSn;

}
