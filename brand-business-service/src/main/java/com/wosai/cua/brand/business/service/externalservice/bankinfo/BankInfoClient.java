package com.wosai.cua.brand.business.service.externalservice.bankinfo;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.BankInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.DistrictInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.IndustryInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.LicenseDicInfo;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.LicenseTypeCode;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.info.api.dto.BankInfoResponse;
import com.wosai.upay.bank.info.api.model.BusinessLicenseDicChangeVo;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.model.Districts;
import com.wosai.upay.bank.info.api.model.Industry;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.bank.info.api.service.BusinessLicenseDicV2Service;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Component
public class BankInfoClient {

    @Autowired
    private DistrictsServiceV2 districtsServiceV2;
    @Autowired
    private IndustryV2Service industryV2Service;
    @Autowired
    private BusinessLicenseDicV2Service businessLicenseDicV2Service;
    @Autowired
    private BankInfoService bankInfoService;

    private final Cache<String, Object> cache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    /**
     * 根据区code获取区信息
     * @param districtCode 区code
     * @return 区信息
     */
    public DistrictInfoQueryResult getDistrictInfoByCode(String districtCode) {
        District district = districtsServiceV2.getDistrict(CollectionUtil.hashMap(Districts.CODE, districtCode));
        if (Objects.isNull(district)) {
            throw new CommonInvalidParameterException("地区信息不存在");
        }
        return new DistrictInfoQueryResult()
                .setProvince(district.getProvince_name())
                .setCity(district.getCity_name())
                .setDistrict(district.getName())
                .setDistrictCode(districtCode);
    }

    /**
     * 根据二级行业code获取行业信息
     * @param industryCode 行业code
     * @return 行业信息
     */
    public IndustryInfoQueryResult getIndustryInfoByCode(String industryCode) {
        List<Map<String, Object>> industries = industryV2Service.findIndustries(CollectionUtil.hashMap(Industry.CODE2, industryCode));
        Map<String, Object> industry = industries.get(0);
        return new IndustryInfoQueryResult()
                .setIndustryId(WosaiMapUtils.getString(industry, DaoConstants.ID));
    }

    /**
     * 查询所有的营业证照配置枚举
     * @return key:证照类型，value:证照信息
     */
    public Map<Integer, LicenseDicInfo> queryAllLicenseDic() {
        try {
            return (Map<Integer, LicenseDicInfo>) cache.get("all_license", () -> {
                List<BusinessLicenseDicChangeVo> businessLicenseDicChangeVos = businessLicenseDicV2Service.allBusinessLicenseDic(null);
                return businessLicenseDicChangeVos.stream().collect(Collectors.toMap(BusinessLicenseDicChangeVo::getId,
                        r -> new LicenseDicInfo()
                                .setType(r.getId())
                                .setName(r.getName())
                                .setLegalPersonTypes(r.getLegal_person_types())
                                .setTypeCodes(r.getType_codes().stream().map(t -> new LicenseTypeCode().setType(t.getType()).setCode(t.getCode())).collect(Collectors.toList()))
                ));
            });
        } catch (ExecutionException e) {
            return new HashMap<>();
        }
    }

    /**
     * 根据证照号找到对应的营业证照类型
     * @param number 营业证照号
     * @return 类型
     */
    public Integer getLicenseTypeByNumber(String number) {
        Collection<LicenseDicInfo> licenseDicInfos = queryAllLicenseDic().values();
        for (LicenseDicInfo licenseDicInfo : licenseDicInfos) {
            List<LicenseTypeCode> typeCodes = licenseDicInfo.getTypeCodes();
            if (WosaiCollectionUtils.isNotEmpty(typeCodes)) {
                for (LicenseTypeCode typeCode : typeCodes) {
                    //三合一类型规则
                    if (Integer.valueOf(1).equals(typeCode.getType()) && number.startsWith(typeCode.getCode())) {
                        return licenseDicInfo.getType();
                    }
                    //个体工商户、非三合一规则
                    if (Integer.valueOf(1).equals(licenseDicInfo.getType()) && number.length() == 15) {
                        return licenseDicInfo.getType();
                    }
                }
            }
        }
        throw new CommonInvalidParameterException("未找到营业证照注册号对应的营业证照类型");
    }

    public BankInfoQueryResult getBankInfoByOpeningNumber(String openingNumber) {
        BankInfoResponse bankInfoResponse = bankInfoService.getBankInfoByOpenNumber(openingNumber);
        if (Objects.isNull(bankInfoResponse)) {
            return null;
        }
        return new BankInfoQueryResult()
                .setBankName(bankInfoResponse.getBankName())
                .setBranchName(bankInfoResponse.getBranchName())
                .setClearingNumber(bankInfoResponse.getClearingNumber())
                .setOpeningNumber(bankInfoResponse.getOpeningNumber());
    }
}
