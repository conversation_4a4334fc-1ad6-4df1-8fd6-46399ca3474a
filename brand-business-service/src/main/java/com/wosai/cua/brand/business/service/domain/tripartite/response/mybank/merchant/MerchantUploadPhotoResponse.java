package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.MerchantUploadPhotoResponseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Mybank Api: ant.mybank.merchantprod.merchant.uploadphoto
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
@Data
public class MerchantUploadPhotoResponse extends MybankResponse {

    private static final long serialVersionUID = -801391855214686708L;

    @XmlElementRef
    private MerchantprodMerchantUploadPhoto merchantprodMerchantUploadPhoto;


    @EqualsAndHashCode(callSuper = true)
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "response")
    @Data
    public static class MerchantprodMerchantUploadPhoto extends MybankObject {
        private static final long serialVersionUID = 7727430680239228252L;
        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private ResponseHead responseHead;

        /**
         * 业务应答参数（body）
         */
        @XmlElementRef
        private MerchantUploadPhotoResponseModel merchantUploadphotoResponseModel;
    }
}