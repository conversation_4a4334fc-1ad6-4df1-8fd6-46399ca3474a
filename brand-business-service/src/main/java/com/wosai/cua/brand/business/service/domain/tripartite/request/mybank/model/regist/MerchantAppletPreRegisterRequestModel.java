package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.CDataAdapter;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * 小程序商户预入驻接口<ant.mybank.bkmerchantprod.merch.applet.pre.register>
 * 商户预入驻接口是同步受理，异步通知预入驻接口的返回结果：调用预入驻接口会同步返回
 * IsvOrgId、OutMerchantId、outTradeNo、OrderNo
 * 同时异步通知接口会返回OrderNo、OutMerchantId、OutTradeNo、MerchantId、RegisterStatus、FailReason、AsyncNotifyType这几个字段内容
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
@Data
public class MerchantAppletPreRegisterRequestModel extends RequestBody {

    private static final long serialVersionUID = -2710936983354951502L;
    /**
     * 合作方机构号
     * <p>
     * 网商银行分配
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;

    /**
     * 外部交易号
     * 合作方系统生成的外部交易号，同一交易号被视为同一笔交易。
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;

    /**
     * 外部商户号 必须为32位
     * 合作商对商户的自定义编码，要求同一个合作商下保持唯一。
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "OutMerchantId")
    private String outMerchantId;

    /**
     * 商户类型。可选值  01:自然人; 02:个体工商户; 03:企业商户
     * <p>
     * 说明：以同一个身份证件办理的自然人商户受理信用卡的收款金额上限为日累计1000元、月累计1万，建议有营业执照的商户以个体或企业身份入驻
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MerchantType")
    private String merchantType;

    /**
     * 商户名称
     * <p>
     * 最多50个汉字或字符，可以包含特殊字符，规则如下：
     * <p>
     * 一、自然人商户名称校验规则
     * 商户_XXX负责人名称，中间有分隔符"_"
     * <p>
     * 二、个体工商户名称校验规则
     * 1. 有工商注册名且可见(非*),且工商注册名不是负责人名称（即为店名或企业名），以工商注册名为商户户名
     * 2. 有营业执照名但是名字含*，商户名为：个体户+XXX负责人名称，中间无分隔符
     * 3. 无工商注册名称，商户名为：个体户+XXX负责人名称，中间无分隔符
     * <p>
     * 三、企业商户名称校验规则必须与工商注册名保持一致
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MerchantName")
    private String merchantName;

    /**
     * 商户经营类型   可选值：01:实体特约商户; 02:网络特约商户; 03:实体兼网络特约商户
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "DealType")
    private String dealType;

    /**
     * 身份证图片正面
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "CertPhotoA")
    private String certPhotoA;

    /**
     * 身份证图片反面
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "CertPhotoB")
    private String certPhotoB;

    /**
     * 营业执照图片
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "LicensePhoto")
    private String licensePhoto;

    /**
     * 商户详情列表
     * <p>
     * json格式base64编码，具体报文定义参考商户详情
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MerchantDetail")
    private String merchantDetail;
}