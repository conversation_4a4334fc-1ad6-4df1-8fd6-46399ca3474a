package com.wosai.cua.brand.business.service.domain.https.enums;

import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import lombok.Getter;

@Getter
public enum FuiouAddConcentrateRelationStatusEnum {
    INITIALIZE("00", BrandMerchantAccountOpenStatusEnum.IN_OPENING, "初始化"),
    SUCCESS("01", BrandMerchantAccountOpenStatusEnum.EFFECTIVE, "成功"),
    DECLINED("02", BrandMerchantAccountOpenStatusEnum.DECLINED, "已拒绝"),
    DISSOLUTION_OF_RELATIONSHIP("03", BrandMerchantAccountOpenStatusEnum.REMOVED, "关系解除"),
    AUTHORIZATION_FAILURE("04", BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE, "授权失败"),
    LOSE_EFFECTIVENESS("05", BrandMerchantAccountOpenStatusEnum.INVALID, "失效"),
    ;

    private final String code;
    private final BrandMerchantAccountOpenStatusEnum brandMerchantAccountOpenStatusEnum;
    private final String desc;

    FuiouAddConcentrateRelationStatusEnum(String code, BrandMerchantAccountOpenStatusEnum brandMerchantAccountOpenStatusEnum, String desc) {
        this.code = code;
        this.brandMerchantAccountOpenStatusEnum = brandMerchantAccountOpenStatusEnum;
        this.desc = desc;
    }

    public static FuiouAddConcentrateRelationStatusEnum getByCode(String code) {
        for (FuiouAddConcentrateRelationStatusEnum statusEnum : values()) {
            if (statusEnum.code.equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
