package com.wosai.cua.brand.business.service.domain.tripartite.mybank.util;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CDataAdapter extends XmlAdapter<String, String> {
    // Pattern to identify encoded CDATA sections
    private static final Pattern CDATA_PATTERN = Pattern.compile("<!\\[CDATA\\[(.*?)]]>", Pattern.DOTALL);

    @Override
    public String unmarshal(String v) throws Exception {
        if (v == null) {
            return null;
        }

        // Remove encoded CDATA sections
        Matcher matcher = CDATA_PATTERN.matcher(v);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, Matcher.quoteReplacement(matcher.group(1)));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    @Override
    public String marshal(String v) {
        if (v == null) {
            return null;
        }

        // Escape special characters to ensure valid XML
        return escapeXml(v);
    }
    private String escapeXml(String input) {
        StringBuilder escaped = new StringBuilder();
        for (char c : input.toCharArray()) {
            switch (c) {
                case '<':
                    escaped.append("&lt;");
                    break;
                case '>':
                    escaped.append("&gt;");
                    break;
                case '&':
                    escaped.append("&amp;");
                    break;
                case '"':
                    escaped.append("&quot;");
                    break;
                case '\'':
                    escaped.append("&apos;");
                    break;
                default:
                    escaped.append(c);
                    break;
            }
        }
        return escaped.toString();
    }

}