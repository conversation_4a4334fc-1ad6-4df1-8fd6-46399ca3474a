package com.wosai.cua.brand.business.service.job;

import com.alibaba.fastjson.JSON;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.domain.service.BrandCallbackRecordsService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.enums.third.FunctionEnum;
import com.wosai.cua.brand.business.service.module.job.BaseJobParamDTO;
import com.wosai.cua.brand.business.service.module.merchant.BrandCallbackRecordModule;
import com.wosai.cua.brand.business.service.mybank.manage.StrategyFactory;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BrandCallbackRecordsJobHandler {

    private final BrandCallbackRecordsService brandCallbackRecordsService;

    private final BrandDomainService brandDomainService;

    private final StrategyFactory strategyFactory;

    @Autowired
    public BrandCallbackRecordsJobHandler(BrandCallbackRecordsService brandCallbackRecordsService, BrandDomainService brandDomainService, StrategyFactory strategyFactory) {
        this.brandCallbackRecordsService = brandCallbackRecordsService;
        this.brandDomainService = brandDomainService;
        this.strategyFactory = strategyFactory;
    }

    @XxlJob("invokeBrandCallbackRecords")
    @Transactional(rollbackFor = Exception.class)
    public void invokeBrandCallbackRecords() {
        String jobParam = XxlJobHelper.getJobParam();
        int pageSize = 1000;
        long sleepTime = 1000L;
        if (!StringUtils.isEmpty(jobParam)) {
            log.info("BrandCallbackRecordsJobHandler jobParam is {}", jobParam);
            BaseJobParamDTO jobParamDto = JSON.parseObject(jobParam, BaseJobParamDTO.class);
            if (Objects.nonNull(jobParamDto.getPageSize())) {
                pageSize = jobParamDto.getPageSize();
            }
            if (Objects.nonNull(jobParamDto.getSleepTime())) {
                sleepTime = jobParamDto.getSleepTime();
            }
        }
        long startId = 0;
        List<BrandCallbackRecordModule> brandCallbackRecordModules = null;
        do {
            try {
                LocalTime currentTime = LocalTime.now();
                brandCallbackRecordModules = brandCallbackRecordsService.pageSelectBrandCallbackRecords(pageSize, startId);
                if (CollectionUtils.isEmpty(brandCallbackRecordModules)) {
                    log.info("当前时间：{}，BrandCallbackRecordsJobHandler执行中断：startId = {}，pageSize = {}", currentTime.format(DateTimeFormatter.ofPattern("HH:mm:ss")), startId, pageSize);
                    return;
                }
                Thread.sleep(sleepTime);
                brandCallbackRecordModules.forEach(brandCallbackRecordModule -> {
                    String fundManagementCompanyCode = brandCallbackRecordModule.getFundManagementCompanyCode();
                    if (StringUtils.isEmpty(fundManagementCompanyCode)) {
                        log.warn("brandCallbackRecordModule.getFundManagementCompanyCode is null, brandCallbackRecordModule = {}", brandCallbackRecordModule);
                        return;
                    }
                    if (fundManagementCompanyCode.equals(FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode())) {
                        try {
                            strategyFactory.getStrategy(FunctionEnum.getStrategyValue(brandCallbackRecordModule.getFunction())).notifyHandle(brandCallbackRecordModule);
                        } catch (Exception e) {
                            log.warn("未找到策略。");
                        }
                    }
                });
                startId = brandCallbackRecordModules.get(brandCallbackRecordModules.size() - 1).getId();
            } catch (InterruptedException e) {
                log.warn("执行中断!");
                Thread.currentThread().interrupt();
            }
        } while (!CollectionUtils.isEmpty(brandCallbackRecordModules));

    }
}
