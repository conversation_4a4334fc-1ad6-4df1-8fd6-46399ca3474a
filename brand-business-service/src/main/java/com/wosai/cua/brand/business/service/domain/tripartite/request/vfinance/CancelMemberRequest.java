package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员销户接口请求对象
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CancelMemberRequest extends VfinanceBaseRequest{
    /**
     * 会员id
     */
    @JSONField(name = "member_id")
    private String memberId;

    public CancelMemberRequest(String partnerId) {
        super();
        super.service = CANCEL_MEMBER;
        super.partnerId = partnerId;
    }

    @Override
    public String toString() {
        return "CancelMemberRequest{" +
                "memberId='" + memberId + '\'' +
                ", service='" + service + '\'' +
                ", version='" + version + '\'' +
                ", partnerId='" + partnerId + '\'' +
                ", inputCharset='" + inputCharset + '\'' +
                ", memo='" + memo + '\'' +
                '}';
    }
}
