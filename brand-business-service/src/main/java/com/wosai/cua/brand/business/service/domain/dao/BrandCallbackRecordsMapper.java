package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandCallbackRecordsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【brand_callback_records(资管机构回调请求记录表)】的数据库操作Mapper
* @createDate 2024-09-12 11:51:29
* @Entity com.wosai.cua.brand.business.service.domain.entity.BrandCallbackRecordsDO
*/
public interface BrandCallbackRecordsMapper extends BaseMapper<BrandCallbackRecordsDO> {

    List<BrandCallbackRecordsDO> pageSelectBrandCallbackRecords(@Param("pageSize") int pageSize,@Param("id") Long id);
}




