package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;

public class ChangeDefaultBankCardRequest extends VfinanceBaseRequest{

    @J<PERSON><PERSON>ield(name = "member_id")
    private String memberId;

    @J<PERSON><PERSON>ield(name = "bank_card_id")
    private String bankCardId;

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getBankCardId() {
        return bankCardId;
    }

    public void setBankCardId(String bankCardId) {
        this.bankCardId = bankCardId;
    }

    public ChangeDefaultBankCardRequest(String partnerId) {
        super();
        super.service = CHANGE_DEFAULT_BANK_CARD;
        super.partnerId = partnerId;
    }

    @Override
    public String toString() {
        return "ChangeDefaultBankCardRequest{" +
                "memberId='" + memberId + '\'' +
                ", bankCardId='" + bankCardId + '\'' +
                ", service='" + service + '\'' +
                ", version='" + version + '\'' +
                ", partnerId='" + partnerId + '\'' +
                ", inputCharset='" + inputCharset + '\'' +
                ", memo='" + memo + '\'' +
                '}';
    }
}
