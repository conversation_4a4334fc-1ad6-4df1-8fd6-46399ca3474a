package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.merchant.BrandCallbackRecordModule;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【brand_callback_records(资管机构回调请求记录表)】的数据库操作Service
 * @createDate 2024-09-12 11:51:29
 */
public interface BrandCallbackRecordsService {

    /**
     * 插入品牌回调记录
     *
     * @param brandCallbackRecordModule
     * @return
     */
    int insertBrandCallbackRecords(BrandCallbackRecordModule brandCallbackRecordModule);

    /**
     * 更新品牌回调记录
     *
     * @param brandCallbackRecordModule
     * @return
     */
    int updateBrandCallbackRecords(BrandCallbackRecordModule brandCallbackRecordModule);

    /**
     * 分页查询品牌回调记录
     * @param pageSize 每页条数
     * @param id 主键id
     * @return 结果集
     */
    List<BrandCallbackRecordModule> pageSelectBrandCallbackRecords(int pageSize, Long id);

    /**
     * 删除品牌回调记录
     * @param id 主键id
     * @return 删除结果
     */
    int deleteBrandCallbackRecords(Long id);
}
