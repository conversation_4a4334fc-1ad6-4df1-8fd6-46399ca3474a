package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandAccountDO;

/**
* <AUTHOR>
* @description 针对表【brand_account(品牌相关账户汇总表)】的数据库操作Mapper
* @createDate 2024-07-17 17:02:29
* @Entity com.wosai.cua.brand.business.service.domain.entity.BrandAccountDO
*/
public interface BrandAccountDOMapper extends BaseMapper<BrandAccountDO> {

}




