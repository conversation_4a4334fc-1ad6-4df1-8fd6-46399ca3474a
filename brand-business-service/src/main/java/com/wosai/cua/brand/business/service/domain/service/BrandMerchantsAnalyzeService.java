package com.wosai.cua.brand.business.service.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.AlreadyExistMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.BaseMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.CompanyMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.IndividualBusinessMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.PersonalMerchantAnalyzeModule;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 品牌商户解析Service
 *
 * <AUTHOR>
 */
public interface BrandMerchantsAnalyzeService {
    /**
     * 获取Sheet页枚举
     *
     * @return Excel的sheet页枚举
     * @see BrandImportSheetEnum
     */
    BrandImportSheetEnum getSheetEnum();

    /**
     * 解析字段，获取CreateBrandMerchantRequestDTO集合
     *
     * @param fields 字段数组集合
     * @return CreateBrandMerchantRequestDTO集合
     */
    List<BaseMerchantAnalyzeModule> analyzeData(List<String[]> fields, FundManagementCompanyEnum fundManagementCompanyCode);

    /**
     * 获取批量创建品牌商户的模型
     *
     * @param brandId                品牌id
     * @param merchantAnalyzeModules 商户解析后的模型集合
     * @return 创建品牌商户的请求对象集合
     */
    List<BrandMerchantModule> getBrandMerchantModuleList(String brandId, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules, Long strategyId);

    /**
     * 获取创建银行卡对象
     *
     * @param brandId                品牌id
     * @param merchantAnalyzeModules 商户解析模型集合
     * @return 银行卡创建对象
     */
    List<BankCardModule> getBankCarModules(String brandId, FundManagementCompanyEnum fundManagementCompanyCode, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules);

    /**
     * 向excel中写入错误信息
     *
     * @param filePath 文件地址
     */
    void createErrorMsgIntoExcel(String filePath);

    /**
     * 校验解析参数
     *
     * @param createMerchantAnalyzeModules 商户数据解析模型
     */
    void checkParams(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules);

    /**
     * 校验身份证是否合法
     *
     * @param createMerchantAnalyzeModules 商户数据解析模型
     */
    void checkIsValidChineseID(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules);

    static LambdaQueryWrapper<BrandConfigDO> getBrandIdQueryWrapper(String brandId) {
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandConfigDO::getBrandId, brandId);
        return queryWrapper;
    }

    static BizBankAccountAddReq getBizBankAccountAddReq(BaseMerchantAnalyzeModule analyzeModule, String biz, int type, int idType) {
        if (analyzeModule == null) {
            return null;
        }
        BizBankAccountAddReq req = new BizBankAccountAddReq();
        req.setSet_default(true);
        req.setBiz(biz);
        req.setId_type(idType);
        req.setType(type);
        req.setMerchant_id(analyzeModule.getMerchantId());
        req.setOpening_number(analyzeModule.getOpeningNumber());
        req.setNumber(analyzeModule.getBankNumber());
        if (analyzeModule instanceof CompanyMerchantAnalyzeModule) {
            CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule = (CompanyMerchantAnalyzeModule) analyzeModule;
            req.setHolder(companyMerchantAnalyzeModule.getBankAccountName());
        }
        if (analyzeModule instanceof IndividualBusinessMerchantAnalyzeModule) {
            IndividualBusinessMerchantAnalyzeModule individualBusinessMerchantAnalyzeModule = (IndividualBusinessMerchantAnalyzeModule) analyzeModule;
            req.setIdentity(individualBusinessMerchantAnalyzeModule.getLegalPersonLicenseNumber());
            req.setHolder(individualBusinessMerchantAnalyzeModule.getLegalPersonName());
        }
        if (analyzeModule instanceof PersonalMerchantAnalyzeModule) {
            PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule = (PersonalMerchantAnalyzeModule) analyzeModule;
            req.setIdentity(personalMerchantAnalyzeModule.getIdNumber());
            req.setHolder(personalMerchantAnalyzeModule.getName());
        }
        if (analyzeModule instanceof AlreadyExistMerchantAnalyzeModule){
            AlreadyExistMerchantAnalyzeModule alreadyExistMerchantAnalyzeModule = (AlreadyExistMerchantAnalyzeModule) analyzeModule;
            req.setHolder(alreadyExistMerchantAnalyzeModule.getLegalPersonName());
            req.setIdentity(alreadyExistMerchantAnalyzeModule.getLegalPersonLicenseNumber());
        }
        return req;
    }

    public static void checkThirdStoreSn(BaseMerchantAnalyzeModule baseMerchantAnalyzeModule, BrandMerchantMapper brandMerchantMapper) {
        if (StringUtils.isNotBlank(baseMerchantAnalyzeModule.getMeiTuanStoreSn())) {
            QueryMerchantConditionsDO conditionsDO = new QueryMerchantConditionsDO();
            conditionsDO.setMeiTuanStoreSn(baseMerchantAnalyzeModule.getMeiTuanStoreSn());
            List<BrandMerchantDO> brandMerchants = brandMerchantMapper.pageBrandMerchantByIdAndConditions(0L,2, conditionsDO);
            if (CollectionUtils.isNotEmpty(brandMerchants)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.MEITUAN_STORE_SN_IS_EXIST);
            }
        }
        if (StringUtils.isNotBlank(baseMerchantAnalyzeModule.getElmStoreSn())) {
            QueryMerchantConditionsDO conditionsDO = new QueryMerchantConditionsDO();
            conditionsDO.setElmStoreSn(baseMerchantAnalyzeModule.getElmStoreSn());
            List<BrandMerchantDO> brandMerchants = brandMerchantMapper.pageBrandMerchantByIdAndConditions(0L,2, conditionsDO);
            if (CollectionUtils.isNotEmpty(brandMerchants)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.ELM_STORE_SN_IS_EXIST);
            }
        }
        if (StringUtils.isNotBlank(baseMerchantAnalyzeModule.getDyStoreSn())) {
            QueryMerchantConditionsDO conditionsDO = new QueryMerchantConditionsDO();
            conditionsDO.setDyStoreSn(baseMerchantAnalyzeModule.getDyStoreSn());
            List<BrandMerchantDO> brandMerchants = brandMerchantMapper.pageBrandMerchantByIdAndConditions(0L,2, conditionsDO);
            if (CollectionUtils.isNotEmpty(brandMerchants)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.DY_STORE_SN_IS_EXIST);
            }
        }
        if (StringUtils.isNotBlank(baseMerchantAnalyzeModule.getOutMerchantNo())) {
            QueryMerchantConditionsDO conditionsDO = new QueryMerchantConditionsDO();
            conditionsDO.setOutMerchantNo(baseMerchantAnalyzeModule.getOutMerchantNo());
            List<BrandMerchantDO> brandMerchants = brandMerchantMapper.pageBrandMerchantByIdAndConditions(0L,2, conditionsDO);
            if (CollectionUtils.isNotEmpty(brandMerchants)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.OUT_MERCHANT_NO_IS_EXIST);
            }
        }
    }

}
