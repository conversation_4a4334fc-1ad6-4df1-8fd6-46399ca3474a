package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.bank.BankInfoBeanModule;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BankCardDomainService {
    /**
     * 添加银行卡
     *
     * @param bankCardModule 银行卡模型
     * @return 新增条数
     */
    Long createBankCard(BankCardModule bankCardModule);

    /**
     * 根据merchant-bank-service系统维护的银行卡id查询银行卡记录
     *
     * @param bankCardId 银行卡id
     * @return 银行卡模型
     */
    BankCardModule getBankCardModuleById(String bankCardId);

    /**
     * 更新银行卡信息
     *
     * @param bankCardModule 银行卡模型
     * @return 更新条数
     */
    int updateBankCard(BankCardModule bankCardModule);

    /**
     * 将品牌商户下所有的银行卡设置为非默认
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     */
    void updateAllBankCardIsNotDefault(String brandId, String merchantId);

    /**
     * 获取默认银行卡
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     * @return 银行卡模型
     */
    BankCardModule getDefaultBankCardModule(String brandId, String merchantId);

    /**
     * 批量获取默认银行卡
     *
     * @param brandId     品牌id
     * @param merchantIds 商户id集合
     * @return 银行卡模型集合
     */
    List<BankCardModule> getDefaultBankCardModules(String brandId, List<String> merchantIds);

    /**
     * 根据品牌id、商户id、银行卡id获取银行卡列表
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     * @param cardIds    银行卡id集合
     * @return 银行卡模型集合
     */
    List<BankCardModule> getBankCardModulesByCarIdList(String brandId, String merchantId, List<String> cardIds, Integer status, Integer isDefault);

    /**
     * 批量添加银行卡
     *
     * @param bankCardModules 银行卡模型集合
     * @return 新增条数
     */
    int batchCreateBankCards(List<BankCardModule> bankCardModules);

    /**
     * 根据银行名称获取银行信息
     *
     * @param bankName 银行名称
     * @return 银行信息模型
     */
    BankInfoBeanModule getBankInfoModuleByBankName(String bankName);

    /**
     * 根据银行开户号获取银行信息
     *
     * @param openingNumber 开户号
     * @return 银行信息模型
     */
    BankInfoBeanModule getBankInfoModuleByOpeningNumber(String openingNumber);

    /**
     * 根据商户id删除银行卡
     *
     * @param merchantIds 商户id集合
     * @param brandId     品牌id
     * @return
     */
    List<String> deleteBankCardByMerchantIds(String brandId, List<String> merchantIds);

    /**
     * 根据品牌id分页获取银行卡列表
     *
     * @param brandId  品牌id
     * @param pageSize 偏移量
     * @param startId  开始id
     * @return 银行卡模型集合
     */
    List<BankCardModule> getBankCardListByBrandId(String brandId, Integer pageSize, Long startId);
}
