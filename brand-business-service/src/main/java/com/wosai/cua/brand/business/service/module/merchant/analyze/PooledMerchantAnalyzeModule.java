package com.wosai.cua.brand.business.service.module.merchant.analyze;

import com.wosai.cua.brand.business.service.annotations.AnalyzeFieldCheck;
import lombok.Data;

@Data
public class PooledMerchantAnalyzeModule {

    @AnalyzeFieldCheck(needRequired = true, message = "未填写商户类型！")
    private String brandMerchantType;

    @AnalyzeFieldCheck(needRequired = true, message = "未填写商户编号！")
    private String merchantSn;

    private String outMerchantSn;

    private String mobile;

    private int row;
}
