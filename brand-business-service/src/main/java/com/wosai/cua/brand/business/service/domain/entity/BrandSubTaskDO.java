package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "brand_sub_task")
public class BrandSubTaskDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 任务状态：0-待执行，2-执行成功，3-执行失败
     */
    @TableField(value = "task_status")
    private Byte taskStatus;

    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Date ctime;

    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Date mtime;

    /**
     * 子任务导入任务上下文
     */
    @TableField(value = "sub_task_context")
    private String subTaskContext;

    /**
     * 任务执行结果
     */
    @TableField(value = "task_result")
    private String taskResult;
}