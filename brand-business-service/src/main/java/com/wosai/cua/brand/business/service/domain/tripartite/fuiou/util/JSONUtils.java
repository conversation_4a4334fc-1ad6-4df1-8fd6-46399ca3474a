package com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JSONUtils {
    private static Logger logger = LoggerFactory.getLogger(JSONUtils.class);

    public static String toJSON(Object obj) {
        try {
            return JSONObject.toJSONString(obj, SerializerFeature.WriteMapNullValue);
        } catch (Exception e) {
            logger.error("", e);
        }
        return "";
    }

    public static <T> T toObject(String json, Class<T> clazz) throws Exception{
        ObjectMapper om = new ObjectMapper();
        return om.readValue(json, clazz);
    }
}
