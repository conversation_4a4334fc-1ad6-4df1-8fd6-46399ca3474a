package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.withdraw.BrandWithdrawStrategyModule;
import com.wosai.cua.brand.business.service.module.withdraw.PageBrandWithdrawStrategyModule;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandWithdrawStrategyDomainService {

    /**
     * 创建品牌提现策略
     *
     * @param brandWithdrawStrategyModule 提现策略模型
     * @return 策略id
     */
    Long createBrandWithdrawStrategy(BrandWithdrawStrategyModule brandWithdrawStrategyModule);

    /**
     * 修改品牌提现策略
     *
     * @param brandWithdrawStrategyModule 提现策略模型
     * @return 策略id
     */
    Long modifyBrandWithdrawStrategy(BrandWithdrawStrategyModule brandWithdrawStrategyModule);

    /**
     * 根据策略id获取策略模型
     *
     * @param strategyId 策略id
     * @return 策略模型
     */
    BrandWithdrawStrategyModule getBrandWithdrawStrategyModuleByStrategyId(Long strategyId);

    /**
     * 根据品牌id查询策略模型
     *
     * @param brandId 品牌id
     * @return 策略模型集合
     */
    List<BrandWithdrawStrategyModule> getBrandWithdrawStrategyModuleByBrandId(String brandId);

    /**
     * 批量删除提现策略
     *
     * @param strategyIdList 策略id
     * @return 删除条数
     */
    int deleteBrandWithdrawStrategyByStrategyIdList(List<Long> strategyIdList);

    /**
     * 批量根据策略id查询提现策略
     *
     * @param strategyIdList 策略id集合
     * @return 策略模型集合
     */
    List<BrandWithdrawStrategyModule> getWithdrawStrategyListByStrategyIdList(List<Long> strategyIdList);

    /**
     * 分页查询品牌下的提现策略
     * @param page 当前页
     * @param pageSize 每页展示条数
     * @param brandId 品牌id
     * @return 分页查询模型
     */
    PageBrandWithdrawStrategyModule pageBrandWithdrawStrategy(int page, int pageSize, String brandId);
}
