package com.wosai.cua.brand.business.service.module.bank;

import com.alibaba.fastjson2.annotation.JSO<PERSON>ield;

public class MerchantBizBankAccountModule {
    private String id;
    private int type;
    private String holder;
    @<PERSON><PERSON><PERSON>ield(name = "id_type")
    private int idType;
    private String identity;
    private String number;
    @JSONField(name = "bank_name")
    private String bankName;
    @JSONField(name = "branch_name")
    private String branchName;
    @JSONField(name = "bank_back")
    private String bankBack;
    @JSONField(name = "bank_icon")
    private String bankIcon;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getHolder() {
        return holder;
    }

    public void setHolder(String holder) {
        this.holder = holder;
    }

    public int getIdType() {
        return idType;
    }

    public void setIdType(int idType) {
        this.idType = idType;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBankBack() {
        return bankBack;
    }

    public void setBankBack(String bankBack) {
        this.bankBack = bankBack;
    }

    public String getBankIcon() {
        return bankIcon;
    }

    public void setBankIcon(String bankIcon) {
        this.bankIcon = bankIcon;
    }
}
