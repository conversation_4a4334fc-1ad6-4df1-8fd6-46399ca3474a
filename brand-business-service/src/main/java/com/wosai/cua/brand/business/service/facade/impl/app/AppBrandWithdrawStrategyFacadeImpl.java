package com.wosai.cua.brand.business.service.facade.impl.app;

import com.alibaba.fastjson2.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.CreateWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.DeleteBrandWithdrawStrategyByStrategyIdListDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageQueryMerchantsByWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppRequestBaseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantByWdStrategyResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandWithdrawStrategyResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.EditWithdrawStrategyResponseDTO;
import com.wosai.cua.brand.business.api.enums.ApplicableSceneEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.WithdrawCycleTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.app.AppBrandWithdrawStrategyFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandWithdrawStrategyBusiness;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.module.withdraw.BrandWithdrawStrategyModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class AppBrandWithdrawStrategyFacadeImpl implements AppBrandWithdrawStrategyFacade {

    private final BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness;

    private final BrandBusiness brandBusiness;

    @Autowired
    public AppBrandWithdrawStrategyFacadeImpl(BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness, BrandBusiness brandBusiness) {
        this.brandWithdrawStrategyBusiness = brandWithdrawStrategyBusiness;
        this.brandBusiness = brandBusiness;
    }

    @Override
    public EditWithdrawStrategyResponseDTO createBrandWithdrawStrategy(CreateWithdrawStrategyDTO createWithdrawStrategy) {
        if (Objects.nonNull(createWithdrawStrategy.getWithdrawCycleType()) && Objects.nonNull(createWithdrawStrategy.getWithdrawCycleTime())) {
            ParamsCheckHelper.checkWithdrawCycleTime(WithdrawCycleTypeEnum.getWithdrawCycleTypeEnumByWithdrawCycleType(createWithdrawStrategy.getWithdrawCycleType()), createWithdrawStrategy.getWithdrawCycleTime());
        }
        BrandWithdrawStrategyModule brandWithdrawStrategy = brandWithdrawStrategyBusiness.createBrandWithdrawStrategy(createWithdrawStrategy);
        return JSON.parseObject(JSON.toJSONString(brandWithdrawStrategy), EditWithdrawStrategyResponseDTO.class);
    }

    @Override
    public EditWithdrawStrategyResponseDTO modifyBrandWithdrawStrategy(ModifyWithdrawStrategyDTO modifyWithdrawStrategy) {
        if (Objects.nonNull(modifyWithdrawStrategy.getWithdrawCycleType()) && Objects.nonNull(modifyWithdrawStrategy.getWithdrawCycleTime())) {
            ParamsCheckHelper.checkWithdrawCycleTime(WithdrawCycleTypeEnum.getWithdrawCycleTypeEnumByWithdrawCycleType(modifyWithdrawStrategy.getWithdrawCycleType()), modifyWithdrawStrategy.getWithdrawCycleTime());
        }
        BrandWithdrawStrategyModule brandWithdrawStrategy = brandWithdrawStrategyBusiness.modifyBrandWithdrawStrategy(modifyWithdrawStrategy);
        return JSON.parseObject(JSON.toJSONString(brandWithdrawStrategy), EditWithdrawStrategyResponseDTO.class);
    }

    @Override
    public List<BrandWithdrawStrategyResponseDTO> getBrandWithdrawStrategyByBrandId(AppRequestBaseDTO requestBaseDto) {
        String brandId = brandBusiness.getBrandIdByMerchantId(requestBaseDto.getMerchantId());
        List<BrandWithdrawStrategyResponseDTO> brandWithdrawStrategyResponses = JSON.parseArray(JSON.toJSONString(brandWithdrawStrategyBusiness.getBrandWithdrawStrategyModulesByBrandId(brandId)), BrandWithdrawStrategyResponseDTO.class);
        brandWithdrawStrategyResponses.forEach(brandWithdrawStrategyResponse -> {
            if (Objects.nonNull(brandWithdrawStrategyResponse.getReservedAmount())) {
                brandWithdrawStrategyResponse.setReservedAmount(brandWithdrawStrategyResponse.getReservedAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            if (Objects.nonNull(brandWithdrawStrategyResponse.getMinWithdrawalAmount())) {
                brandWithdrawStrategyResponse.setMinWithdrawalAmount(brandWithdrawStrategyResponse.getMinWithdrawalAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
        });
        return brandWithdrawStrategyResponses;
    }

    @Override
    public int deleteBrandWithdrawStrategyByStrategyIdList(DeleteBrandWithdrawStrategyByStrategyIdListDTO deleteByStrategyIdListDto) {
        return brandWithdrawStrategyBusiness.deleteBrandWithdrawStrategyByStrategyIdList(JSON.parseArray(JSON.toJSONString(deleteByStrategyIdListDto.getStrategyIdList()), Long.class));
    }

    @Override
    public BrandMerchantByWdStrategyResponseDTO pageBrandMerchantWdStrategyList(AppPageQueryMerchantsByWithdrawStrategyDTO queryMerchantsByWithdrawStrategyDto) {
        String brandId = brandBusiness.getBrandIdByMerchantId(queryMerchantsByWithdrawStrategyDto.getMerchantId());
        return brandWithdrawStrategyBusiness.pageBrandMerchantWdStrategyList(brandId, queryMerchantsByWithdrawStrategyDto);
    }

    @Override
    public BrandWithdrawStrategyResponseDTO getBrandMerchantStrategy(AppRequestBaseDTO requestBaseDto) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = brandBusiness.getBrandInfoListByMerchantId(requestBaseDto.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        requestBaseDto.setMerchantId(brandBusiness.replaceOwnerMerchantId(brandSimpleInfoList.get(0).getBrandId(), requestBaseDto.getMerchantId()));
        BrandWithdrawStrategyModule strategyModule = brandWithdrawStrategyBusiness.getStrategyModuleByBrandIdAndMerchantId(brandSimpleInfoList.get(0).getBrandId(), requestBaseDto.getMerchantId());
        if (Objects.isNull(strategyModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_NOT_BIND_STRATEGY);
        }
        BrandWithdrawStrategyResponseDTO brandWithdrawStrategyResponse = JSON.parseObject(JSON.toJSONString(strategyModule), BrandWithdrawStrategyResponseDTO.class);
        if (Objects.nonNull(brandWithdrawStrategyResponse.getReservedAmount())) {
            brandWithdrawStrategyResponse.setReservedAmount(brandWithdrawStrategyResponse.getReservedAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }
        if (Objects.nonNull(brandWithdrawStrategyResponse.getMinWithdrawalAmount())) {
            brandWithdrawStrategyResponse.setMinWithdrawalAmount(brandWithdrawStrategyResponse.getMinWithdrawalAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }
        ApplicableSceneEnum applicableSceneEnum = ApplicableSceneEnum.getEnumByName(brandWithdrawStrategyResponse.getApplicableScene());
        if (Objects.nonNull(applicableSceneEnum)) {
            brandWithdrawStrategyResponse.setApplicableSceneDesc(applicableSceneEnum.getDesc());
        }
        return brandWithdrawStrategyResponse;
    }
}
