package com.wosai.cua.brand.business.service.facade.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.CreatePartnerRequestDTO;
import com.wosai.cua.brand.business.api.facade.PartnerFacade;
import com.wosai.cua.brand.business.service.business.PartnerBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class PartnerFacadeImpl implements PartnerFacade {

    private final PartnerBusiness partnerBusiness;

    @Autowired
    public PartnerFacadeImpl(PartnerBusiness partnerBusiness) {
        this.partnerBusiness = partnerBusiness;
    }

    @Override
    public String createPartner(CreatePartnerRequestDTO createPartnerRequest) {
        return partnerBusiness.createPartner(createPartnerRequest);
    }
}
