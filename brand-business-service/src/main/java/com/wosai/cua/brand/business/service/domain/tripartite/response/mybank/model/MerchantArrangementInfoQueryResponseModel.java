package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *ant.mybank.merchantprod.merchant.arrangement.info.query
 * */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name ="body")
public class MerchantArrangementInfoQueryResponseModel extends RequestBody {


    private static final long serialVersionUID = -6653139574677847541L;

    /**
     * 返回码组件
     */
    @XmlElementRef
    private RespInfo respInfo;

    /**
     * 合约号
     */
    @XmlElement(name = "ArrangementNo")
    private String arrangementNo;

    /**
     * 合约状态
     */
    @XmlElement(name = "Status")
    private String status;


    public RespInfo getRespInfo() {
        return respInfo;
    }

    public void setRespInfo(RespInfo respInfo) {
        this.respInfo = respInfo;
    }

    public String getArrangementNo() {
        return arrangementNo;
    }

    public void setArrangementNo(String arrangementNo) {
        this.arrangementNo = arrangementNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}