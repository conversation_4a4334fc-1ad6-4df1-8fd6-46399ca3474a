package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter

public enum PersonalExcelFieldEnum {
    /**
     *
     */
    BRAND_MERCHANT_TYPE(0,"brandMerchantType","未填写或填写错误品牌商户类型！",true, false, "", ""),
    MERCHANT_NAME(1,"merchantName","未填写商户名称！",true, false, "", ""),

    NAME(2,"name","未填写真实姓名",true, false, "", ""),

    ID_TYPE(3,"idType","未填写证件类型",true, false, "", ""),

    ID_NUMBER(4,"idNumber","未填写证件号码",true, false, "", ""),

    /**
     *
     */
    PROVINCE(5,"province","未填写所在省份",true, false, "", ""),
    /**
     *
     */
    CITY(6,"city","未填写所在市",true, false, "", ""),
    /**
     *
     */
    DISTRICT(7,"district","",false, false, "", ""),

    ADDRESS(8,"address","未填写详细地址",true, false, "", ""),

    CELLPHONE(9,"cellphone","未填写手机号",true, true, "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$", "手机号填写错误"),

    EMAIL(10,"email","",false, true, "^[0-9A-Za-z_]+([-+.][0-9A-Za-z_]+)*@[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*\\.[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*$", "邮箱格式不正确"),
    BANK_NAME(11,"bankName","银行卡开户行未填写",true, false, "", ""),
    BANK_NUMBER(12,"bankNumber","银行卡卡号未填写",true, false, "", ""),
    CELL_PHONE_NUMBER(13,"cellPhoneNumber","预留手机号未填写",true, true, "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$", "预留手机号格式错误"),
    STORE_NAME(14,"storeSn","门店名称未填写",true, false, "", ""),
    /**
     *
     */
    MEI_TUAN_STORE_SN(15,"meiTuanStoreSn","",false, false, "", ""),
    /**
     *
     */
    ELM_STORE_SN(16,"elmStoreSn","",false, false, "", ""),

    DY_STORE_SN(17,"dyStoreSn","",false, false, "", ""),
    /**
     *
     */
    OUT_MERCHANT_ID(18,"outMerchantNo","",false, false, "", ""),
    ;
    private final int columnNo;

    private final String fieldName;

    private final String checkMessage;

    private final boolean needRequired;

    private final boolean needFormatCheck;

    private final String regex;

    private final String formatCheckMsg;

    PersonalExcelFieldEnum(int columnNo, String fieldName, String checkMessage, boolean needRequired, boolean needFormatCheck, String regex, String formatCheckMsg) {
        this.columnNo = columnNo;
        this.fieldName = fieldName;
        this.checkMessage = checkMessage;
        this.needRequired = needRequired;
        this.needFormatCheck = needFormatCheck;
        this.regex = regex;
        this.formatCheckMsg = formatCheckMsg;
    }
}
