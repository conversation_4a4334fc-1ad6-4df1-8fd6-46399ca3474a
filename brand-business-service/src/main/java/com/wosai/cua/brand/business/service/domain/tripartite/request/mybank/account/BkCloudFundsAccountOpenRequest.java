package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.account;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.XmlUtils;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MyBankRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.account.BkCloudFundsAccountOpenResponse;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * Mybank Api: ant.mybank.bkcloudfunds.account.open
 *
 */
public class BkCloudFundsAccountOpenRequest implements MyBankRequest<BkCloudFundsAccountOpenResponse> {

    /**
     * API接口代码
     */
    private final String apiFunction = "ant.mybank.bkcloudfunds.account.open";

    /**
     * API接口版本
     */
    private String apiVersion = "1.0.0";

    /**
     * 请求是否需要加签 true/false
     *
     * 默认：true
     */
    private boolean needSign = true;

    /**
     * 请求路由分组
     */
    private String cookieValue;

    /**
     * 公共请求参数（head）
     */
    private RequestHead requestHead;

    /**
     * 业务请求参数（body）
     */
    private RequestBody requestBody;

    /**
     * @param appId 应用ID 由浙江网商银行统一分配,用于识别合作伙伴应用系统，即对端系统编号。联调前线下提供
     */
    public BkCloudFundsAccountOpenRequest(String appId){
        this.requestHead = RequestHead.builder(this.apiVersion, appId, this.apiFunction)
                                      .inputCharset(MybankConstants.CHARSET_UTF8)
                                      .reqMsgId(UUID.randomUUID().toString().replace(MybankConstants.CONNECT_SYMBOL_STRING, MybankConstants.NULL_STRING))
                                      .reqTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(MybankConstants.DATE_TIME_FORMAT)))
                                      .reqTimeZone(MybankConstants.DATE_TIMEZONE)
                                      .reserve(MybankConstants.NULL_STRING)
                                      .signType(MybankConstants.SIGN_TYPE_RSA)
                                      .build();
    }

    public BkCloudFundsAccountOpenRequest(RequestHead requestHead, RequestBody requestBody){
        this.requestHead = requestHead;
        this.requestBody = requestBody;
    }

    @Override
    public String getApiFunction() { return this.apiFunction; }

    @Override
    public String getApiVersion() { return this.apiVersion; }
    @Override
    public void setApiVersion(String apiVersion) { this.apiVersion = apiVersion; }

    @Override
    public void setCookieValue(String cookieValue) { this.cookieValue = cookieValue; }
    @Override
    public String getCookieValue() { return this.cookieValue; }

    @Override
    public Class<BkCloudFundsAccountOpenResponse> getResponseClass() { return BkCloudFundsAccountOpenResponse.class; }

    @Override
    public boolean isNeedSign() { return this.needSign; }
    @Override
    public void setNeedSign(boolean needSign) { this.needSign = needSign; }

    @Override
    public RequestHead getRequestHead() { return this.requestHead; }
    @Override
    public void setRequestHead(RequestHead requestHead) { this.requestHead = requestHead; }

    @Override
    public RequestBody getRequestBody() { return this.requestBody; }
    @Override
    public void setRequestBody(RequestBody requestBody) { this.requestBody = requestBody; }

    @Override
    public String xmlBuild() throws MybankApiException {
        return XmlUtils.requestXmlBuild(this.requestHead, this.requestBody);
    }

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.MY_BANK_OPEN_FUNDS_ACCOUNT;
    }
}