package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.CDataAdapter;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * 商户入驻申请<ant.mybank.merchantprod.merch.register>
 * <p>
 * 合作方需要将委托的商户信息通过本接口申请入驻网商银行并开立商户子账户。开户申请后，网商会异步通知开户结果。合作方也可通过开户结果查询接口获知处理结果。
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
@Data
public class MerchantRegisterRequestModel extends RequestBody {

    private static final long serialVersionUID = -7997043172475800178L;
    /**
     * 合作方机构号
     * <p>
     * 网商银行分配
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "IsvOrgId")
    private String isvOrgId;

    /**
     * 外部交易号
     * 合作方系统生成的外部交易号，同一交易号被视为同一笔交易。
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "OutTradeNo")
    private String outTradeNo;

    /**
     * 外部商户号 必须为32位
     * 合作商对商户的自定义编码，要求同一个合作商下保持唯一。
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "OutMerchantId")
    private String outMerchantId;

    /**
     * 商户类型。可选值  01:自然人; 02:个体工商户; 03:企业商户
     * <p>
     * 说明：以同一个身份证件办理的自然人商户受理信用卡的收款金额上限为日累计1000元、月累计1万，建议有营业执照的商户以个体或企业身份入驻
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MerchantType")
    private String merchantType;

    /**
     * 商户名称
     * <p>
     * 最多50个汉字或字符，可以包含特殊字符，规则如下：
     * <p>
     * 一、自然人商户名称校验规则
     * 商户_XXX负责人名称，中间有分隔符"_"
     * <p>
     * 二、个体工商户名称校验规则
     * 1. 有工商注册名且可见(非*),且工商注册名不是负责人名称（即为店名或企业名），以工商注册名为商户户名
     * 2. 有营业执照名但是名字含*，商户名为：个体户+XXX负责人名称，中间无分隔符
     * 3. 无工商注册名称，商户名为：个体户+XXX负责人名称，中间无分隔符
     * <p>
     * 三、企业商户名称校验规则必须与工商注册名保持一致
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MerchantName")
    private String merchantName;

    /**
     * 商户经营类型   可选值：01:实体特约商户; 02:网络特约商户; 03:实体兼网络特约商户
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "DealType")
    private String dealType;

    /**
     * 商户详情列表
     * <p>
     * json格式base64编码，具体报文定义参考商户详情
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MerchantDetail")
    private String merchantDetail;

    /**
     * 清算卡参数
     * <p>
     * json格式base64编码，具体报文定义参考下面的清算卡，仅结算方式为“01结算到他行卡”需要填写
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "BankCardParam")
    private String bankCardParam;

    /**
     * 手机验证码 仅结算方式为“01结算到他行卡”及商户类型为“01 自然人、02 个体工商户”需要填写。
     * <p>
     * 备注：合作方系统须先调网商短信验证码发送接口，商户获取验证码后在申请页面回填验证码，合作方系统通过本接口统一上送。
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "AuthCode")
    private String authCode;

    /**
     * 经营类目
     * <p>
     * 参见附录的经营类目上送
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "Mcc")
    private String mcc;

    /**
     * 云资金信息
     * json格式base64编码，具体报文定义参考云资金信息
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "CloudFundsInfo")
    private String cloudFundsInfo;

    /**
     * 支持支付渠道列表
     * 该商户能支持的第三方支付渠道。多个用逗号隔开。可选值：
     * 06：直付通
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "PayChannelList")
    private String payChannelList;

    /**
     * 直付通信息
     * json格式base64编码，具体报文定义参考直付通信息
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "ZftAlipayInfo")
    private String zftAlipayInfo;

    /**
     * 直付通开票信息
     * json格式base64编码，具体报文定义参考直付通开票信息
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "InvoiceInfo")
    private String invoiceInfo;

    /**
     * ISV身份类别
     *（共管均传CORMANAGEMENT）
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "MainIdentity")
    private String mainIdentity;

    /**
     * 进件商户类别
     * (人资公司：HR_COR, 用工企业：LAB_COR)
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "SubIdentity")
    private String subIdentity;

    /**
     * 进件身份类别信息拓展字段，用工企业可上传模式标
     *
     *{"LAB_MODEL":"PAYROLL"} --纯代发
     * {"LAB_MODEL":"CORMANAGE"}--共管
     * 上述二选一
     */
    @XmlJavaTypeAdapter(CDataAdapter.class)
    @XmlElement(name = "IdentityExt")
    private String identityExt;

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.service.domain.https.request.mybank.model.MerchantRegisterRequest");
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.service.domain.https.request.mybank.model.MerchantRegisterRequest");
    }
}