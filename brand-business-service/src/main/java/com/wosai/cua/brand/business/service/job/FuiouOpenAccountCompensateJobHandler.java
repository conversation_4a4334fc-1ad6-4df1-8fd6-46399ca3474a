package com.wosai.cua.brand.business.service.job;

import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FuiouOpenAccountCompensateJobHandler {

    private final BrandBusiness brandBusiness;

    @Autowired
    public FuiouOpenAccountCompensateJobHandler(BrandBusiness brandBusiness) {
        this.brandBusiness = brandBusiness;
    }

    @XxlJob("fuiouOpenAccountCompensate")
    public void fuiouOpenAccountCompensate() {
        brandBusiness.fuiouOpenAccountCompensate();
    }
}
