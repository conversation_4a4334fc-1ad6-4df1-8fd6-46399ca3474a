package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "request")
@XmlType(propOrder = {"requestHead", "requestBody"})
public class Request extends MybankObject {
    private static final long serialVersionUID = 5843370350430152961L;

    @XmlAttribute(name = "id")
    private String id;

    @XmlElementRef
    private RequestHead requestHead;

    @XmlElementRef
    private RequestBody requestBody;

    public Request() {
    }

    public Request(RequestHead requestHead, RequestBody requestBody) {
        this.requestHead = requestHead;
        this.requestBody = requestBody;
        this.id = MybankConstants.REQUEST;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public RequestHead getRequestHead() {
        return requestHead;
    }

    public void setRequestHead(RequestHead requestHead) {
        this.requestHead = requestHead;
    }

    public RequestBody getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(RequestBody requestBody) {
        this.requestBody = requestBody;
    }
}