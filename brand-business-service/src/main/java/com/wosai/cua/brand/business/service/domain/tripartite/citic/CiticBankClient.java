package com.wosai.cua.brand.business.service.domain.tripartite.citic;

import com.wosai.cua.brand.business.service.domain.tripartite.citic.helper.CiticXmlHelper;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.BaseCiticRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.CiticResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.Map;

/**
 * 中信银行调用客户端
 *
 * @param <T>
 */
public class CiticBankClient<T extends CiticResponse<?>> {

    private static final SimpleDateFormat DEFAULT_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss.SSSSSS");
    private static final Logger log = LoggerFactory.getLogger(CiticBankClient.class);
    public static final String FAILED_CODE = "400";
    private static final String SERVICE_URL_NOT_EXISTS = "中信银行链接没有配置！";

    private static final String SIGNATURE_VALIDATION_FAILED = "中信银行响应信息验签失败，请检查签名！";
    private static final String SERVICE_INVOCATION_EXCEPTION = "调用中信银行发生异常！";

    private final HttpClient httpClient;

    // 商户编号
    private final String merchantId;
    // 私钥密码
    private final String privateKeyPassword;
    // 私钥
    private final String privateKey;
    // 发送证书
    private final String publicKey;
    // 交易码
    private final Map<String, String> transCodeEndpoints;


    public CiticBankClient(String merchantId, String privateKeyPassword, String privateKey, String publicKey, Map<String, String> transCodeEndpoints) {
        this.transCodeEndpoints = transCodeEndpoints;
        this.merchantId = merchantId;
        this.privateKeyPassword = privateKeyPassword;
        this.privateKey = privateKey;
        this.publicKey = publicKey;

        HttpClient httpClient = null;
        try {
            httpClient = createHttpClient();
        } catch (Exception e) {
            log.error("[调用中信银行]创建Client失败>>>", e);
        }
        this.httpClient = httpClient;
    }

    public T invoke(BaseCiticRequest request, Class<T> responseType) {
        try {

            String serviceUrl = transCodeEndpoints.get(request.getTransCode());
            if (serviceUrl == null) {
                return createFailedResponse(responseType, SERVICE_URL_NOT_EXISTS);
            }

            String responseString = sendRequest(
                    serviceUrl,
                    request.getTransCode(),
                    request.getLaasSsn(),
                    request.generateSignedRequestXml(privateKeyPassword, privateKey, publicKey)
            );

            T response = CiticXmlHelper.fromXml(responseString, responseType);

            if (response == null) {
                log.error(SERVICE_INVOCATION_EXCEPTION);
                return createFailedResponse(responseType, SERVICE_INVOCATION_EXCEPTION);
            }

//            if (response.verifySignNotPass(responseString, publicKey)) {
//                log.error(SIGNATURE_VALIDATION_FAILED);
//                return createFailedResponse(responseType, SIGNATURE_VALIDATION_FAILED);
//            }

            return response;
        } catch (Exception e) {
            log.error(SERVICE_INVOCATION_EXCEPTION, e);
            return createFailedResponse(responseType, SERVICE_INVOCATION_EXCEPTION);
        }
    }

    private String sendRequest(String httpsUrl, String tranCode, String laasSsn, String xmlStr) {
        if (StringUtils.isEmpty(httpsUrl) || StringUtils.isEmpty(xmlStr)) {
            log.info("[调用中信银行]请求url或发送内容为空！");
            return null;
        }

        try {
            HttpPost post = new HttpPost(httpsUrl);
            log.info("[调用中信银行]请求url>>>{}", httpsUrl);
            log.info("[调用中信银行]入参>>>{}", xmlStr);

            post.setHeader("Content-Type", "application/xml; charset=UTF-8");
            post.setHeader("merchantNo", merchantId);
            post.setHeader("tranCode", tranCode);
            post.setHeader("serialNo", laasSsn);
            post.setHeader("tranTmpt", DEFAULT_DATE_FORMAT.format(new Date()));
            post.setHeader("version", "v1.0");

            post.setEntity(new StringEntity(xmlStr, StandardCharsets.UTF_8));

            Instant start = Instant.now();
            HttpResponse response = httpClient.execute(post);
            Instant end = Instant.now();
            Duration duration = Duration.between(start, end);

            HttpEntity entity = response.getEntity();
            String responseString = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            log.info("[调用中信银行]出参>>>{}", responseString);
            log.info("[调用中信银行]花费时间>>> {}ms", duration.toMillis());
            return responseString;
        } catch (IOException e) {
            log.warn("[调用中信银行]异常>>>",e);
        }

        return null;
    }

    private HttpClient createHttpClient() throws Exception {

        SSLContext ctx = SSLContext.getInstance(SSLConnectionSocketFactory.TLS);
        ctx.init(null, new TrustManager[]{new X509TrustManager() {
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }

            @Override
            public void checkClientTrusted(X509Certificate[] xcs, String str) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] xcs, String str) {
            }
        }}, null);

        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(ctx,
                NoopHostnameVerifier.INSTANCE);

        return HttpClients.custom()
                .setSSLSocketFactory(sslSocketFactory)
                .build();
    }

    private T createFailedResponse(Class<T> responseType, String message) {
        try {
            T response = responseType.getDeclaredConstructor().newInstance();
            response.setCode(FAILED_CODE);
            response.setMessage(message);
            return response;
        } catch (Exception e) {
            log.error("Failed to create default response", e);
            return null;
        }
    }
}



