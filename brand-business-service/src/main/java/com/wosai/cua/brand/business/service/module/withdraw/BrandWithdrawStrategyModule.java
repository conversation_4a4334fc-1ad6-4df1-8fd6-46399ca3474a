package com.wosai.cua.brand.business.service.module.withdraw;

import com.wosai.cua.brand.business.api.enums.ApplicableSceneEnum;
import com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum;
import com.wosai.cua.brand.business.service.domain.entity.BrandWithdrawStrategyDO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提现策略模型
 * <AUTHOR>
 */
@Data
public class BrandWithdrawStrategyModule {

    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 提现类型
     * @see com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum
     */
    private String withdrawType;
    /**
     * 提现类型描述
     */
    private String withdrawTypeDesc;
    /**
     * 提现周期描述
     */
    private String withdrawCycleDesc;

    /**
     * 最少单笔提现金额，单位：元
     */
    private Integer minWithdrawalAmount;

    /**
     * 提现预留金额，单位：元
     */
    private Integer reservedAmount;

    /**
     * 关联的商户数量
     */
    private Integer merchantNumber;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 提现周期类型
     */
    private String withdrawCycleType;

    /**
     * 提现周期时间
     */
    @Deprecated
    private Integer withdrawCycleTime;

    /**
     * 提现周期时间（多选）
     */
    private String withdrawCycleTimes;

    /**
     * 提现策略描述（用于前端展示）
     */
    private String withdrawStrategyDesc;

    /**
     * 适用场景
     * @see com.wosai.cua.brand.business.api.enums.ApplicableSceneEnum
     */
    private String applicableScene;

    /**
     * 适用场景描述
     */
    private String applicableSceneDesc;

    /**
     * 备注
     */
    private String remark;

    public static BrandWithdrawStrategyModule convert(BrandWithdrawStrategyDO brandWithdrawStrategy) {
        if (brandWithdrawStrategy == null) {
            return null;
        }
        BrandWithdrawStrategyModule brandWithdrawStrategyModule = new BrandWithdrawStrategyModule();
        brandWithdrawStrategyModule.setId(brandWithdrawStrategy.getId());
        brandWithdrawStrategyModule.setBrandId(brandWithdrawStrategy.getBrandId());
        brandWithdrawStrategyModule.setStrategyId(brandWithdrawStrategy.getStrategyId());
        brandWithdrawStrategyModule.setWithdrawType(brandWithdrawStrategy.getWithdrawType());
        brandWithdrawStrategyModule.setWithdrawCycleType(brandWithdrawStrategy.getWithdrawCycleType());
        brandWithdrawStrategyModule.setWithdrawCycleTimes(brandWithdrawStrategy.getWithdrawCycleTimes());
        brandWithdrawStrategyModule.setWithdrawCycleTime(brandWithdrawStrategy.getWithdrawCycleTime());
        brandWithdrawStrategyModule.setMinWithdrawalAmount(brandWithdrawStrategy.getMinWithdrawalAmount());
        brandWithdrawStrategyModule.setReservedAmount(brandWithdrawStrategy.getReservedAmount());
        brandWithdrawStrategyModule.setApplicableScene(brandWithdrawStrategy.getApplicableScene());
        brandWithdrawStrategyModule.setRemark(brandWithdrawStrategy.getRemark());
        brandWithdrawStrategyModule.setCreatedTime(brandWithdrawStrategy.getCreatedTime());
        brandWithdrawStrategyModule.setApplicableSceneDesc(ApplicableSceneEnum.getDescByName(brandWithdrawStrategy.getApplicableScene()));
        brandWithdrawStrategyModule.setWithdrawTypeDesc(WithdrawTypeEnum.getDescByWithdrawType(brandWithdrawStrategy.getWithdrawType()));
        return brandWithdrawStrategyModule;
    }

    public static List<BrandWithdrawStrategyModule> convert(List<BrandWithdrawStrategyDO> brandWithdrawStrategyList) {
        return brandWithdrawStrategyList.stream().map(BrandWithdrawStrategyModule::convert).collect(Collectors.toList());
    }
}
