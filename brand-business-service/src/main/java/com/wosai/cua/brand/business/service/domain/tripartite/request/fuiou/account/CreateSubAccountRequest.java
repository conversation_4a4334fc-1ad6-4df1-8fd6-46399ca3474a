package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

/**
 * 商户自有子账户创建接口（TS001）
 * 接口名称：/createSubAccount.fuiou
 * 接口说明：
 * 1、调用此接口，商户创建自有子账户。商户自有子账户支持作为分账交易的入账方，也可支持作为分账交易的出账方。商户自有子账户结算账户信息同商户对公账户信息。
 * 2、商户自有子账户余额提现，通过JY010接口处理。
 */
@Data
public class CreateSubAccountRequest implements TripartiteSystemCallRequest {

    public static final String REQUEST_URI = "/createSubAccount.fuiou";

    private CreateSubAccountRequestBody body;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_CREATE_SUB_ACCOUNT;
    }
}
