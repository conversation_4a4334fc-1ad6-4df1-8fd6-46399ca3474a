package com.wosai.cua.brand.business.service.module.brand.convert;

import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;

import java.util.Objects;

public class BrandConverter {
    private BrandConverter() {
        throw new IllegalStateException("Utility class");
    }

    public static BrandDO convertDO(BrandModule brandModule) {
        if (brandModule == null) {
            return null;
        }
        BrandDO brandDO = new BrandDO();
        brandDO.setId(brandModule.getId());
        brandDO.setBrandId(brandModule.getBrandId());
        brandDO.setSn(brandModule.getSn());
        brandDO.setName(brandModule.getName());
        brandDO.setAlias(brandModule.getAlias());
        brandDO.setMerchantSn(brandModule.getMerchantSn());
        brandDO.setDockingMode(brandModule.getDockingMode());
        brandDO.setParentId(brandModule.getParentId());
        brandDO.setProvince(brandModule.getProvince());
        brandDO.setCity(brandModule.getCity());
        brandDO.setDistrict(brandModule.getDistrict());
        brandDO.setAddress(brandModule.getAddress());
        brandDO.setIndustry(brandModule.getIndustry());
        brandDO.setContactName(brandModule.getContactName());
        brandDO.setContactPhone(brandModule.getContactPhone());
        brandDO.setContactEmail(brandModule.getContactEmail());
        if (Objects.nonNull(brandModule.getFundManagementCompanyCode())) {
            brandDO.setFundManagementCompanyCode(brandModule.getFundManagementCompanyCode().getFundManagementCompanyCode());
        }
        brandDO.setFundManagementCompany(brandModule.getFundManagementCompany());
        brandDO.setLevel(brandModule.getLevel());
        brandDO.setRemark(brandModule.getRemark());
        brandDO.setExtra(brandModule.getExtra());
        brandDO.setCreatedTime(brandModule.getCreatedTime());
        brandDO.setUpdatedTime(brandModule.getUpdatedTime());
        brandDO.setSftTag(brandModule.getSftTag());
        brandDO.setEnableSft(brandModule.getEnableSft());
        return brandDO;
    }
}
