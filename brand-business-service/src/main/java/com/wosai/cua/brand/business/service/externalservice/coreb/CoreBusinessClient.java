package com.wosai.cua.brand.business.service.externalservice.coreb;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.cua.brand.business.service.externalservice.coreb.model.MerchantFindRequest;
import com.wosai.cua.brand.business.service.externalservice.coreb.model.MerchantQueryResult;
import com.wosai.cua.brand.business.service.externalservice.coreb.model.MerchantSimpleQueryResult;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@Component
@Slf4j
public class CoreBusinessClient {

    @Autowired
    private MerchantService merchantService;

    /**
     * 每批处理的商户ID数量
     */
    private static final int BATCH_SIZE = 500;

    /**
     * 并发线程池大小
     */
    private static final int THREAD_POOL_SIZE = 10;

    /**
     * 线程池，用于并发处理请求
     */
    private final ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);

    public List<MerchantQueryResult> findMerchants(MerchantFindRequest request) {
        if (WosaiCollectionUtils.isNotEmpty(request.getMerchantIds()) && WosaiCollectionUtils.isNotEmpty(request.getMerchantSns())) {
            throw new IllegalArgumentException("merchantIds和merchantSns不能同时存在");
        }

        // 检查是否需要分批处理
        boolean needBatchProcess = (request.getMerchantIds() != null && request.getMerchantIds().size() > BATCH_SIZE) ||
                (request.getMerchantSns() != null && request.getMerchantSns().size() > BATCH_SIZE);

        if (!needBatchProcess) {
            return findMerchantsInternal(request, new PageInfo(1, 10000));
        }

        return batchProcess(request.getMerchantIds(), request.getMerchantSns(), request, merchantFindRequest -> findMerchantsInternal(merchantFindRequest, new PageInfo(1, 10000)), BATCH_SIZE);
    }

    public List<MerchantSimpleQueryResult> findSimpleMerchants(MerchantFindRequest request) {
        if (WosaiCollectionUtils.isNotEmpty(request.getMerchantIds()) && WosaiCollectionUtils.isNotEmpty(request.getMerchantSns())) {
            throw new IllegalArgumentException("merchantIds和merchantSns不能同时存在");
        }

        // 检查是否需要分批处理
        boolean needBatchProcess = (request.getMerchantIds() != null && request.getMerchantIds().size() > BATCH_SIZE) ||
                (request.getMerchantSns() != null && request.getMerchantSns().size() > BATCH_SIZE);

        if (!needBatchProcess) {
            return findSimpleMerchantsInternal(request, new PageInfo(1, 10000));
        }

        return batchProcess(request.getMerchantIds(), request.getMerchantSns(), request,
                merchantFindRequest -> findSimpleMerchantsInternal(merchantFindRequest, new PageInfo(1, 10000)), BATCH_SIZE);
    }

    /**
     * 内部方法，直接调用merchantService查询商户信息
     */
    private List<MerchantQueryResult> findMerchantsInternal(MerchantFindRequest request, PageInfo pageInfo) {
        try {
            ListResult merchants = merchantService.findMerchants(pageInfo, CollectionUtil.hashMap(
                    "merchant_sn", request.getMerchantSn(),
                    "merchant_name", request.getMerchantName(),
                    "merchant_business_name", request.getMerchantBusinessName(),
                    "contact_cellphone", request.getContactCellphone(),
                    "merchant_ids", request.getMerchantIds(),
                    "merchant_sns", request.getMerchantSns()
            ));

            if (merchants == null || merchants.getRecords() == null) {
                return Collections.emptyList();
            }

            return merchants.getRecords().stream().map(r -> new MerchantQueryResult()
                        .setMerchantId(WosaiMapUtils.getString(r, DaoConstants.ID))
                        .setMerchantSn(WosaiMapUtils.getString(r, Merchant.SN))
                        .setName(WosaiMapUtils.getString(r, Merchant.NAME))
                        .setBusinessName(WosaiMapUtils.getString(r, Merchant.BUSINESS_NAME))
                        .setContactName(WosaiMapUtils.getString(r, Merchant.CONTACT_NAME))
                        .setContactCellphone(WosaiMapUtils.getString(r, Merchant.CONTACT_CELLPHONE)))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询商户信息异常, 请求参数: {}", request, e);
            return Collections.emptyList();
        }
    }

    /**
     * 内部方法，直接调用merchantService查询简单商户信息
     */
    private List<MerchantSimpleQueryResult> findSimpleMerchantsInternal(MerchantFindRequest request, PageInfo pageInfo) {
        try {
            ListResult merchants = merchantService.findSimpleMerchants(pageInfo, CollectionUtil.hashMap(
                    "merchant_sn", request.getMerchantSn(),
                    "merchant_name", request.getMerchantName(),
                    "merchant_business_name", request.getMerchantBusinessName(),
                    "contact_cellphone", request.getContactCellphone(),
                    "merchant_ids", request.getMerchantIds(),
                    "merchant_sns", request.getMerchantSns()
            ));

            if (merchants == null || merchants.getRecords() == null) {
                return Collections.emptyList();
            }

            return merchants.getRecords().stream().map(r -> new MerchantSimpleQueryResult()
                        .setMerchantId(WosaiMapUtils.getString(r, DaoConstants.ID))
                        .setMerchantSn(WosaiMapUtils.getString(r, Merchant.SN))
                        .setName(WosaiMapUtils.getString(r, Merchant.NAME)))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询简单商户信息异常, 请求参数: {}", request, e);
            return Collections.emptyList();
        }
    }

    /**
     * 分批处理通用方法
     */
    private <T> List<T> batchProcess(List<String> ids, List<String> sns, MerchantFindRequest request,
                                 Function<MerchantFindRequest, List<T>> processor, int batchSize) {
        if (ids == null && sns == null) {
            return Collections.emptyList();
        }

        List<CompletableFuture<List<T>>> futures = new ArrayList<>();

        // 处理merchantIds
        if (ids != null && !ids.isEmpty()) {
            List<List<String>> batchedIds = partitionList(ids, batchSize);
            for (List<String> batchIds : batchedIds) {
                MerchantFindRequest batchRequest = new MerchantFindRequest()
                    .setMerchantIds(batchIds)
                    .setMerchantSn(request.getMerchantSn())
                    .setMerchantName(request.getMerchantName())
                    .setMerchantBusinessName(request.getMerchantBusinessName())
                    .setContactCellphone(request.getContactCellphone());

                CompletableFuture<List<T>> future = CompletableFuture.supplyAsync(
                    () -> processor.apply(batchRequest), executorService);
                futures.add(future);
            }
        }

        // 处理merchantSns
        if (sns != null && !sns.isEmpty()) {
            List<List<String>> batchedSns = partitionList(sns, batchSize);
            for (List<String> batchSns : batchedSns) {
                MerchantFindRequest batchRequest = new MerchantFindRequest()
                    .setMerchantSns(batchSns)
                    .setMerchantSn(request.getMerchantSn())
                    .setMerchantName(request.getMerchantName())
                    .setMerchantBusinessName(request.getMerchantBusinessName())
                    .setContactCellphone(request.getContactCellphone());

                CompletableFuture<List<T>> future = CompletableFuture.supplyAsync(
                    () -> processor.apply(batchRequest), executorService);
                futures.add(future);
            }
        }

        // 等待所有任务完成并合并结果
        return futures.stream()
            .map(CompletableFuture::join)
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }

    /**
     * 将列表分割成指定大小的批次
     *
     * @param list 原始列表
     * @param batchSize 每批大小
     * @param <T> 列表元素类型
     * @return 分批后的列表集合
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        List<List<T>> result = new ArrayList<>();
        int size = list.size();
        for (int i = 0; i < size; i += batchSize) {
            int endIndex = Math.min(i + batchSize, size);
            result.add(list.subList(i, endIndex));
        }
        return result;
    }

    /**
     * 在应用关闭时关闭线程池
     */
    @PreDestroy
    public void destroy() {
        if (!executorService.isShutdown()) {
            executorService.shutdown();
            log.info("CoreBusinessClient线程池已关闭");
        }
    }
}
