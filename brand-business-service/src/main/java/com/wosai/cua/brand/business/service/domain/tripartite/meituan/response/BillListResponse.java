package com.wosai.cua.brand.business.service.domain.tripartite.meituan.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class BillListResponse {
    // 汇总信息
    @JsonProperty("extra_info")
    private ExtraInfo extraInfo;

    // 账单信息列表
    private List<Bill> data;

    public static class ExtraInfo {
        // 当前查询时间条件下返回的账单数据总条数
        @JsonProperty("total_count")
        private Integer totalCount;

        // 当前查询时间条件下返回的账单数据总结算金额（settleMilli汇总）,单位分
        @JsonProperty("settle_sum")
        private String settleSum;

        public Integer getTotalCount() {
            return totalCount;
        }

        public String getSettleSum() {
            return settleSum;
        }

        public void setSettleSum(String settleSum) {
            this.settleSum = settleSum;
        }

        public void setTotalCount(Integer totalCount) {
            this.totalCount = totalCount;
        }
    }

    public static class Bill {
        // 账单的唯一标识id
        private String id;

        // 门店名称
        private String wmPoiName;

        // 结算类型
        private Integer billChargeType;

        // 结算类型描述
        private String chargeFeeDesc;

        // 用户支付方式
        private Integer userPayType;

        // 订单流水号
        private String wmPoiOrderPushDayseq;

        // 订单展示ID
        private String wmOrderViewId;

        // 订单下单时间
        private String orderTime;

        // 订单完成时间
        private String finishTime;

        // 订单退款时间
        private String refundTime;

        // 订单当前状态
        private Integer orderState;

        // 订单配送方式
        private Integer shippingType;

        // 配送状态
        private Integer shippingStatus;

        // 结算状态
        private Integer accountState;

        // 账单日期
        private String daliyBillDate;

        // 归账日期
        private String settleBillDesc;

        // 商家应收款
        private Long settleAmount;

        // 商品总价
        private Long totalFoodAmount;

        // 商品包装盒费总价
        private Long boxAmount;

        // 商家活动总支出金额
        private Long activityPoiAmount;

        // 美团活动补贴总金额
        private Long activityMeituanAmount;

        // 代理商活动承担金额
        private Long activityAgentAmount;

        // 佣金
        private Long platformChargeFee;

        // 订单配送服务费金额
        private Long performanceServiceFee;

        // 配送服务费-基础价格
        private Long baseShippingAmount;

        // 配送服务费-导航距离
        private Long distance;

        // 配送服务费-距离收费
        private Long distanceFee;

        // 配送服务费-重量收费
        private Long weightChargeFee;

        // 配送服务费-节假日收费
        private Long holidayChargeFee;

        // 配送服务费-品类收费
        private Long categoryChargeFee;

        // 配送服务费-时段收费
        private Long slaFee;

        // 佣金2
        private Long platformChargeFee2;

        // 用户支付配送费
        private Long userPayShippingAmount;

        // 用户在线支付金额
        private Long userOnlinePayAmount;

        // 用户线下支付金额
        private Long userOfflinePayAmount;

        // 佣金的费率
        private Long rate;

        // 保底金额
        private Long bottom;

        // 退款id
        @JsonProperty("refund_id")
        private Long refundId;

        // 分成折扣
        private Integer discount;

        // 结算金额
        private Long settleMilli;

        // 结算id
        private String settleSettingId;

        // 青山计划-公益捐赠金额
        private Long wmDonationAmount;

        // 商超-打包袋金额
        private Long wmDoggyBagAmount;

        // 配送小费
        private Long dealTip;

        // 商家活动支出分摊到商品上的优惠总金额
        private Double productPreferences;

        // 商家活动支出的未分摊到商品上的总金额
        private Double notProductPreferences;

        // 商家承担成本的商品优惠分摊明细
        private List<WmAppOrderSkuBenefitDetail> wmAppOrderSkuBenefitDetailList;

        // 商家承担配送费活动分摊明细
        private List<WmAppOrderSkuShippingDetail> wmAppOrderSkuShippingDetailList;
        // 用来标识退款是部分退还是退差价
        private Integer chargeFeeType;

        // 医保报销费用字段
        private String medicalInsuranceFee;

        // 无门槛订单的返利字段
        private Long agreementCommissionRebateAmount;

        // 分单信息
        private TransferInfo transferInfo;

        public static class WmAppOrderSkuBenefitDetail {
            // 商品id，即商家中台系统里商品的编码（spu_code值）
            @JsonProperty("app_food_code")
            private String appFoodCode;

            // 商品id，即商家中台系统里商品的编码（spu_code值）
            @JsonProperty("app_medicine_code")
            private String appMedicineCode;

            // 商品名称
            private String name;

            // 商品sku的规格编码，SKU码/货号。
            @JsonProperty("sku_id")
            private String skuId;

            // 商品数量
            // 注：当字段count=0时，此账单记录为商家发起的按重量退差价，但结算类型仍为“26-闪购品类订单部分退款”（billChargeType=26）。
            private Integer count;

            // 商品原价总价（含商品包装盒费），单位分。
            private Double totalOriginPrice;

            // 商品实付价总价（含商品包装盒费），单位分。
            private Double totalActivityPrice;

            // 商品优惠总金额，包括商家承担金额和美团承担金额，单位分。
            private Double totalReducePrice;

            // 商品优惠商家承担总金额，单位分。
            private Double totalPoiCharge;

            // 商品参与活动详情，json格式数组。
            private List<WmAppOrderActDetail> wmAppOrderActDetails;

            // 技术服务费，单位分
            private Long commission;

            // 技术服务费费率,表示5%,非医药2.2 该字段值为0
            private Double commissionRate;

            // 赠品原价，单位分
            private Long giftOriginPrice;

            // 配送费原价，单位分
            private Long shippingOriginPrice;

            // 推广服务费，单位分
            private Long totalPromotionServiceFee;

            public String getAppFoodCode() {
                return appFoodCode;
            }

            public void setAppFoodCode(String appFoodCode) {
                this.appFoodCode = appFoodCode;
            }

            public String getAppMedicineCode() {
                return appMedicineCode;
            }

            public void setAppMedicineCode(String appMedicineCode) {
                this.appMedicineCode = appMedicineCode;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getSkuId() {
                return skuId;
            }

            public void setSkuId(String skuId) {
                this.skuId = skuId;
            }

            public Integer getCount() {
                return count;
            }

            public void setCount(Integer count) {
                this.count = count;
            }

            public Double getTotalOriginPrice() {
                return totalOriginPrice;
            }

            public void setTotalOriginPrice(Double totalOriginPrice) {
                this.totalOriginPrice = totalOriginPrice;
            }

            public Double getTotalActivityPrice() {
                return totalActivityPrice;
            }

            public void setTotalActivityPrice(Double totalActivityPrice) {
                this.totalActivityPrice = totalActivityPrice;
            }

            public Double getTotalReducePrice() {
                return totalReducePrice;
            }

            public void setTotalReducePrice(Double totalReducePrice) {
                this.totalReducePrice = totalReducePrice;
            }

            public Double getTotalPoiCharge() {
                return totalPoiCharge;
            }

            public void setTotalPoiCharge(Double totalPoiCharge) {
                this.totalPoiCharge = totalPoiCharge;
            }

            public List<WmAppOrderActDetail> getWmAppOrderActDetails() {
                return wmAppOrderActDetails;
            }

            public void setWmAppOrderActDetails(List<WmAppOrderActDetail> wmAppOrderActDetails) {
                this.wmAppOrderActDetails = wmAppOrderActDetails;
            }

            public Long getCommission() {
                return commission;
            }

            public void setCommission(Long commission) {
                this.commission = commission;
            }

            public Double getCommissionRate() {
                return commissionRate;
            }

            public void setCommissionRate(Double commissionRate) {
                this.commissionRate = commissionRate;
            }

            public Long getGiftOriginPrice() {
                return giftOriginPrice;
            }

            public void setGiftOriginPrice(Long giftOriginPrice) {
                this.giftOriginPrice = giftOriginPrice;
            }

            public Long getShippingOriginPrice() {
                return shippingOriginPrice;
            }

            public void setShippingOriginPrice(Long shippingOriginPrice) {
                this.shippingOriginPrice = shippingOriginPrice;
            }

            public Long getTotalPromotionServiceFee() {
                return totalPromotionServiceFee;
            }

            public void setTotalPromotionServiceFee(Long totalPromotionServiceFee) {
                this.totalPromotionServiceFee = totalPromotionServiceFee;
            }

        }

        public static class WmAppOrderSkuShippingDetail {
            // 商品名称。
            private String name;

            // 商品sku的规格编码，SKU码/货号。
            @JsonProperty("sku_id")
            private String skuId;

            // 商品数量。
            private Integer count;

            // 商品原价总价（含商品包装盒费），单位分。
            private Double totalOriginPrice;

            // 配送费优惠商家承担总金额，单位分。
            private Double totalPoiCharge;

            // sku商品参与的配送费活动详情。
            private List<WmAppOrderActDetail> wmAppOrderActDetails;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getSkuId() {
                return skuId;
            }

            public void setSkuId(String skuId) {
                this.skuId = skuId;
            }

            public Integer getCount() {
                return count;
            }

            public void setCount(Integer count) {
                this.count = count;
            }

            public Double getTotalOriginPrice() {
                return totalOriginPrice;
            }

            public void setTotalOriginPrice(Double totalOriginPrice) {
                this.totalOriginPrice = totalOriginPrice;
            }

            public Double getTotalPoiCharge() {
                return totalPoiCharge;
            }

            public void setTotalPoiCharge(Double totalPoiCharge) {
                this.totalPoiCharge = totalPoiCharge;
            }

            public List<WmAppOrderActDetail> getWmAppOrderActDetails() {
                return wmAppOrderActDetails;
            }

            public void setWmAppOrderActDetails(List<WmAppOrderActDetail> wmAppOrderActDetails) {
                this.wmAppOrderActDetails = wmAppOrderActDetails;
            }

        }

        // Getters and Setters
        public static class WmAppOrderActDetail {
            // 商品参与活动的活动id
            @JsonProperty("act_id")
            private Long actId;

            // 参与配送费活动的活动类型
            private Integer type;

            // 商家承担金额，单位分。
            private Long poiCharge;

            public Long getActId() {
                return actId;
            }

            public void setActId(Long actId) {
                this.actId = actId;
            }

            public Integer getType() {
                return type;
            }

            public void setType(Integer type) {
                this.type = type;
            }

            public Long getPoiCharge() {
                return poiCharge;
            }

            public void setPoiCharge(Long poiCharge) {
                this.poiCharge = poiCharge;
            }
        }

        public static class TransferInfo {
            // 是否分单订单
            @JsonProperty("transfer_flag")
            private Integer transferFlag;

            // 分单门店及应用信息列表，非一门店多应用则列表中只有一条数据
            @JsonProperty("transfer_poi_list")
            private List<TransfePoi> transferPoiList;

            public static class TransfePoi {
                // 分单门店appId
                @JsonProperty("transfer_app_id")
                private Long transferAppId;

                // 分单后的APP方门店id，即商家中台系统里门店的编码。
                @JsonProperty("transfer_app_poi_code")
                private String transferAppPoiCode;

                public Long getTransferAppId() {
                    return transferAppId;
                }

                public void setTransferAppId(Long transferAppId) {
                    this.transferAppId = transferAppId;
                }

                public String getTransferAppPoiCode() {
                    return transferAppPoiCode;
                }

                public void setTransferAppPoiCode(String transferAppPoiCode) {
                    this.transferAppPoiCode = transferAppPoiCode;
                }
            }

            public Integer getTransferFlag() {
                return transferFlag;
            }

            public void setTransferFlag(Integer transferFlag) {
                this.transferFlag = transferFlag;
            }

            public List<TransfePoi> getTransferPoiList() {
                return transferPoiList;
            }

            public void setTransferPoiList(List<TransfePoi> transferPoiList) {
                this.transferPoiList = transferPoiList;
            }
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getWmPoiName() {
            return wmPoiName;
        }

        public void setWmPoiName(String wmPoiName) {
            this.wmPoiName = wmPoiName;
        }

        public Integer getBillChargeType() {
            return billChargeType;
        }

        public void setBillChargeType(Integer billChargeType) {
            this.billChargeType = billChargeType;
        }

        public String getChargeFeeDesc() {
            return chargeFeeDesc;
        }

        public void setChargeFeeDesc(String chargeFeeDesc) {
            this.chargeFeeDesc = chargeFeeDesc;
        }

        public Integer getUserPayType() {
            return userPayType;
        }

        public void setUserPayType(Integer userPayType) {
            this.userPayType = userPayType;
        }

        public String getWmPoiOrderPushDayseq() {
            return wmPoiOrderPushDayseq;
        }

        public void setWmPoiOrderPushDayseq(String wmPoiOrderPushDayseq) {
            this.wmPoiOrderPushDayseq = wmPoiOrderPushDayseq;
        }

        public String getWmOrderViewId() {
            return wmOrderViewId;
        }

        public void setWmOrderViewId(String wmOrderViewId) {
            this.wmOrderViewId = wmOrderViewId;
        }

        public String getOrderTime() {
            return orderTime;
        }

        public void setOrderTime(String orderTime) {
            this.orderTime = orderTime;
        }

        public String getFinishTime() {
            return finishTime;
        }

        public void setFinishTime(String finishTime) {
            this.finishTime = finishTime;
        }

        public String getRefundTime() {
            return refundTime;
        }

        public void setRefundTime(String refundTime) {
            this.refundTime = refundTime;
        }

        public Integer getOrderState() {
            return orderState;
        }

        public void setOrderState(Integer orderState) {
            this.orderState = orderState;
        }

        public Integer getShippingType() {
            return shippingType;
        }

        public void setShippingType(Integer shippingType) {
            this.shippingType = shippingType;
        }

        public Integer getShippingStatus() {
            return shippingStatus;
        }

        public void setShippingStatus(Integer shippingStatus) {
            this.shippingStatus = shippingStatus;
        }

        public Integer getAccountState() {
            return accountState;
        }

        public void setAccountState(Integer accountState) {
            this.accountState = accountState;
        }

        public String getDaliyBillDate() {
            return daliyBillDate;
        }

        public void setDaliyBillDate(String daliyBillDate) {
            this.daliyBillDate = daliyBillDate;
        }

        public String getSettleBillDesc() {
            return settleBillDesc;
        }

        public void setSettleBillDesc(String settleBillDesc) {
            this.settleBillDesc = settleBillDesc;
        }

        public Long getSettleAmount() {
            return settleAmount;
        }

        public void setSettleAmount(Long settleAmount) {
            this.settleAmount = settleAmount;
        }

        public Long getTotalFoodAmount() {
            return totalFoodAmount;
        }

        public void setTotalFoodAmount(Long totalFoodAmount) {
            this.totalFoodAmount = totalFoodAmount;
        }

        public Long getBoxAmount() {
            return boxAmount;
        }

        public void setBoxAmount(Long boxAmount) {
            this.boxAmount = boxAmount;
        }

        public Long getActivityPoiAmount() {
            return activityPoiAmount;
        }

        public void setActivityPoiAmount(Long activityPoiAmount) {
            this.activityPoiAmount = activityPoiAmount;
        }

        public Long getActivityMeituanAmount() {
            return activityMeituanAmount;
        }

        public void setActivityMeituanAmount(Long activityMeituanAmount) {
            this.activityMeituanAmount = activityMeituanAmount;
        }

        public Long getActivityAgentAmount() {
            return activityAgentAmount;
        }

        public void setActivityAgentAmount(Long activityAgentAmount) {
            this.activityAgentAmount = activityAgentAmount;
        }

        public Long getPlatformChargeFee() {
            return platformChargeFee;
        }

        public void setPlatformChargeFee(Long platformChargeFee) {
            this.platformChargeFee = platformChargeFee;
        }

        public Long getPerformanceServiceFee() {
            return performanceServiceFee;
        }

        public void setPerformanceServiceFee(Long performanceServiceFee) {
            this.performanceServiceFee = performanceServiceFee;
        }

        public Long getBaseShippingAmount() {
            return baseShippingAmount;
        }

        public void setBaseShippingAmount(Long baseShippingAmount) {
            this.baseShippingAmount = baseShippingAmount;
        }

        public Long getDistance() {
            return distance;
        }

        public void setDistance(Long distance) {
            this.distance = distance;
        }

        public Long getDistanceFee() {
            return distanceFee;
        }

        public void setDistanceFee(Long distanceFee) {
            this.distanceFee = distanceFee;
        }

        public Long getWeightChargeFee() {
            return weightChargeFee;
        }

        public void setWeightChargeFee(Long weightChargeFee) {
            this.weightChargeFee = weightChargeFee;
        }

        public Long getHolidayChargeFee() {
            return holidayChargeFee;
        }

        public void setHolidayChargeFee(Long holidayChargeFee) {
            this.holidayChargeFee = holidayChargeFee;
        }

        public Long getCategoryChargeFee() {
            return categoryChargeFee;
        }

        public void setCategoryChargeFee(Long categoryChargeFee) {
            this.categoryChargeFee = categoryChargeFee;
        }

        public Long getSlaFee() {
            return slaFee;
        }

        public void setSlaFee(Long slaFee) {
            this.slaFee = slaFee;
        }

        public Long getPlatformChargeFee2() {
            return platformChargeFee2;
        }

        public void setPlatformChargeFee2(Long platformChargeFee2) {
            this.platformChargeFee2 = platformChargeFee2;
        }

        public Long getUserPayShippingAmount() {
            return userPayShippingAmount;
        }

        public void setUserPayShippingAmount(Long userPayShippingAmount) {
            this.userPayShippingAmount = userPayShippingAmount;
        }

        public Long getUserOnlinePayAmount() {
            return userOnlinePayAmount;
        }

        public void setUserOnlinePayAmount(Long userOnlinePayAmount) {
            this.userOnlinePayAmount = userOnlinePayAmount;
        }

        public Long getUserOfflinePayAmount() {
            return userOfflinePayAmount;
        }

        public void setUserOfflinePayAmount(Long userOfflinePayAmount) {
            this.userOfflinePayAmount = userOfflinePayAmount;
        }

        public Long getRate() {
            return rate;
        }

        public void setRate(Long rate) {
            this.rate = rate;
        }

        public Long getBottom() {
            return bottom;
        }

        public void setBottom(Long bottom) {
            this.bottom = bottom;
        }

        public Long getRefundId() {
            return refundId;
        }

        public void setRefundId(Long refundId) {
            this.refundId = refundId;
        }

        public Integer getDiscount() {
            return discount;
        }

        public void setDiscount(Integer discount) {
            this.discount = discount;
        }

        public Long getSettleMilli() {
            return settleMilli;
        }

        public void setSettleMilli(Long settleMilli) {
            this.settleMilli = settleMilli;
        }

        public String getSettleSettingId() {
            return settleSettingId;
        }

        public void setSettleSettingId(String settleSettingId) {
            this.settleSettingId = settleSettingId;
        }

        public Long getWmDonationAmount() {
            return wmDonationAmount;
        }

        public void setWmDonationAmount(Long wmDonationAmount) {
            this.wmDonationAmount = wmDonationAmount;
        }

        public Long getWmDoggyBagAmount() {
            return wmDoggyBagAmount;
        }

        public void setWmDoggyBagAmount(Long wmDoggyBagAmount) {
            this.wmDoggyBagAmount = wmDoggyBagAmount;
        }

        public Long getDealTip() {
            return dealTip;
        }

        public void setDealTip(Long dealTip) {
            this.dealTip = dealTip;
        }

        public Double getProductPreferences() {
            return productPreferences;
        }

        public void setProductPreferences(Double productPreferences) {
            this.productPreferences = productPreferences;
        }

        public Double getNotProductPreferences() {
            return notProductPreferences;
        }

        public void setNotProductPreferences(Double notProductPreferences) {
            this.notProductPreferences = notProductPreferences;
        }

        public List<WmAppOrderSkuBenefitDetail> getWmAppOrderSkuBenefitDetailList() {
            return wmAppOrderSkuBenefitDetailList;
        }

        public void setWmAppOrderSkuBenefitDetailList(List<WmAppOrderSkuBenefitDetail> wmAppOrderSkuBenefitDetailList) {
            this.wmAppOrderSkuBenefitDetailList = wmAppOrderSkuBenefitDetailList;
        }

        public List<WmAppOrderSkuShippingDetail> getWmAppOrderSkuShippingDetailList() {
            return wmAppOrderSkuShippingDetailList;
        }

        public void setWmAppOrderSkuShippingDetailList(
                List<WmAppOrderSkuShippingDetail> wmAppOrderSkuShippingDetailList) {
            this.wmAppOrderSkuShippingDetailList = wmAppOrderSkuShippingDetailList;
        }

        public Integer getChargeFeeType() {
            return chargeFeeType;
        }

        public void setChargeFeeType(Integer chargeFeeType) {
            this.chargeFeeType = chargeFeeType;
        }

        public String getMedicalInsuranceFee() {
            return medicalInsuranceFee;
        }

        public void setMedicalInsuranceFee(String medicalInsuranceFee) {
            this.medicalInsuranceFee = medicalInsuranceFee;
        }

        public Long getAgreementCommissionRebateAmount() {
            return agreementCommissionRebateAmount;
        }

        public void setAgreementCommissionRebateAmount(Long agreementCommissionRebateAmount) {
            this.agreementCommissionRebateAmount = agreementCommissionRebateAmount;
        }

        public TransferInfo getTransferInfo() {
            return transferInfo;
        }

        public void setTransferInfo(TransferInfo transferInfo) {
            this.transferInfo = transferInfo;
        }
    }

    public ExtraInfo getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(ExtraInfo extraInfo) {
        this.extraInfo = extraInfo;
    }

    public List<Bill> getData() {
        return data;
    }

    public void setData(List<Bill> data) {
        this.data = data;
    }
}
