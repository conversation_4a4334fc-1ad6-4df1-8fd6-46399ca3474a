package com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryAccountInfoResponse extends VfinanceBaseResponse {

    /**
     * account_list : [{"account_code":"****************","account_name":"上海电饱饱科技有限公司(品牌商)","account_type":"1","available_balance":"0.00","maintenance_date":"********","tran_net_member_code":"20010010032000002402130000147852","withdraw_balance":"0.00"}]
     * end_flag : 1
     * result_num : 1
     * start_record_no : 1
     * total_num : 1
     */

    @JSONField(name = "end_flag")
    private String endFlag;
    @JSONField(name = "result_num")
    private String resultNum;
    @JSONField(name = "start_record_no")
    private String startRecordNo;
    @JSONField(name = "total_num")
    private String totalNum;
    @JSONField(name = "account_list")
    private List<AccountListBean> accountList;

    @Data
    public static class AccountListBean {
        /**
         * account_code : ****************
         * account_name : 上海电饱饱科技有限公司(品牌商)
         * account_type : 1
         * available_balance : 0.00
         * maintenance_date : ********
         * tran_net_member_code : 20010010032000002402130000147852
         * withdraw_balance : 0.00
         */

        @JSONField(name = "account_code")
        private String accountCode;
        @JSONField(name = "account_name")
        private String accountName;
        @JSONField(name = "account_type")
        private String accountType;
        @JSONField(name = "available_balance")
        private String availableBalance;
        @JSONField(name = "maintenance_date")
        private String maintenanceDate;
        @JSONField(name = "tran_net_member_code")
        private String tranNetMemberCode;
        @JSONField(name = "withdraw_balance")
        private String withdrawBalance;
    }
}
