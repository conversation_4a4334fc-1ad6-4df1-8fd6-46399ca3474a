package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.PooledMerchantAnalyzeModule;

import java.util.List;

/**
 * 归集商户解析
 */
public interface PooledMerchantAnalyzeService {

    List<PooledMerchantAnalyzeModule> analyzeData(List<String[]> fields);

    List<BrandMerchantModule> getBrandMerchantModuleList(String brandId, List<PooledMerchantAnalyzeModule> merchantAnalyzeModules);

    void createErrorMsgIntoExcel(String filePath);

    void checkParams(List<PooledMerchantAnalyzeModule> merchantAnalyzeModules);
}
