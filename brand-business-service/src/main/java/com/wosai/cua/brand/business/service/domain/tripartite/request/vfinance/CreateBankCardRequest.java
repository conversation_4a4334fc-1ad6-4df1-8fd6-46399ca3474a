package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CreateBankCardRequest extends VfinanceBaseRequest{
    /**
     * 会员Id
     * 如果identity_type是MEMBER_ID就填写会员id
     * 不可空
     * 例：************
     * 注册时返回的会员id
     */
    @JSONField(name = "member_id")
    private String memberId;
    /**
     * 卡类型
     * DEBIT(借记卡)
     * 不可空
     * 例：DEBIT
     */
    @JSONField(name = "card_type")
    private String cardType;
    /**
     * 卡属性
     * C(对私),B(对公)
     * 不可空
     * 企业：B个体工商户：C
     */
    @JSONField(name = "card_attribute")
    private String cardAttribute;
    /**
     * 银行编码
     * 不可空
     * 例：ICBC
     */
    @JSONField(name = "bank_code")
    private String bankCode;
    /**
     * 银行名称
     * 不可空
     * 中国工商银行
     * 若大小额行号不填则送超级网银号对应的银行名称，若填大小额行号则送大小额行号对应的银行名称
     */
    @JSONField(name = "bank_name")
    private String bankName;
    /**
     * 支行名称
     * 可空	工商银行xxx支行
     */
    @JSONField(name = "bank_branch")
    private String bankBranch;
    /**
     * 账户号
     * 银行卡号
     * 不可空
     * 企业：公司银行账号个体工商户：个人账号。
     */
    @JSONField(name = "bank_account_no")
    private String bankAccountNo;
    /**
     * 账户名
     * 银行账户名
     * 不可空
     * 企业：公司名称个体工商户：法定代表人名称。
     */
    @JSONField(name = "account_name")
    private String accountName;
    /**
     * 省份
     * 不可空
     * 江苏省，默认
     */
    private String province;
    /**
     * 城市
     * 不可空
     * 苏州市，默认
     */
    private String city;
    /**
     * 手机号
     * 此为接收银行短信验证码的手机号
     * 不可空
     * 都填，没有经办人，需要这个手机号进行验证。
     * 申请往账鉴权有上送经办人的，指令号会发到经办人手机号，未输入经办人的发到此手机号
     */
    private String mobile;
    /**
     * 超级网银号
     * 大小额行号和超级网银行号两者二选一必填。
     * 可空
     * 选择超级网银号	银行类型送1时，大小额行号和超级网银号都不用送
     */
    @JSONField(name = "super_bank_no")
    private String superBankNo;
    /**
     * 大小额行号
     * 大小额行号和超级网银行号两者二选一必填。
     * 可空	************这个不填
     */
    @JSONField(name = "branch_no")
    private String branchNo;
    /**
     * 校验方式
     * 1:往账鉴权,3:银联鉴权(短信)
     * 不可空
     * 企业：1个体工商户：3
     */
    @JSONField(name = "check_mode")
    private String checkMode;
    /**
     * 是否是默认卡
     * 1:是 2：否
     * 不可空
     * 如果是1，那么强制提现就从默认卡进行提现，默认卡不能有2个。全部默认为 1
     */
    @JSONField(name = "is_default")
    private String isDefault;
    /**
     * 是否个体工商户
     * 1:是 2：否
     * 不可空
     * 企业：2个人：2个体工商户：1
     */
    @JSONField(name = "indiv_business_flag")
    private String indicBusinessFlag;
    /**
     * 会员名称
     * 个人或个体工商户，此处填写客户真实姓名企业填写企业名称
     * 不可空
     * 企业：公司名称个体工商户：法定代表人名称。
     * 1、个体工商户此信息默认为法人信息, 是否是法人字段上送2否时，该信息是代办人信息2、1为纯个人或者个体工商户客户,3、5为纯个人，证件类型73、52、68为企业客户
     */
    @JSONField(name = "member_name")
    private String memberName;
    /**
     * 会员证件类型
     * 参考接口证件类型说明	不可空
     * 企业：73 个体工商户：1
     */
    @JSONField(name = "member_globla_type")
    private String memberGlobalType;
    /**
     * 会员证件号码
     * 个人或个体工商户，此处填写身份证号码企业填写统一社会信用代码证号	不可空
     * 企业：统一社会信用代码个体工商户：身份证号
     */
    @JSONField(name = "member_globle_id")
    private String memberGlobalId;
    /**
     * 银行类型
     * 1-本行 2-他行	不可空
     * 平安银行：1 其他银行：2
     */
    @JSONField(name = "bank_type")
    private String bankType;
    /**
     * 会员名称是否是法人
     * 1-是 2-否（个体工商户和企业必输，纯个人无需填写）
     * 可空	企业：2个体工商户：1
     */
    @JSONField(name = "rep_flag")
    private String repFlag;
    /**
     * 是否存在经办人
     * 1：是 2：否（企业必输，个纯个人与个体工商户无需填写）
     * 可空	企业：1个体工商户：2
     */
    @JSONField(name = "agency_client_flag")
    private String agencyClientFlag;
    /**
     * 经办人姓名
     * 是否存在经办人agency_client_flag=1时必输	可空	企业：经办人姓名 个体工商户：可空	企业客户：经办人标志（agency_client_flag）选是时必输
     */
    @JSONField(name = "agent_name")
    private String agentName;
    /**
     * 经办人证件类型
     * 是否存在经办人agency_client_flag=1时必输，仅支持1-身份证
     * 企业：1个体工商户：可空
     */
    @JSONField(name = "agency_global_type")
    private String agencyGlobalType;
    /**
     * 经办人证件号
     * String(32)
     * 是否存在经办人agency_client_flag=1时必输
     * 企业：经办人身份证号个体工商户：可空
     */
    @JSONField(name = "agent_id_code")
    private String agentIdCode;
    /**
     * 经办人手机号
     * 是否存在经办人agency_client_flag=1时必输，短信指令号优先发送至此手机号
     * 企业：经办人手机号。个体工商户：可空
     */
    @JSONField(name = "agent_phone")
    private String agentPhone;
    /**
     * 法人名称
     * 会员名称是否是法人rep_flag为2时必输
     * 可空
     * 张三	1、企业客户必输2、个体工商户：会员名称、会员证件号码等信息不是法人信息时，且会员名称是否是法人RepFlag字段上送2否时，需补充法人信息
     */
    @JSONField(name = "legal_name")
    private String legalName;
    /**
     * 法人证件类型
     * 会员名称是否是法人rep_flag为2时必输、企业必输,
     * 仅支持1-身份证；3-港澳回乡证；5-台胞证；19-外国护照
     * 可空
     * 企业：1
     * 个体工商户：1
     */
    @JSONField(name = "repr_global_type")
    private String reprGlobalType;

    @JSONField(name = "repr_global_id")
    private String reprGlobalId;
    /**
     * 法人证件号码
     * 会员名称是否是法人rep_flag为2时必输、企业必输
     * 可空
     * 例：430***************
     */
    @JSONField(name = "lecerti_code")
    private String erectileCode;
    /**
     * 公司名称
     * 个体工商户必输
     * 可空
     * 例：xxxx有限责任公司
     */
    @JSONField(name = "company_name")
    private String companyName;
    /**
     * 公司证件类型
     * 个体工商户必输，参考接口证件类型说明
     * 例：BL
     */
    @JSONField(name = "credit_type")
    private String creditType;
    /**
     * 公司证件号
     * 个体工商户必输，公司证件号码
     * 例：***************
     */
    @JSONField(name = "credit_code")
    private String creditCode;
    /**
     * 店铺名称
     * 个体工商户、企业必输
     * 例：xxxx店铺
     */
    @JSONField(name = "shop_name")
    private String shopName;

    public CreateBankCardRequest(String partnerId) {
        super();
        super.service = CREATE_BANK_CARD;
        super.partnerId = partnerId;
    }
}
