package com.wosai.cua.brand.business.service.controller.rest;

import com.wosai.cua.brand.business.api.dto.request.merchant.ModifyBrandMerchantOpenStatusDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.sharing.proxy.model.notify.AggregationOpenNotifyModel;
import com.wosai.upay.common.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@RestController
@RequestMapping("/internal/callback")
@Slf4j
public class InternalCallbackNotifyController {

    private final BrandBusiness brandBusiness;

    @Autowired
    public InternalCallbackNotifyController(BrandBusiness brandBusiness) {
        this.brandBusiness = brandBusiness;
    }

    @PostMapping(value = "/aggregationOpenNotify")
    public String aggregationOpenNotify(@RequestBody AggregationOpenNotifyModel aggregationOpenNotifyModel) {
        log.info("aggregationOpenNotify request:{}", JacksonUtil.toJsonString(aggregationOpenNotifyModel));
        BrandDetailInfoDTO brandDetailInfoDTO = brandBusiness.getBrandDetailInfoByAdminMerchantSn(aggregationOpenNotifyModel.getAggregationMerchantSn(), false);
        if (Objects.isNull(brandDetailInfoDTO)) {
            log.warn("未查到品牌信息。");
            return "fail";
        }
        String accountOpenStatus = null;
        if (Objects.isNull(aggregationOpenNotifyModel.getStatus())){
            accountOpenStatus = "";
        }
        if (aggregationOpenNotifyModel.getStatus() == 1){
            accountOpenStatus = BrandMerchantAccountOpenStatusEnum.OPENED.getStatus();
        }
        if (aggregationOpenNotifyModel.getStatus() == 0){
            accountOpenStatus = BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus();
        }
        brandBusiness.updateMerchantOpenStatus(
                ModifyBrandMerchantOpenStatusDTO.builder()
                        .brandId(brandDetailInfoDTO.getBrandId())
                        .merchantSn(aggregationOpenNotifyModel.getMerchantSn())
                        .accountOpenStatus(accountOpenStatus)
                        .openFailReason(aggregationOpenNotifyModel.getErrorMsg())
                        .build()
        );
        return "success";
    }
}
