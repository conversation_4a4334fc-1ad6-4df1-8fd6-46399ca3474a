package com.wosai.cua.brand.business.service.enums.third;

import lombok.Getter;

@Getter
public enum FuiouOpenAccountStatusEnum {
    UNSIGNED("00", "协议未签署"),
    LOSE_EFFECTIVENESS("01", "失效（24小时失效或者3次签约失效）"),
    SUCCESS("02", "生效"),
    FORBIDDEN("03", "禁用"),
    FROZEN("05","冻结"),
    WAIT_OPEN_BANK_ACCOUNT("06","待银行开户"),
    SIGN_OUT("07","注销"),
    OPEN_BANK_ACCOUNT_FAIL("08","银行开户失败"),
    SIGNING_OUT("09","注销中")
    ;
    private final String code;

    private final String desc;

    FuiouOpenAccountStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
