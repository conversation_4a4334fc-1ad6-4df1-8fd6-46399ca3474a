package com.wosai.cua.brand.business.service.domain.tripartite.citic.helper;

import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.service.domain.tripartite.citic.annotations.NoNeedForSignature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> Date: 2024/08/08 Time: 3:53 PM
 * 中信银行XML处理工具类
 */
@Slf4j
public class CiticXmlHelper {

    private CiticXmlHelper() {
        throw new IllegalStateException("Utility class");
    }

    public static String toXml(Object obj) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(obj.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
        StringWriter sw = new StringWriter();
        marshaller.marshal(obj, sw);
        return sw.toString();
    }

    public static <T> T fromXml(String xml, Class<T> clazz) throws JAXBException {
        if (StringUtils.isEmpty(xml)) {
            return null;
        }
        JAXBContext context = JAXBContext.newInstance(clazz);
        Unmarshaller unmarshaller = context.createUnmarshaller();

        unmarshaller.setEventHandler(event -> true);
        Object result = unmarshaller.unmarshal(new StringReader(xml));

        // Ensure the result is of the correct type
        if (clazz.isInstance(result)) {
            return clazz.cast(result);
        } else {
            throw new JAXBException("Unmarshalled object is not of type " + clazz.getName());
        }
    }

    public static <T> String sortSignInfo(String xml, Class<T> clazz) {
        List<String> signList = Lists.newArrayList();
        List<String> noNeedSignList = Lists.newArrayList("SIGN_INFO");
        // 取出无需验签字段的name
        NoNeedForSignature annotation = clazz.getAnnotation(NoNeedForSignature.class);
        if (annotation != null) {
            Collections.addAll(noNeedSignList, annotation.name());
        }
        try {
            // 解析XML字符串
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(xml.getBytes()));

            // 获取根元素下的DATA元素
            NodeList dataElements = doc.getElementsByTagName("DATA");
            if (dataElements.getLength() > 0) {
                Element dataElement = (Element) dataElements.item(0);

                // 遍历DATA元素的子元素
                NodeList childNodes = dataElement.getChildNodes();
                for (int i = 0; i < childNodes.getLength(); i++) {
                    Node node = childNodes.item(i);
                    if (node.getNodeType() == Node.ELEMENT_NODE) {
                        Element element = (Element) node;
                        if (!noNeedSignList.contains(element.getTagName())) {
                            String tagContent = element.getTextContent();
                            signList.add(tagContent);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 排序
        Collections.sort(signList);

        // 拼接排序后的字符串
        StringBuilder signInfo = new StringBuilder();
        for (String sign : signList) {
            signInfo.append(sign);
        }

        // 若验证银行侧响应报文签名未通过，可与银行侧对比该串内容是否一致
        log.info("响应字段排序串：{}", signInfo);
        return signInfo.toString();
    }

    public static List<String> sortSignInfo(Document doc) {
        List<String> signList = new ArrayList<>();
        NodeList nodeList = doc.getDocumentElement().getElementsByTagName("*");

        for (int i = 0; i < nodeList.getLength(); i++) {
            Element element = (Element) nodeList.item(i);
            if ("SIGN_INFO".equals(element.getTagName())) {
                continue;
            }
            signList.add(element.getTextContent());
        }

        Collections.sort(signList);

        StringBuilder signInfo = new StringBuilder();
        for (String sign : signList) {
            signInfo.append(sign);
        }

        log.info("请求字段排序串：{}", signInfo);
        return signList;
    }

    public static Document parseXml(String xml) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        return builder.parse(new ByteArrayInputStream(xml.getBytes()));
    }
}
