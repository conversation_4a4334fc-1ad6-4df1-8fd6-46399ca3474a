package com.wosai.cua.brand.business.service.domain.tripartite.response.feishu;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class BaseFeiShuMessageResponse<T> {

    /**
     * StatusCode : 0
     * StatusMessage : success
     * code : 0
     * data : {}
     * msg : success
     */
    @JSONField(name = "StatusCode")
    private int statusCode;
    @JSONField(name = "StatusMessage")
    private String statusMessage;
    private int code;
    private T data;
    private String msg;
}
