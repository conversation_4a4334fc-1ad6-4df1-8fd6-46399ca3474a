package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BusinessLicenseEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.feign.FeignConfig;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.CreateMemberRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.VfinanceBaseRequest;
import com.wosai.cua.brand.business.service.enums.third.MemberGlobalTypeEnum;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 维金相关接口调用
 *
 * <AUTHOR>
 */
@FeignClient(url = "${vygin-service-address}", name = "vfinance-service",configuration = FeignConfig.class)
public interface VfinanceInterfaceService {
    /**
     * 调用维金接口
     *
     * @param request 请求
     * @return 接口返回
     */
    @PostMapping(consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String invokeService(VfinanceBaseRequest request);

    /**
     * 获取创建会员接口的对象的请求
     * @param merchantBusinessLicense 商户证件信息
     * @param merchant 商户信息
     * @return 获取创建会员接口的对象
     */
    static CreateMemberRequest getCreateMemberRequest(MerchantBusinessLicenseInfo merchantBusinessLicense, MerchantInfo merchant, PabConfigModule pabConfigModule) {
        List<Integer> businessLicenseTypeList = Arrays.stream(BusinessLicenseEnum.values()).map(BusinessLicenseEnum::getType).collect(Collectors.toList());
        if (!businessLicenseTypeList.contains(merchantBusinessLicense.getType())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_BUSINESS_LICENSE);
        }
        // 创建品牌子账号（同步）
        CreateMemberRequest createMemberRequest = new CreateMemberRequest(pabConfigModule.getPartnerId());
        createMemberRequest.setMemberName(merchant.getBusiness_name());
        createMemberRequest.setMobile(merchant.getContact_cellphone());
        createMemberRequest.setEmail(merchant.getContact_email());
        if (BusinessLicenseEnum.MICRO_MERCHANT.getType().equals(merchantBusinessLicense.getType())) {
            createMemberRequest.setUid(merchant.getSn());
            createMemberRequest.setRealName(merchantBusinessLicense.getLegal_person_name());
            createMemberRequest.setMemberGlobalType(MemberGlobalTypeEnum.getTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
            createMemberRequest.setMemberGlobalId(merchantBusinessLicense.getLegal_person_id_number());
        }
        if (BusinessLicenseEnum.INDIVIDUAL_BUSINESS_MERCHANT.getType().equals(merchantBusinessLicense.getType())) {
            createMemberRequest.setUid(merchant.getSn());
            createMemberRequest.setRealName(merchant.getName());
            createMemberRequest.setMemberGlobalType(MemberGlobalTypeEnum.MEMBER_GLOBAL_TYPE_COMPANY.getType());
            createMemberRequest.setMemberGlobalId(merchantBusinessLicense.getNumber());
            createMemberRequest.setIndivBusinessFlag("1");
            createMemberRequest.setCompanyName(merchant.getName());
            createMemberRequest.setCompanyGlobalType(MemberGlobalTypeEnum.MEMBER_GLOBAL_TYPE_COMPANY.getType());
            createMemberRequest.setCompanyGlobalId(merchantBusinessLicense.getNumber());
            createMemberRequest.setShopName(merchant.getName());
            createMemberRequest.setShopId(merchant.getSn());
            createMemberRequest.setRepFlag("1");
            createMemberRequest.setReprClientName(merchantBusinessLicense.getLegal_person_name());
            createMemberRequest.setReprGlobalType(MemberGlobalTypeEnum.getTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
            createMemberRequest.setReprGlobalId(merchantBusinessLicense.getLegal_person_id_number());
        }
        if (merchantBusinessLicense.getType().equals(BusinessLicenseEnum.ENTERPRISE_BUSINESS_LICENSE.getType())) {
            createMemberRequest.setUid(merchant.getSn());
            createMemberRequest.setRealName(merchant.getName());
            createMemberRequest.setMemberGlobalType(MemberGlobalTypeEnum.MEMBER_GLOBAL_TYPE_COMPANY.getType());
            createMemberRequest.setMemberGlobalId(merchantBusinessLicense.getNumber());
            createMemberRequest.setCompanyName(merchant.getName());
            createMemberRequest.setCompanyGlobalType(MemberGlobalTypeEnum.MEMBER_GLOBAL_TYPE_COMPANY.getType());
            createMemberRequest.setCompanyGlobalId(merchantBusinessLicense.getNumber());
            createMemberRequest.setShopName(merchant.getName());
            createMemberRequest.setShopId(merchant.getSn());
            createMemberRequest.setRepFlag("1");
            createMemberRequest.setReprClientName(merchantBusinessLicense.getLegal_person_name());
            createMemberRequest.setReprGlobalType(MemberGlobalTypeEnum.getTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
            createMemberRequest.setReprGlobalId(merchantBusinessLicense.getLegal_person_id_number());
        }
        return createMemberRequest;
    }
}
