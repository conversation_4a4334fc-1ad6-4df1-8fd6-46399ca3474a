package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModifyBrandMerchantResponse extends BaseOpenApiResponse{
    /**
     * 品牌编号
     */
    @JsonProperty("brand_sn")
    private String brandSn;
    /**
     * 商户编号
     */
@JsonProperty("merchant_sn")
    private String merchantSn;

    /**
     * 商户名称
     */
    @JsonProperty("merchant_name")
    private String merchantName;

    /**
     * 商户类型
     * @see MerchantTypeEnum
     */
    @JsonProperty("merchant_type")
    private String merchantType;
}
