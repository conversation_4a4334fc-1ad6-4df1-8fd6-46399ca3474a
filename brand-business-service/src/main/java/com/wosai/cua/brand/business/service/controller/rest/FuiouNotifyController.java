package com.wosai.cua.brand.business.service.controller.rest;

import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.service.domain.service.FuiouEncryptedNotifyService;
import com.wosai.cua.brand.business.service.domain.service.FuiouNotifyService;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.util.XmlConvertUtil;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.BaseNotifyBody;
import com.wosai.cua.brand.business.service.mybank.manage.CommonRequestHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/fuiou")
@Slf4j
public class FuiouNotifyController {

    private final CommonRequestHandle commonRequestHandle;

    private final List<FuiouNotifyService> fuiouNotifyServices;

    private final List<FuiouEncryptedNotifyService> fuiouEncryptedNotifyServices;

    private static final Map<String, FuiouNotifyService> NOTIFY_SERVICE_MAP = Maps.newHashMap();

    private static final Map<String, FuiouEncryptedNotifyService> ENCRYPTED_NOTIFY_SERVICE_MAP = Maps.newHashMap();

    @Autowired
    public FuiouNotifyController(CommonRequestHandle commonRequestHandle, List<FuiouNotifyService> fuiouNotifyServices, List<FuiouEncryptedNotifyService> fuiouEncryptedNotifyServices) {
        this.commonRequestHandle = commonRequestHandle;
        this.fuiouNotifyServices = fuiouNotifyServices;
        this.fuiouEncryptedNotifyServices = fuiouEncryptedNotifyServices;
    }

    @PostConstruct
    public void intNotifyServiceMap() {
        fuiouNotifyServices.forEach(fuiouNotifyService -> NOTIFY_SERVICE_MAP.put(fuiouNotifyService.getNotifyType(), fuiouNotifyService));
        fuiouEncryptedNotifyServices.forEach(fuiouEncryptedNotifyService -> ENCRYPTED_NOTIFY_SERVICE_MAP.put(fuiouEncryptedNotifyService.getNotifyType(), fuiouEncryptedNotifyService));
    }

    /**
     * 接收Fuiou的异步通知(非加密)
     *
     * @param httpServletRequest
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/notify", produces = MediaType.APPLICATION_XML_VALUE)
    public String notifyManage(HttpServletRequest httpServletRequest, @RequestParam String merchantId) throws Exception {
        //获取请求报文
        String xmlContext = commonRequestHandle.getXmlContextString(httpServletRequest);
        log.info("fuyou notify request：{}", xmlContext);
        BaseNotifyBody baseNotifyBody = XmlConvertUtil.notifyXml2Bean(xmlContext, BaseNotifyBody.class);
        if ("accountInResult".equals(baseNotifyBody.getNotifyType())) {
            FuiouEncryptedNotifyService fuiouEncryptedNotifyService = ENCRYPTED_NOTIFY_SERVICE_MAP.get(baseNotifyBody.getNotifyType());
            if (fuiouEncryptedNotifyService != null) {
                return fuiouEncryptedNotifyService.notifyHandle(baseNotifyBody.getMessage(), baseNotifyBody.getNotifyType(), merchantId);
            }
        } else {
            FuiouNotifyService fuiouNotifyService = NOTIFY_SERVICE_MAP.get(baseNotifyBody.getNotifyType());
            if (fuiouNotifyService != null) {
                return fuiouNotifyService.notifyHandle(xmlContext);
            }
        }
        return "no find service";
    }
}
