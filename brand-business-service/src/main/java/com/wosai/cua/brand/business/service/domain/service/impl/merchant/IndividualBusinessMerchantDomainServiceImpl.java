package com.wosai.cua.brand.business.service.domain.service.impl.merchant;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.request.merchant.BaseCreateMerchantDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.MerchantCenterDomainService;
import com.wosai.mc.model.req.AccountReq;
import com.wosai.mc.model.req.CreateMerchantAndStoreReq;
import com.wosai.mc.model.req.CreateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.CreateStoreReq;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
public class IndividualBusinessMerchantDomainServiceImpl implements MerchantCenterDomainService {

    @Override
    public BrandMerchantTypeEnum getBrandMerchantType() {
        return BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS;
    }

    @Override
    public CreateMerchantAndStoreReq getCreateMerchantAndStoreReq(BaseCreateMerchantDTO createMerchantDto, String ucUserId) {
        BaseCreateMerchantDTO.IndividualBusinessMerchant individualBusinessMerchant = JSON.parseObject(JSON.toJSONString(createMerchantDto), BaseCreateMerchantDTO.IndividualBusinessMerchant.class);
        return this.getMerchantComplete(individualBusinessMerchant, ucUserId);
    }

    private CreateMerchantAndStoreReq getMerchantComplete(BaseCreateMerchantDTO.IndividualBusinessMerchant individualBusinessMerchant, String ucUserId) {
        CreateMerchantAndStoreReq merchantComplete = new CreateMerchantAndStoreReq();
        // 创建商户
        CreateMerchantReq merchantReq = new CreateMerchantReq();
        merchantReq.setId(UUID.randomUUID().toString());
        merchantReq.setName(individualBusinessMerchant.getMerchantName());
        merchantReq.setBusinessName(individualBusinessMerchant.getMerchantName());
        merchantReq.setContactName(individualBusinessMerchant.getContactName());
        merchantReq.setContactCellphone(individualBusinessMerchant.getContactPhone());
        merchantReq.setContactEmail(individualBusinessMerchant.getContactEmail());
        merchantReq.setStreetAddress(individualBusinessMerchant.getAddress());
        merchantReq.setProvince(individualBusinessMerchant.getProvince());
        merchantReq.setCity(individualBusinessMerchant.getCity());
        merchantReq.setDistrict(individualBusinessMerchant.getDistrict());
        merchantComplete.setMerchant(merchantReq);
        CreateMerchantBusinessLicenseReq license = this.getCreateMerchantBusinessLicenseReq(individualBusinessMerchant, merchantReq);
        merchantComplete.setLicense(license);
        AccountReq account = this.getAccountReq(individualBusinessMerchant, ucUserId);
        merchantComplete.setAccount(account);
        CreateStoreReq store = new CreateStoreReq();
        store.setMerchantId(merchantReq.getId());
        store.setName(individualBusinessMerchant.getStoreName());
        merchantComplete.setStore(store);
        return merchantComplete;
    }

    private AccountReq getAccountReq(BaseCreateMerchantDTO.IndividualBusinessMerchant individualBusinessMerchant, String ucUserId) {
        AccountReq account = new AccountReq();
        account.setIdentifier(individualBusinessMerchant.getContactPhone());
        account.setIdentityType(1);
        account.setUcUserId(ucUserId);
        account.setName(individualBusinessMerchant.getContactName());
        account.setIdNumber(individualBusinessMerchant.getLegalPersonLicenseNumber());
        account.setIdCardFrontPhoto("https://images.wosaimg.com/c9/8fb1c032d51d7ed1539d6d54d014400ebb601c.png");
        account.setIdCardValidity("********-********");
        return account;
    }

    private CreateMerchantBusinessLicenseReq getCreateMerchantBusinessLicenseReq(BaseCreateMerchantDTO.IndividualBusinessMerchant individualBusinessMerchant, CreateMerchantReq merchantReq) {
        CreateMerchantBusinessLicenseReq license = new CreateMerchantBusinessLicenseReq();
        license.setMerchantId(merchantReq.getId());
        license.setType(individualBusinessMerchant.getLicenseType());
        license.setNumber(individualBusinessMerchant.getLicenseNumber());
        license.setLegalPersonName(individualBusinessMerchant.getLegalPersonName());
        license.setLegalPersonIdType(individualBusinessMerchant.getLegalPersonLicenseType());
        license.setLegalPersonIdNumber(individualBusinessMerchant.getLegalPersonLicenseNumber());
        return license;
    }
}
