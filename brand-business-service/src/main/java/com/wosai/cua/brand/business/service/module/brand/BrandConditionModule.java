package com.wosai.cua.brand.business.service.module.brand;

import com.wosai.cua.brand.business.service.module.PageModule;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BrandConditionModule extends PageModule {
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 品牌编号
     */
    private String brandSn;

    /**
     * 品牌账号
     */
    private String identifier;

    /**
     * 父品牌ID
     */
    private String parentId;

    /**
     * 收付通品牌标识
     */
    private Integer sftTag;

    /**
     * 启用收付通标识
     */
    private Integer enableSft;
}
