package com.wosai.cua.brand.business.service.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.service.kafka.dto.DataCenterMessageBody;
import com.wosai.cua.brand.business.service.kafka.enums.MessageTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Component
@Slf4j
@Data
public class DataCenterKafkaProducer extends ParentKafkaProducer {

    @Value("${brand.kafka.bootstrap-servers}")
    private String bootstrapServersConfig;

    @Value("${brand.kafka.data-center.topic}")
    private String topic;

    @Value("${app-id.merchant}")
    private String merchantAppId;

    @Override
    protected KafkaProducer<String, Object> getKafkaProducer() {
        return this.kafkaProducer;
    }

    @Override
    public void afterPropertiesSet() {
        log.info("DataCenterKafkaProducer kafka bootstrap-servers is {}",bootstrapServersConfig);
        properties = new Properties();
        String[] configArr = bootstrapServersConfig.split(",");
        properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, Lists.newArrayList(configArr));
        properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        kafkaProducer = new KafkaProducer<>(properties);
    }

    private void appendTimestamp(Object eventParams) {
        try {
            if (eventParams instanceof List) {
                return;
            }
            JSONObject eventParamsJsonObj = JSON.parseObject(JSON.toJSONString(eventParams));
            if (MapUtils.isEmpty(eventParamsJsonObj)) {
                return;
            }
            final String timestampFiled = "timestamp";
            if (eventParamsJsonObj.get(timestampFiled) != null) {
                return;
            }
            eventParamsJsonObj.put(timestampFiled, System.currentTimeMillis());
        } catch (Exception e) {
            log.error("appendTimestamp error. eventParams:{}", JSON.toJSONString(eventParams), e);
        }
    }

    public void publishEvent(DataCenterMessageBody body) {
        if (check(body)) return;
        body.setAppId(merchantAppId);
        body.setMessageType(MessageTypeEnum.EVENT.name());
        //追加时间戳
        appendTimestamp(body.getEventParams());
        this.sendMessage(this.topic, JSON.toJSONString(body));
    }

    private boolean check(DataCenterMessageBody body) {
        if (Objects.isNull(body)) {
            log.warn("DataCenterMessageBody is null");
            return true;
        }
        if (Objects.isNull(body.getEventParams())) {
            log.warn("当前事件eventParams为空. body:{}", JSON.toJSONString(body));
            return true;
        }
        return false;
    }
}
