package com.wosai.cua.brand.business.service.controller.dto.openapi.request.openapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.OpenApiBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OpenApiPageQueryBrandMerchantRequest extends OpenApiBaseRequest {
    /**
     * 当前页
     */
    private Integer page;

    /**
     * 每页展示条数
     */
    @JsonProperty("page_size")
    private Integer pageSize;

    /**
     * 商户类型:FRANCHISEE-加盟商、SUPPLIER-供应商、BRAND_OPERATED_STORES-品牌经营门店、BRAND_OWNER-品牌商户、AGENT-代理商
     */
    @JsonProperty("merchant_type")
    private String merchantType;

}
