package com.wosai.cua.brand.business.service.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimpleBrandMerchantOpData {
    /**
     * 商户号
     */
    @ExcelProperty("商户号")
    @NotEmpty(message = "商户号为空")
    private String merchantSn;

    @ExcelProperty("结果")
    private String result;
}
