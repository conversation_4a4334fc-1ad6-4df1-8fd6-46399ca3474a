package com.wosai.cua.brand.business.service.excel.handler;

import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.excel.AbstractImportExcelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.EnumMap;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Component
@Slf4j
public class ImportExcelHandlerFactory implements ApplicationRunner {

    private final EnumMap<ExcelImportTypeEnum, AbstractImportExcelHandler> handlerMap = Maps.newEnumMap(ExcelImportTypeEnum.class);

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public void run(ApplicationArguments args) {
        applicationContext.getBeansOfType(AbstractImportExcelHandler.class)
                .values().forEach(handler -> handlerMap.put(handler.getAuditImportType(), handler));
    }

    public AbstractImportExcelHandler getHandler(ExcelImportTypeEnum typeEnum) {
        if (!handlerMap.containsKey(typeEnum)) {
            log.error("获取内部处理器失败,taskType: {}", typeEnum);
            throw new BrandBusinessException("获取内部处理器失败");
        }
        return handlerMap.get(typeEnum);
    }
}
