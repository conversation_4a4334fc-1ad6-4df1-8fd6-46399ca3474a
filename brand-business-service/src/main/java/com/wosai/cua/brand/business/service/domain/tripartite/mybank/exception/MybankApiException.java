package com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.enums.MybankApiExceptionEnum;

public class MybankApiException extends Exception {

    private static final long serialVersionUID = -4436216427135334395L;

    private String errCode;
    private String errMsg;

    public MybankApiException() {
        super();
    }

    public MybankApiException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public MybankApiException(String message) {
        super(message);
    }

    public MybankApiException(Throwable throwable) {
        super(throwable);
    }

    public MybankApiException(String errCode, String errMsg) {
        super(errCode + ":" + errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public MybankApiException(MybankApiExceptionEnum exceptionEnum, Throwable throwable) {
        super(exceptionEnum.getErrorCode(), throwable);
    }

    public MybankApiException(MybankApiExceptionEnum exceptionEnum) {
        this(exceptionEnum.getErrorCode(), exceptionEnum.getErrorDesc());
    }

    public String getErrCode() {
        return this.errCode;
    }

    public String getErrMsg() {
        return this.errMsg;
    }
}