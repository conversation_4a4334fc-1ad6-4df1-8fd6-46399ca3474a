package com.wosai.cua.brand.business.service.event.listener;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.kafka.BrandMerchantStatusChangeMsg;
import com.wosai.cua.brand.business.api.enums.MessageTypeEnum;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.Listener;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantStatusChangeEvent;
import com.wosai.cua.brand.business.service.event.type.BrandMerchantStatusChangeEventType;
import com.wosai.cua.brand.business.service.kafka.BrandBaseKafkaProducer;
import com.wosai.cua.brand.business.service.kafka.helper.AvroBeanHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class BrandMerchantStatusChangeListener implements Listener<BrandMerchantStatusChangeEvent> {


    private final BrandBaseKafkaProducer brandBaseKafkaProducer;
    private final ThreadPoolTaskScheduler brandMerchantStatusChangePoolScheduler;

    @Autowired
    public BrandMerchantStatusChangeListener(BrandBaseKafkaProducer brandBaseKafkaProducer, @Qualifier("brandMerchantStatusChangePoolScheduler") ThreadPoolTaskScheduler brandMerchantStatusChangePoolScheduler) {
        this.brandBaseKafkaProducer = brandBaseKafkaProducer;
        this.brandMerchantStatusChangePoolScheduler = brandMerchantStatusChangePoolScheduler;
    }

    @Override
    public EventType getEventType() {
        return BrandMerchantStatusChangeEventType.STATUS_CHANGE;
    }

    @Override
    public void handle(BrandMerchantStatusChangeEvent event) {
        log.info("BrandMerchantStatusChangeListener 监听到品牌商户状态变更的事件：{}", JSON.toJSONString(event));
        // CUA-11168 延迟发送消息。为了让切换支付模式的任务完成，储值才消费到消息
        brandMerchantStatusChangePoolScheduler.getScheduledExecutor().schedule(() ->
                brandBaseKafkaProducer.syncSendMessage(
                        brandBaseKafkaProducer.getTopic(),
                        AvroBeanHelper.getBaseBrandMsg(
                                JSON.toJSONString(
                                        BrandMerchantStatusChangeMsg.builder()
                                                .brandId(event.getBrandId())
                                                .mainMerchantId(event.getMainMerchantId())
                                                .merchantIds(event.getMerchantIds())
                                                .status(event.getStatus())
                                                .deleteBrand(event.isDeleteBrand())
                                                .build()
                                ), MessageTypeEnum.BRAND_MERCHANT_STATUS_CHANGE
                        )
                ), 5, TimeUnit.SECONDS);
    }
}
