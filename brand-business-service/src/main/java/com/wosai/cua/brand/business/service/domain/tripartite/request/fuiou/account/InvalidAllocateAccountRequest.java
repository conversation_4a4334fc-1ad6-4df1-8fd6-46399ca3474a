package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

/**
 * 用户删除接口（CJ004）
 */
@Data
public class InvalidAllocateAccountRequest implements TripartiteSystemCallRequest {

    public static final String REQUEST_URI = "/invalidAllocateAccount.fuiou";

    private InvalidAllocateAccountRequestBody body;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_INVALID_ALLOCATE_ACCOUNT;
    }
}
