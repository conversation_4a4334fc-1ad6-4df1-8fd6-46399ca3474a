package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
public class ModifyBrandMerchantRequest extends OpenApiBaseRequest{
    /**
     * 商户编号
     */
    @NotBlank(message = "商户编号不能为空")
    @JsonProperty("merchant_sn")
    private String merchantSn;
    /**
     * 法人身份证号
     */
    @JsonProperty("legal_person_number")
    private String legalPersonNumber;
    /**
     * 银行卡号
     */
    @JsonProperty("bank_card_number")
    private String bankCarNumber;
    /**
     * 银行预留手机号
     */
    @JsonProperty("reserved_mobile_number")
    private String reservedMobileNumber;
    /**
     * 统一社会信用代码
     */
    @JsonProperty("unify_the_social_credit_code")
    private String unifyTheSocialCreditCode;
    /**
     * 美团门店编号
     */
    @JsonProperty("mei_tuan_store_sn")
    private String meiTuanStoreSn;
    /**
     * 饿了么门店编号
     */
    @JsonProperty("elm_store_sn")
    private String elmStoreSn;

    /**
     * 外部商户编号
     */
    @JsonProperty("out_merchant_no")
    private String outMerchantNo;
}
