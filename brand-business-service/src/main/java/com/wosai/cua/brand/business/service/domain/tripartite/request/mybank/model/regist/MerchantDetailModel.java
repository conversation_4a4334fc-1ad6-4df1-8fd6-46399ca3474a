package com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.regist;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class MerchantDetailModel {
    /**
     * String	联系人手机号。为商户常用联系人联系手机号。	20	M
     */
    @JSONField(name = "ContactMobile")
    private String contactMobile;
    /**
     * String	联系人姓名。为商户常用联系人姓名。	256	M
     */
    @JSONField(name = "ContactName")
    private String contactName;
    /**
     * 负责人手机号。开户发送的短信验证码的手机号需要和负责人手机号一致。自然人和个体户必传，企业非必传	32	C
     */
    @JSONField(name = "PrincipalMobile")
    private String principalMobile;
    /**
     * 负责人证件类型。
     * 01：身份证	4	M
     */
    @JSONField(name = "PrincipalCertType")
    private String principalCertType;
    /**
     * String	负责人证件号码。
     * 填写自然人证件号码、个体工商户证件号码、企业法人证件号码。	32	M
     */
    @JSONField(name = "PrincipalCertNo")
    private String principalCertNo;
    /**
     * String	负责人名称或企业法人代表姓名。
     * 备注：若商户类型为“自然人”填写责任人本人，若为“个体工商户”填写经营者本人，若为“企业”填写企业法人代表名称	256	M
     */
    @JSONField(name = "PrincipalPerson")
    private String principalPerson;
    /**
     * String	负责人证件有效期
     * 如果负责人证件图片上传，则该字段必传	32	C
     */
    @JSONField(name = "PrincipalCertVld")
    private String principalCertVld;
    /**
     * String	经办人姓名：用于企业户后续激活；企业可选填	256	C
     */
    @JSONField(name = "HandlePerson")
    private String handlePerson;
    /**
     * String	经办人手机号，企业可选填	32	C
     */
    @JSONField(name = "HandlePersonMobile")
    private String handlePersonMobile;
    /**
     * String	经办人证件号码	32	C
     */
    @JSONField(name = "HandlePersonCertNo")
    private String handlePersonCertNo;
    /**
     * String	经办人证件号，企业可选填	4	C
     */
    @JSONField(name = "HandlerPersonCertType")
    private String handlerPersonCertType;
    /**
     * 证件类型
     * 若商户类型为“个体工商户”、“企业”需要填写证件类型
     * 12：营业执照/统一社会信用代码
     * 41：政府机关证件
     * 42：部队证件
     * 43：社会团体证件
     * 44：事业单位证件
     * 45：民办非企业组织证件
     * 99：其它企业类型证件
     *
     * 备注：
     * 个体工商户：选择12
     * 企业：选择12
     */
    @JSONField(name = "BussAuthType")
    private String bussAuthType;

    /**
     * 证件号码
     * 若商户类型为“个体工商户”、“企业”需要填写证件号码
     *     备注：
     *     个体工商户：填写营业执照号
     *     企业：填写营业执照号或统一社会信用代码	256	C
     */
    @JSONField(name = "BussAuthNo")
    private String bussAuthNo;

    /**
     * String	营业执照有效期，如果营业执照证件照上传，则该字段必传	32	C
     */
    @JSONField(name = "BussAuthVld")
    private String bussAuthVld;
}
