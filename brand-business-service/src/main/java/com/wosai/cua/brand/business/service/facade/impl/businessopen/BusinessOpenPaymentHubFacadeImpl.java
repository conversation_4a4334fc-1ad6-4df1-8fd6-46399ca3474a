package com.wosai.cua.brand.business.service.facade.impl.businessopen;

import com.alibaba.fastjson2.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.businessopen.CheckOpenPaymentHubResponseDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubResponseDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BusinessLicenseEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.businessopen.BusinessOpenPaymentHubFacade;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.enums.OpenPaymentHubFailCodeEnum;
import com.wosai.cua.brand.business.service.handler.CreateBrandMerchantBankAccountHandler;
import com.wosai.cua.brand.business.service.handler.CreateBrandMerchantHandler;
import com.wosai.cua.brand.business.service.handler.OpenPaymentHubHandler;
import com.wosai.cua.brand.business.service.handler.PaymentHubOpenContext;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.StoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/19
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusinessOpenPaymentHubFacadeImpl implements BusinessOpenPaymentHubFacade {

    private final BrandDomainService brandDomainService;
    private final MerchantBusinessLicenseService merchantBusinessLicenseService;
    private final ApplicationContext context;
    private final StoreService storeService;
    private OpenPaymentHubHandler[] openHandlers;

    @PostConstruct
    public void initHandlers() {
        openHandlers = new OpenPaymentHubHandler[]{
                context.getBean(CreateBrandMerchantHandler.class),
                context.getBean(CreateBrandMerchantBankAccountHandler.class)
        };
    }

    @Override
    public CheckOpenPaymentHubResponseDTO checkOpenPaymentHub(OpenPaymentHubRequestDTO openPaymentHubRequest) {
        Map merchantDraft = openPaymentHubRequest.getMerchantInfo();
        Map submitInfo = (Map) BeanUtil.getProperty(merchantDraft, "submit_info");
        Map licenseMap = (Map) BeanUtil.getProperty(submitInfo, "license");
        PaymentHubOpenContext context = new PaymentHubOpenContext()
                .setOpenPaymentHubRequestDTO(openPaymentHubRequest)
                .setMerchantBusinessLicenseInfo(JSON.parseObject(JSON.toJSONString(licenseMap), MerchantBusinessLicenseInfo.class));
        return doCheckOpenPaymentHub(context);
    }

    @Override
    public OpenPaymentHubResponseDTO openPaymentHub(OpenPaymentHubRequestDTO openPaymentHubRequest) {
        PaymentHubOpenContext context = new PaymentHubOpenContext()
                .setOpenPaymentHubRequestDTO(openPaymentHubRequest)
                .setMerchantBusinessLicenseInfo(merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(openPaymentHubRequest.getMerchantId()));
        CheckOpenPaymentHubResponseDTO checkOpenPaymentHubResponseDTO = doCheckOpenPaymentHub(context);
        if (!checkOpenPaymentHubResponseDTO.isCheckSuccess()) {
            return new OpenPaymentHubResponseDTO()
                    .setStatus(OpenPaymentHubResponseDTO.FAIL_OPEN)
                    .setFailCode(checkOpenPaymentHubResponseDTO.getSubmitCheckCode())
                    .setFailMsg(checkOpenPaymentHubResponseDTO.getSubmitCheckMsg());
        }
        try {
            for (OpenPaymentHubHandler openHandler : openHandlers) {
                openHandler.handle(context);
            }
            return OpenPaymentHubResponseDTO.process();
        } catch (Exception e) {
            log.error("开通收付通异常: {}", JSON.toJSONString(openPaymentHubRequest), e);
            throw e;
        }
    }

    @Override
    public OpenPaymentHubResponseDTO getPaymentHubOpenStatus(OpenPaymentHubRequestDTO openPaymentHubRequest) {
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(openPaymentHubRequest.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            return null;
        }
        if (BrandMerchantAccountOpenStatusEnum.OPENED.getStatus().equals(brandMerchantModule.getAccountOpenStatus())) {
            return new OpenPaymentHubResponseDTO()
                    .setStatus(OpenPaymentHubResponseDTO.SUCCESS_OPEN);
        }
        if (BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus().equals(brandMerchantModule.getAccountOpenStatus())) {
            return new OpenPaymentHubResponseDTO()
                    .setStatus(OpenPaymentHubResponseDTO.FAIL_OPEN)
                    .setFailCode(OpenPaymentHubFailCodeEnum.COMMON_FAIL.getCode())
                    .setFailMsg(brandMerchantModule.getAccountOpenFailureReason())
                    .setAppSubStatusText(OpenPaymentHubFailCodeEnum.COMMON_FAIL.getDesc());
        }
        if (BrandMerchantAccountOpenStatusEnum.HAVE_NOT_OPENED.getStatus().equals(brandMerchantModule.getAccountOpenStatus())) {
            return new OpenPaymentHubResponseDTO()
                    .setStatus(OpenPaymentHubResponseDTO.FAIL_OPEN)
                    .setFailCode(OpenPaymentHubFailCodeEnum.COMMON_FAIL.getCode())
                    .setFailMsg(brandMerchantModule.getAccountOpenFailureReason())
                    .setAppSubStatusText(OpenPaymentHubFailCodeEnum.COMMON_FAIL.getDesc());
        }
        return new OpenPaymentHubResponseDTO()
                .setStatus(OpenPaymentHubResponseDTO.PROCESS_OPEN)
                .setAppSubStatusText(Optional.ofNullable(BrandMerchantAccountOpenStatusEnum.getByStatus(brandMerchantModule.getAccountOpenStatus())).orElse(BrandMerchantAccountOpenStatusEnum.IN_OPENING).getStatus());
    }


    private CheckOpenPaymentHubResponseDTO doCheckOpenPaymentHub(PaymentHubOpenContext context) {
        OpenPaymentHubRequestDTO requestDTO = context.getOpenPaymentHubRequestDTO();
        try {
            // 品牌是否存在
            BrandModule brandModule = brandDomainService.getBrandModuleByBrandSn(requestDTO.getDevParam().getBrandSn());
            if (Objects.isNull(brandModule)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
            }
            context.setBrandModule(brandModule);

            if (WosaiStringUtils.isNotEmpty(requestDTO.getDevParam().getSqbStoreSn())) {
                StoreInfo storeInfo = storeService.getStoreBySn(requestDTO.getDevParam().getSqbStoreSn(), null);
                if (Objects.isNull(storeInfo)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_STORE);
                }
                context.setStoreInfo(storeInfo);
            }

            // 营业执照信息为空
            if (Objects.isNull(context.getMerchantBusinessLicenseInfo())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_INFO_NOT_INTEGRITY);
            }
            // 不支持的营业执照类型
            Integer type = context.getMerchantBusinessLicenseInfo().getType();
            List<Integer> businessLicenseTypeList = Arrays.stream(BusinessLicenseEnum.values()).map(BusinessLicenseEnum::getType).collect(Collectors.toList());
            if (!businessLicenseTypeList.contains(type)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_BUSINESS_LICENSE);
            }
            // 对应的必填参数是否
            if (BusinessLicenseEnum.ENTERPRISE_BUSINESS_LICENSE.getType().equals(type) || BusinessLicenseEnum.UNIFIED_SOCIAL_CREDIT_IDENTIFIER.getType().equals(type)) {
                if (WosaiStringUtils.isEmpty(requestDTO.getDevParam().getHolder())) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PASS_FIELD_CHECK);
                }
            }
            return CheckOpenPaymentHubResponseDTO.success();
        } catch (BrandBusinessException e) {
            return CheckOpenPaymentHubResponseDTO.fail(String.valueOf(e.getCode()), e.getMessage());
        } catch (Exception e) {
            log.error("校验开通收付通系统异常 {}", requestDTO.getMerchantId(), e);
            return CheckOpenPaymentHubResponseDTO.fail(String.valueOf(BrandBusinessExceptionEnum.SYSTEM_ERROR.getCode()), BrandBusinessExceptionEnum.SYSTEM_ERROR.getMessage());
        }
    }
}
