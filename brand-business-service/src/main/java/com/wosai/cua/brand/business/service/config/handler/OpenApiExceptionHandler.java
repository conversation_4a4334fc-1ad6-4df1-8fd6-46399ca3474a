package com.wosai.cua.brand.business.service.config.handler;

import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.exception.OpenApiParamsException;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.BaseResponse;
import com.wosai.cua.brand.business.service.controller.rest.OpenApiController;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ControllerAdvice(assignableTypes = {OpenApiController.class})
public class OpenApiExceptionHandler {

//    @ExceptionHandler(CustomException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public ResponseEntity<ErrorResponse> handleCustomException(CustomException ex) {
//        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode(), ex.getMessage());
//        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
//    }
//
//    @ExceptionHandler(AnotherException.class)
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
//    public ResponseEntity<ErrorResponse> handleAnotherException(AnotherException ex) {
//        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode(), ex.getMessage());
//        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
//    }

    // 可以继续添加其他异常处理方法

    @ExceptionHandler(OpenApiParamsException.class)
    @ResponseBody
    public BaseResponse<Void> handleOpenApiParamsException(OpenApiParamsException ex) {
        return BaseResponse.failParams(ex.getCode(), ex.getMessage());
    }

    @ExceptionHandler(BrandBusinessException.class)
    @ResponseBody
    public BaseResponse<Void> handleException(BrandBusinessException ex) {
        return BaseResponse.businessFail(String.valueOf(ex.getCode()), ex.getMessage());
    }
}
