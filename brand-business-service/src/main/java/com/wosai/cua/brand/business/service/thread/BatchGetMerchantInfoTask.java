package com.wosai.cua.brand.business.service.thread;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.service.module.merchant.MerchantModule;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class BatchGetMerchantInfoTask implements Callable<List<MerchantModule>> {

    private final List<String> merchantIds;

    private final List<MerchantModule> result;

    private final CountDownLatch latch;

    private final MerchantService merchantService;

    public BatchGetMerchantInfoTask(List<String> merchantIds, List<MerchantModule> result, CountDownLatch latch, MerchantService merchantService) {
        this.merchantIds = merchantIds;
        this.result = result;
        this.latch = latch;
        this.merchantService = merchantService;
    }

    @Override
    public List<MerchantModule> call() throws Exception {
        List<MerchantInfo> merchantInfoList = merchantService.getMerchantListByMerchantIds(merchantIds);
        if (CollectionUtils.isEmpty(merchantInfoList)) {
            return Collections.emptyList();
        }
        String jsonString = JSON.toJSONString(merchantInfoList);
        List<MerchantModule> merchantModules = JSON.parseArray(jsonString, MerchantModule.class);
        synchronized (result) {
            result.addAll(merchantModules);
        }
        log.info("Thread：{}，merchant result is {}", Thread.currentThread().getId(), jsonString);
        latch.countDown();
        return merchantModules;
    }
}
