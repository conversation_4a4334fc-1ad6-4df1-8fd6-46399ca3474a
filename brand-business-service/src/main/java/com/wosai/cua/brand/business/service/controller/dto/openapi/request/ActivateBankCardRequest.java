package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
public class ActivateBankCardRequest extends OpenApiBaseRequest{

    public static final String ACTIVATION_TYPE_ACTIVATE = "01";

    public static final String ACTIVATION_TYPE_ADVANCE = "02";

    @JsonProperty("merchant_id")
    @NotBlank(message = "商户id必填")
    private String merchantId;

    /**
     * 银行卡id（收付通系统）
     */
    @NotBlank(message = "收付通银行卡id必填")
    @JsonProperty("bank_card_id")
    private String bankCardId;

    /**
     * 激活类型：01-激活 02-激活推进
     */
    @NotBlank(message = "激活类型必填")
    @JsonProperty("activation_type")
    private String activationType;

    /**
     * 手机号（申请往账鉴权有上送经办人的，指令号会发到经办人手机号，未输入经办人的发到此手机号）
     */
    private String mobile;

    /**
     * 校验方式
     * @see com.wosai.cua.brand.business.api.enums.BankCardCheckModeEnum
     */
    @JsonProperty("check_mode")
    private String checkMode;

    /**
     * 激活方式：1-个人 2-法人办理，3-经办人办理
     */
    @JsonProperty("activate_type")
    private String activateType;

    /**
     * 经办人姓名
     */
    @JsonProperty("agent_name")
    private String agentName;

    /**
     * 经办人证件类型：activateType=3时必填
     * 仅支持1-身份证
     */
    @JsonProperty("agency_global_type")
    private String agencyGlobalType;
    /**
     * 经办人证件号
     */
    @JsonProperty("agency_global_id")
    private String agentIdCode;

    /**
     * 经办人手机号
     */
    @JsonProperty("agent_phone")
    private String agentPhone;

    /**
     * 验证码
     */
    @JsonProperty("verification_code")
    private String verificationCode;

    /**
     * 银行打款金额
     * 企业会员绑卡（往账鉴权使用）
     */
    @JsonProperty("bank_payment_amount")
    private String bankPaymentAmount;

    public boolean isActivate() {
        return ACTIVATION_TYPE_ACTIVATE.equals(activationType);
    }

    public boolean isAdvance() {
        return ACTIVATION_TYPE_ADVANCE.equals(activationType);
    }
}
