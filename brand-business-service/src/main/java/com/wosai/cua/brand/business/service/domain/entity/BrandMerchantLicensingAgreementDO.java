package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 品牌商户授权协议表
 * @TableName brand_merchant_licensing_agreement
 */
@TableName(value ="brand_merchant_licensing_agreement")
@Data
public class BrandMerchantLicensingAgreementDO implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 协议类型：COLLECTION_AUTHORIZATION-归集授权
     */
    private String type;

    /**
     * 协议url
     */
    private String url;

    /**
     * 协议内容
     */
    private String context;

    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 添加时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}