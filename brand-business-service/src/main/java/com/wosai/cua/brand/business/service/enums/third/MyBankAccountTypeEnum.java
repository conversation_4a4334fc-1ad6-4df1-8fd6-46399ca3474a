package com.wosai.cua.brand.business.service.enums.third;

import lombok.Getter;

@Getter
public enum MyBankAccountTypeEnum {
    BASIC("BASIC","开通可用和冻结子户（商户入驻接口默认开通，特殊场景或未开出的选择）"),
    PREPAY("PREPAY","开通合并支付子户"),
    SETTLING("SETTLING","开通待结算子户"),
    TRADE_DEPOSIT("TRADE_DEPOSIT","开通保证金子户");

    private final String code;
    private final String desc;

    MyBankAccountTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
