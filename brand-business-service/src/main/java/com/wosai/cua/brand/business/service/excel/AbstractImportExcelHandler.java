package com.wosai.cua.brand.business.service.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.BrandTaskLogDomainService;
import com.wosai.cua.brand.business.service.excel.context.AbstractImportContext;
import com.wosai.cua.brand.business.service.helper.OssFileHelper;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.cua.brand.business.service.module.log.TaskApplyLogResultModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Slf4j
public abstract class AbstractImportExcelHandler<C extends AbstractImportContext, T> {

    @Autowired
    protected BrandTaskLogDomainService brandTaskLogDomainService;

    /**
     * 读取并上传处理结果
     * 该方法主要用于处理品牌商户导入的审核请求，
     * 处理数据，然后将处理结果写入新文件并上传到oss
     *
     * @param brandTaskLogModule  主任务
     * @param brandSubTaskModules 子任务列表
     * @return 返回上传文件的URL
     */
    public String doHandleImportTask(BrandTaskLogModule brandTaskLogModule, List<BrandSubTaskModule> brandSubTaskModules) {
        String resultUrl = null;
        C context = null;
        try {
            context = initContext(brandTaskLogModule);
            preCheck(context);
            preImport(context, brandSubTaskModules);
            List<T> result = doImport(context, brandSubTaskModules);
            resultUrl = afterImport(context, brandTaskLogModule, result);
            return resultUrl;
        } catch (Exception e) {
            log.error("导入任务处理失败 id:{}", brandTaskLogModule.getId(), e);
            importException(brandTaskLogModule, e);
            throw e;
        } finally {
            if (Objects.nonNull(context) && Objects.nonNull(context.getPhotoFile())) {
                FileUtils.deleteQuietly(context.getPhotoFile());
            }
        }
    }

    private List<T> doImport(C context, List<BrandSubTaskModule> brandSubTaskModules) {
        List<T> result = new ArrayList<>();
        for (BrandSubTaskModule brandSubTaskModule : brandSubTaskModules) {
            if (brandSubTaskModule.getTaskStatus().equals(BrandTaskStatusEnum.PENDING.getTaskStatus())) {
                doHandleData(context, brandSubTaskModule);
                result.add(JSON.parseObject(brandSubTaskModule.getSubTaskContext(), getDataClass()));
                brandTaskLogDomainService.updateBrandSubTask(brandSubTaskModule);
            } else {
                result.add(JSON.parseObject(brandSubTaskModule.getSubTaskContext(), getDataClass()));
            }
        }
        return result;
    }

    private String afterImport(C context, BrandTaskLogModule brandTaskLogModule, List<T> result) {
        brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
        brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
        String resultUrl = uploadResult(context.getFileName(), result);
        brandTaskLogModule.setTaskResult(JSON.toJSONString(
                TaskApplyLogResultModule.builder()
                        .downloadUrl(resultUrl).build()));
        brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
        return resultUrl;
    }

    private void importException(BrandTaskLogModule brandTaskLogModule, Exception e) {
        brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
        brandTaskLogModule.setErrorLog(e.getMessage());
        brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
    }

    private String uploadResult(String fileName, List<T> result) {
        String filePath = "/tmp/brand" + fileName;
        try {
            EasyExcel.write(filePath, getDataClass())
                    .sheet()
                    .doWrite(result);
            OssFileHelper.uploadFile(OssFileHelper.IMAGE_BUCKET_NAME, OssFileHelper.KEY_PREFIX + fileName, new File(filePath));
            return OssFileHelper.STATIC_BASE_URL + "/" + OssFileHelper.getStaticsFileUrl(OssFileHelper.KEY_PREFIX + fileName);
        } catch (Exception e) {
            throw new CommonInvalidParameterException("处理完成：结果文件上传失败");
        } finally {
            if (WosaiStringUtils.isNotEmpty(filePath)) {
                FileUtils.deleteQuietly(new File(filePath));
            }
        }
    }

    /**
     * 初始化导入任务上下文
     *
     * @param brandTaskLogModule 主任务
     * @return 上下文
     */
    public abstract C initContext(BrandTaskLogModule brandTaskLogModule);

    /**
     * 获取审批商户导入类型
     *
     * @return 导入类型
     */
    public abstract ExcelImportTypeEnum getAuditImportType();

    /**
     * 前置校验
     *
     * @param context 请求参数上下文
     */
    public abstract void preCheck(C context);

    /**
     * 导入操作前的预处理方法
     * 该方法旨在在导入流程开始之前，对相关的子任务模块进行必要的预处理或初始化
     *
     * @param context 导入操作的上下文，可能包含与导入相关的配置信息或环境参数
     * @param brandSubTaskModules 一组品牌子任务模块的列表，这些模块将在导入操作中被处理
     */
    protected void preImport(C context, List<BrandSubTaskModule> brandSubTaskModules) {

    }

    /**
     * 对数据进行处理
     *
     * @param context 上下文参数
     */
    protected abstract void doHandleData(C context, BrandSubTaskModule brandSubTaskModule);

    @SuppressWarnings("unchecked")
    public Class<T> getDataClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }

    /**
     * 校验excel每一行数据是否合乎要求
     * @param o 每一行的数据
     */
    public void checkData(C context, T o){};

    /**
     * 获取导入限制数量
     * @param context 上下文参数
     * @return 限制数量 默认 500条
     */
    public int getLimitQuantity(C context) {
        return 2000;
    }
}
