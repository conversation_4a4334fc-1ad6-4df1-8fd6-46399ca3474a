package com.wosai.cua.brand.business.service.domain.tripartite.mybank.enums;

/**
 * 网关异常响应的枚举信息
 **/
public enum MybankApiExceptionEnum {

    /**
     * 超网定义异常
     **/
    UNKNOWN_EXCEPTION("SGW01002", "系统未知异常"),
    ENCRYPT_EXCEPTION("SGW11009", "签名异常"),
    MESSAGE_ISNULL("SGW12001", "接收到空报文"),
    MESSAGE_PARSE_ERROR("SGW12014", "报文解析错误"),
    VERIFY_ERROR("SGW12016", "验签错误"),
    SYSTEM_TIMEOUT("SGW21008", "业务系统处理超时"),
    NOT_FOUND_BUSINESS_SYSTEM("SGW21010", "无法找到报文对应的业务系统"),
    INVOKE_BUSINESS_SYSTEM_EXCEPTION("SGW21013", "调用内部业务系统异常"),
    REPEAT_MESSAGE("SGW31001", "重复报文"),
    GATEWAY_TRANSACTION_TYPE_NOT_EXIST("SGW31004", "网关交易类型不存在"),
    GATEWAY_EXTERNAL_TRANSACTION_CODE_NOT_EXIST("SGW31005", "网关外部交易编码不存在"),
    INVALID_PARAMETER("SGW81000", "非法参数"),
    MESSAGE_CONVERT_EXCEPTION("SGW81001", "报文转换异常"),
    MESSAGE_PARAMETER_ERROR("SGW13012", "报文参数错误"),
    /**
     * 签名相关
     **/
    SERVER_SYSTEM_EXCEPTION("MBD50000","系统内部异常"),

    SERVER_ENCRYPT_ERROR("MBD40001", "报文签名异常"),
    SERVER_VERIFY_ERROR("MBD40002", "报文验签异常"),
    SIGNATURE_ELEMENT_NOT_EXIST("MBD40003", "验签时签名元素不存在"),
    DOCUMENT_ELEMENT_NOT_EXIST("MBD40004", "加签时文档元素不存在"),
    ILLEGAL_APPEND_MODE("MBD40005", "非法的签名模式"),
    ILLEGAL_SIGN_TYPE("MBD40006", "不支持的签名类型"),
    VERIFY_FAIL("MBD40007", "网商返回信息验签失败"),
    NOTICE_NOT_MATCH("MBD40008", "通知Appid不匹配"),

    ILLEGAL_RSA_PRIVATE_KEY("MBD41001", "RSA私钥格式不正确，请检查是否正确配置了PKCS8格式的私钥"),
    ILLEGAL_RSA_PUBLIC_KEY("MBD41002", "RSA公钥钥格式不正确，请检查是否正确配置了PKCS8格式的公钥"),

    XML_PARSE_ERROR("MBD42005", "XML信息解析异常"),
    NOT_XML_TYPE("MBD42001", "不是xml类型"),
    BUILD_XML_EXCEPTION("MBD42002", "构建xml报文失败"),
    LOAD_JAXB_CONTEXT_ERROR("MBD42003", "加载JAXB对象失败,参数为空"),
    XML_CONVERT_ERROR("MBD42004", "XML与Bean转换异常"),

    REQUEST_REMOTE_SERVER_ERROR("MBD43001", "调用远程服务异常"),

    BUILD_HTTP_ENTITY_EXCEPTION("MBD44001","构建HttpEntity异常"),

    STRATEGY_NOT_DEFINED("MBD45001","未定义策略,未找到通知接口对应的处理实现类"),

    ;

    private String errorCode;
    private String errorDesc;

    MybankApiExceptionEnum(String errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static MybankApiExceptionEnum getMybankApiExceptionEnum(String errorCode) {
        for (MybankApiExceptionEnum gatewayResponseExceptionEnum : MybankApiExceptionEnum.values()) {
            if (gatewayResponseExceptionEnum.getErrorCode().equals(errorCode)) {
                return gatewayResponseExceptionEnum;
            }
        }
        return null;
    }


    public static String getErrorDesc(String errorCode) {
        MybankApiExceptionEnum[] exceptionEnums = values();
        for (MybankApiExceptionEnum exceptionEnum : exceptionEnums) {
            if (exceptionEnum.getErrorCode().equals(errorCode)) {
                return exceptionEnum.getErrorDesc();
            }
        }
        return null;
    }

    public static String getErrorCode(String errorDesc) {
        MybankApiExceptionEnum[] exceptionEnums = values();
        for (MybankApiExceptionEnum exceptionEnum : exceptionEnums) {
            if (exceptionEnum.getErrorDesc().equals(errorDesc)) {
                return exceptionEnum.getErrorCode();
            }
        }
        return null;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public String getErrorDesc() {
        return this.errorDesc;
    }
}
