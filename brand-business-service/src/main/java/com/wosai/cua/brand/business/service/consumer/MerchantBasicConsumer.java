package com.wosai.cua.brand.business.service.consumer;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.merchant.DataBusMerchant;
import com.wosai.databus.event.merchant.basic.MerchantBasicUpdateEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.upay.common.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.nio.ByteBuffer;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/13
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class MerchantBasicConsumer {

    @Autowired
    private BrandDomainService brandDomainService;

    protected EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();

    @KafkaListener(topics = "#{'${databus.consumer.topic}'.split(',')}", containerFactory = "dataBusKafkaListenerContainerFactoryV2")
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord datum = record.value();
        if (datum == null) {
            log.error("{} getValue null", record);
            return;
        }
        AbstractEvent event;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = (AbstractEvent) persistenceHelper.fromJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
        } catch (Throwable t) {
            log.error("{} MerchantBasicConsumer consume error", record, t);
            return;
        }
        try {
            doHandleEvent(event);
        } catch (Throwable t) {
            log.error("{} MerchantBasicConsumer doHandleEvent error", record, t);
        }
    }

    protected void doHandleEvent(AbstractEvent event) {
        if (event instanceof MerchantBasicUpdateEvent) {
            handleMerchantBasicUpdateEvent((MerchantBasicUpdateEvent) event);
        }
    }

    private void handleMerchantBasicUpdateEvent(MerchantBasicUpdateEvent event) {
        log.info("MerchantBasicUpdateEvent {}", JacksonUtil.toJsonString(event));
        DataBusMerchant before = event.getBefore();
        DataBusMerchant after = event.getAfter();
        if (WosaiStringUtils.equals(before.getName(), after.getName())) {
            return;
        }
        String merchantId = event.getMerchantId();
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(merchantId);
        if (Objects.isNull(brandMerchantModule)) {
            return;
        }
        if (!WosaiStringUtils.equals(after.getName(), brandMerchantModule.getMerchantName())) {
            brandMerchantModule.setMerchantName(after.getName());
            brandDomainService.updateBrandMerchant(brandMerchantModule);
        }
    }
}
