package com.wosai.cua.brand.business.service.controller.rest;

import com.alibaba.fastjson2.JSON;
import com.lark.oapi.core.utils.Lists;
import com.wosai.cua.brand.business.api.dto.brand.SubAccountOpenDetailDTO;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardAdvanceDTO;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.BaseBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.DeleteBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.OutMerchantNoDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.CheckAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.fuiou.ResendRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ModifyBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateBrandMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.CheckAccountResultEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import com.wosai.cua.brand.business.api.facade.BrandMerchantBankAccountFacade;
import com.wosai.cua.brand.business.api.facade.FuiouFacade;
import com.wosai.cua.brand.business.api.facade.MyBankFacade;
import com.wosai.cua.brand.business.service.context.OpenApiContext;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.ActivateBankCardRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.ActivationStatusRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.ChangeDefaultBankCardRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.CheckAccountRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.CreateBankCardRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.CreateBrandMerchantRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.DeleteBankCardRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.MerchantActivateRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.ModifyBrandMerchantRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.PageQueryBankCardRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.QueryBrandMerchantRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.openapi.OpenApiPageQueryBrandMerchantRequest;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.ActivateBankCardResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.ActivationStatusResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.BaseResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.ChangeDefaultBankCardResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.CheckAccountResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.CreateBankCardResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.DeleteBankCardResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.MerchantActivateResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.ModifyBrandMerchantResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.OpenApiPageQueryBrandMerchantsResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.PageBrandMerchantBankCardsResponse;
import com.wosai.cua.brand.business.service.controller.dto.openapi.response.QueryBrandMerchantResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Objects;

@RestController
@RequestMapping("/sft/brand")
@Slf4j
public class OpenApiController {

    private final BrandFacade brandFacade;

    private final BrandMerchantBankAccountFacade brandMerchantBankAccountFacade;

    private final FuiouFacade fuiouFacade;

    @Autowired
    public OpenApiController(BrandFacade brandFacade, BrandMerchantBankAccountFacade brandMerchantBankAccountFacade, MyBankFacade myBankFacade, FuiouFacade fuiouFacade) {
        this.brandFacade = brandFacade;
        this.brandMerchantBankAccountFacade = brandMerchantBankAccountFacade;
        this.fuiouFacade = fuiouFacade;
    }

    /**
     * 创建商户
     */
    @PostMapping(value = "/merchant/create")
    public BaseResponse<ModifyBrandMerchantResponse> createBrandMerchant(@Valid @RequestBody CreateBrandMerchantRequest request) {
        String requestContext = JSON.toJSONString(request);
        log.info("createBrandMerchant request:{}", requestContext);
        CreateBrandMerchantRequestDTO createBrandMerchantRequestDTO = JSON.parseObject(requestContext, CreateBrandMerchantRequestDTO.class);
        createBrandMerchantRequestDTO.setBrandId(OpenApiContext.getBrandId());
        CreateBrandMerchantResponseDTO brandMerchant = brandFacade.createBrandMerchant(createBrandMerchantRequestDTO);
        ModifyBrandMerchantResponse response = ModifyBrandMerchantResponse.builder()
                .brandSn(OpenApiContext.getBrandSn())
                .merchantSn(brandMerchant.getMerchantSn())
                .merchantName(brandMerchant.getMerchantName())
                .merchantType(brandMerchant.getMerchantType())
                .build();
        response.setClientSn(OpenApiContext.getClientSn());
        return BaseResponse.success(response);
    }

    /**
     * 获取商户信息
     */
    @PostMapping(value = "/merchantInfo")
    public BaseResponse<QueryBrandMerchantResponse> getBrandMerchantByMerchantSn(@Valid @RequestBody QueryBrandMerchantRequest request) {
        BrandMerchantInfoDTO merchant = null;
        if (StringUtils.isNotBlank(request.getMerchantSn())) {
            QueryBrandMerchantInfoDTO queryBrandMerchantInfo = new QueryBrandMerchantInfoDTO();
            queryBrandMerchantInfo.setBrandId(OpenApiContext.getBrandId());
            queryBrandMerchantInfo.setMerchantSn(request.getMerchantSn());
            merchant = brandFacade.getBrandMerchantInfoByStoreIdOrMerchantSn(queryBrandMerchantInfo);
        }
        if (StringUtils.isNotBlank(request.getOutMerchantNo())) {
            OutMerchantNoDTO outMerchantNo = new OutMerchantNoDTO();
            outMerchantNo.setBrandId(OpenApiContext.getBrandId());
            outMerchantNo.setOutMerchantNo(request.getOutMerchantNo());
            merchant = brandFacade.getBrandMerchantInfoByOutMerchantNo(outMerchantNo);
        }
        if (merchant == null) {
            return null;
        }
        QueryBrandMerchantResponse response = QueryBrandMerchantResponse.builder(merchant);
        response.setClientSn(OpenApiContext.getClientSn());
        return BaseResponse.success(response);
    }

    /**
     * 修改商户信息
     */
    @PostMapping(value = "/merchant/modify")
    public BaseResponse<ModifyBrandMerchantResponse> modifyBrandMerchant(@Valid @RequestBody ModifyBrandMerchantRequest request) {
        ModifyBrandMerchantDTO modifyBrandMerchant = JSON.parseObject(JSON.toJSONString(request), ModifyBrandMerchantDTO.class);
        modifyBrandMerchant.setBrandId(OpenApiContext.getBrandId());
        brandFacade.modifyBrandMerchant(modifyBrandMerchant);
        ModifyBrandMerchantResponse response = ModifyBrandMerchantResponse.builder().brandSn(OpenApiContext.getBrandSn()).merchantSn(request.getMerchantSn()).build();
        response.setClientSn(OpenApiContext.getClientSn());
        return BaseResponse.success(response);
    }

    /**
     * 分页查询银行卡列表（网商通道没有银行卡功能）
     */
    @PostMapping(value = "/brand/merchant/page/findBankCardList")
    public BaseResponse<PageBrandMerchantBankCardsResponse> pageFindBankCardList(@Valid @RequestBody PageQueryBankCardRequest request) {
        PageQueryBankCardDTO pageQueryBankCardDto = JSON.parseObject(JSON.toJSONString(request), PageQueryBankCardDTO.class);
        pageQueryBankCardDto.setBrandId(OpenApiContext.getBrandId());
        pageQueryBankCardDto.setMerchantSn(request.getMerchantSn());
        if (Objects.isNull(pageQueryBankCardDto.getPage())) {
            pageQueryBankCardDto.setPage(1);
        }
        if (Objects.isNull(pageQueryBankCardDto.getPageSize())) {
            pageQueryBankCardDto.setPageSize(10);
        }
        PageBrandMerchantBankCardsResponse response = JSON.parseObject(JSON.toJSONString(brandMerchantBankAccountFacade.pageFindBankCardList(pageQueryBankCardDto)), PageBrandMerchantBankCardsResponse.class);
        response.setClientSn(OpenApiContext.getClientSn());
        return BaseResponse.success(response);
    }

    /**
     * 添加银行卡（网商通道没有银行卡功能）
     */
    @PostMapping(value = "/merchant/createBankCard")
    public BaseResponse<CreateBankCardResponse> createBankCard(@Valid @RequestBody CreateBankCardRequest request) {
        CreateBankCardResponse response = new CreateBankCardResponse();
        request.setBrandId(OpenApiContext.getBrandId());
        response.setClientSn(OpenApiContext.getClientSn());
        CreateBankCardDTO createBankCardDTO = JSON.parseObject(JSON.toJSONString(request), CreateBankCardDTO.class);
        createBankCardDTO.setBrandId(OpenApiContext.getBrandId());
        createBankCardDTO.setMerchantSn(request.getMerchantSn());
        BankCardResponseDTO bankCard = brandMerchantBankAccountFacade.createBankCard(createBankCardDTO);
        response.setStatus("0");
        response.setBankCardId(bankCard.getBankCardId());
        return BaseResponse.success(response);
    }

    /**
     * 激活银行卡（网商通道没有银行卡功能）
     */
    @PostMapping(value = "/merchant/activateBankCard")
    public BaseResponse<ActivateBankCardResponse> activateBankCard(@Valid @RequestBody ActivateBankCardRequest request) {
        if (Objects.nonNull(OpenApiContext.getFundManagementCompanyEnum()) && OpenApiContext.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.MY_BANK)) {
            throw new BrandBusinessException("暂不支持网商银行激活银行卡");
        }
        ActivateBankCardResponse response = new ActivateBankCardResponse();
        request.setBrandId(OpenApiContext.getBrandId());
        response.setClientSn(OpenApiContext.getClientSn());
        if (request.isActivate()) {
            ActivateBankCardDTO activateBankCardDTO = JSON.parseObject(JSON.toJSONString(request), ActivateBankCardDTO.class);
            brandMerchantBankAccountFacade.activateBankCard(activateBankCardDTO);
            if (Objects.nonNull(OpenApiContext.getFundManagementCompanyEnum()) && OpenApiContext.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.PAB)) {
                response.setStatus("1");
            } else {
                response.setStatus("2");
            }
        }
        if (request.isAdvance() && Objects.nonNull(OpenApiContext.getFundManagementCompanyEnum()) && OpenApiContext.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.PAB)) {
            brandMerchantBankAccountFacade.activateBankCardAdvance(JSON.parseObject(JSON.toJSONString(request), ActivateBankCardAdvanceDTO.class));
            response.setStatus("2");
        }
        response.setBankCardId(request.getBankCardId());
        return BaseResponse.success(response);
    }

    /**
     * 修改默认银行卡（网商通道没有银行卡功能）
     */
    @PostMapping(value = "/merchant/changeDefaultBankCard")
    public BaseResponse<ChangeDefaultBankCardResponse> changeDefaultBankCard(@Valid @RequestBody ChangeDefaultBankCardRequest request) {
        ChangeDefaultBankCardResponse response = new ChangeDefaultBankCardResponse();
        response.setClientSn(OpenApiContext.getClientSn());
        brandMerchantBankAccountFacade.changeDefaultBankCard(JSON.parseObject(JSON.toJSONString(request), BaseBankCardDTO.class));
        response.setBankCardId(request.getBankCardId());
        return BaseResponse.success(response);
    }

    /**
     * 删除银行卡（网商通道没有银行卡功能）
     */
    @PostMapping(value = "/merchant/deleteBankCard")
    public BaseResponse<DeleteBankCardResponse> deleteBankCard(@Valid @RequestBody DeleteBankCardRequest request) {
        DeleteBankCardResponse response = new DeleteBankCardResponse();
        response.setClientSn(OpenApiContext.getClientSn());
        brandMerchantBankAccountFacade.deleteBankCard(JSON.parseObject(JSON.toJSONString(request), DeleteBankCardDTO.class));
        response.setBankCardId(request.getBankCardId());
        return BaseResponse.success(response);
    }

    /**
     * 查询商户激活状态（仅支持富友、网商通道）
     */
    @PostMapping(value = "/merchant/query/activation/status")
    public BaseResponse<ActivationStatusResponse> queryBrandMerchantActivationStatus(@Valid @RequestBody ActivationStatusRequest request) {
        ActivationStatusResponse response = new ActivationStatusResponse();
        response.setClientSn(OpenApiContext.getClientSn());
        QueryBrandMerchantInfoDTO queryBrandMerchantInfoDTO = new QueryBrandMerchantInfoDTO();
        queryBrandMerchantInfoDTO.setBrandId(OpenApiContext.getBrandId());
        queryBrandMerchantInfoDTO.setMerchantSn(request.getMerchantSn());
        SubAccountOpenDetailDTO subAccountOpenDetail = brandFacade.querySubAccountOpenStatus(queryBrandMerchantInfoDTO);
        response.setStatus(subAccountOpenDetail.getStatus());
        response.setMerchantSn(subAccountOpenDetail.getMerchantSn());
        return BaseResponse.success(response);
    }

    /**
     * 商户激活（仅支持富友（短信重发）)
     */
    @PostMapping(value = "/merchant/activate")
    public BaseResponse<MerchantActivateResponse> merchantActivate(@Valid @RequestBody MerchantActivateRequest request) {
        MerchantActivateResponse response = new MerchantActivateResponse();
        if (Objects.nonNull(OpenApiContext.getFundManagementCompanyEnum()) && OpenApiContext.getFundManagementCompanyEnum().equals(FundManagementCompanyEnum.FUIOU)) {
            ResendRequestDTO requestDTO = new ResendRequestDTO();
            requestDTO.setBrandId(OpenApiContext.getBrandId());
            requestDTO.setMerchantSn(request.getMerchantSn());
            response.setUrl(fuiouFacade.resend(requestDTO));
            response.setMerchantSn(request.getMerchantSn());
            response.setClientSn(request.getClientSn());
            return BaseResponse.success(response);
        }
        return BaseResponse.businessFail(String.valueOf(BrandBusinessExceptionEnum.OPEN_API_THIS_OPERATION_IS_NOT_SUPPORTED.getCode()), BrandBusinessExceptionEnum.OPEN_API_THIS_OPERATION_IS_NOT_SUPPORTED.getMessage());
    }

    /**
     * 验证商户账号
     */
    @PostMapping(value = "/merchant/checkAccount")
    public BaseResponse<CheckAccountResponse> checkAccount(@Valid @RequestBody CheckAccountRequest request) {
        CheckAccountResponse response = new CheckAccountResponse();
        response.setClientSn(OpenApiContext.getClientSn());
        CheckAccountDTO checkAccount = new CheckAccountDTO();
        checkAccount.setBrandId(OpenApiContext.getBrandId());
        checkAccount.setMerchantSn(request.getMerchantSn());
        checkAccount.setIdentifyNo(request.getIdentifyNo());
        checkAccount.setOutMerchantNo(request.getOutMerchantNo());
        CheckAccountResultEnum checkAccountResultEnum = brandFacade.checkMerchantAccount(checkAccount);
        response.setResultCode(checkAccountResultEnum.getCode());
        response.setResultMsg(checkAccountResultEnum.getMessage());
        return BaseResponse.success(response);
    }

    @PostMapping(value = "/merchant/page/query")
    public BaseResponse<OpenApiPageQueryBrandMerchantsResponse> pageQueryBrandMerchants(@Valid @RequestBody OpenApiPageQueryBrandMerchantRequest request) {
        PageQueryBrandMerchantsDTO pageQueryBrandMerchantsDto = new PageQueryBrandMerchantsDTO();
        pageQueryBrandMerchantsDto.setBrandIds(Lists.newArrayList(OpenApiContext.getBrandId()));
        pageQueryBrandMerchantsDto.setPage(Objects.isNull(request.getPage()) ? 1 : request.getPage());
        pageQueryBrandMerchantsDto.setPageSize(Objects.isNull(request.getPageSize()) || request.getPageSize() > 100 ? 100 : request.getPageSize());
        if (StringUtils.isNotBlank(request.getMerchantType())){
            pageQueryBrandMerchantsDto.setMerchantTypes(Lists.newArrayList(request.getMerchantType()));
        }
        PageBrandMerchantsDTO pageBrandMerchantsDTO = brandFacade.pageQueryBrandMerchants(pageQueryBrandMerchantsDto);
        OpenApiPageQueryBrandMerchantsResponse response = new OpenApiPageQueryBrandMerchantsResponse();
        response.setTotal(pageBrandMerchantsDTO.getTotal());
        response.setRecords(JSON.parseArray(JSON.toJSONString(pageBrandMerchantsDTO.getRecords()), OpenApiPageQueryBrandMerchantsResponse.QueryBrandMerchant.class));
        return BaseResponse.success(response);
    }
}
