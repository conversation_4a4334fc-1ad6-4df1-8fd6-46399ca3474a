package com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * <AUTHOR>
 */
public class RegisterBehaviorRecordInfoRequest extends VfinanceBaseRequest{
    /**
     * 会员id
     */
    @JSONField(name = "member_id")
    private String memberId;

    @JSONField(name = "function_flag")
    private String functionFlag;

    /**
     * 操作点击的时间(时间戳)	功能标志function_flag=1时，不可空,202308021165200
     */
    @JSONField(name = "op_click_time")
    private String opClickTime;
    /**
     *  IP地址
     *  功能标志function_flag=1时，不可空
     * 	*************
     */
    @JSONField(name = "ip_address")
    private String ipAddress;
    /**
     *  MAC地址
     *  功能标志function_flag=1时，不可空
     * 	5E:E3:90:4R:E5:33
     */
    @JSONField(name = "mac_address")
    private String macAddress;

    /**
     * 签约渠道
     * 	1-app 2-平台H5网页 3-公众号 4-小程序    功能标志FunctionFlag=1时必输	功能标志function_flag=1时，不可空
     * 	1
     */
    @JSONField(name = "signing_channe")
    private String signingChannel;
    /**
     * 保留域1，可空
     */
    @JSONField(name = "reserved_msg_one")
    private String reservedMsgOne;
    /**
     * 保留域2，可空
     */
    @JSONField(name = "reserved_msg_two")
    private String reservedMsgTwo;

    public RegisterBehaviorRecordInfoRequest(String partnerId) {
        super();
        super.service = REGISTER_BEHAVIOR_RECORD_INFO;
        super.partnerId = partnerId;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getFunctionFlag() {
        return functionFlag;
    }

    public void setFunctionFlag(String functionFlag) {
        this.functionFlag = functionFlag;
    }

    public String getOpClickTime() {
        return opClickTime;
    }

    public void setOpClickTime(String opClickTime) {
        this.opClickTime = opClickTime;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getSigningChannel() {
        return signingChannel;
    }

    public void setSigningChannel(String signingChannel) {
        this.signingChannel = signingChannel;
    }

    public String getReservedMsgOne() {
        return reservedMsgOne;
    }

    public void setReservedMsgOne(String reservedMsgOne) {
        this.reservedMsgOne = reservedMsgOne;
    }

    public String getReservedMsgTwo() {
        return reservedMsgTwo;
    }

    public void setReservedMsgTwo(String reservedMsgTwo) {
        this.reservedMsgTwo = reservedMsgTwo;
    }
}
