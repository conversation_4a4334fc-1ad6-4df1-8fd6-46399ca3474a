package com.wosai.cua.brand.business.service.module.merchant;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * 商户模型
 * <AUTHOR>
 */
public class MerchantModule {
    private String id;

    private String name;

    private String sn;

    private String alias;

    private String industry;

    private Integer status;

    private Integer type;

    @JSONField(name = "contact_name")
    private String contactName;

    @JSONField(name = "contact_phone")
    private String contactPhone;

    @JSONField(name = "contact_cellphone")
    private String contactCellphone;

    @JSONField(name = "contact_email")
    private String contactEmail;

    /**
     * 法人类型
     */
    @JSONField(name = "legal_person_type")
    private Integer legalPersonType;

    /**
     * 法人姓名
     */
    @JSONField(name = "legal_person_name")
    private String legalPersonName;

    /**
     * 营业执照注册号/个体户注册号
     */
    @JSONField(name = "legal_person_register_no")
    private String legalPersonRegisterNo;

    /**
     * 所有人姓名
     */
    @JSONField(name = "owner_name")
    private String ownerName;

    /**
     * 所有人手机号
     */
    @JSONField(name = "owner_cellphone")
    private String ownerCellphone;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactCellphone() {
        return contactCellphone;
    }

    public void setContactCellphone(String contactCellphone) {
        this.contactCellphone = contactCellphone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public Integer getLegalPersonType() {
        return legalPersonType;
    }

    public void setLegalPersonType(Integer legalPersonType) {
        this.legalPersonType = legalPersonType;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getLegalPersonRegisterNo() {
        return legalPersonRegisterNo;
    }

    public void setLegalPersonRegisterNo(String legalPersonRegisterNo) {
        this.legalPersonRegisterNo = legalPersonRegisterNo;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerCellphone() {
        return ownerCellphone;
    }

    public void setOwnerCellphone(String ownerCellphone) {
        this.ownerCellphone = ownerCellphone;
    }
}
