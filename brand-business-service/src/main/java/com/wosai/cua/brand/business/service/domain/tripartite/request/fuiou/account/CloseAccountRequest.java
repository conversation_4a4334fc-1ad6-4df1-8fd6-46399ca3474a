package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import lombok.Data;

@Data
public class CloseAccountRequest implements TripartiteSystemCallRequest {

    public static final String CLOSE_ACCOUNT_URI = "/closeAccount.fuiou";

    private CloseAccountRequestBody body;
    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_CLOSE_ACCOUNT;
    }
}
