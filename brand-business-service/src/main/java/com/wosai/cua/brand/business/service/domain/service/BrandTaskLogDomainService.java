package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.cua.brand.business.service.module.log.QueryLogConditionsModule;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandTaskLogDomainService {
    /**
     * 新增日志
     * @param module 日志模型
     * @return 插入数量
     */
    Long insertBrandTaskLog(BrandTaskLogModule module);

    /**
     * 更新日志
     * @param module 日志模型
     * @return 插入数量
     */
    int updateBrandTaskLog(BrandTaskLogModule module);

    /**
     * 统计品牌日志数量
     * @param conditionsModule 条件模型
     * @return 数量
     */
    Long countBrandTaskLog(QueryLogConditionsModule conditionsModule);

    /**
     * 分页查询品牌人物日志
     * @param conditionsModule 条件模型
     * @param page 当前页
     * @param pageSize 每页展示条数
     * @return 品牌任务日志列表
     */
    List<BrandTaskLogModule> pageSearchBrandTaskLog(QueryLogConditionsModule conditionsModule, int page,int pageSize);

    /**
     * 查询品牌导入任务日志集合
     * @param start 开始时间
     * @param size 查询条数
     * @return 品牌导入任务日志集合
     */
    List<BrandTaskLogModule> queryAuditImportTaskLog(List<Integer> taskTypes, String start, int size);

    /**
     * 批量插入子任务
     * @param brandSubTasks 子任务集合
     * @return 插入行数
     */
    int batchInsertBrandSubTasks(List<BrandSubTaskModule> brandSubTasks);

    /**
     * 根据任务ID查询子任务列表
     * @param taskId 任务ID
     * @return 子任务列表
     */
    List<BrandSubTaskModule> queryBrandSubTasksByTaskId(Long taskId);

    /**
     * 更新子任务
     * @param brandSubTaskModule 子任务
     * @return 影响行数
     */
    int updateBrandSubTask(BrandSubTaskModule brandSubTaskModule);
}
