package com.wosai.cua.brand.business.service.domain.tripartite.mybank;

import com.wosai.cua.brand.business.service.domain.tripartite.mybank.enums.MybankApiExceptionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping.SignChecker;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping.Signer;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.parse.xml.ObjectXmlParser;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.HttpsUtil;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.MybankLogger;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.WebUtils;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.DefaultRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MyBankRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankUploadRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantUploadPhotoRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.Security;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public abstract class AbstractMybankClient implements MyBankClient {

    private String serverUrl;

    private String format   = MybankConstants.FORMAT_XML;
    private String signType = MybankConstants.SIGN_TYPE_RSA;
    private String charset = MybankConstants.CHARSET_UTF8;

    private int connectTimeout = 3000;
    private int readTimeout    = 15000;

    static {
        Security.setProperty("jdk.certpath.disabledAlgorithms", MybankConstants.NULL_STRING);
    }

    public AbstractMybankClient(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public AbstractMybankClient(String serverUrl, String charset) {
        this.serverUrl = serverUrl;
        this.charset = charset;
    }

    public AbstractMybankClient(String serverUrl, String charset, String signType) {
        this(serverUrl, charset);
        if (!StringUtils.isEmpty(signType)) {
            this.signType = signType;
        }
    }

    public AbstractMybankClient(String serverUrl, String format, String charset, String signType) {
        this(serverUrl, charset);
        if (!StringUtils.isEmpty(format)) {
            this.format = format;
        }
        if (!StringUtils.isEmpty(signType)) {
            this.signType = signType;
        }
    }

    @Override
    public <T extends MybankResponse> T execute(MyBankRequest<T> request) throws MybankApiException {
        if (Objects.isNull(request)){
            throw new MybankApiException(MybankApiExceptionEnum.MESSAGE_ISNULL);
        }
        MybankParser<T> parser;
        if (MybankConstants.FORMAT_XML.equals(this.format)) {
            parser = new ObjectXmlParser<>(request.getResponseClass());
        } else {
            throw new MybankApiException(MybankApiExceptionEnum.NOT_XML_TYPE);
        }
        return execute(request, parser);
    }

    @Override
    public <T extends MybankResponse> T execute(MybankUploadRequest<T> request) throws MybankApiException {
        MybankParser<T> parser = new ObjectXmlParser<>(request.getResponseClass());
        return execute(request, parser);
    }

    private <T extends MybankResponse> T execute(DefaultRequest<T> request, MybankParser<T> parser) throws MybankApiException {
        long beginTime = System.currentTimeMillis();
        Map<String, Object> rt;
        if (request instanceof MyBankRequest){
            rt = doPost((MyBankRequest<T>) request);
        }else {
            rt = doPost((MybankUploadRequest<T>) request);
        }

        Map<String, Long> costTimeMap = new HashMap<>();
        if (rt.containsKey(MybankConstants.PREPARE_TIME)) {
            costTimeMap.put("prepareCostTime", (Long)(rt.get(MybankConstants.PREPARE_TIME)) - beginTime);
            if (rt.containsKey(MybankConstants.REQUEST_TIME)) {
                costTimeMap.put("requestCostTime", (Long)(rt.get(MybankConstants.REQUEST_TIME)) - (Long)(rt.get(MybankConstants.PREPARE_TIME)));
            }
        }
        T tRsp;
        try {
            tRsp = parser.parse((String)rt.get(MybankConstants.RSP_CONTENT));
            tRsp.setXmlContent((String)rt.get(MybankConstants.RSP_CONTENT));
            tRsp.setRequestContent((String)rt.get("requestContent"));

            checkResponseSign((String)rt.get(MybankConstants.RSP_CONTENT));

            if (costTimeMap.containsKey(MybankConstants.REQUEST_COST_TIME)) {
                costTimeMap.put("postCostTime", System.currentTimeMillis() - (Long)(rt.get(MybankConstants.REQUEST_TIME)));
            }
        } catch (RuntimeException e) {
            MybankLogger.logBizError((String)rt.get(MybankConstants.RSP_CONTENT), costTimeMap);
            throw new MybankApiException(e);
        } catch (MybankApiException e) {
            MybankLogger.logBizError((String)rt.get(MybankConstants.RSP_CONTENT), costTimeMap);
            throw e;
        }
        MybankLogger.logBizSummary(rt, costTimeMap);
        return tRsp;
    }

    private <T extends MybankResponse> Map<String, Object> doPost(MyBankRequest<T> request) throws MybankApiException {
        Map<String, Object> result = new HashMap<>();
        String xmlContent;
        try {
            xmlContent = request.xmlBuild();
        }catch (MybankApiException e){
            MybankLogger.logBizError(e);
            throw new MybankApiException(e);
        }
        if (request.isNeedSign()){
            try {
                xmlContent = getSigner().sign(xmlContent, this.charset, this.signType);
            }catch (MybankApiException e){
                MybankLogger.logBizError(e);
                throw new MybankApiException(e);
            }
        }
        result.put("requestContent", xmlContent);
        result.put("prepareTime", System.currentTimeMillis());

        String rsp;
        try {
            log.info("requestContent:{}", xmlContent);
            rsp = WebUtils.doPost(this.serverUrl, xmlContent, request.getCookieValue(), this.charset, this.connectTimeout,
            this.readTimeout, request.getApiFunction(), request.getApiVersion());
        } catch (MybankApiException e) {
            throw new MybankApiException(e);
        }
        result.put("requestTime", System.currentTimeMillis());
        result.put("rspContent", rsp);
        result.put("url", this.serverUrl);
        return result;
    }

    private <T extends MybankResponse> Map<String, Object> doPost(MybankUploadRequest<T> request) throws MybankApiException {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> map;
        try {
            map = request.getMapByModel();
        }catch (MybankApiException e){
            MybankLogger.logBizError(e);
            throw new MybankApiException(MybankApiExceptionEnum.SERVER_SYSTEM_EXCEPTION,e);
        }

        String signString = request.uploadRequestSignString(map);

        try {
            signString = getSigner().webSign(signString, this.charset, this.signType);
        }catch (MybankApiException e){
            MybankLogger.logBizError(e);
            throw new MybankApiException(MybankApiExceptionEnum.SERVER_SYSTEM_EXCEPTION,e);
        }

        if (request instanceof MerchantUploadPhotoRequest){
            MerchantUploadPhotoRequest uploadphotoRequest = (MerchantUploadPhotoRequest) request;
            uploadphotoRequest.getUploadphotoRequestModel().setSignature(signString);
        }

        map.put("Signature", signString);
        HttpEntity httpEntity = request.entityBuilder(map);
        ByteArrayOutputStream out = new ByteArrayOutputStream((int) httpEntity.getContentLength());
        try {
            httpEntity.writeTo(out);
            result.put("requestContent", out.toString());
        }catch (IOException e){
            MybankLogger.logBizError(e);
            throw new MybankApiException(MybankApiExceptionEnum.SERVER_SYSTEM_EXCEPTION,e);
        }finally{
            try {
                out.close();
            }catch (IOException e){
                MybankLogger.logBizError(e);
                throw new MybankApiException(MybankApiExceptionEnum.SERVER_SYSTEM_EXCEPTION,e);
            }
        }
        result.put("prepareTime", System.currentTimeMillis());

        String rsp = null;
        try {
            rsp = HttpsUtil.httpPost(this.serverUrl, httpEntity, this.charset, request.getApiFunction(), request.getApiVersion());
        } catch (MybankApiException e) {
            throw new MybankApiException(MybankApiExceptionEnum.REQUEST_REMOTE_SERVER_ERROR, e);
        }
        result.put("requestTime", System.currentTimeMillis());
        result.put("rspContent", rsp);
        result.put("url", this.serverUrl);
        return result;
    }

    private void checkResponseSign(String rspContent) throws MybankApiException {
        boolean checkResult = getSignChecker().check(rspContent, this.charset, this.signType);
        if (!checkResult) {
            throw new MybankApiException(MybankApiExceptionEnum.VERIFY_FAIL);
        }
    }

    void setServerUrl(String serverUrl) { this.serverUrl = serverUrl; }

    void setFormat(String format) { this.format = format; }

    void setSignType(String signType) { this.signType = signType; }

    void setCharset(String charset) { this.charset = charset; }

    void setConnectTimeout(int connectTimeout) { this.connectTimeout = connectTimeout; }

    void setReadTimeout(int readTimeout) { this.readTimeout = readTimeout; }

    abstract Signer getSigner();

    abstract SignChecker getSignChecker();
}