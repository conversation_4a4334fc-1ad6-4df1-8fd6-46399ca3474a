package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.account.AccountsModule;
import com.wosai.cua.brand.business.service.module.account.BrandAccountModule;

/**
 * <AUTHOR>
 * @description 针对表【brand_account(品牌相关账户汇总表)】的数据库操作Service
 * @createDate 2024-07-17 17:02:29
 */
public interface BrandAccountDomainService {

    void insertBrandAccount(BrandAccountModule brandAccountModule);

    <T extends AccountsModule> T getAccountsByBrandId(String brandId, Class<T> clazz);

    void dealPabAccount();

    BrandAccountModule getBrandAccountModuleByBrandId(String brandId);

    void updateBrandAccount(BrandAccountModule brandAccountModule);
}
