package com.wosai.cua.brand.business.service.kafka;

import com.google.common.collect.Lists;
import io.confluent.kafka.serializers.AbstractKafkaAvroSerDeConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BrandBaseKafkaProducer extends ParentKafkaProducer {

    @Value("${brand.kafka.bootstrap-servers}")
    private String bootstrapServersConfig;

    @Value("${brand.kafka.schema.registry.url}")
    private String schemaRegistryUrl;

    @Value("${brand.kafka.base.topic}")
    private String topic;

    @Override
    protected KafkaProducer<String, Object> getKafkaProducer() {
        return this.kafkaProducer;
    }

    @Override
    public void afterPropertiesSet() {
        log.info("BrandBaseKafkaProducer kafka bootstrap-servers is {}",bootstrapServersConfig);
        properties = new Properties();
        String[] configArr = bootstrapServersConfig.split(",");
        properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "io.confluent.kafka.serializers.KafkaAvroSerializer");
        properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "io.confluent.kafka.serializers.KafkaAvroSerializer");
        properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, Lists.newArrayList(configArr));
        properties.put(AbstractKafkaAvroSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG, schemaRegistryUrl);
        kafkaProducer = new KafkaProducer<>(properties);
    }

    public String getTopic() {
        return topic;
    }
}
