package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.enums.MerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryBrandMerchantResponse extends BaseOpenApiResponse{
    /**
     * 品牌id
     */
    @JsonProperty("brand_id")
    private String brandId;
    /**
     * 品牌名称
     */
    @JsonProperty("brand_name")
    private String brandName;
    /**
     * 品牌编号
     */
    @JsonProperty("brand_sn")
    private String brandSn;
    /**
     * 商户名称
     */
    @JsonProperty("merchant_name")
    private String merchantName;

    /**
     * 商户经营名称
     */
    @JsonProperty("merchant_business_name")
    private String merchantBusinessName;

    /**
     * 商户类型
     * @see MerchantTypeEnum
     */
    @JsonProperty("merchant_type")
    private String merchantType;

    @JsonProperty("merchant_type_desc")
    private String merchantTypeDesc;

    /**
     * 商户对接模式
     */
    @JsonProperty("merchant_docking_mode")
    private MerchantDockingModeEnum merchantDockingMode;
    /**
     * 商户id
     */
    @JsonProperty("merchant_id")
    private String merchantId;
    /**
     * 商户编号
     */
    @JsonProperty("merchant_sn")
    private String merchantSn;

    /**
     * 商户联系人姓名
     */
    @JsonProperty("merchant_contact_name")
    private String merchantContactName;
    /**
     * 商户联系人电话
     */
    @JsonProperty("merchant_contact_phone")
    private String merchantContactPhone;

    /**
     * 商户联系人邮箱
     */
    @JsonProperty("merchant_contact_email")
    private String merchantContactEmail;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 街道地址
     */
    @JsonProperty("street_address")
    private String streetAddress;

    /**
     * 行业id
     */
    private String industry;

    /**
     * 行业名称
     */
    @JsonProperty("industry_name")
    private String industryName;
    /**
     * 收钱吧门店号
     */
    @JsonProperty("sqb_store_id")
    private String sqbStoreId;
    /**
     * 美团门店编号
     */
    @JsonProperty("mei_tuan_store_sn")
    private String meiTuanStoreSn;
    /**
     * 饿了么门店编号
     */
    @JsonProperty("elm_store_sn")
    private String elmStoreSn;
    /**
     * 外部商户编号
     */
    @JsonProperty("out_merchant_no")
    private String outMerchantNo;
    /**
     * 提现策略
     */
    @JsonProperty("withdraw_strategy_info")
    private OpenApiWithdrawStrategyInfoDTO withdrawStrategyInfo;
    /**
     * 提现银行卡信息
     */
    @JsonProperty("brand_merchant_bank_card")
    private OpenApiBrandMerchantBankCardResponse brandMerchantBankCard;
    /**
     * 商户执照信息
     */
    @JsonProperty("merchant_business_license_info")
    private OpenApiMerchantBusinessLicenseInfoDTO merchantBusinessLicenseInfo;

    private String type;

    /**
     * 商户账户开通状态
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum
     */
    @JsonProperty("account_open_status")
    private String accountOpenStatus;

    /**
     * 商户账户开通状态描述
     */
    @JsonProperty("account_open_status_desc")
    private String accountOpenStatusDesc;

    /**
     * 商户账号信息
     */
    @JsonProperty("merchant_account")
    private OpenApiMerchantAccountDTO merchantAccount;

    /**
     * 激活链接
     */
    @JsonProperty("activation_url")
    private String activationUrl;

    public static QueryBrandMerchantResponse builder(BrandMerchantInfoDTO brandMerchantInfo){
        if(brandMerchantInfo == null){
            return null;
        }
        QueryBrandMerchantResponse queryBrandMerchantResponse = new QueryBrandMerchantResponse();
        queryBrandMerchantResponse.setBrandId(brandMerchantInfo.getBrandId());
        queryBrandMerchantResponse.setBrandName(brandMerchantInfo.getBrandName());
        queryBrandMerchantResponse.setBrandSn(brandMerchantInfo.getBrandSn());
        queryBrandMerchantResponse.setMerchantName(brandMerchantInfo.getMerchantName());
        queryBrandMerchantResponse.setMerchantBusinessName(brandMerchantInfo.getMerchantBusinessName());
        queryBrandMerchantResponse.setMerchantType(brandMerchantInfo.getMerchantType());
        queryBrandMerchantResponse.setMerchantTypeDesc(brandMerchantInfo.getMerchantTypeDesc());
        queryBrandMerchantResponse.setMerchantDockingMode(brandMerchantInfo.getMerchantDockingMode());
        queryBrandMerchantResponse.setMerchantId(brandMerchantInfo.getMerchantId());
        queryBrandMerchantResponse.setMerchantSn(brandMerchantInfo.getMerchantSn());
        queryBrandMerchantResponse.setMerchantContactName(brandMerchantInfo.getMerchantContactName());
        queryBrandMerchantResponse.setMerchantContactPhone(brandMerchantInfo.getMerchantContactPhone());
        queryBrandMerchantResponse.setMerchantContactEmail(brandMerchantInfo.getMerchantContactEmail());
        queryBrandMerchantResponse.setCity(brandMerchantInfo.getCity());
        queryBrandMerchantResponse.setStreetAddress(brandMerchantInfo.getStreetAddress());
        queryBrandMerchantResponse.setIndustry(brandMerchantInfo.getIndustry());
        queryBrandMerchantResponse.setIndustryName(brandMerchantInfo.getIndustryName());
        queryBrandMerchantResponse.setSqbStoreId(brandMerchantInfo.getSqbStoreId());
        queryBrandMerchantResponse.setMeiTuanStoreSn(brandMerchantInfo.getMeiTuanStoreSn());
        queryBrandMerchantResponse.setElmStoreSn(brandMerchantInfo.getElmStoreSn());
        queryBrandMerchantResponse.setOutMerchantNo(brandMerchantInfo.getOutMerchantNo());
        queryBrandMerchantResponse.setAccountOpenStatus(brandMerchantInfo.getAccountOpenStatus());
        queryBrandMerchantResponse.setAccountOpenStatusDesc(brandMerchantInfo.getAccountOpenStatusDesc());
        queryBrandMerchantResponse.setActivationUrl(brandMerchantInfo.getActivationUrl());
        return queryBrandMerchantResponse;
    }

}
