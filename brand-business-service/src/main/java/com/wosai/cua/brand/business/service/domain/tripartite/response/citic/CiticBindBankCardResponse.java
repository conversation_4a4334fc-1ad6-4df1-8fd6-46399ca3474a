package com.wosai.cua.brand.business.service.domain.tripartite.response.citic;

import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data.BindBankCardResponseData;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "ROOT")
public class CiticBindBankCardResponse extends CiticResponse<BindBankCardResponseData>{

    public CiticBindBankCardResponse() {
    }

    public CiticBindBankCardResponse(String code, String message) {
        super(code, message);
    }

    @Override
    @XmlElement(name = "CODE")
    public String getCode() {
        return super.getCode();
    }

    @Override
    @XmlElement(name = "MESSAGE")
    public String getMessage() {
        return super.getMessage();
    }

    @Override
    public void setCode(String code) {
        super.setCode(code);
    }

    @Override
    public void setMessage(String message) {
        super.setMessage(message);
    }

    @XmlElement(name = "DATA")
    @Override
    public BindBankCardResponseData getData() {
        return data;
    }

    @Override
    public void setData(BindBankCardResponseData data) {
        this.data = data;
    }

}
