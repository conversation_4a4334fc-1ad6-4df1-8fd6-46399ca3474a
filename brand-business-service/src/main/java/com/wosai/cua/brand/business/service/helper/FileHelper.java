package com.wosai.cua.brand.business.service.helper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.annotations.ExcelColumn;
import com.wosai.cua.brand.business.api.enums.ExcelConvertValueEnum;
import com.wosai.data.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.io.Writer;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Slf4j
public class FileHelper {

    public static File excelToFile(Workbook workbook, String filePath) throws IOException {
        File file = createDirFile(filePath);
        FileOutputStream fileOutStream = null;
        try {
            fileOutStream = new FileOutputStream(file);
        } catch (Exception e) {
            log.warn("FileHelper#excelToFile Exception", e);
        }
        assert fileOutStream != null;
        BufferedOutputStream out = new BufferedOutputStream(fileOutStream);
        workbook.write(out);
        out.flush();
        out.close();
        return file;
    }

    public static void iteratorMkdir(File file) {
        if (file == null) {
            return;
        }
        while (!file.getParentFile().exists()) {
            iteratorMkdir(file.getParentFile());
            file.getParentFile().mkdirs();
        }
    }

    public static File createDirFile(String pathname) {
        File file = new File(pathname);
        iteratorMkdir(file);
        return new File(pathname);

    }

    public static String formartStatementPathFile(String dir, String type, long start, long end, String ext) {
        return dir + String.format("%s%s_%s.%s",
                type,
                new SimpleDateFormat("yyyy-MM-dd").format(new Date(start)),
                new SimpleDateFormat("yyyy-MM-dd").format(new Date(end)),
                ext);
    }

    public static String hash(byte[] bytes) {

        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            byte[] hashedBytes = digest.digest(bytes);

            return convertByteArrayToHexString(hashedBytes);
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    private static String convertByteArrayToHexString(byte[] arrayBytes) {
        StringBuilder sb = new StringBuilder();
        for (byte arrayByte : arrayBytes) {
            sb.append(Integer.toString((arrayByte & 0xff) + 0x100, 16).substring(1));
        }
        return sb.toString();
    }

    public static void closeStream(InputStream inputStream) {
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.warn("FileHelper#closeStream error", e);
            }
        }
    }

    public static void closeStream(OutputStream outputStream) {
        if (outputStream != null) {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.warn("FileHelper#closeStream error", e);
            }
        }
    }

    public static Map<Integer, List<String[]>> readXlsx(String fileName) {
        Map<Integer, List<String[]>> map = Maps.newHashMap();
        try {
            Path path = Paths.get(fileName);
            InputStream is = Files.newInputStream(path);
            Workbook workbook;
            try {
                workbook = new XSSFWorkbook(is);
            } catch (Exception ex) {
                workbook = new HSSFWorkbook(Files.newInputStream(path));
            }
            //循环工作表Sheet
            for (int numSheet = 0; numSheet < workbook.getNumberOfSheets(); numSheet++) {

                Sheet xssfSheet = workbook.getSheetAt(numSheet);
                if (xssfSheet == null) {
                    continue;
                }
                List<String[]> list = new ArrayList<>();

                for (int row = 0; row <= xssfSheet.getLastRowNum(); row++) {
                    Row xssfRow = xssfSheet.getRow(row);
                    if (xssfRow == null) {
                        continue;
                    }
                    try {
                        String[] singleRow = new String[xssfRow.getLastCellNum()];
                        for (int column = 0; column < xssfRow.getLastCellNum(); column++) {
                            Cell cell = xssfRow.getCell(column, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                            switch (cell.getCellType()) {
                                case BLANK:
                                    singleRow[column] = "";
                                    break;
                                case BOOLEAN:
                                    singleRow[column] = Boolean.toString(cell.getBooleanCellValue());
                                    break;
                                case ERROR:
                                    singleRow[column] = "";
                                    break;
                                case FORMULA:
                                    cell.setCellType(CellType.STRING);
                                    singleRow[column] = cell.getStringCellValue();
                                    if (singleRow[column] != null) {
                                        singleRow[column] = singleRow[column].replace("#N/A", "").trim();
                                    }
                                    break;
                                case NUMERIC:
                                    if (DateUtil.isCellDateFormatted(cell)) {
                                        singleRow[column] = String.valueOf(cell.getDateCellValue());
                                    } else {
                                        cell.setCellType(CellType.STRING);
                                        String temp = cell.getStringCellValue();
                                        //判断是否包含小数点，如果不含小数点，则以字符串读取，如果含小数点，则转换为Double类型的字符串
                                        if (temp.contains(".")) {
                                            singleRow[column] = String.valueOf(new Double(temp)).trim();
                                        } else {
                                            singleRow[column] = temp.trim();
                                        }
                                    }

                                    break;
                                case STRING:
                                    singleRow[column] = cell.getStringCellValue().trim();
                                    break;
                                default:
                                    singleRow[column] = "";
                                    break;
                            }
                        }
                        list.add(singleRow);
                    } catch (Exception e) {
                        log.error("readXlsx is error", e);
                    }
                }
                map.put(numSheet, list);

            }
        } catch (Exception e) {
            log.error("readXlsx is error", e);
        }
        return map;
    }

    public static void writeCsvLine(Writer out, String... params) throws IOException {
        String line = null;
        for (int i = 0; i < params.length; i++) {
            if (StringUtil.empty(params[0])) {
                return;
            }
            if (i == 0) {
                line = params[0];
                continue;
            }
            line = String.format("%s,%s", line, params[i] + "\t");
        }
        out.write(String.format("%s\n", line));
        out.flush();
    }

    /**
     * 添加错误信息至excel表
     *
     * @param filePath  文件路径
     * @param sheetNum  sheet页编号
     * @param colNum    列
     * @param rowMsgMap 行号对应的错误信息集合
     */
    public static void createExcelErrorMsg(String filePath, int sheetNum, int colNum, Map<Integer, String> rowMsgMap) {
        if (MapUtils.isEmpty(rowMsgMap)) {
            return;
        }
        try {
            FileInputStream file = new FileInputStream(filePath);
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(sheetNum);
            rowMsgMap.forEach((rowNum, errorMsg) -> {
                if (Objects.isNull(sheet)) {
                    return;
                }
                Row row = sheet.getRow(rowNum);
                if (Objects.isNull(row)) {
                    return;
                }
                // 创建或获取单元格
                Cell cell = row.createCell(colNum);
                // 设置单元格类型为字符串
                cell.setCellType(CellType.STRING);
                // 向单元格写入数据
                cell.setCellValue(errorMsg);
            });
            file.close();
            FileOutputStream outFile = new FileOutputStream(filePath);
            workbook.write(outFile);
            outFile.close();
            workbook.close();
        } catch (IOException e) {
            log.error("写入excel异常");
        }
    }

    public static void createExcel(String filePath, String sheetName, List<Map<Integer, String>> context) {
        if (CollectionUtils.isEmpty(context)) {
            return;
        }
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream file = new FileOutputStream(filePath)){
            Sheet sheet = workbook.createSheet(sheetName);
            AtomicInteger rowNum = new AtomicInteger();
            context.forEach(rowContext -> {
                if (MapUtils.isEmpty(rowContext)) {
                    return;
                }
                Row row = sheet.createRow(rowNum.get());
                rowContext.forEach((cellNum, cellContext) -> {
                    // 创建或获取单元格
                    Cell cell = row.createCell(cellNum);
                    // 设置单元格类型为字符串
                    cell.setCellType(CellType.STRING);
                    // 向单元格写入数据
                    cell.setCellValue(cellContext);
                });
                rowNum.getAndIncrement();
            });
            workbook.write(file);
        } catch (IOException e) {
            log.error("写入excel异常");
        }
    }

    public static Map<Long, List<String>> getCsvFileData(String filePath) {
        Map<Long, List<String>> result = Maps.newHashMap();
        try (Reader reader = new FileReader(filePath);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT)) {
            for (CSVRecord csvRecord : csvParser) {
                if (csvRecord.get(0).trim().startsWith("#")) {
                    continue;
                }
                List<String> lineResults = Lists.newArrayList();
                for (String s : csvRecord) {
                    lineResults.add(s);
                }
                result.put(csvRecord.getRecordNumber(), lineResults);
            }
        } catch (IOException e) {
            log.error("获取文件异常。", e);
        }
        return result;
    }

    public static <T> List<T> parseExcel(String filePath, int sheetNum, int startRowNum, Class<T> clazz) {
        // 参数校验
        if (filePath == null || filePath.isEmpty()) {
            throw new IllegalArgumentException("File path cannot be null or empty.");
        }
        if (sheetNum < 0) {
            throw new IllegalArgumentException("Sheet number cannot be negative.");
        }
        if (startRowNum < 0) {
            throw new IllegalArgumentException("Start row number cannot be negative.");
        }

        List<T> result = Lists.newArrayList();

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            // 获取指定的工作表
            if (workbook.getNumberOfSheets() <= sheetNum) {
                throw new IllegalArgumentException("Invalid sheet number: " + sheetNum);
            }
            Sheet sheet = workbook.getSheetAt(sheetNum);

            // 精确迭代从 startRowNum 开始的行
            for (int rowNum = startRowNum; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    continue; // 跳过空行
                }

                // 使用 Constructor 创建实例
                Constructor<T> constructor = clazz.getDeclaredConstructor();
                if (!constructor.isAccessible()) {
                    constructor.setAccessible(true); // 允许访问私有构造函数
                }
                T instance = constructor.newInstance();

                // 处理字段
                processFields(instance, clazz, row);
                result.add(instance);
            }
        } catch (ReflectiveOperationException | IOException e) {
            log.error("解析excel异常。", e);
        }

        return result;
    }

    private static <T> void processFields(T instance, Class<?> clazz, Row row) throws IllegalAccessException {
        if (clazz == null) {
            return;
        }

        // 处理当前类的字段
        for (Field field : clazz.getDeclaredFields()) {
            processField(instance, row, field);
        }

        // 递归处理父类的字段
        processFields(instance, clazz.getSuperclass(), row);
    }

    private static <T> void processField(T instance, Row row, Field field) throws IllegalAccessException {
        if (field.isAnnotationPresent(ExcelColumn.class)) {
            ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
            int colIndex = annotation.col();
            Cell cell = row.getCell(colIndex);

            if (cell != null) {
                field.setAccessible(true);
                String cellValue = getCellValue(cell);
                if (annotation.convertValue()) {
                    field.set(instance, ExcelConvertValueEnum.getConvertValue(cellValue));
                }else{
                    field.set(instance, cellValue);
                }
                if (annotation.needTrim()) {
                    field.set(instance, cellValue.replaceAll("\\s+",""));
                }
            }
        }
    }


    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue()).trim();
                } else {
                    return String.valueOf(cell.getNumericCellValue()).trim();
                }
            case BOOLEAN:
                return Boolean.toString(cell.getBooleanCellValue()).trim();
            case FORMULA:
                try {
                    String formulaValue = cell.getCellFormula();
                    if (formulaValue != null) {
                        cell.setCellFormula(formulaValue);
                        String value = cell.getStringCellValue();
                        return value.replace("#N/A", "").trim();
                    }
                } catch (Exception e) {
                    log.error("Error evaluating formula cell", e);
                }
                return "";
            default:
                return "";
        }
    }

}

