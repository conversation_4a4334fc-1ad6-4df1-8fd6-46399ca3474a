package com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.account;

import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.MybankObject;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.MybankResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.model.BkcloudfundsAccountOpenResponseModel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Mybank Api: ant.mybank.bkcloudfunds.account.open
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
public class BkCloudFundsAccountOpenResponse extends MybankResponse {

    private static final long serialVersionUID = 6939972059653721930L;
    @XmlElementRef
    private BkcloudfundsAccountOpen bkcloudfundsAccountOpen;

    public BkcloudfundsAccountOpen getBkcloudfundsAccountOpen() {
        return bkcloudfundsAccountOpen;
    }

    public void setBkcloudfundsAccountOpen(BkcloudfundsAccountOpen bkcloudfundsAccountOpen) {
        this.bkcloudfundsAccountOpen = bkcloudfundsAccountOpen;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "response")
    public static class BkcloudfundsAccountOpen extends MybankObject {

        private static final long serialVersionUID = -8627134867540044397L;
        /**
         * 公共应答参数（head）
         */
        @XmlElementRef
        private ResponseHead responseHead;

        /**
         * 业务应答参数（body）
         */
        @XmlElementRef
        private BkcloudfundsAccountOpenResponseModel bkcloudfundsAccountOpenResponseModel;

        public ResponseHead getResponseHead() {
            return responseHead;
        }

        public void setResponseHead(ResponseHead responseHead) {
            this.responseHead = responseHead;
        }

        public BkcloudfundsAccountOpenResponseModel getBkcloudfundsAccountOpenResponseModel() {
            return bkcloudfundsAccountOpenResponseModel;
        }

        public void setBkcloudfundsAccountOpenResponseModel(BkcloudfundsAccountOpenResponseModel bkcloudfundsAccountOpenResponseModel) {
            this.bkcloudfundsAccountOpenResponseModel = bkcloudfundsAccountOpenResponseModel;
        }
    }
}