package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
public class ChangeDefaultBankCardRequest extends OpenApiBaseRequest {

    @NotBlank(message = "银行卡id必填")
    @JsonProperty("bank_card_id")
    private String bankCardId;
}
