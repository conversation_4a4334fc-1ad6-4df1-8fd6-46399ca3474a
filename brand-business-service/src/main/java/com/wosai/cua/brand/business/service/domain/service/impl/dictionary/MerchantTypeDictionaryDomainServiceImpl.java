package com.wosai.cua.brand.business.service.domain.service.impl.dictionary;

import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.DictionaryDomainService;
import com.wosai.cua.brand.business.service.module.common.dictionary.DictionaryModule;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MerchantTypeDictionaryDomainServiceImpl implements DictionaryDomainService {

    /**
     * 字典名称
     */
    private static final String DICTIONARY = "MERCHANT_TYPE";

    @Override
    public String dictionary() {
        return DICTIONARY;
    }

    @Override
    public List<DictionaryModule> getDictionaryModules() {
        List<DictionaryModule> list = Lists.newArrayList();
        Iterator<MerchantTypeEnum> iterator = Arrays.stream(MerchantTypeEnum.values()).iterator();
        while (iterator.hasNext()) {
            MerchantTypeEnum next = iterator.next();
            if (next.equals(MerchantTypeEnum.BRAND_ADMIN) || next.equals(MerchantTypeEnum.SERVICE_PROVIDER_SQB)){
                continue;
            }
            list.add(new DictionaryModule(next.getMerchantType(),next.getDesc()));
        }
        return list;
    }
}
