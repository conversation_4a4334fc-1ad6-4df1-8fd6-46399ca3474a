package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OpenApiBaseRequest {

    /**
     * 应用ID，品牌向收钱吧申请后获取
     */
    @JsonProperty("appid")
    protected String appId;

    /**
     * 品牌编号
     */
    @JsonProperty("brand_sn")
    protected String brandSn;

    /**
     * 品牌ID
     */
    @JsonProperty("brand_id")
    protected String brandId;

    /**
     * 请求流水号
     */
    @JsonProperty("client_sn")
    protected String clientSn;
}
