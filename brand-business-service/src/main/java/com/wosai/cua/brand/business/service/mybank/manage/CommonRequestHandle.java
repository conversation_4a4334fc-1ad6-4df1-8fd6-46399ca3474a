package com.wosai.cua.brand.business.service.mybank.manage;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.MybankConstants;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.enums.MybankApiExceptionEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.mapping.DefaultSigner;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.JaxbUtil;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.util.MybankSignature;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.Response;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.ResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.ResponseDocument;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify.BkcloudfundsNotifyResponseBody;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.RespInfo;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.ResponseHead;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * 统一处理
 **/
@Component
@Slf4j
public class CommonRequestHandle {

    private final BrandConfigDomainService brandConfigDomainService;

    @Autowired
    public CommonRequestHandle(BrandConfigDomainService brandConfigDomainService) {
        this.brandConfigDomainService = brandConfigDomainService;
    }

    public MyBankConfigModule getConfig(String xmlContext) throws MybankApiException {
        String notifyAppId = this.getNotifyAppId(xmlContext);
        return this.getMyBankConfigModule(notifyAppId);
    }

    private MyBankConfigModule getMyBankConfigModule(String appId) throws MybankApiException {
        BrandConfigModule configModule = brandConfigDomainService.getBrandConfigByChannelIdAndChannelType(appId, FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
        if (Objects.isNull(configModule) || StringUtils.isBlank(configModule.getConfig())) {
            throw new MybankApiException(MybankApiExceptionEnum.NOTICE_NOT_MATCH);
        }
        return JSON.parseObject(configModule.getConfig(), MyBankConfigModule.class);
    }

    public String getXmlContextString(HttpServletRequest httpServletRequest) throws BrandBusinessException {
        StringWriter writer = new StringWriter();
        BufferedReader reader = null;
        try {
            reader = httpServletRequest.getReader();
            char[] chars = new char[256];
            int count;
            while ((count = reader.read(chars)) > 0) {
                writer.write(chars, 0, count);
            }
        } catch (Exception e) {
            log.error("解析xml报文异常。", e);
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR);
                }
            }
        }
        return writer.toString();
    }

    /**
     * @param xmlContext 请求的xml报文
     * @Description 对通知报文进行校验
     **/
    public void checkNotifyRequest(String xmlContext) throws MybankApiException {
        //验签操作
        boolean signBool = checkSign(xmlContext);
        if (!signBool) {
            throw new MybankApiException(MybankApiExceptionEnum.VERIFY_FAIL);
        }
        //通过验证appId,确定通知是发送到本isv的。
        String notifyAppId = this.getNotifyAppId(xmlContext);
        BrandConfigModule configModule = brandConfigDomainService.getBrandConfigByChannelIdAndChannelType(notifyAppId, FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
        if (Objects.isNull(configModule) || StringUtils.isBlank(configModule.getConfig())) {
            throw new MybankApiException(MybankApiExceptionEnum.NOTICE_NOT_MATCH);
        }
    }

    /**
     * @param xmlContext 请求的xml报文
     * @return 验签结果
     * @Description 对通知报文验签
     **/
    public boolean checkSign(String xmlContext) throws MybankApiException {
        MyBankConfigModule config = this.getConfig(xmlContext);
        //验签
        boolean check;
        try {
            check = MybankSignature.check(xmlContext, config.getPublicKey(), MybankConstants.CHARSET_UTF8, MybankConstants.SIGN_TYPE_RSA);
        } catch (MybankApiException e) {
            throw new MybankApiException(e);
        }
        return check;
    }

    /**
     * @param xmlContext 请求的xml报文
     * @return TreeMap 请求头信息
     * @Description 获取请求头的信息
     **/
    public SortedMap<String, String> getNotifyHead(String xmlContext) throws MybankApiException {
        TreeMap<String, String> headMap = new TreeMap<>();
        Document doc = MybankSignature.parseDocumentByString(xmlContext);
        Element root = doc.getDocumentElement();
        Node head = root.getElementsByTagName(MybankConstants.HEAD).item(0);
        NodeList childNodes = head.getChildNodes();
        for (int i = 0; i < childNodes.getLength(); i++) {
            if (childNodes.item(i).getNodeType() == Node.ELEMENT_NODE) {
                headMap.put(childNodes.item(i).getNodeName(), childNodes.item(i).getTextContent());
            }
        }
        return headMap;
    }

    /**
     * @param xmlContext 请求的xml报文
     * @return appId
     * @Description 获取请求头中的appId信息
     **/
    public String getNotifyAppId(String xmlContext) throws MybankApiException {
        SortedMap<String, String> notifyHead = getNotifyHead(xmlContext);
        String notifyAppId;
        notifyAppId = notifyHead.get(MybankConstants.APPID);
        if (null == notifyAppId) {
            notifyAppId = notifyHead.get(MybankConstants.APP_ID);
        }
        return notifyAppId;
    }

    /**
     * @param flag 持久化处理结果
     * @param head 请求头信息
     * @return 加签后的响应串
     * @Description 统一处理响应报文加签
     **/
    public String getSignResult(boolean flag, RequestHead head) throws MybankApiException {
        RespInfo respInfo = new RespInfo();
        if (flag) {
            respInfo.setResultCode("0000");
            respInfo.setResultMsg("成功");
            respInfo.setResultStatus("S");
        } else {
            respInfo.setResultCode("9000");
            respInfo.setResultMsg("失败");
            respInfo.setResultStatus("F");
        }
        BkcloudfundsNotifyResponseBody responseBody = new BkcloudfundsNotifyResponseBody();
        responseBody.setRespInfo(respInfo);
        return responseAndSign(head, responseBody);
    }

    /**
     * @param head         通知报文的RequestHead
     * @param responseBody 封装的响应body
     * @return 加签后的响应串
     * @Description 统一处理响应报文加签
     **/
    public String responseAndSign(RequestHead head, ResponseBody responseBody) throws MybankApiException {
        ResponseHead responseHead;
        String appId;
        if (null != head.getAppid()) {
            responseHead = ResponseHead.builder(head.getVersion(), null, head.getAppid(), head.getFunction(), head.getReqMsgId())
                    .inputCharset(MybankConstants.CHARSET_UTF8)
                    .respTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(MybankConstants.DATE_TIME_FORMAT)))
                    .respTimeZone(MybankConstants.DATE_TIMEZONE)
                    .reserve(MybankConstants.NULL_STRING)
                    .signType(MybankConstants.SIGN_TYPE_RSA)
                    .build();
            appId = head.getAppid();
        } else {
            responseHead = ResponseHead.builder(head.getVersion(), head.getAppId(), null, head.getFunction(), head.getReqMsgId())
                    .inputCharset(MybankConstants.CHARSET_UTF8)
                    .respTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(MybankConstants.DATE_TIME_FORMAT)))
                    .respTimeZone(MybankConstants.DATE_TIMEZONE)
                    .reserve(MybankConstants.NULL_STRING)
                    .signType(MybankConstants.SIGN_TYPE_RSA)
                    .build();
            appId = head.getAppId();
        }

        ResponseDocument responseDocument = new ResponseDocument(new Response(responseHead, responseBody));
        MyBankConfigModule myBankConfigModule = this.getMyBankConfigModule(appId);
        DefaultSigner signer = new DefaultSigner(myBankConfigModule.getIsvPrivateKey());
        String result;
        String xml;
        try {
            xml = JaxbUtil.convertToXml(responseDocument, MybankConstants.CHARSET_UTF8);
            result = signer.notifyResponseSign(xml, MybankConstants.CHARSET_UTF8, MybankConstants.SIGN_TYPE_RSA);
        } catch (MybankApiException e) {
            throw new MybankApiException(e);
        }
        return result;
    }

    /**
     * @param flag 持久化处理结果
     * @param head 请求头信息
     * @return 打款结果通知加签后的响应串
     * @Description 打款结果通知响应报文加签
     **/
//    public String getNotifyPayResult(boolean flag, RequestHead head) throws MybankApiException {
//        BkmerchantSettleNotifyPayResultResponseBody notifyPayResultResponseBody = new BkmerchantSettleNotifyPayResultResponseBody();
//        notifyPayResultResponseBody.setIsvOrgId(MybankConfig.isvOrgId);
//        if (flag) {
//            notifyPayResultResponseBody.setResponseCode(MybankConstants.OK);
//        } else {
//            notifyPayResultResponseBody.setResponseCode(MybankConstants.FAIL);
//        }
//        return responseAndSign(head, notifyPayResultResponseBody);
//    }
}
