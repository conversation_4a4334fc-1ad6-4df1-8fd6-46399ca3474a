package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OpenApiBrandMerchantBankCardResponse {
    /**
     * 品牌id
     */
    @JsonProperty("brand_id")
    private String brandId;

    /**
     * 商户id
     */
    @JsonProperty("merchant_id")
    private String merchantId;
    /**
     * 收钱吧银行卡id
     */
    @JsonProperty("bank_card_id")
    private String bankCardId;

    /**
     * 银行预留手机号
     */
    @JsonProperty("reserved_mobile_number")
    private String reservedMobileNumber;

    /**
     * 银行开户名
     */
    @JsonProperty("bank_account_name")
    private String bankAccountName;

    /**
     * 银行卡号
     */
    @JsonProperty("bank_card_number")
    private String bankCardNumber;

    /**
     * 联行号/大小额行号/开户行行号
     */
    @JsonProperty("opening_number")
    private String openingNumber;

    /**
     * 账户类型：1-个人，2-企业
     */
    @JsonProperty("account_type")
    private Integer accountType;

    /**
     * 三方系统返回的银行卡id
     */
    @JsonProperty("third_bank_card_id")
    private String thirdBankCardId;

    /**
     * 三方系统会员id
     */
    @JsonProperty("member_id")
    private String memberId;

    /**
     * 是否是默认卡：0-否，1-是
     */
    @JsonProperty("is_default")
    private Boolean isDefault;
    /**
     * 激活状态
     */
    @JsonProperty("activate_status")
    private String activateStatus;

    /**
     * 银行名称
     */
    @JsonProperty("bank_name")
    private String bankName;
    /**
     * 支行名称
     */
    @JsonProperty("branch_name")
    private String branchName;

    /**
     * 激活时间
     */
    @JsonProperty("activation_time")
    private Date activationTime;
}
