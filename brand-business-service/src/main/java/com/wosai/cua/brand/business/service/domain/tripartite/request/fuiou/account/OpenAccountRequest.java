package com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account;

import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.tripartite.fuiou.enums.ProtocolTypeEnum;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantCreationRecordModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import lombok.Data;

import java.util.Objects;

@Data
public class OpenAccountRequest implements TripartiteSystemCallRequest {

    public static final String OPEN_ACCOUNT_URL = "/V2/openAccount.fuiou";

    private OpenAccountRequestBody body;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.FUIOU_OPEN_ACCOUNT;
    }

    public static OpenAccountRequestBody buildBody(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense , BrandMerchantModule brandMerchantModule, FuiouConfigModule configModule, BrandMerchantCreationRecordModule recordModule) {
        if (Objects.isNull(brandMerchantModule.getBankCardInfo())){
            throw new BrandBusinessException("富友通道银行卡信息不能为空!");
        }
        OpenAccountRequestBody body = new OpenAccountRequestBody(configModule.getMerchantNo());
        body.setTraceNo(recordModule.getRecordId());
        // 账户专用协议
        body.setProtocolType(ProtocolTypeEnum.ACCOUNT_SPECIFIC_AGREEMENT.getProtocolType());
        body.setMchntCdUserId(brandMerchantModule.getThreePartyMerchantSn());
        body.setOutAcntNm(merchant.getName());
        body.setMobile(brandMerchantModule.getBankCardInfo().getMobile());
        body.setOutAcntNo(brandMerchantModule.getBankCardInfo().getBankCardNo());
        body.setInterBankNo(brandMerchantModule.getBankCardInfo().getOpeningBankNumber());
        body.setCheckType(configModule.getCheckType());
        switch (merchantBusinessLicense.getType()) {
            case 0:
                // 个人户
                body.setCleanType("01");
                if (merchantBusinessLicense.getLegal_person_id_type() == 1){
                    body.setCertTp("0");
                }else {
                    body.setCertTp("1");
                }
                body.setCertNo(merchantBusinessLicense.getLegal_person_id_number());
                break;
            case 1:
                // 个体工商户
                body.setCleanType("03");
                body.setCertTp("1");
                body.setCertNo(merchantBusinessLicense.getNumber());
                body.setOutAcntNoType("01");
                body.setOutAcntNo(brandMerchantModule.getBankCardInfo().getBankCardNo());
                body.setLegalName(merchantBusinessLicense.getLegal_person_name());
                body.setLegalMobile(merchant.getContact_cellphone());
                body.setLegalCertTp("0");
                body.setLegalCertNo(merchantBusinessLicense.getLegal_person_id_number());
                break;
            case 2:
                // 企业
                body.setCleanType("02");
                body.setCertTp("1");
                body.setCertNo(merchantBusinessLicense.getNumber());
                break;
            default:
                throw new BrandBusinessException("未知的商户类型");
        }
        return body;
    }
}
