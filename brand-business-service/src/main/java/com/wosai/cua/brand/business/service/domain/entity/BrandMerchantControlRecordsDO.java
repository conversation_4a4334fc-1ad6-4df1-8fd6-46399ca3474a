package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 品牌商户管控记录表
 * @TableName brand_merchant_control_records
 */
@TableName(value ="brand_merchant_control_records")
@Data
public class BrandMerchantControlRecordsDO implements Serializable {
    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String brandId;

    /**
     * 外部交易号（来自或调用第三方系统的请求编号）
     */
    private String outTradeNo;

    /**
     * 子账号
     */
    private String subAccountNo;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 管控单号
     */
    private String controlOrderNo;

    /**
     * 管控通知类型：CONTROL-管控，RELEASE_CONTROL-解管控，WEAK_CONTROL-弱管控，DELAY_CONTROL-延迟管控
     */
    private String controlNotifyType;

    /**
     * 消息通知时间
     */
    private Long punishDate;

    /**
     * 管控类型：IDENTITY_CATEGORY-身份类，JUDICIAL_CATEGORY-司法类，RISK_CATEGORY-风险类
     */
    private String controlCategory;

    /**
     * 管控策略：CANNOT_OUT-不能出金，CANNOT_IN-不能入金，CANNOT_IN_OUT-不能出入金，UNLIMITED-不限制
     */
    private String controlStrategy;

    /**
     * 管控原因：PERSON_ONLINE_AUTH ：个人联网核查、PERSON_AML：个人反洗钱检查， IDENTITY_CARD_AUTH：身份证影像件认证，IDENTITY_CARD_EXPIRE：身份证过期
     */
    private String changeReason;

    /**
     * 是否可引导解限：0-不可以，1-可以
     */
    private Integer canUpgrade;

    /**
     * 升级链接解限页面
     */
    private String upgradeUrl;

    /**
     * 延迟管控生效时间
     */
    private Long effectiveTime;

    /**
     * 删除标识：0-未删除，1-已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}