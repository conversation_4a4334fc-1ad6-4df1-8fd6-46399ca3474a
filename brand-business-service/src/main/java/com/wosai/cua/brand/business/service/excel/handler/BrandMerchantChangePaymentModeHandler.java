package com.wosai.cua.brand.business.service.excel.handler;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.excel.AbstractImportExcelHandler;
import com.wosai.cua.brand.business.service.excel.context.BrandMerchantChangePaymentModeContext;
import com.wosai.cua.brand.business.service.excel.data.SimpleBrandMerchantOpData;
import com.wosai.cua.brand.business.service.externalservice.contractjob.MerchantContractJobClient;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeRequest;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeResult;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.MerchantBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppInfoOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.MerchantCenterClient;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.data.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Slf4j
@Component
public class BrandMerchantChangePaymentModeHandler extends AbstractImportExcelHandler<BrandMerchantChangePaymentModeContext, SimpleBrandMerchantOpData> {

    @Autowired
    private BrandDomainService brandDomainService;
    @Autowired
    private MerchantCenterClient merchantCenterClient;
    @Autowired
    private MerchantContractJobClient merchantContractJobClient;
    @Autowired
    private MerchantBusinessOpenClient merchantBusinessOpenClient;
    @Value("${appid.indirect}")
    protected String indirectAppId;

    @Override
    public BrandMerchantChangePaymentModeContext initContext(BrandTaskLogModule brandTaskLogModule) {
        return BrandMerchantChangePaymentModeContext.newInstance(brandTaskLogModule);
    }

    @Override
    public ExcelImportTypeEnum getAuditImportType() {
        return ExcelImportTypeEnum.BATCH_CHANGE_PAYMENT_MODE;
    }

    @Override
    public void preCheck(BrandMerchantChangePaymentModeContext context) {
        String brandId = context.getBrandId();
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        context.bindBrandModule(brandModule);

        // 业务校验，是否主商户是否开通间连扫码成功
        MerchantInfoQueryResult merchantInfoQueryResult = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(brandModule.getMerchantSn()));
        List<AppInfoOpenResult> appInfoOpenResults = merchantBusinessOpenClient.queryAppInfoOpenResultByMerchantId(merchantInfoQueryResult.getMerchantId());
        Optional<AppInfoOpenResult> brandIndirectOpenResult = appInfoOpenResults.stream().filter(r -> indirectAppId.equals(r.getAppId()) && r.getStatus().equals(AppInfoStatusEnum.SUCCESS)).findFirst();
        if (!brandIndirectOpenResult.isPresent()) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_OPEN_INDIRECT);
        }
    }

    @Override
    protected void doHandleData(BrandMerchantChangePaymentModeContext context, BrandSubTaskModule brandSubTaskModule) {
        SimpleBrandMerchantOpData data = JSON.parseObject(brandSubTaskModule.getSubTaskContext(), SimpleBrandMerchantOpData.class);
        try {
            doChangePaymentMode(context, data);
            brandSubTaskModule.setSubTaskContext(JSON.toJSONString(data));
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "成功")));
        } catch (Exception e) {
            log.error("处理数据异常 brandSubTaskId:{}", brandSubTaskModule.getId(), e);
            data.setResult("失败：" + e.getMessage());
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "失败:" + e.getMessage())));
        }
    }

    private void doChangePaymentMode(BrandMerchantChangePaymentModeContext context, SimpleBrandMerchantOpData data) {
        String brandId = context.getBrandModule().getBrandId();
        MerchantInfoQueryResult merchantInfo = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(data.getMerchantSn()));
        if (Objects.isNull(merchantInfo)) {
            data.setResult("失败:商户不存在");
            return;
        }

        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantInfo.getMerchantId());
        if (Objects.isNull(brandMerchantModule)) {
            data.setResult("失败:品牌下无该商户");
            return;
        }
        // 判断下支付模式是不是已经是了
        if (Objects.equals(brandMerchantModule.getPaymentMode(), context.getBrandMerchantChangePaymentModeDTO().getTargetPaymentMode())) {
            data.setResult("失败:商户在该支付模式");
            return;
        }
        // 调用进件接口
        PaymentModeChangeResult changeResult = merchantContractJobClient.changePaymentMode(new PaymentModeChangeRequest()
                .setMerchantId(brandMerchantModule.getMerchantId())
                .setTargetPaymentMode(context.getBrandMerchantChangePaymentModeDTO().getTargetPaymentMode())
                .setPlatform(context.getPlatform())
                .setOperatorId(context.getOperatorId())
                .setRemark("审批:" + context.getAuditSn()));
        if (!changeResult.isSuccess()) {
            data.setResult("失败:" + changeResult.getMsg());
        } else {
            brandMerchantModule.setPaymentMode(context.getBrandMerchantChangePaymentModeDTO().getTargetPaymentMode());
            brandDomainService.updateBrandMerchant(brandMerchantModule);
            String resultMsg = WosaiStringUtils.isNotEmpty(changeResult.getMsg()) ? "成功:" + changeResult.getMsg() : "成功";
            data.setResult(resultMsg);
        }
    }
}
