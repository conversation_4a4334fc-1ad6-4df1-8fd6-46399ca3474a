package com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model;

import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/18
 */
@Data
@Accessors(chain = true)
public class AppOpenResultNoticeRequest {

    private String merchantId;
    private String storeId;
    private String devCode;
    private AppInfoStatusEnum status;
    private String appSubStatusText;
    private String failCode;
    private String failMsg;
}
