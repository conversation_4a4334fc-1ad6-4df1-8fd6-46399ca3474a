package com.wosai.cua.brand.business.service.enums;

/**
 * 维金接口调用返回code枚举
 * <AUTHOR>
 */

public enum VFinanceResponseCodeEnum {
    /**
     *
     */
    SUCCESS("T","调用成功"),
    FAIL("F","调用失败")
    ;
    private final String code;

    private final String desc;

    VFinanceResponseCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
