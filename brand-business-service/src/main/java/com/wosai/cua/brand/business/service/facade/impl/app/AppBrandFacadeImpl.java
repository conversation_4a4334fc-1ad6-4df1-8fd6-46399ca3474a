package com.wosai.cua.brand.business.service.facade.impl.app;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.brand.ModifyAccountInfoResultDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppDeleteBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageQueryBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppQueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppReRegisterMemberInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppRequestBaseDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppSubmitOpenAccountRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ModifyBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.MerchantAccountDTO;
import com.wosai.cua.brand.business.api.dto.response.MerchantBusinessLicenseInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.response.VfinanceInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.WithdrawStrategyInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.app.AppSubmitOpenAccountResponseDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.app.AppBrandFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.business.BrandWithdrawStrategyBusiness;
import com.wosai.cua.brand.business.service.business.v2.BrandBusinessV2;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.withdraw.BrandWithdrawStrategyModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 品牌服务实现
 *
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class AppBrandFacadeImpl implements AppBrandFacade {

    private final BrandBusiness brandBusiness;

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    private final BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness;

    private final BrandBusinessV2 brandBusinessV2;

    @Autowired
    public AppBrandFacadeImpl(BrandBusiness brandBusiness, BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness, BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness, BrandBusinessV2 brandBusinessV2) {
        this.brandBusiness = brandBusiness;
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
        this.brandWithdrawStrategyBusiness = brandWithdrawStrategyBusiness;
        this.brandBusinessV2 = brandBusinessV2;
    }

    @Override
    public List<BrandSimpleInfoDTO> getBrandInfoListByMerchantId(QueryBrandsDTO queryBrandsDto) {
        if (StringUtils.isBlank(queryBrandsDto.getMerchantId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_ID_NOT_BE_NULL);
        }
        return brandBusiness.getBrandInfoListByMerchantId(queryBrandsDto.getMerchantId(), queryBrandsDto.getNeedGetConfig());
    }

    @Override
    public List<BrandSimpleInfoDTO> getBrandInfoListByMerchantIds(QueryBrandsDTO queryBrandsDto) {
        if (CollectionUtils.isEmpty(queryBrandsDto.getMerchantIds())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_ID_NOT_BE_NULL);
        }
        return brandBusiness.getBrandInfoListByMerchantIds(queryBrandsDto.getMerchantIds(), queryBrandsDto.getNeedGetConfig());
    }

    @Override
    public BrandDetailInfoDTO getBrandDetailInfoByBrandId(QueryBrandsDTO queryBrandsDto) {
        List<BrandSimpleInfoDTO> brandInfoList = brandBusiness.getBrandInfoListByMerchantId(queryBrandsDto.getMerchantId(), queryBrandsDto.getNeedGetConfig());
        if (CollectionUtils.isEmpty(brandInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        return brandBusiness.getBrandDetailInfoByBrandId(brandInfoList.get(0).getBrandId(), queryBrandsDto.getNeedGetConfig());
    }

    @Override
    public int deleteBrandMerchant(AppDeleteBrandMerchantDTO deleteBrandMerchantDto) {
        String brandId = brandBusiness.getBrandIdByMerchantId(deleteBrandMerchantDto.getTokenMerchantId());
        BrandMerchantModule brandMerchantModule = brandBusiness.getBrandMerchantModuleByBrandIdAndMerchantId(brandId, deleteBrandMerchantDto.getTokenMerchantId());
        if (!brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
        return brandBusiness.deleteBrandMerchant(brandId, deleteBrandMerchantDto.getMerchantIdList(), null);
    }

    @Override
    public PageBrandMerchantsDTO pageQueryBrandMerchants(AppPageQueryBrandMerchantsDTO appPageQueryBrandMerchants) {
        PageQueryBrandMerchantsDTO queryBrandMerchantsDto = JSON.parseObject(JSON.toJSONString(appPageQueryBrandMerchants), PageQueryBrandMerchantsDTO.class);
        List<BrandSimpleInfoDTO> brandInfoList = brandBusiness.getBrandInfoListByMerchantId(appPageQueryBrandMerchants.getMerchantId(), false);
        if (CollectionUtils.isEmpty(brandInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (Objects.nonNull(appPageQueryBrandMerchants.getWithdrawStrategyId())) {
            queryBrandMerchantsDto.setStrategyIdList(Lists.newArrayList(appPageQueryBrandMerchants.getWithdrawStrategyId()));
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(queryBrandMerchantsDto.getMerchantTypes())) {
            queryBrandMerchantsDto.setMerchantTypes(
                    Lists.newArrayList(
                            MerchantTypeEnum.BRAND_OWNER.getMerchantType(),
                            MerchantTypeEnum.FRANCHISEE.getMerchantType(),
                            MerchantTypeEnum.SUPPLIER.getMerchantType(),
                            MerchantTypeEnum.AGENT.getMerchantType(),
                            MerchantTypeEnum.BRAND_OPERATED_STORES.getMerchantType()
                    )
            );
        }
        queryBrandMerchantsDto.setNeedPaging(true);
        queryBrandMerchantsDto.setBrandIds(Lists.newArrayList(brandInfoList.get(0).getBrandId()));
        return brandBusiness.pageQueryBrandMerchants(queryBrandMerchantsDto);
    }

    @Override
    public Boolean modifyBrandMerchant(ModifyBrandMerchantDTO modifyBrandMerchantRequest) {
        brandBusiness.modifyBrandMerchant(modifyBrandMerchantRequest);
        return true;
    }

    @Override
    public BrandMerchantInfoDTO getBrandMerchantInfoByMerchantSn(QueryBrandMerchantInfoDTO queryBrandMerchantInfo) {
        if (StringUtils.isEmpty(queryBrandMerchantInfo.getMerchantSn())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_SN_NOT_BE_NULL);
        }
        String brandId = brandBusiness.getBrandIdByMerchantId(queryBrandMerchantInfo.getTokenMerchantId());
        List<BrandMerchantModule> brandMerchantByConditions = brandBusiness.getBrandMerchantByConditions(queryBrandMerchantInfo);
        if (CollectionUtils.isEmpty(brandMerchantByConditions)) {
            return null;
        }
        List<String> brandIdList = brandMerchantByConditions.stream().map(BrandMerchantModule::getBrandId).distinct().collect(Collectors.toList());
        List<BrandModule> brandByBrandIds = brandBusiness.getBrandByBrandIds(brandIdList);
        Map<String, BrandModule> brandModuleMap = brandByBrandIds.stream().collect(Collectors.toMap(BrandModule::getBrandId, Function.identity()));
        BrandMerchantModule brandMerchantModule = brandMerchantByConditions.get(0);
        BrandModule brandModule = brandModuleMap.get(brandMerchantModule.getBrandId());
        if (Objects.isNull(brandModule)) {
            log.warn("未查到品牌信息。");
            return null;
        }
        if (!brandModule.getBrandId().equals(brandId)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
        brandBusiness.getSubAccountInfoAndTopUpAccountInfo(brandMerchantModule, brandModule.getFundManagementCompanyCode());
        brandBusiness.supplementaryBrandMerchantParameter(brandMerchantModule);
        brandBusinessV2.supplementMerchantBusinessLicenseAndContactInfo(brandMerchantModule);
        // 查默认提现银行卡
        BrandMerchantInfoDTO brandMerchantInfo = BrandMerchantModule.convertToBrandMerchantInfo(brandMerchantModule);
        brandMerchantInfo.setPartnerId(brandModule.getPartnerId());
        brandMerchantInfo.setTypeDesc(BrandMerchantTypeEnum.getMerchantTypeDescByType(brandMerchantInfo.getType()));
        VfinanceInfoDTO vfinanceInfoDto = new VfinanceInfoDTO();
        vfinanceInfoDto.setMemberId(brandMerchantModule.getMemberId());
        vfinanceInfoDto.setMerchantAccountId(brandMerchantModule.getSubAccountNo());
        brandMerchantInfo.setVfinanceInfo(vfinanceInfoDto);
        MerchantBusinessLicenseInfo merchantBusinessLicense = brandMerchantModule.getMerchantBusinessLicense();
        if (Objects.nonNull(merchantBusinessLicense) && Objects.nonNull(merchantBusinessLicense.getType())) {
            brandMerchantInfo.setMerchantBusinessLicenseInfo(
                    new MerchantBusinessLicenseInfoDTO(
                            merchantBusinessLicense.getType() == 0 ? merchantBusinessLicense.getLegal_person_name() : merchantBusinessLicense.getName(),
                            merchantBusinessLicense.getLegal_person_name(),
                            merchantBusinessLicense.getType(),
                            merchantBusinessLicense.getType() == 0 ? merchantBusinessLicense.getLegal_person_id_number() : merchantBusinessLicense.getNumber(),
                            merchantBusinessLicense.getLegal_person_id_number(),
                            merchantBusinessLicense.getLegal_person_id_type()
                    )
            );
        }
        // 获取提现银行卡
        brandMerchantInfo.setBrandMerchantBankCard(brandMerchantBankAccountBusiness.getDefaultBankCard(brandModule.getBrandId(), brandMerchantModule.getMerchantId()));
        brandMerchantInfo.setAccountOpenStatusDesc(BrandMerchantAccountOpenStatusEnum.getStatusDescription(brandMerchantInfo.getAccountOpenStatus()));
        brandMerchantInfo.setMeiTuanStoreStatus(brandBusiness.getMeiTuanStoreStatus(brandModule.getBrandId(), brandMerchantModule.getAssociatedMeituanStoreSn()));
        return brandMerchantInfo;
    }

    @Override
    public Boolean registerBehaviorRecord(AppRequestBaseDTO appRequest) {
        String brandId = brandBusiness.getBrandIdByMerchantId(appRequest.getMerchantId());
        if (StringUtils.isEmpty(brandId)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        return brandBusiness.registerBehaviorRecord(brandId, appRequest.getMerchantId());
    }

    @Override
    public BrandMerchantInfoDTO getBrandMerchantInfoByStoreId(AppQueryBrandMerchantInfoDTO queryBrandMerchantRequest) {
        if (StringUtils.isEmpty(queryBrandMerchantRequest.getSqbStoreId())) {
            throw new BrandBusinessException("收钱吧门店id不能为空");
        }
        String brandId = brandBusiness.getBrandIdByMerchantId(queryBrandMerchantRequest.getTokenMerchantId());
        QueryBrandMerchantInfoDTO queryBrandMerchantInfo = new QueryBrandMerchantInfoDTO();
        queryBrandMerchantInfo.setSqbStoreId(queryBrandMerchantRequest.getSqbStoreId());
        queryBrandMerchantInfo.setBrandId(brandId);
        List<BrandMerchantModule> brandMerchantByConditions = brandBusiness.getBrandMerchantByConditions(queryBrandMerchantInfo);
        if (CollectionUtils.isEmpty(brandMerchantByConditions)) {
            return null;
        }
        BrandMerchantModule brandMerchantModule = brandMerchantByConditions.get(0);
        brandBusinessV2.supplementMerchantBusinessLicenseAndContactInfo(brandMerchantModule);
        List<BrandWithdrawStrategyModule> withdrawStrategyList = brandWithdrawStrategyBusiness.getWithdrawStrategyListByStrategyIdList(Lists.newArrayList(brandMerchantModule.getStrategyId()));
        Map<Long, BrandWithdrawStrategyModule> brandWithdrawStrategyModuleMap = withdrawStrategyList.stream().collect(Collectors.toMap(BrandWithdrawStrategyModule::getStrategyId, Function.identity()));
        BrandDetailInfoDTO brandDetailInfo = brandBusiness.getBrandDetailInfoByBrandId(brandId, false);
        if (Objects.isNull(brandDetailInfo)) {
            log.warn("未查到品牌信息。");
            return null;
        }
        BrandWithdrawStrategyModule brandWithdrawStrategyModule = brandWithdrawStrategyModuleMap.get(brandMerchantModule.getStrategyId());
        // 查询子账号以及充值账户信息
        brandBusiness.getSubAccountInfoAndTopUpAccountInfo(brandMerchantModule, brandDetailInfo.getFundManagementCompanyCode());
        // 查默认提现银行卡
        BrandMerchantInfoDTO brandMerchantInfo = BrandMerchantModule.convertToBrandMerchantInfo(brandMerchantModule);
        brandMerchantInfo.setTypeDesc(BrandMerchantTypeEnum.getMerchantTypeDescByType(brandMerchantInfo.getType()));
        brandMerchantInfo.setBrandSn(brandDetailInfo.getSn());
        brandMerchantInfo.setBrandName(brandDetailInfo.getName());
        brandMerchantInfo.setMerchantType(brandMerchantModule.getMerchantType());
        brandMerchantInfo.setElmStoreSn(brandMerchantModule.getAssociatedElmStoreSn());
        brandMerchantInfo.setSqbStoreId(brandMerchantModule.getAssociatedSqbStoreId());
        brandMerchantInfo.setMeiTuanStoreSn(brandMerchantModule.getAssociatedMeituanStoreSn());
        BrandModule brandModule = JSON.parseObject(JSON.toJSONString(brandDetailInfo), BrandModule.class);
        brandMerchantInfo.setConfig(brandBusiness.getBrandConfig(brandModule, queryBrandMerchantInfo.getNeedGetConfig()));
        MerchantAccountDTO merchantAccount = new MerchantAccountDTO();
        merchantAccount.setMemberId(brandMerchantModule.getMemberId());
        merchantAccount.setSubAccountName(brandMerchantModule.getSubAccountName());
        merchantAccount.setTopUpAccountName(brandMerchantModule.getTopUpAccountName());
        merchantAccount.setSubAccount(brandMerchantModule.getSubAccountNo());
        merchantAccount.setTopUpAccountNo(brandMerchantModule.getTopUpAccountNo());
        brandMerchantInfo.setMerchantAccount(merchantAccount);
        if (Objects.nonNull(brandWithdrawStrategyModule)) {
            WithdrawStrategyInfoDTO withdrawStrategyInfo = JSON.parseObject(JSON.toJSONString(brandWithdrawStrategyModule), WithdrawStrategyInfoDTO.class);
            if (Objects.nonNull(brandWithdrawStrategyModule.getReservedAmount()) && brandWithdrawStrategyModule.getReservedAmount() > 0) {
                withdrawStrategyInfo.setReservedAmount(new BigDecimal(brandWithdrawStrategyModule.getReservedAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            if (Objects.nonNull(brandWithdrawStrategyModule.getMinWithdrawalAmount()) && brandWithdrawStrategyModule.getMinWithdrawalAmount() > 0) {
                withdrawStrategyInfo.setMinWithdrawalAmount(new BigDecimal(brandWithdrawStrategyModule.getMinWithdrawalAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            brandMerchantInfo.setWithdrawStrategyInfo(withdrawStrategyInfo);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = brandMerchantModule.getMerchantBusinessLicense();
        if (Objects.nonNull(merchantBusinessLicense) && Objects.nonNull(merchantBusinessLicense.getType())) {
            brandMerchantInfo.setMerchantBusinessLicenseInfo(
                    new MerchantBusinessLicenseInfoDTO(
                            merchantBusinessLicense.getType() == 0 ? merchantBusinessLicense.getLegal_person_name() : merchantBusinessLicense.getName(),
                            merchantBusinessLicense.getLegal_person_name(),
                            merchantBusinessLicense.getType(),
                            merchantBusinessLicense.getType() == 0 ? merchantBusinessLicense.getLegal_person_id_number() : merchantBusinessLicense.getNumber(),
                            merchantBusinessLicense.getLegal_person_id_number(),
                            merchantBusinessLicense.getLegal_person_id_type()
                    )
            );
        }
        // 获取提现银行卡
        brandMerchantInfo.setBrandMerchantBankCard(brandMerchantBankAccountBusiness.getDefaultBankCard(brandModule.getBrandId(), brandMerchantModule.getMerchantId()));
        brandMerchantInfo.setAccountOpenStatusDesc(BrandMerchantAccountOpenStatusEnum.getStatusDescription(brandMerchantInfo.getAccountOpenStatus()));
        return brandMerchantInfo;
    }

    @Override
    public AppSubmitOpenAccountResponseDTO submitOpenAccount(AppSubmitOpenAccountRequestDTO request) {
        // 校验参数
        request.validateRequiredFields();
        // 修改信息
        ModifyAccountInfoResultDTO result = brandBusinessV2.modifyAccountInfo(request);
        // 提交资管机构开户
        CreateMerchantResponseDTO createMerchantResponse = brandBusiness.registerMemberInfo(request.getBrandId(), result.getMerchantId());
        if (Objects.isNull(createMerchantResponse) || Objects.isNull(createMerchantResponse.getSubmitOpenAccountResult())) {
            return AppSubmitOpenAccountResponseDTO.builder().openSuccess(false).openFailReason("资管机构开户失败").build();
        }
        return AppSubmitOpenAccountResponseDTO.builder()
                .openSuccess(createMerchantResponse.getSubmitOpenAccountResult().getOpenSuccess())
                .openFailReason(createMerchantResponse.getSubmitOpenAccountResult().getOpenFailReason())
                .build();
    }

    @Override
    public CreateMerchantResponseDTO reRegisterMemberInfo(AppReRegisterMemberInfoDTO request) {
        String brandId = brandBusiness.getBrandIdByMerchantId(request.getTokenMerchantId());
        if (StringUtils.isEmpty(brandId)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        return brandBusiness.registerMemberInfo(brandId, request.getDoMerchantId());
    }


}
