package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.module.merchant.BrandCallbackRecordModule;

/**
 * (网商银行)策略模式的接口类
 **/
public interface MyBankNotifyService {

    /**
     * 处理通知数据逻辑
     **/
    String notifyHandle(String context, String function) throws Exception;

    /**
     * 处理通知数据逻辑
     **/
    void notifyHandle(BrandCallbackRecordModule baseNotifyModule);

}
