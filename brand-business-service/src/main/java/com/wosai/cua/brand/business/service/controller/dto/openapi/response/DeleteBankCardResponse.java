package com.wosai.cua.brand.business.service.controller.dto.openapi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.service.controller.dto.openapi.request.OpenApiBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteBankCardResponse extends OpenApiBaseRequest {

    @JsonProperty("bank_card_id")
    private String bankCardId;
}
