package com.wosai.cua.brand.business.service.domain.tripartite.request.citic;

import com.wosai.cua.brand.business.service.domain.tripartite.citic.helper.CiticSignHelper;
import com.wosai.cua.brand.business.service.domain.tripartite.citic.helper.CiticXmlHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;

import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlTransient;
import java.util.List;

@Data
@Slf4j
@XmlAccessorType(XmlAccessType.FIELD)
public class BaseCiticRequest {

    public static final String REGISTERED_USER_REQUEST_TRANS_CODE = "********";

    public static final String BIND_BANK_CARD_REQUEST_TRANS_CODE = "********";
    /**
     * 外联平台流水号，该字段在报文头中上送，可使用任意规则生成长度为36位的流水号，保证不重复即可。
     */
    @XmlTransient
    protected String laasSsn;

    /**
     * 交易码
     */
    @XmlElement(name = "TRANS_CODE")
    protected String transCode;
    /**
     * 请求流水号
     */
    @XmlElement(name = "REQ_SSN")
    protected String reqSsn;
    /**
     * 商户编号
     */
    @XmlElement(name = "MCHNT_ID")
    protected String merchantId;

    /**
     * 发起方保留域
     */
    @XmlElement(name = "REQ_RESERVED")
    protected String reqReserved;

    /**
     * 签名域
     */
    @XmlElement(name = "SIGN_INFO")
    protected String signInfo;


    public String toXml() {
        try {
            return CiticXmlHelper.toXml(this);
        } catch (JAXBException e) {
            log.error("转xml报错", e);
        }
        return null;
    }

    public Document toDocument() {
        try {
            return CiticXmlHelper.parseXml(toXml());
        } catch (Throwable ignore) {
        }
        return null;
    }

    public String generateSignedRequestXml(String privateKeyPassword, String privateKey, String publicKey) {
        try {
            Document doc = this.toDocument();
            List<String> signInfoList = CiticXmlHelper.sortSignInfo(doc);
            String signInfo = String.join("", signInfoList);
            String sign = CiticSignHelper.sign(signInfo.getBytes(), privateKeyPassword, privateKey, publicKey);
            this.setSignInfo(sign);
            return this.toXml();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
