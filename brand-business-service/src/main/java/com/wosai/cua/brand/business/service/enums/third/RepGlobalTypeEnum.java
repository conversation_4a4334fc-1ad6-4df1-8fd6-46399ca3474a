package com.wosai.cua.brand.business.service.enums.third;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum RepGlobalTypeEnum {
    /**
     *
     */
    IDENTITY_CARD("1","身份证"),
    HK_MACAU_HOME_RETURN_PERMIT_CARD("3","港澳回乡证"),
    TAIWAN_COMPATRIOT_CERTIFICATE("5","台胞证"),
    FOREIGN_PASSPORTS("19","外国护照")
    ;
    private final String repGlobalType;

    private final String desc;


    RepGlobalTypeEnum(String repGlobalType, String desc) {
        this.repGlobalType = repGlobalType;
        this.desc = desc;
    }
}
