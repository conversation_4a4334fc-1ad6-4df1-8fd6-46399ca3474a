package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

@Getter
public enum AccountIdTypeEnum {
    ID_CARD(1, 1, "身份证"),
    H_M_PASS_CARD(2, 4, "港澳通行证"),
    TW_PASS_CARD(3, 3, "台胞证"),
    PASSPORT(4, 2, "外国护照"),
    ;

    private final Integer accountIdType;

    private final Integer analyzeIdType;

    private final String name;

    AccountIdTypeEnum(Integer accountIdType, Integer analyzeIdType, String name) {
        this.accountIdType = accountIdType;
        this.analyzeIdType = analyzeIdType;
        this.name = name;
    }

    public static int getAccountIdTypeByAnalyzeIdType(Integer analyzeIdType) {
        for (AccountIdTypeEnum accountIdTypeEnum : AccountIdTypeEnum.values()) {
            if (accountIdTypeEnum.getAnalyzeIdType().equals(analyzeIdType)) {
                return accountIdTypeEnum.getAccountIdType();
            }
        }
        return 1;
    }
}
