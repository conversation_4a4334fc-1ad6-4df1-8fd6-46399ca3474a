package com.wosai.cua.brand.business.service.domain.dao;

import com.wosai.cua.brand.business.service.domain.entity.BrandSmsTemplateConfigDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【brand_sms_template_config(品牌短信模板配置表)】的数据库操作Mapper
* @createDate 2025-01-14 09:55:09
* @Entity com.wosai.cua.brand.business.service.domain.entity.BrandSmsTemplateConfigDO
*/
public interface BrandSmsTemplateConfigDOMapper extends BaseMapper<BrandSmsTemplateConfigDO> {

}




