<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai.cua</groupId>
        <artifactId>brand-business</artifactId>
        <version>1.5.2</version>
    </parent>

    <packaging>jar</packaging>

    <artifactId>brand-business-service</artifactId>
    <description>品牌业务服务</description>
    <properties>
        <java.version>8</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.wosai.cua</groupId>
            <artifactId>brand-business-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcprov-jdk15</artifactId>
                    <groupId>bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-rpc</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mysql-connector-java</artifactId>
                    <groupId>mysql</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>byte-buddy</artifactId>
                    <groupId>net.bytebuddy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-instrumentation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>wosai-database-instrumentation-springboot2</artifactId>
                    <groupId>com.wosai</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>animal-sniffer-annotations</artifactId>
                    <groupId>org.codehaus.mojo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- redis-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.oss</groupId>
            <artifactId>oss-service-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>aliyun-java-sdk-core</artifactId>
                    <groupId>com.aliyun</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>org.web3j</groupId>
            <artifactId>core</artifactId>
            <version>5.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.3.1</version>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>

        <!-- 中信银行依赖类库 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>

        <!-- rpc接口引入 -->
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-poi-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.marketing.prepaid</groupId>
            <artifactId>marketing-saas-prepaid-card-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                    <groupId>org.mybatis.spring.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>apollo-core</artifactId>
                    <groupId>com.ctrip.framework.apollo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>apollo-openapi</artifactId>
                    <groupId>com.ctrip.framework.apollo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay.bank</groupId>
            <artifactId>merchant-bank-service-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>manage-api</artifactId>
                    <groupId>com.wosai.trade</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.mc</groupId>
            <artifactId>merchant-center-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
        </dependency>

        <!--hera trace-->
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-trace</artifactId>
        </dependency>

        <!-- 接入sls日志模块-->
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.data</groupId>
            <artifactId>crow-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-transaction-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shouqianba.workflow</groupId>
            <artifactId>sp-workflow-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.cua</groupId>
            <artifactId>xxl-job-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--添加openfeign依赖，依赖于sprin-cloud-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>merchant-user-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.trade</groupId>
            <artifactId>manage-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>merchant-business-open-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.uc</groupId>
            <artifactId>uc-user-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-web</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>core-crypto-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>bank-info-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
        </dependency>

        <!--kafka-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lettuce-core</artifactId>
                    <groupId>io.lettuce</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>xml-security</groupId>
            <artifactId>xmlsec</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>

        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>cua-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>merchant-contract-access-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-service-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>apollo-openapi</artifactId>
                    <groupId>com.ctrip.framework.apollo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>apollo-core</artifactId>
                    <groupId>com.ctrip.framework.apollo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.shorturl</groupId>
            <artifactId>shorturl-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>profit-sharing-proxy-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>vault-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-database-instrumentation-springboot2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>shouqianba-tools-service-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>merchant-business-open-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>brand-business-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.5</version>
                <configuration>
                    <layout>ZIP</layout><!-- enables PropertiesLauncher -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.6</version>
                <configuration>
                    <configurationFile>
                        ${basedir}/src/main/resources/generatorConfig.xml
                    </configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.46</version>
                    </dependency>
                    <dependency>
                        <groupId>tk.mybatis</groupId>
                        <artifactId>mapper</artifactId>
                        <version>4.1.5</version>
                    </dependency>
                    <dependency>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-boot-starter</artifactId>
                        <version>${mybatis-plus.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>1.8.0</version>
                <configuration>
                    <enableCallerData>true</enableCallerData>
                    <profiles>
                        <profile>
                            <name>vpc,prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 以JSON格式输出到控制台，适用于线上ACK -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_FILE</ref> <!-- 以JSON格式输出到文件，适用于华北三弹性环境 -->
                            </references>
                        </profile>
                        <profile>
                            <name>default,local</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 以字符串格式化形式输出到控制台，即标准输出，适用于本地开发 -->
                            </references>
                        </profile>
                    </profiles>
                    <maxFileHistory>10</maxFileHistory>
                    <patternLayout>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%tid] %-5level %logger{36}.%M - %msg%n
                    </patternLayout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.apache.avro</groupId>-->
            <!--                <artifactId>avro-maven-plugin</artifactId>-->
            <!--                <version>1.8.2</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <phase>generate-sources</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>schema</goal>-->
            <!--                        </goals>-->
            <!--                        <configuration>-->
            <!--                            <sourceDirectory>${project.basedir}/src/main/resources/avro/</sourceDirectory>-->
            <!--                            <outputDirectory>${project.basedir}/src/main/java/</outputDirectory>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
        </plugins>
    </build>

</project>
