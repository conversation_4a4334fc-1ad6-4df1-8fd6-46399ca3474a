#Persisted by DefaultConfig
#Tue Mar 25 11:50:48 CST 2025
bank.card.open.error.message.temple=\u54C1\u724C\u3010${brandSn}\u3011\u3010${brandName}\u3011\u4E0B\u5165\u9A7B\u5546\u6237\u3010${merchantName}\u3011\u3010${merchantSn}\u3011\u5F00\u901A\u94F6\u884C\u5361\u5931\u8D25\uFF0C\u94F6\u884C\u5361id\uFF1A\u3010${bankCardId}\u3011\u3002\n\\n\u5931\u8D25\u539F\u56E0\uFF1A${causeOfFailure}\u3002 \n\\n\u5806\u6808\u4FE1\u606F\uFF1A${stackInformation}
citic_length=2048
auto_open_sub_accounts_switch=false
citic_user_type_map={"FRANCHISEE"\:"015002","SUPPLIER"\:"015003","BRAND_OWNER"\:"015002","BRAND_OPERATED_STORES"\:"015001","AGENT"\:"015002"}
brand_tag_id=a64f3b7e-906d-4d8e-8f83-ddb399798f7c
fy_public_key=1111
max_amount=1000000
citic_signature_algorithm=SHA1WithRSAEncryption
base_bank_info=[{"bankNo"\:"ABC","bankName"\:"\u4E2D\u56FD\u519C\u4E1A\u94F6\u884C"},{"bankNo"\:"BCCB","bankName"\:"\u5317\u4EAC\u94F6\u884C"},{"bankNo"\:"BJRCB","bankName"\:"\u5317\u4EAC\u519C\u5546\u94F6\u884C"},{"bankNo"\:"BOC","bankName"\:"\u4E2D\u56FD\u94F6\u884C"},{"bankNo"\:"BOS","bankName"\:"\u4E0A\u6D77\u94F6\u884C"},{"bankNo"\:"CBHB","bankName"\:"\u6E24\u6D77\u94F6\u884C"},{"bankNo"\:"CCB","bankName"\:"\u4E2D\u56FD\u5EFA\u8BBE\u94F6\u884C"},{"bankNo"\:"CCQTGB","bankName"\:"\u91CD\u5E86\u4E09\u5CE1\u94F6\u884C"},{"bankNo"\:"CEB","bankName"\:"\u4E2D\u56FD\u5149\u5927\u94F6\u884C"},{"bankNo"\:"CIB","bankName"\:"\u5174\u4E1A\u94F6\u884C"},{"bankNo"\:"CITIC","bankName"\:"\u4E2D\u4FE1\u94F6\u884C"},{"bankNo"\:"CMB","bankName"\:"\u62DB\u5546\u94F6\u884C"},{"bankNo"\:"CMBC","bankName"\:"\u4E2D\u56FD\u6C11\u751F\u94F6\u884C"},{"bankNo"\:"COMM","bankName"\:"\u4EA4\u901A\u94F6\u884C"},{"bankNo"\:"CSCB","bankName"\:"\u957F\u6C99\u94F6\u884C"},{"bankNo"\:"CZB","bankName"\:"\u6D59\u5546\u94F6\u884C"},{"bankNo"\:"CZCB","bankName"\:"\u6D59\u6C5F\u7A20\u5DDE\u5546\u4E1A\u94F6\u884C"},{"bankNo"\:"GDB","bankName"\:"\u5E7F\u53D1\u94F6\u884C"},{"bankNo"\:"GNXS","bankName"\:"\u5E7F\u5DDE\u5E02\u519C\u4FE1\u793E"},{"bankNo"\:"GZCB","bankName"\:"\u5E7F\u5DDE\u5E02\u5546\u4E1A\u94F6\u884C"},{"bankNo"\:"HCCB","bankName"\:"\u676D\u5DDE\u94F6\u884C"},{"bankNo"\:"HKBCHINA","bankName"\:"\u6C49\u53E3\u94F6\u884C"},{"bankNo"\:"HSBANK","bankName"\:"\u5FBD\u5546\u94F6\u884C"},{"bankNo"\:"HXB","bankName"\:"\u534E\u590F\u94F6\u884C"},{"bankNo"\:"ICBC","bankName"\:"\u4E2D\u56FD\u5DE5\u5546\u94F6\u884C"},{"bankNo"\:"NBCB","bankName"\:"\u5B81\u6CE2\u94F6\u884C"},{"bankNo"\:"NJCB","bankName"\:"\u5357\u4EAC\u94F6\u884C"},{"bankNo"\:"PSBC","bankName"\:"\u4E2D\u56FD\u90AE\u653F\u50A8\u84C4\u94F6\u884C"},{"bankNo"\:"SHRCB","bankName"\:"\u4E0A\u6D77\u519C\u6751\u5546\u4E1A\u94F6\u884C"},{"bankNo"\:"SNXS","bankName"\:"\u6DF1\u5733\u519C\u6751\u5546\u4E1A\u94F6\u884C"},{"bankNo"\:"SPDB","bankName"\:"\u4E0A\u6D77\u6D66\u4E1C\u53D1\u5C55\u94F6\u884C"},{"bankNo"\:"SXJS","bankName"\:"\u664B\u57CE\u5E02\u5546\u4E1A\u94F6\u884C"},{"bankNo"\:"PAB","bankName"\:"\u5E73\u5B89\u94F6\u884C"},{"bankNo"\:"UPOP","bankName"\:"\u94F6\u8054\u5728\u7EBF\u652F\u4ED8"},{"bankNo"\:"WZCB","bankName"\:"\u6E29\u5DDE\u5E02\u5546\u4E1A\u94F6\u884C"},{"bankNo"\:"GDNYB","bankName"\:"\u5E7F\u4E1C\u5357\u7CA4\u94F6\u884C"},{"bankNo"\:"BODG","bankName"\:"\u4E1C\u839E\u94F6\u884C"}]
fy_private_key=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIaDw5Gb27ezyjrS1yAPtmwv/RZHm9tHSSuCY6SmbfK3WGAGhgAGhSGItNO3pXo3hEmD1graS70neFM0+aIZt9pShtxiz/1EszVMLD3HIkRKhBJFcq8p0gWH+l0Bpl0wGxlf4u2mTkf490YEiP+W/EyEnksQsoIdHpsn+/rZ20CnAgMBAAECgYB1r65ZJJ10+Y3DLVgdquGVgd7RsVEA5jt0H54CHcIwCoz9ZneyagHsNujOGuxiI1RP5VJNKHP/SBsT4VNOqWWaDoVn97thBr+JYCZX0SMdNznSEPMuZu6GKNSeuDl5alKZMB6CumIJmjl6JlDUeUturqbv5XwW1FaiNtrWE7x4sQJBAMEkNvj1hyKtopoLsOca5ydpR/vLOfQgPXGlKHRcOJfj45T3bSYCZ6fHc1t6IpvFUVqYElY1pLA8JYPc9PQaePMCQQCySv+mZfkAz3f2V0U8aw9nfbks4vnUi9XS3e+V593NaJ6IVX3M6sSXAHWJFgezEt7LdB+d54taMpKVeyUDpYZ9AkB/BjBZYDF2LzhHk/TOqbTpCKbdBPWihymh+ns2vAhEbQ6aRHg2jVJa2CQYP6VPSWCN8oHszO75MTWDGejIOjjdAkAKJb6bJ96eLzCyspDcOXOs/jjV1y1E7ZiD4eHK9GFpWXT8aXE5gnsh5QLLhJd3l7Fafwd1o0IJJiu1mkanCHq5AkEAwMfKw/GCAj8i+c7hHTccO0d69fC+fGDakb5zzTpzwNi/6VVIKYG6idOmnHqknGrj9h0UR3sKdE0iXvSBuHHBdg\=\=
check_valid_chinese_id_switch=false
store_tag_id=312ea701-6910-41f1-a56b-7199ac3428ff
citic_subject=CN\=test,OU\=PTNR,C\=CN
store_sn_check_white_list=e3c924a6-1fcc-489b-9ace-050e30fbe253,c1e81ef6-7164-43bb-b865-9fd105600ff1,4254ebdc-d28a-401a-8e18-4e7a07e443ef
default_page=1
merchant.entry.error.message.temple=\u54C1\u724C\u3010${brandSn}\u3011\u3010${brandName}\u3011\u4E0B\u5165\u9A7B\u5546\u6237\u3010${merchantName}\u3011\u3010${merchantSn}\u3011\u5931\u8D25\u3002\n\u5931\u8D25\u539F\u56E0\uFF1A${causeOfFailure}\u3002\n\u5806\u6808\u4FE1\u606F\uFF1A${stackInformation}
citic_validity=3650
used_short_url=true
default_page_size=10
min_amount=0
citic_key_algorithm=RSA
sms_template_config=[{"templateCode"\:"U3CV3AVH9TW6","terminalCode"\:"TERMINALAPP","templateName"\:"\u5165\u8D26\u901A\u77E5","method"\:"SMS"},{"templateCode"\:"DZGEO5LKPBKF","terminalCode"\:"TERMINALAPP","templateName"\:"\u6263\u6B3E\u6210\u529F\u901A\u77E5","method"\:"SMS"},{"templateCode"\:"MTHYFSZSDSYK","terminalCode"\:"TERMINALAPP","templateName"\:"\u6263\u6B3E\u5931\u8D25\u901A\u77E5","method"\:"SMS"},{"templateCode"\:"IWTZPVGPK1UN","terminalCode"\:"TERMINALAPP","templateName"\:"\u63D0\u73B0\u901A\u77E5","method"\:"SMS"},{"templateCode"\:"TMQSHQLLOHD1","terminalCode"\:"TERMINALAPP","templateName"\:"\u5145\u503C\u901A\u77E5","method"\:"SMS"}]
collection_mode_merchant_tag_id=055ef105-e0bc-4ff9-9fc2-c5307e278ba4
lock_expire_time=5
mybank_activate_qrcode_url=alipays\://platformapi/startapp?appId\=****************&nbupdate\=syncforce&query\=outMchId%3D{outMchId}%26isv%3D{ISV_ORG_ID}%26app%3Dbkclfs%26t%3DREGISTER%26uid%3D{uid}
format_check_switch=false
batch_processing_merchant_count=10
appid_brand_mapping=[{"appid"\:"****************","brandSnList"\:["**************","**************","**************","**************","**************","**************","**************","**************","**************","**************","**************"]}]
mybank_handler_switch=true
brand_tag_entity_id=dd0cd0fb-0659-4cc5-b7c2-f72637fde27c
