package com.wosai.cua.brand.business.api.dto.request.brandmerchant;

import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 批量关联新增商户请求审批信息
 * <AUTHOR>
 * @date 2024/9/10
 */
@Data
public class BrandBatchAddMerchantsDTO {

    /**
     * 是否开通间连扫码 true开通  false不开通
     */
    @NotNull(message = "是否开通间连扫码")
    private Boolean openIndirect;
    /**
     * 是否开通收付通 true开通 false不开通
     */
    @NotNull(message = "是否开通收付通")
    private Boolean openPaymentHub;
    /**
     * 是否后补照片 true后补 false不后补
     */
    @NotNull(message = "是否后补照片")
    private Boolean laterSupplyPhoto;

    /**
     * 如果选择品牌模式，这个字段不能为null
     */
    @NotNull(message = "支付模式不能为空")
    private PaymentModeEnum paymentModeDetail;

    /**
     * 维护人 ID，可以为空。为空则取审批提交人的 ID
     */
    private String crmUserId;

    /**
     * 商家主体照片压缩包
     */
    private String photoZip;


}
