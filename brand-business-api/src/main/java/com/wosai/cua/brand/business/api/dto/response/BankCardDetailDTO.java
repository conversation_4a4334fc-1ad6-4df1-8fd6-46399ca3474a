package com.wosai.cua.brand.business.api.dto.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BankCardDetailDTO extends BankCardResponseDTO{

    /**
     * 账户名称
     */
    private String accountName;
    /**
     * 银行卡号
     */
    private String bankCardNumber;

    /**
     * 账号类型
     */
    private Integer accountType;

    /**
     * 账号类型描述
     */
    private String accountTypeDesc;

    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 支行名称
     */
    private String branchName;
    /**
     * 背景图
     */
    private String bankBackPicture;
    /**
     * 银行图标
     */
    private String bankIcon;

    /**
     * 激活状态
     */
    private Integer activateStatus;
    /**
     * 激活状态描述
     */
    private String activateStatusDesc;
    /**
     * 是否是默认卡
     */
    private Boolean defaultCard;

    /**
     * 激活失败原因
     */
    private String activateFailReason;

    /**
     * 预留手机号
     */
    private String reservedMobileNumber;

    /**
     * 激活时间
     */
    private Date activationTime;

    /**
     * 证件号
     */
    private String idNumber;
}
