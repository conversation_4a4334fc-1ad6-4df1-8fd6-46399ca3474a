package com.wosai.cua.brand.business.api.dto.request.businessopen;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/9/19
 */
@Data
public class PaymentHubDevParamDTO {

    @NotBlank(message = "品牌号不能为空")
    private String brandSn;

    @NotBlank(message = "商户类型不能为空")
    private String merchantType;

    /**
     * 银行卡号
     */
    private String number;

    /**
     * 开户行号
     */
    private String openingNumber;

    /**
     * 银行预留手机号
     */
    private String phoneNumber;

    /**
     * 银行卡持有人姓名
     */
    private String holder;
    /**
     * 收钱吧门店号
     */
    private String sqbStoreSn;
    /**
     * 美团门店 ID
     */
    private String meituanStoreId;
    /**
     * 饿了么门店 ID
     */
    private String elmStoreId;
    /**
     * 外部门店号
     */
    private String outMerchantSn;
}
