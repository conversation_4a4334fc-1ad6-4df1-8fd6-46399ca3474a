package com.wosai.cua.brand.business.api.dto.response.brand;

import com.wosai.cua.brand.business.api.dto.brand.citic.CiticConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.fuiou.FuiouConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.mybank.MyBankConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.pab.PabConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import lombok.Data;

@Data
public class BrandConfigDTO {
    /**
     * 品牌ID
     */
    private  String brandId;
    /**
     * 渠道ID
     */
    private String channelId;
    /**
     * 渠道类型（资管机构编号）
     * @see FundManagementCompanyEnum
     */
    private String fundManagementCompanyCode;
    /**
     * 平安银行配置，对应渠道类型为PAB
     * @see FundManagementCompanyEnum
     */
    private PabConfigDTO pabConfig;

    /**
     * 网商银行配置，对应渠道类型为MY_BANK
     * @see FundManagementCompanyEnum
     */
    private MyBankConfigDTO myBankConfig;

    /**
     * 中信银行配置，对应渠道类型为CITIC
     * @see FundManagementCompanyEnum
     */
    private CiticConfigDTO citicConfig;

    /**
     * 富友银行配置，对应渠道类型为FUIOU
     * @see FundManagementCompanyEnum
     */
    private FuiouConfigDTO fuiouConfig;
}
