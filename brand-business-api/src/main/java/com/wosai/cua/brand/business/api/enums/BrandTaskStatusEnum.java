package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum BrandTaskStatusEnum {
    /**
     *
     */
    PENDING(0,"待执行"),
    EXECUTION(1,"执行中"),
    SUCCESS(2,"执行成功"),
    FAIL(3,"执行失败")
    ;
    private final Integer taskStatus;

    private final String desc;

    BrandTaskStatusEnum(Integer taskStatus, String desc) {
        this.taskStatus = taskStatus;
        this.desc = desc;
    }
}
