package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.enums.ApplicableSceneEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 创建提现策略
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BaseEditWithdrawStrategyDTO {
    /**
     * 提现类型
     * @see com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum
     */
    @NotBlank(message = "结算类型不能为空！")
    private String withdrawType;

    /**
     * 提现周期类型
     * @see com.wosai.cua.brand.business.api.enums.WithdrawCycleTypeEnum
     */
    private String withdrawCycleType;

    /**
     * 提现周期时间
     */
    @Deprecated
    private Integer withdrawCycleTime;

    /**
     * 提现周期时间集合
     */
    private String withdrawCycleTimes;

    /**
     * 最少单笔提现金额
     */
    private BigDecimal minWithdrawalAmount;

    /**
     * 提现预留金额
     */
    private BigDecimal reservedAmount;

    /**
     * 适用场景
     * @see ApplicableSceneEnum
     */
    private String applicableScene;

    /**
     * 备注
     */
    private String remark;
}
