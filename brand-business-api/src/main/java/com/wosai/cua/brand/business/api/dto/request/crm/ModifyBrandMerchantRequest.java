package com.wosai.cua.brand.business.api.dto.request.crm;

import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ModifyBrandMerchantRequest {

    @NotNull(message = "商户类型不能为空")
    private BrandMerchantTypeEnum merchantType;

    /**
     * 商户名称/营业执照名称
     */
    private String name;

    /**
     * 统一社会信用代码/身份证号
     */
    private String identificationNumber;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人身份证号
     */
    private String legalPersonId;

    /**
     * 结算卡信息
     */
    private SettlementCardVO settlementCard;

}
