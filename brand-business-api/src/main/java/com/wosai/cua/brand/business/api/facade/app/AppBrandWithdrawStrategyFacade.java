package com.wosai.cua.brand.business.api.facade.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.CreateWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.DeleteBrandWithdrawStrategyByStrategyIdListDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageQueryMerchantsByWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppRequestBaseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantByWdStrategyResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandWithdrawStrategyResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.EditWithdrawStrategyResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * 品牌提现策略相关接口
 *
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/app/brand/withdraw/strategy")
@Validated
public interface AppBrandWithdrawStrategyFacade {

    /**
     * 创建提现策略
     *
     * @param createWithdrawStrategy 创建对象
     * @return 创建结果返回
     */
    EditWithdrawStrategyResponseDTO createBrandWithdrawStrategy(@Validated CreateWithdrawStrategyDTO createWithdrawStrategy);

    /**
     * 编辑修改提现策略
     *
     * @param modifyWithdrawStrategy 编辑策略对象
     * @return 编辑结果返回
     */
    EditWithdrawStrategyResponseDTO modifyBrandWithdrawStrategy(@Validated ModifyWithdrawStrategyDTO modifyWithdrawStrategy);

    /**
     * 根据品牌id查询提现策略
     *
     * @param requestBaseDto 品牌id
     * @return 提现策略集合
     */
    List<BrandWithdrawStrategyResponseDTO> getBrandWithdrawStrategyByBrandId(AppRequestBaseDTO requestBaseDto);

    /**
     * 批量删除提现策略
     *
     * @param deleteBrandWithdrawStrategyByStrategyIdListDto 提现策略id集合
     * @return 删除条数
     */
    int deleteBrandWithdrawStrategyByStrategyIdList(@Valid DeleteBrandWithdrawStrategyByStrategyIdListDTO deleteBrandWithdrawStrategyByStrategyIdListDto);

    /**
     * 分页查询提现策略对应的商户
     * @param queryMerchantsByWithdrawStrategyDto 查询条件
     * @return 查询结果
     */
    BrandMerchantByWdStrategyResponseDTO pageBrandMerchantWdStrategyList(AppPageQueryMerchantsByWithdrawStrategyDTO queryMerchantsByWithdrawStrategyDto);

    /**
     * 获取商户的提现策略
     * @return 提现策略返回
     */
    BrandWithdrawStrategyResponseDTO getBrandMerchantStrategy(AppRequestBaseDTO requestBaseDto);
}
