package com.wosai.cua.brand.business.api.facade.crm;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.FileUrlRequest;
import com.wosai.cua.brand.business.api.dto.request.GenerateBrandKeyRequest;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.BaseBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.GenerateKeyResponse;
import com.wosai.cua.brand.business.api.dto.response.PageBrandInfoDTO;
import org.springframework.validation.annotation.Validated;
import vo.ApiRequestParam;

import javax.validation.Valid;
import java.util.Map;

@JsonRpcService(value = "rpc/crm/brand")
@Validated
public interface CrmBrandFacade {

    /**
     * 生成品牌密钥
     * @return 密钥
     */
    GenerateKeyResponse generateBrandKey(@Valid ApiRequestParam<GenerateBrandKeyRequest, Map> apiRequestParam);

    /**
     * 获取品牌公钥
     * @return 密钥
     */
    GenerateKeyResponse getBrandKey(@Valid ApiRequestParam<BaseBrandRequestDTO, Map> apiRequestParam);

    /**
     * 批量开通商户
     */
    void batchOpenMerchant(@Valid ApiRequestParam<FileUrlRequest, Map> apiRequestParam);

    /**
     * 分页查询品牌信息
     * @param apiRequestParam 分页查询参数
     * @return 分页查询结果
     */
    PageBrandInfoDTO pageBrandInfoList(@Valid ApiRequestParam<PageQueryBrandsDTO, Map> apiRequestParam);

    /**
     * 启用收付通
     * @param apiRequestParam 入参
     */
    void enableSft(@Valid ApiRequestParam<BaseBrandRequestDTO, Map> apiRequestParam);
}
