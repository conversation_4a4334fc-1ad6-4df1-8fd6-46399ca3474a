package com.wosai.cua.brand.business.api.facade.businessopen;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.businessopen.CheckOpenPaymentHubResponseDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenBrandPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/9/19
 */
@JsonRpcService(value = "rpc/businessOpen/brandPaymentHub")
@Validated
public interface BusinessOpenBrandPaymentHubFacade {

    /**
     * 开通收付通品牌
     * @param openBrandPaymentHubRequestDTO 请求参数
     * @return
     */
    CheckOpenPaymentHubResponseDTO checkBrandOpenPaymentHub(@Valid OpenBrandPaymentHubRequestDTO openBrandPaymentHubRequestDTO);

    /**
     * 业务开通 开通收付通品牌
     *
     * @param openBrandPaymentHubRequestDTO 开通收付通品牌参数
     * @return 开通结果
     */
    OpenPaymentHubResponseDTO openBrandPaymentHub(@Valid OpenBrandPaymentHubRequestDTO openBrandPaymentHubRequestDTO);

    /**
     * 业务开通 查询收付通开通状态
     *
     * @param openBrandPaymentHubRequestDTO 开通收付通品牌查询参数
     * @return 开通状态
     */
    OpenPaymentHubResponseDTO getBrandPaymentHubOpenStatus(@Valid OpenBrandPaymentHubRequestDTO openBrandPaymentHubRequestDTO);
}
