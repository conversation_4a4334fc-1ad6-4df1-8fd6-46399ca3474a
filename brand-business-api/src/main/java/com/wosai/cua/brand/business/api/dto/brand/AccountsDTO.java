package com.wosai.cua.brand.business.api.dto.brand;

import com.wosai.cua.brand.business.api.dto.brand.citic.CiticAccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.fuiou.FuiouAccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.mybank.MyBankAccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.pab.PabAccountsDTO;
import lombok.Data;

@Data
public class AccountsDTO {
    /**
     * 平安银行
     */
    private PabAccountsDTO pabAccounts;

    /**
     * 网商银行
     */
    private MyBankAccountsDTO myBankAccounts;

    /**
     * 中信银行
     */
    private CiticAccountsDTO citicAccounts;

    /**
     * 富友支付
     */
    private FuiouAccountsDTO fuiouAccounts;
}
