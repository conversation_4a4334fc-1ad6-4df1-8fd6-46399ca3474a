package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/9
 */
@Getter
public enum MerchantBrandTypeEnum {
    /**
     * 非品牌商户
     */
    NO_BRAND_MERCHANT(0, "非品牌商户"),
    /**
     * 品牌主商户
     */
    MAIN_BRAND_MERCHANT(1, "品牌主商户"),
    /**
     * 品牌子商户
     */
    SUB_BRAND_MERCHANT(2, "品牌子商户");

    private final Integer code;
    private final String desc;

    MerchantBrandTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 是否是品牌主商户
     * @return true是 false否
     */
    public boolean isMainBrandMerchant() {
        return MAIN_BRAND_MERCHANT.equals(this);
    }

    /**
     * 是否是品牌子商户
     * @return true是 false否
     */
    public boolean isSubBrandMerchant() {
        return SUB_BRAND_MERCHANT.equals(this);
    }

    /**
     * 是否不是品牌商户
     * @return true:不是品牌商户 false：是品牌商户
     */
    public boolean isNoBrandMerchant() {
        return NO_BRAND_MERCHANT.equals(this);
    }
}
