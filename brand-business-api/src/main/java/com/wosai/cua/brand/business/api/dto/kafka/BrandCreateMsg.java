package com.wosai.cua.brand.business.api.dto.kafka;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BrandCreateMsg {
    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 品牌编号
     */
    private String brandSn;
    /**
     * 商户编号
     */
    private String merchantSn;
    /**
     * 商户 ID
     */
    private String merchantId;
}
