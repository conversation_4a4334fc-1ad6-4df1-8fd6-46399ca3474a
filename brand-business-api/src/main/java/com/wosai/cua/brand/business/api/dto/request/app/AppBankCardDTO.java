package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class AppBankCardDTO {
    @JsonProperty("merchant_id")
    private String merchantId;
    /**
     * 收付通银行卡id
     */
    @NotBlank(message = "银行卡id不能为空")
    private String bankCardId;

    private String sqbStoreId;
}
