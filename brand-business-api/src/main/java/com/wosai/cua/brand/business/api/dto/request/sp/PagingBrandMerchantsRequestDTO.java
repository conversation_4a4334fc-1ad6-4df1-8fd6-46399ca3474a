package com.wosai.cua.brand.business.api.dto.request.sp;

import com.wosai.cua.brand.business.api.model.BrandMerchantFilterModel;
import com.wosai.cua.brand.business.api.model.PagingModel;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/9/29
 */
@Data
public class PagingBrandMerchantsRequestDTO {

    @Valid
    @NotNull(message = "分页信息不能为空")
    private PagingModel paging;

    @Valid
    @NotNull(message = "查询条件不能为空")
    private BrandMerchantFilterModel filter;
}
