package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum ExcelConvertValueEnum {
    OPEN_TYPE_MERCHANT("168商户", "MERCHANT"),
    OPEN_TYPE_STORE("158门店", "STORE"),
    FRANCHISEE("FRA<PERSON><PERSON><PERSON><PERSON>","加盟商"),
    SUPPLIER("SUPPLIER","供应商"),
    BRAND_OWNER("BRAND_OWNER","品牌商"),
    BRAND_OPERATED_STORES("BRAND_OPERATED_STORES","品牌自营门店"),
    SERVICE_PROVIDER("SERVICE_PROVIDER","服务商"),
    AGENT("AGENT","代理商"),
    PERSONAL("PERSONAL","个人"),
    INDIVIDUAL_BUSINESS("INDIVIDUAL_BUSINESS","个体工商户"),
    COMPANY("COMPANY","企业"),
    // 新增身份证、护照、台胞证、港澳通行证枚举值
    ID_CARD("1", "身份证"),
    PASSPORT("2", "护照"),
    TAIWAN_PASS("3", "台胞证"),
    HK_MACAO_PASS("4", "港澳通行证")
    ;

    private final String excelValue;

    private final String convertValue;

    ExcelConvertValueEnum(String excelValue, String convertValue) {
        this.excelValue = excelValue;
        this.convertValue = convertValue;
    }

    public static String getConvertValue(String excelValue) {
        for (ExcelConvertValueEnum valueEnum : ExcelConvertValueEnum.values()) {
            if (valueEnum.getExcelValue().equals(excelValue)) {
                return valueEnum.getConvertValue();
            }
        }
        return null;
    }
}