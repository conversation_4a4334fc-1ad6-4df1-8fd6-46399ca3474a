package com.wosai.cua.brand.business.api.dto.response;

import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class BrandSimpleInfoDTO implements Serializable {
    private static final long serialVersionUID = -4181228500483350436L;
    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 品牌编号
     */
    private String sn;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌简称
     */
    private String alias;

    /**
     * 资管机构编号，MY_BANK、PAB
     */
    private FundManagementCompanyEnum fundManagementCompanyCode;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 父品牌id
     */
    private String parentId;

    /**
     * 品牌层级
     */
    private Integer level;
    /**
     * 商户类型
     * @see MerchantTypeEnum
     */
    private String merchantType;
    /**
     * 商户类型描述
     */
    private String merchantTypeDesc;

    /**
     * 支付模式
     */
    private Integer paymentMode;
    /**
     * 合作方id
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在config中
     */
    @Deprecated
    private String partnerId;
    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 是否允许登录
     */
    private Boolean allowLogin;

    /**
     * 配置
     */
    private BrandConfigDTO config;


    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO");
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO");
    }
}
