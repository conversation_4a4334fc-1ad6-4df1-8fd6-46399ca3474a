package com.wosai.cua.brand.business.api.facade.crm;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.crm.ModifyBrandMerchantRequest;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import org.springframework.validation.annotation.Validated;
import vo.ApiRequestParam;

import javax.validation.Valid;
import java.util.Map;

@JsonRpcService(value = "rpc/crm/brand/merchant")
@Validated
public interface CrmBrandMerchantFacade {
    /**
     * 根据商户编号查询品牌商户信息
     *
     * @param queryBrandMerchantInfo 查询条件
     * @return 查询结果
     */
    BrandMerchantInfoDTO getBrandMerchantInfoMerchantId(QueryBrandMerchantInfoDTO queryBrandMerchantInfo);

    /**
     * 修改品牌商户信息
     *
     * @param apiRequestParam common request param
     */
    void modifyBrandMerchantInfo(@Valid ApiRequestParam<ModifyBrandMerchantRequest, Map> apiRequestParam);
}
