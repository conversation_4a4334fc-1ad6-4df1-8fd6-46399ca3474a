package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum BrandMerchantDockingModeEnum {
    SEPARATE_ACCOUNT("SEPARATE_ACCOUNT", "分账"),
    COLLECTION("COLLECTION", "归集"),
    SEPARATE_COLLECTION("SEPARATE_COLLECTION", "分账+归集");

    private final String code;

    private final String desc;

    BrandMerchantDockingModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BrandMerchantDockingModeEnum getByCode(String code)
    {
        for (BrandMerchantDockingModeEnum item : values())
        {
            if (item.getCode().equals(code))
            {
                return item;
            }
        }
        return null;
    }

    public static String getDescByCode(String code)
    {
        for (BrandMerchantDockingModeEnum item : values())
        {
            if (item.getCode().equals(code))
            {
                return item.getDesc();
            }
        }
        return "";
    }
}
