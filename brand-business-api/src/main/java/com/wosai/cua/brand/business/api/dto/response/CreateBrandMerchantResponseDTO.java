package com.wosai.cua.brand.business.api.dto.response;

import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;

/**
 * <AUTHOR>
 */
public class CreateBrandMerchantResponseDTO {

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户类型
     * @see MerchantTypeEnum
     */
    private String merchantType;

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantSn() {
        return merchantSn;
    }

    public void setMerchantSn(String merchantSn) {
        this.merchantSn = merchantSn;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }
}
