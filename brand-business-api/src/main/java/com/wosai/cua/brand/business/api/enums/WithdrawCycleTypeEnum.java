package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum WithdrawCycleTypeEnum {
    /**
     * 提现周期类型
     */
    DAY("DAY", "按日", "每日%s点"),

    WEEK("WEEK", "按周", "每周%s"),

    MONTH("MONTH", "按月", "每月%s号"),

//    YEAR("YEAR", '', "按年")
    ;

    /**
     * 提现周期类型
     */
    private final String withdrawCycleType;
    /**
     * 提现周期描述
     */
    private final String typeDesc;

    private final String desc;

    WithdrawCycleTypeEnum(String withdrawCycleType, String typeDesc, String desc) {
        this.withdrawCycleType = withdrawCycleType;
        this.typeDesc = typeDesc;
        this.desc = desc;
    }

    public static String getTypeDescByWithdrawCycleType(String withdrawCycleType) {
        WithdrawCycleTypeEnum[] values = WithdrawCycleTypeEnum.values();
        Iterator<WithdrawCycleTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()) {
            WithdrawCycleTypeEnum withdrawCycleTypeEnum = iterator.next();
            if (withdrawCycleTypeEnum.withdrawCycleType.equals(withdrawCycleType)) {
                return withdrawCycleTypeEnum.desc;
            }
        }
        return null;
    }

    public static String getDescByWithdrawCycleTypeAndNumber(String withdrawCycleType, Integer number) {
        if (StringUtils.isEmpty(withdrawCycleType) || Objects.isNull(number)){
            return null;
        }
        WithdrawCycleTypeEnum[] values = WithdrawCycleTypeEnum.values();
        Iterator<WithdrawCycleTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()) {
            WithdrawCycleTypeEnum withdrawCycleTypeEnum = iterator.next();
            if (withdrawCycleTypeEnum.withdrawCycleType.equals(withdrawCycleType)) {
                return String.format(withdrawCycleTypeEnum.getDesc(), number);
            }
        }
        return null;
    }

    public static String getDescByWithdrawCycleTypeAndNumbers(String withdrawCycleType, List<String> numbers) {
        if (StringUtils.isEmpty(withdrawCycleType) || CollectionUtils.isEmpty(numbers)){
            return null;
        }
        WithdrawCycleTypeEnum[] values = WithdrawCycleTypeEnum.values();
        Iterator<WithdrawCycleTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()) {
            WithdrawCycleTypeEnum withdrawCycleTypeEnum = iterator.next();
            if (withdrawCycleTypeEnum.withdrawCycleType.equals(withdrawCycleType)) {
                return String.format(withdrawCycleTypeEnum.getDesc(), String.join("、", numbers));
            }
        }
        return null;
    }

    public static WithdrawCycleTypeEnum getWithdrawCycleTypeEnumByWithdrawCycleType(String withdrawCycleType) {
        WithdrawCycleTypeEnum[] values = WithdrawCycleTypeEnum.values();
        Iterator<WithdrawCycleTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()) {
            WithdrawCycleTypeEnum withdrawCycleTypeEnum = iterator.next();
            if (withdrawCycleTypeEnum.withdrawCycleType.equals(withdrawCycleType)) {
                return withdrawCycleTypeEnum;
            }
        }
        return null;
    }
}
