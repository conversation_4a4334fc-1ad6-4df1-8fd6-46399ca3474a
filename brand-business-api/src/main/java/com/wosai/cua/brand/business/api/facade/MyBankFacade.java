package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.mybank.OpenAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.mybank.SynchronousProtocolStatusDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

@JsonRpcService(value = "rpc/mybank")
@Validated
public interface MyBankFacade {
    /**
     * 开户
     * @param openAccountDto 开户信息
     * @return 是否开户成功
     */
    Boolean openAccount(OpenAccountDTO openAccountDto);

    /**
     * 同步协议状态
     * @return 是否同步成功
     */
    Boolean synchronousProtocolStatus(@Valid SynchronousProtocolStatusDTO synchronousProtocolStatusDto);
}
