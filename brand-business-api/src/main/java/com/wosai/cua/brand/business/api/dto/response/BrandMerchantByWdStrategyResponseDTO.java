package com.wosai.cua.brand.business.api.dto.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BrandMerchantByWdStrategyResponseDTO {

    private Integer total;
    private List<BrandMerchantWdStrategyDTO> brandMerchants;

    @Data
    public static class BrandMerchantWdStrategyDTO{
        /**
         * 提现策略id
         */
        private String strategyId;
        /**
         * 提现策略描述
         */
        private String withdrawStrategyDesc;

        /**
         * 提现类型
         * @see com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum
         */
        private String withdrawType;

        private String withdrawTypeDesc;

        private String withdrawCycleDesc;

        /**
         * 最少单笔提现金额，单位：元
         */
        private Integer minWithdrawalAmount;

        /**
         * 商户编号
         */
        private String merchantSn;
        /**
         * 商户名称
         */
        private String merchantName;
        /**
         * 创建时间
         */
        private Date createdTime;

        /**
         * 提现周期类型
         */
        private String withdrawCycleType;

        /**
         * 提现周期时间
         */
        @Deprecated
        private Integer withdrawCycleTime;

        /**
         * 提现周期时间
         */
        private String withdrawCycleTimes;

        /**
         * 可用场景
         * @see com.wosai.cua.brand.business.api.enums.ApplicableSceneEnum
         */
        private String applicableScene;

        private String outMerchantNo;
    }
}
