package com.wosai.cua.brand.business.api.dto.request.app;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class AppQueryBrandMerchantInfoDTO {
    @JsonProperty("merchant_id")
    private String tokenMerchantId;

    private String merchantSn;

    private String merchantId;

    private String sqbStoreId;

    private String meiTuanStoreSn;

    private String elmStoreSn;

    private Boolean needGetConfig;

    private Integer deleted = 0;
}
