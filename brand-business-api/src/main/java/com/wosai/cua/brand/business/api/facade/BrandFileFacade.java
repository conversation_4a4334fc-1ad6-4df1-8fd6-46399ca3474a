package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.auth.WithdrawStrategy;
import com.wosai.cua.brand.business.api.dto.request.OssFileRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.PageRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ExportBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.ExportMerchantBankAccountResponse;
import com.wosai.cua.brand.business.api.dto.response.ImportMerchantTaskResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/brand/file")
@Validated
public interface BrandFileFacade {
    /**
     * 导入品牌商户
     *
     * @param ossFileRequest oss信息封装对象
     * @return 解析结果文件信息对象
     */
    @Validated({WithdrawStrategy.class})
    Boolean importBrandMerchant(@Valid OssFileRequestDTO ossFileRequest);

    /**
     * 分页获取导入任务
     * @param pageRequest 分页入参
     * @return 导入任务结果
     */
    ImportMerchantTaskResponseDTO getImportTaskList(PageRequestDTO pageRequest);

    /**
     * 分页获取任务列表
     * @param pageRequest 分页入参
     * @return 任务结果
     */
    ImportMerchantTaskResponseDTO getTaskList(PageRequestDTO pageRequest);

    /**
     * 导入品牌商户的提现策略
     * @param ossFileRequest oss信息封装对象
     * @return 解析结果
     */
    @Validated({WithdrawStrategy.class})
    Boolean importBrandMerchantWithdrawStrategy(@Valid OssFileRequestDTO ossFileRequest);

    /**
     * 导入品牌商户的饿了么和美团id
     * @param ossFileRequest oss信息封装对象
     * @return 解析结果
     */
    Boolean importBrandMerchantMetTuanIdAndElmId(@Valid OssFileRequestDTO ossFileRequest);

    /**
     * 导出品牌商户
     * @param exportBrandMerchantRequest 导出请求对象
     */
    Boolean exportBrandMerchant(ExportBrandMerchantRequestDTO exportBrandMerchantRequest);

    /**
     * 导出品牌商户银行账号（未激活）
     * @param exportBrandMerchantRequest 导出请求对象
     */
    ExportMerchantBankAccountResponse exportBrandMerchantBankAccount(ExportBrandMerchantRequestDTO exportBrandMerchantRequest);

    /**
     * 导出品牌下没有银行账号的商户
     * @param exportBrandMerchantRequest 导出请求对象
     */
    Boolean exportNotHaveBankAccountBrandMerchant(ExportBrandMerchantRequestDTO exportBrandMerchantRequest);

    /**
     * 批量处理品牌商户开户 (海科分账)
     * @param ossFileRequest oss信息封装对象
     * @return 解析结果
     */
    Boolean batchSpecialTreatmentBrandMerchantOpenAccount(@Valid OssFileRequestDTO ossFileRequest);
}
