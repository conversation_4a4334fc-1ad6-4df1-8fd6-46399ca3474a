package com.wosai.cua.brand.business.api.dto.response.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonResult {

    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 错误信息
     */
    private String message;

    public static CommonResult success() {
        return new CommonResult(true, null);
    }

    public static CommonResult success(String message) {
        return new CommonResult(true, message);
    }

    public static CommonResult fail(String message) {
        return new CommonResult(false, message);
    }
}
