package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum UseOfFundsEnum {
    PAY_BRAND_FEE("PAY_BRAND_FEE", "01", "缴纳品牌费"),
    PAY_MANAGEMENT_FEE("PAY_MANAGEMENT_FEE", "02", "缴纳管理费"),;

    private final String code;
    private final String fyCode;
    private final String desc;

    UseOfFundsEnum(String code, String fyCode, String desc) {
        this.code = code;
        this.fyCode = fyCode;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (UseOfFundsEnum useOfFundsEnum : UseOfFundsEnum.values()) {
            if (useOfFundsEnum.getCode().equals(code)) {
                return useOfFundsEnum.getDesc();
            }
        }
        return null;
    }

    public static String getFyCodeByCode(String code) {
        for (UseOfFundsEnum useOfFundsEnum : UseOfFundsEnum.values()) {
            if (useOfFundsEnum.getCode().equals(code)) {
                return useOfFundsEnum.getFyCode();
            }
        }
        return null;
    }

    public static UseOfFundsEnum getByCode(String code) {
        for (UseOfFundsEnum useOfFundsEnum : UseOfFundsEnum.values()) {
            if (useOfFundsEnum.getCode().equals(code)) {
                return useOfFundsEnum;
            }
        }
        return null;
    }
}
