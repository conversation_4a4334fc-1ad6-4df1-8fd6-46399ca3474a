package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/21
 */
public enum AppOpenStatusEnum {

    /**
     * 待开通
     */
    INIT(0,"待开通"),
    /**
     * 开通中
     */
    PENDING(1,"开通中"),
    /**
     * 开通失败
     */
    FAIL(2,"开通失败"),
    /**
     * 开通成功
     */
    SUCCESS(3,"开通成功");

    @Getter
    private final int code;
    @Getter
    private final String desc;

    AppOpenStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AppOpenStatusEnum getByCode(int code) {
        for (AppOpenStatusEnum statusEnum : AppOpenStatusEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        return null;
    }
}
