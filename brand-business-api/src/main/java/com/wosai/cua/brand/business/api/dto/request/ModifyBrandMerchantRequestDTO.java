package com.wosai.cua.brand.business.api.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ModifyBrandMerchantRequestDTO {
    /**
     * 品牌id
     */
    @NotBlank(message = "品牌id不能为空")
    private String brandId;
    /**
     * 商户id
     */
    @NotBlank(message = "商户id不能为空")
    private String merchantId;
    /**
     * 商户编号
     */
    private String merchantSn;
    /**
     * 提现策略id
     */
    private String strategyId;

    /**
     * 关联品牌商户门店编号
     */
    private String associatedSqbStoreId;

    /**
     * 关联美团门店号
     */
    private String associatedMeituanStoreSn;

    /**
     * 关联饿了么门店号
     */
    private String associatedElmStoreSn;
}
