package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 存脸品牌开通收付通
 * <AUTHOR>
 * @date 2024/9/29
 */
@Data
public class ExistBrandOpenPaymentHubRequestDTO {

    @NotBlank(message = "品牌编号不能为空")
    private String brandSn;

    /**
     * 资管机构编号
     */
    @NotNull(message = "资管机构编号不能为空！")
    private FundManagementCompanyEnum fundManagementCompanyCode;

    /**
     * 品牌配置
     */
    private ConfigDTO config;

    /**
     * 银行账户信息
     */
    private AccountsDTO accounts;
}
