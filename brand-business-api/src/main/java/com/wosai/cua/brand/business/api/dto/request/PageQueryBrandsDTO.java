package com.wosai.cua.brand.business.api.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class PageQueryBrandsDTO {

    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 品牌编号
     */
    private String brandSn;
    /**
     * 品牌账号
     */
    private String identifier;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 父品牌ID
     */
    private String parentId;

    /**
     * 当前页
     */
    private Integer page;

    /**
     * 收付通品牌标识
     */
    private Integer sftTag;

    /**
     * 启用收付通标识
     */
    private Integer enableSft;

    private Integer pageSize;
}
