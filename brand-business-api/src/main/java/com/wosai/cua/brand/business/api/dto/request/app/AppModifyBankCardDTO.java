package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class AppModifyBankCardDTO {

    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 商户id
     */
    @NotBlank(message = "商户di必传")
    @JsonProperty("merchant_id")
    private String merchantId;

    @NotBlank(message = "银行卡id不能为空")
    private String bankCardId;

    /**
     * 账户类型 1 个人账户 2 企业账户
     */
    private Integer type;
    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 开户行号
     */
    private String openingNumber;

    /**
     * 银行卡持有人名称
     */
    private String holder;


    /**
     * 银行预留手机号
     */
    private String reservedMobileNumber;
}
