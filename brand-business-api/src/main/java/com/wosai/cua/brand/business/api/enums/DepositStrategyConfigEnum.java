package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum DepositStrategyConfigEnum {
    TRADING_STORE("TRADING_STORE","交易门店"),
    BRAND_MERCHANT("BRAND_MERCHANT","品牌商户")
    ;

    private final String depositStrategy;

    private final String description;

    DepositStrategyConfigEnum(String depositStrategy, String description) {
        this.depositStrategy = depositStrategy;
        this.description = description;
    }

    public static DepositStrategyConfigEnum getByDepositStrategy(String depositStrategy) {
        if (StringUtils.isEmpty(depositStrategy)){
            return TRADING_STORE;
        }
        for (DepositStrategyConfigEnum depositStrategyConfigEnum : values()) {
            if (depositStrategyConfigEnum.depositStrategy.equals(depositStrategy)) {
                return depositStrategyConfigEnum;
            }
        }
        return null;
    }
}
