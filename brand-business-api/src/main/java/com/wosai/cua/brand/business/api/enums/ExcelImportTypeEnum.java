package com.wosai.cua.brand.business.api.enums;

/**
 * <AUTHOR>
 * @date 2024/8/30
 */
public enum ExcelImportTypeEnum {

    /**
     * 对账申请增加商户
     */
    RECONCILIATION_ADD_MERCHANT("RECONCILIATION_ADD_MERCHANT", "对账申请增加商户", 9),
    /**
     * 对账申请删除商户
     */
    RECONCILIATION_DELETE_MERCHANT("RECONCILIATION_DELETE_MERCHANT", "对账申请删除商户", 10),
    /**
     * 批量新增小微商户，开通间连扫码或者是收付通
     */
    BUSINESS_OPEN_ADD_MICRO_MERCHANT("BUSINESS_OPEN_ADD_MICRO_MERCHANT", "批量新增小微商户", 4),
    /**
     * 批量新增非小微商户，开通间连扫码或者是收付通
     */
    BUSINESS_OPEN_ADD_NO_MICRO_MERCHANT("BUSINESS_OPEN_ADD_NO_MICRO_MERCHANT", "批量新增非小微商户", 5),
    /**
     * 批量关联以后商户，可选开通收付通
     */
    BUSINESS_OPEN_ASSOCIATE_MERCHANT("BUSINESS_OPEN_ASSOCIATE_MERCHANT", "批量关联商户", 6),
    /**
     * 批量切换支付模式
     */
    BATCH_CHANGE_PAYMENT_MODE("BATCH_CHANGE_PAYMENT_MODE", "批量切换支付模式", 7),
    /**
     * 批量解除关联关系
     */
    BATCH_REMOVE_MERCHANT_ASSOCIATION("BATCH_REMOVE_ASSOCIATION", "批量解除关联关系", 8),
    ;

    private final String code;
    private final String desc;
    private final Integer taskType;

    ExcelImportTypeEnum(String code, String desc, Integer taskType) {
        this.code = code;
        this.desc = desc;
        this.taskType = taskType;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public static ExcelImportTypeEnum getExcelImportTypeEnumByTaskType(Integer taskType) {
        for (ExcelImportTypeEnum excelImportTypeEnum : ExcelImportTypeEnum.values()) {
            if (excelImportTypeEnum.getTaskType().equals(taskType)) {
                return excelImportTypeEnum;
            }
        }
        return null;
    }
}
