package com.wosai.cua.brand.business.api.dto.request.app;

import com.wosai.cua.brand.business.api.enums.DictionaryEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DictionaryRequestDTO {

    /**
     * 字典名称
     * @see com.wosai.cua.brand.business.api.enums.DictionaryEnum
     */
    @NotNull(message = "查询的字典必传")
    private DictionaryEnum dictionary;
}
