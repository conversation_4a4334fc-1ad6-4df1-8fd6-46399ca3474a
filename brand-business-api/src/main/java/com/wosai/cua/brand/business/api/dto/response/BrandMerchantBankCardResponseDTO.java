package com.wosai.cua.brand.business.api.dto.response;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BrandMerchantBankCardResponseDTO {
    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 商户id
     */
    private String merchantId;
    /**
     * 收钱吧银行卡id
     */
    private String bankCardId;

    /**
     * 银行预留手机号
     */
    private String reservedMobileNumber;

    /**
     * 银行开户名
     */
    private String bankAccountName;

    /**
     * 银行卡号
     */
    private String bankCardNumber;

    /**
     * 联行号/大小额行号/开户行行号
     */
    private String openingNumber;

    /**
     * 账户类型：1-个人，2-企业
     */
    private Integer accountType;

    /**
     * 账户类型描述
     */
    private String accountTypeDesc;
    /**
     * 三方系统返回的银行卡id
     * （当前三方系统为维金）
     */
    private String thirdBankCardId;

    /**
     * 三方系统会员id
     */
    private String memberId;

    /**
     * 是否是默认卡：0-否，1-是
     */
    private Boolean isDefault;
    /**
     * 激活状态
     */
    private String activateStatus;
    /**
     * 激活状态描述
     */
    private String activateStatusDesc;
    /**
     * 银行logo(背景)
     */
    private String bankBack;
    /**
     * 银行logo（小图标）
     */
    private String bankIcon;

    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 支行名称
     */
    private String branchName;

    /**
     * 背景图
     */
    private String bankBackPicture;

    /**
     * 激活时间
     */
    private Date activationTime;
}
