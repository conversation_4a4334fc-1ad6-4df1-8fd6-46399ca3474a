package com.wosai.cua.brand.business.api.facade.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardAdvanceDTO;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.SearchBankInfoRequest;
import com.wosai.cua.brand.business.api.dto.request.app.AppBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppBaseBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppCreateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppModifyBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppRequestBaseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantBankCardsDTO;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/app/bank/account")
@Validated
public interface AppBrandMerchantBankAccountFacade {

    /**
     * 新增银行卡
     * @param createBankCard 新增银行卡对象
     * @return 新增条数
     */
    BankCardResponseDTO createBankCard(@Valid AppCreateBankCardDTO createBankCard);

    /**
     * 根据收钱吧门店id新增银行卡
     * @param createBankCard 新增银行卡对象
     * @return 新增条数
     */
    BankCardResponseDTO createBankCardBySqbStoreId(@Valid AppCreateBankCardDTO createBankCard);

    /**
     * 激活银行卡
     * @param activateBankCard 激活银行卡对象
     * @return 激活请求是否成功
     */
    Boolean activateBankCard(@Valid ActivateBankCardDTO activateBankCard);

    /**
     * 根据收钱吧门店id激活银行卡
     * @param activateBankCard 激活银行卡对象
     * @return 激活请求是否成功
     */
    Boolean activateBankCardBySqbStoreId(@Valid ActivateBankCardDTO activateBankCard);

    /**
     * 激活银行卡推进
     * @param activateBankCardAdvanceDto 激活银行卡推进对象
     * @return 调用是否成功
     */
    Boolean activateBankCardAdvance(@Valid ActivateBankCardAdvanceDTO activateBankCardAdvanceDto);

    /**
     * 激活银行卡推进
     * @param activateBankCardAdvanceDto 激活银行卡推进对象
     * @return 调用是否成功
     */
    Boolean activateBankCardAdvanceBySqbStoreId(@Valid ActivateBankCardAdvanceDTO activateBankCardAdvanceDto);

    /**
     * 切换默认提现卡
     * @param appBankCard 请求对象
     * @return 接口调用结果
     */
    Boolean changeDefaultBankCard(@Valid AppBankCardDTO appBankCard);

    /**
     * 根据收钱吧id切换默认提现卡
     * @param appBankCard 请求对象
     * @return 接口调用结果
     */
    Boolean changeDefaultBankCardBySqbStoreId(@Valid AppBankCardDTO appBankCard);

    /**
     * 删除银行卡
     * @param appBankCard 收付通银行卡请求对象
     * @return 接口调用结果
     */
    Boolean deleteBankCard(@Valid AppBankCardDTO appBankCard);

    /**
     * 根据收钱吧门店id删除银行卡
     * @param appBankCard 收付通银行卡请求对象
     * @return 接口调用结果
     */
    Boolean deleteBankCardBySqbStoreId(@Valid AppBankCardDTO appBankCard);

    /**
     * 分页查询银行卡列表
     * @param pageQueryBankCardDto 分页查询条件对象
     * @return 查询结果
     */
    PageBrandMerchantBankCardsDTO pageFindBankCardList(PageQueryBankCardDTO pageQueryBankCardDto);

    /**
     * 分页查询银行卡列表
     * @param pageQueryBankCardDto 分页查询条件对象
     * @return 查询结果
     */
    PageBrandMerchantBankCardsDTO pageFindBankCardListBySqbStoreId(PageQueryBankCardDTO pageQueryBankCardDto);

    /**
     * 获取银行卡激活状态
     * @param appBankCard 请求对象
     * @return 银行卡激活状态
     */
    BankCardActivateStatusEnum getBankCardActivateStatus(AppBankCardDTO appBankCard);

    /**
     * 获取已激活的银行卡
     * @param requestBaseDto 请求对象
     * @return 已激活银行卡列表
     */
    List<BankCardDetailDTO> getActivatedBankCardList(AppRequestBaseDTO requestBaseDto);

    /**
     * 修改银行卡信息
     * @param modifyBankCard 修改银行卡信息对象
     * @return 接口调用结果
     */
    BankCardResponseDTO modifyBankCard(AppModifyBankCardDTO modifyBankCard);

    /**
     * 查询银行卡详情
     * @param bankCard 银行卡对象
     * @return 银行卡详情对象
     */
    BankCardDetailResponseDTO getBankCardDetail(AppBaseBankCardDTO bankCard);

    /**
     * 查询银行卡详情
     * @param bankCard 银行卡对象
     * @return 银行卡详情对象
     */
    BankCardDetailResponseDTO getBankCardDetailBySqbStoreId(AppBaseBankCardDTO bankCard);

    /**
     * 查询银行卡详情
     * @param searchBankInfoRequest 银行卡对象
     * @return 银行卡详情对象
     */
    BankInfoResponseDTO getBankInfoByCardNumber(@Valid SearchBankInfoRequest searchBankInfoRequest);
}
