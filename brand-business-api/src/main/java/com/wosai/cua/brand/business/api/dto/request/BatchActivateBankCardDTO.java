package com.wosai.cua.brand.business.api.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class BatchActivateBankCardDTO {
    /**
     * 品牌id
     */
    @NotBlank(message = "品牌id必填")
    private String brandId;
    /**
     * 银行卡id
     */
    private List<ActivateBankCardDTO> batchActivateBankCardList;
}
