package com.wosai.cua.brand.business.api.dto.request.businessopen;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 开通收付通请求值
 *
 * <AUTHOR>
 * @date 2020-08-10
 */
@Data
public class OpenPaymentHubRequestDTO {

    /**
     * 商户id
     */
    @NotBlank(message = "商户id不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * 门店id
     */
    @JsonProperty("store_id")
    private String storeId;

    /**
     * 应用开发者标识
     */
    @NotBlank(message = "应用开发者标识不能为空")
    @JsonProperty("dev_code")
    private String devCode;

    /**
     * 客户经理用户id
     */
    @NotBlank(message = "客户经理用户不能为空")
    @JsonProperty("user_id")
    private String userId;

    /**
     * 客户经理组织id
     */
    @NotBlank(message = "客户经理组织不能为空")
    @JsonProperty("organization_id")
    private String organizationId;

    /**
     * 业务数据
     */
    @JsonProperty("dev_param")
    @Valid
    @NotNull(message = "收付通信息不能为空")
    private PaymentHubDevParamDTO devParam;

    @JsonProperty("merchant_info")
    private Map merchantInfo;

    @NotBlank(message = "提交平台不能为空")
    private String platform;

    /**
     * 开通方式，默认按照商户开通
     */
    @JsonProperty("open_way")
    private int openWay = 1;

    /**
     * 备注信息
     */
    @JsonProperty("remark")
    private String remark;

}
