package com.wosai.cua.brand.business.api.dto.request.merchant;

import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ExportBrandMerchantRequestDTO {

    @NotBlank(message = "品牌id不能为空")
    private String brandId;
    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 商户类型
     */
    private MerchantTypeEnum merchantType;

    /**
     * 账户开通状态
     */
    private BrandMerchantAccountOpenStatusEnum accountOpenStatus;

}