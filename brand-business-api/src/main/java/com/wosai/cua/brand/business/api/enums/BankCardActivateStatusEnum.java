package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Iterator;

/**
 * 银行卡激活状态
 * <AUTHOR>
 */

@Getter
public enum BankCardActivateStatusEnum {
    /**
     * 已激活
     */
    ACTIVATED("ACTIVATED","已激活"),
    /**
     * 未激活
     */
    UNACTIVATED("UNACTIVATED","未激活"),
    /**
     * 未绑定
     */
    NOT_BOUND("NOT_BOUND","未绑定"),
    /**
     * 无需绑卡（管理账号）
     */
    NOT_NEED_BOUND("NOT_NEED_BOUND","无需绑卡"),
    ;

    private final String activateStatus;

    private final String desc;

    BankCardActivateStatusEnum(String activateStatus, String desc) {
        this.activateStatus = activateStatus;
        this.desc = desc;
    }

    public static String getDescByActivateStatus(String activateStatus){
        BankCardActivateStatusEnum[] values = BankCardActivateStatusEnum.values();
        Iterator<BankCardActivateStatusEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            BankCardActivateStatusEnum bankCardActivateStatusEnum = iterator.next();
            if (bankCardActivateStatusEnum.activateStatus.equals(activateStatus)){
                return bankCardActivateStatusEnum.desc;
            }
        }
        return null;
    }
}
