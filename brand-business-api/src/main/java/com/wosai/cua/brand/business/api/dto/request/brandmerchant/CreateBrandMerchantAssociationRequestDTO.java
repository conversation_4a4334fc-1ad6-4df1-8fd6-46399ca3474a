package com.wosai.cua.brand.business.api.dto.request.brandmerchant;

import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
public class CreateBrandMerchantAssociationRequestDTO {

    @NotEmpty(message = "品牌ID不能为空")
    private String brandId;
    @NotEmpty(message = "商户ID不能为空")
    private String merchantId;
    @NotNull(message = "商户类型不能为空")
    private String merchantType;
    @NotNull(message = "支付模式不能为空")
    private Integer paymentMode;
}
