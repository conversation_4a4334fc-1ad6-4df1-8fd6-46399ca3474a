package com.wosai.cua.brand.business.api.facade.businessopen;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.businessopen.CheckOpenPaymentHubResponseDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.businessopen.OpenPaymentHubResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/9/19
 */
@JsonRpcService(value = "rpc/businessOpen/paymentHub")
@Validated
public interface BusinessOpenPaymentHubFacade {

    /**
     * 业务开通 校验开通收付通
     *
     * @param openPaymentHubRequest 开通收付通参数
     * @return 校验结果
     */
    CheckOpenPaymentHubResponseDTO checkOpenPaymentHub(@Valid OpenPaymentHubRequestDTO openPaymentHubRequest);

    /**
     * 业务开通 开通收付通
     *
     * @param openPaymentHubRequest 开通收付通参数
     * @return 开通结果
     */
    OpenPaymentHubResponseDTO openPaymentHub(@Valid OpenPaymentHubRequestDTO openPaymentHubRequest);

    /**
     * 业务开通 查询收付通开通状态
     *
     * @param openPaymentHubRequest 请求参数
     * @return 开通状态
     */
    OpenPaymentHubResponseDTO getPaymentHubOpenStatus(@Valid OpenPaymentHubRequestDTO openPaymentHubRequest);
}
