package com.wosai.cua.brand.business.api.dto.request.brand;

import com.wosai.cua.brand.business.api.dto.brand.TemplateConfigDTO;
import com.wosai.cua.brand.business.api.enums.SmsTemplateMethodEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BrandSaveSmsTemplateConfigRequest {

    @NotBlank(message = "品牌id不能为空")
    private String brandId;

    @NotNull(message = "通知方式不能为空")
    private SmsTemplateMethodEnum method;

    private List<TemplateConfigDTO> templateConfigs;
}
