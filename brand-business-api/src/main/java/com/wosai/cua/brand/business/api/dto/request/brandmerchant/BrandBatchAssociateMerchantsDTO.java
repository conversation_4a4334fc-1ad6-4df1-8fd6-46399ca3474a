package com.wosai.cua.brand.business.api.dto.request.brandmerchant;

import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 批量关联商户请求
 * <AUTHOR>
 * @date 2024/10/8
 */
@Data
public class BrandBatchAssociateMerchantsDTO {

    @NotNull(message = "是否开通收付通")
    private Boolean openPaymentHub;

    /**
     * 支付模式
     */
    @NotNull(message = "支付模式不能为空")
    private PaymentModeEnum paymentModeDetail;

    /**
     * 维护人 ID，可以为空。为空则取审批提交人的 ID
     */
    private String crmUserId;
}
