package com.wosai.cua.brand.business.api.enums;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Getter
public enum PurposeOfTransferEnum {
    REGISTER_AND_PUT_ON_ACCOUNT("REGISTER_AND_PUT_ON_ACCOUNT","登记挂账"),
    MARKETING_SUBSIDY_DISBURSEMENT("MARKETING_SUBSIDY_DISBURSEMENT","营销补贴发放"),
    BRAND_DEDUCT_MANAGEMENT_FEES("BRAND_DEDUCT_MANAGEMENT_FEES","品牌扣取管理费"),
    GENERAL_TRANSFER("GENERAL_TRANSFER","普通转账"),
    PURCHASE("PURCHASE","采购"),
    ;
    private final String code;
    private final String desc;

    PurposeOfTransferEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PurposeOfTransferEnum getByCode(String code) {
        for (PurposeOfTransferEnum purposeOfTransferEnum : PurposeOfTransferEnum.values()) {
            if (purposeOfTransferEnum.getCode().equals(code)) {
                return purposeOfTransferEnum;
            }
        }
        return null;
    }

    public static List<PurposeOfTransferEnum> getByCodeList(List<String> codeList) {
        List<PurposeOfTransferEnum> purposeOfTransferEnums = Lists.newArrayList();
        if (CollectionUtils.isEmpty(codeList)){
            return purposeOfTransferEnums;
        }
        for (String code : codeList) {
            PurposeOfTransferEnum transferEnum = getByCode(code);
            if (transferEnum == null) {
                continue;
            }
            purposeOfTransferEnums.add(transferEnum);
        }
        return purposeOfTransferEnums;
    }

    public static List<String> getCodeListByEnumList(List<PurposeOfTransferEnum> purposeOfTransferEnums) {
        List<String> codeList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(purposeOfTransferEnums)){
            return codeList;
        }
        for (PurposeOfTransferEnum purposeOfTransferEnum : purposeOfTransferEnums) {
            codeList.add(purposeOfTransferEnum.getCode());
        }
        return codeList;
    }
}
