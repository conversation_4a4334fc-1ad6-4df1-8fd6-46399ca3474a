package com.wosai.cua.brand.business.api.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wosai.cua.brand.business.api.serializer.BigDecimalToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class BrandWithdrawStrategyResponseDTO {
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 策略id
     */
    private String strategyId;

    /**
     * 提现类型
     * @see com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum
     */
    private String withdrawType;

    private String withdrawTypeDesc;

    private String withdrawCycleDesc;

    /**
     * 最少单笔提现金额，单位：元
     */
    @JsonSerialize(using = BigDecimalToStringSerializer.class)
    private BigDecimal minWithdrawalAmount;

    /**
     * 提现预留金额，单位：元
     */
    @JsonSerialize(using = BigDecimalToStringSerializer.class)
    private BigDecimal reservedAmount;

    /**
     * 关联的商户数量
     */
    private Integer merchantNumber;

    /**
     * 提现周期类型
     */
    private String withdrawCycleType;

    /**
     * 提现周期时间
     */
    @Deprecated
    private Integer withdrawCycleTime;

    /**
     * 提现周期时间集合
     */
    private String withdrawCycleTimes;

    /**
     * 提现策略描述（用于前端展示）
     */
    private String withdrawStrategyDesc;

    /**
     * 可用场景
     * @see com.wosai.cua.brand.business.api.enums.ApplicableSceneEnum
     */
    private String applicableScene;

    /**
     * 可用场景描述（用于前端展示）
     */
    private String applicableSceneDesc;

    /**
     * 备注
     */
    private String remark;

}
