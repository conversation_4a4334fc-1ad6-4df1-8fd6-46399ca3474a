package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AppResendRequestDTO {

    @JsonProperty("merchant_id")
    private String tokenMerchantId;

    @NotNull(message = "品牌id不能为空")
    private String brandId;

    @NotNull(message = "操作的商户id不能为空")
    private String doMerchantId;
}