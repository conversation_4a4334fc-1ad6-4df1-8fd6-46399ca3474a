package com.wosai.cua.brand.business.api.enums;

import java.util.Arrays;
import java.util.Iterator;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum BankAccountTypeEnum {
    /**
     *
     */
    PERSONAL(1,"个人"),
    COMPANY(2,"企业")
    ;

    private final Integer accountType;

    private final String desc;


    BankAccountTypeEnum(Integer accountType, String desc) {
        this.accountType = accountType;
        this.desc = desc;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByAccountType(Integer accountType){
        if (Objects.isNull(accountType)){
            return null;
        }
        BankAccountTypeEnum[] values = BankAccountTypeEnum.values();
        Iterator<BankAccountTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            BankAccountTypeEnum accountTypeEnum = iterator.next();
            if (accountTypeEnum.accountType.equals(accountType)){
                return accountTypeEnum.desc;
            }
        }
        return null;
    }
}
