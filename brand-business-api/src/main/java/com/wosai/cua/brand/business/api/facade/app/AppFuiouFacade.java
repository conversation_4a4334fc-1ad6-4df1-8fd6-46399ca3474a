package com.wosai.cua.brand.business.api.facade.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.app.AppResendRequestDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

@JsonRpcService(value = "rpc/app/brand/fuiou")
@Validated
public interface AppFuiouFacade {
    /**
     * 重发短信验证码
     * @param resendRequest
     * @return
     */
    Boolean resend(@Valid AppResendRequestDTO resendRequest);
}
