package com.wosai.cua.brand.business.api.dto.kafka;

import com.wosai.cua.brand.business.api.enums.EnrollChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BrandMerchantEnrollMsg {
    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 商户编号
     */
    private String merchantSn;
    /**
     * 报备渠道
     * @see EnrollChannelEnum
     */
    private EnrollChannelEnum enrollChannelEnum;
}
