package com.wosai.cua.brand.business.api.dto.request;

import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
public class BatchCreateBrandMerchantRequestDTO {
    @NotBlank(message = "品牌id不能为空")
    private String brandId;

    @Valid
    private List<CreateBrandMerchantRequestDTO> createBrandMerchantRequestList;

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public List<CreateBrandMerchantRequestDTO> getCreateBrandMerchantRequestList() {
        return createBrandMerchantRequestList;
    }

    public void setCreateBrandMerchantRequestList(List<CreateBrandMerchantRequestDTO> createBrandMerchantRequestList) {
        this.createBrandMerchantRequestList = createBrandMerchantRequestList;
    }

    @Override
    public String toString() {
        return "BatchCreateBrandMerchantRequestDTO{" +
                "brandId='" + brandId + '\'' +
                ", createBrandMerchantRequestList=" + createBrandMerchantRequestList +
                '}';
    }
}
