package com.wosai.cua.brand.business.api.model;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/9/29
 */
@Data
public class PagingModel {

    /**
     * 当前页
     */
    @NotNull(message = "页数不能为空")
    @Min(value = 1, message = "页数格式错误")
    private Integer page;

    /**
     * 每页大小
     */
    @NotNull(message = "查询数量不能为空")
    @Min(value = 1, message = "查询数量格式错误")
    private Integer pageSize;
}
