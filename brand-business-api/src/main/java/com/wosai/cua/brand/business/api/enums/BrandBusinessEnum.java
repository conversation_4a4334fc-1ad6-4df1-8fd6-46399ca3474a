package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
public enum BrandBusinessEnum {
    /**
     * 收付通业务
     */
    PAYMENT_HUB("PAYMENT_HUB", "收付通业务"),
    /**
     * 品牌支付业务
     */
    BRAND_PAYMENT("BRAND_PAYMENT", "品牌支付业务"),
    /**
     * 对账业务
     */
    RECONCILIATION("RECONCILIATION", "对账业务")
    ;

    @Getter
    private final String code;
    @Getter
    private final String desc;

    BrandBusinessEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BrandBusinessEnum ofCode(String code) {
        for (BrandBusinessEnum brandBusinessEnum : values()) {
            if (brandBusinessEnum.getCode().equals(code)) {
                return brandBusinessEnum;
            }
        }
        return null;
    }
}
