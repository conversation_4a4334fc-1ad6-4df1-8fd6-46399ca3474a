package com.wosai.cua.brand.business.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class ActivateBankCardAdvanceDTO {

    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * 银行卡id（收付通系统）
     */
    @NotBlank(message = "收付通银行卡id必填！")
    private String bankCardId;

    /**
     * 校验方式
     * @see com.wosai.cua.brand.business.api.enums.BankCardCheckModeEnum
     */
    private String checkMode;

    /**
     * 验证码
     */
    private String verificationCode;

    /**
     * 银行打款金额
     * 企业会员绑卡（往账鉴权使用）
     */
    private String bankPaymentAmount;

    private String sqbStoreId;
}
