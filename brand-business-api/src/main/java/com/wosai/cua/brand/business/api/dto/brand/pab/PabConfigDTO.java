package com.wosai.cua.brand.business.api.dto.brand.pab;

import com.wosai.cua.brand.business.api.dto.brand.BaseConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PabConfigDTO extends BaseConfigDTO {

    /**
     * 合作方id
     * （平安维金）
     */
    private String partnerId;

    /**
     * 是否提交营业执照
     */
    private Boolean submitBusinessLicense;
}
