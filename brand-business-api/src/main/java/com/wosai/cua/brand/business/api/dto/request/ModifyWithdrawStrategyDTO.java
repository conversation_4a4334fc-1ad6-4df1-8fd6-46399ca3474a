package com.wosai.cua.brand.business.api.dto.request;

import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
public class ModifyWithdrawStrategyDTO extends BaseEditWithdrawStrategyDTO {
    private String strategyId;

    public String getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    @Override
    public String toString() {
        return "ModifyWithdrawStrategyDTO{" +
                "strategyId='" + strategyId + '\'' +
                '}';
    }
}
