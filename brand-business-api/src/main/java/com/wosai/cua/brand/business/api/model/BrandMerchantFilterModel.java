package com.wosai.cua.brand.business.api.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/9/29
 */
@Data
public class BrandMerchantFilterModel {

    @NotBlank(message = "品牌ID不能为空")
    private String brandId;
    /**
     * 商户名称
     */
    private String merchantName;
    /**
     * 商户编号
     */
    private String merchantSn;
    /**
     * 商户经营名称
     */
    private String merchantBusinessName;
    /**
     * 联系电话
     */
    private String contactCellphone;
}
