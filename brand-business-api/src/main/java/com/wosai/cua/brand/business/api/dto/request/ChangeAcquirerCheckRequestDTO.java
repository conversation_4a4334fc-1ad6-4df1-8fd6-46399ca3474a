package com.wosai.cua.brand.business.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 判断是否允许切换收单机构
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ChangeAcquirerCheckRequestDTO {

    @JsonProperty("merchant_id")
    private String merchantId;

    @JsonProperty("merchant_sn")
    private String merchantSn;

    @JsonProperty("source_acquirer")
    private String sourceAcquirer;

    @JsonProperty("target_acquirer")
    private String targetAcquirer;

}