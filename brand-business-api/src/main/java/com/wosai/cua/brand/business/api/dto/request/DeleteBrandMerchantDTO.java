package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.dto.request.brand.DeleteBrandDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class DeleteBrandMerchantDTO extends DeleteBrandDTO {
    @NotEmpty(message = "商户id集合不能为空！")
    private List<String> merchantIdList;

    private Boolean needDeleteMerchant;
}
