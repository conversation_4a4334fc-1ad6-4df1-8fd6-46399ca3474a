package com.wosai.cua.brand.business.api.enums;

import java.util.Arrays;
import java.util.Iterator;

/**
 * 提现类型枚举
 * <AUTHOR>
 */

public enum WithdrawTypeEnum {
    /**
     * 提现类型
     */
    AUTO("AUTO","自动结算"),
    MANUAL_OPERATION("MANUAL_OPERATION","手动结算")
    ;
    private final String withdrawType;

    private final String desc;


    WithdrawTypeEnum(String withdrawType, String desc) {
        this.withdrawType = withdrawType;
        this.desc = desc;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByWithdrawType(String withdrawType){
        WithdrawTypeEnum[] values = WithdrawTypeEnum.values();
        Iterator<WithdrawTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            WithdrawTypeEnum withdrawTypeEnum = iterator.next();
            if (withdrawTypeEnum.withdrawType.equals(withdrawType)){
                return withdrawTypeEnum.desc;
            }
        }
        return null;
    }
}
