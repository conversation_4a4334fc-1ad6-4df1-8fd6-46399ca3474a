package com.wosai.cua.brand.business.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ActivateBankCardDTO {

    private String brandId;

    @JsonProperty("merchant_id")
    @NotBlank(message = "商户id必填")
    private String merchantId;

    /**
     * 银行卡id（收付通系统）
     */
    @NotBlank(message = "收付通银行卡id必填")
    private String bankCardId;

    /**
     * 手机号（申请往账鉴权有上送经办人的，指令号会发到经办人手机号，未输入经办人的发到此手机号）
     */
    private String mobile;

    /**
     * 校验方式
     * @see com.wosai.cua.brand.business.api.enums.BankCardCheckModeEnum
     */
    private String checkMode;

    /**
     * 激活方式：1-个人 2-法人办理，3-经办人办理
     */
    private String activateType;

    /**
     * 经办人姓名
     */
    private String agentName;

    /**
     * 经办人证件类型：activateType=3时必填
     * 仅支持1-身份证
     */
    private String agencyGlobalType;
    /**
     * 经办人证件号
     */
    private String agentIdCode;

    /**
     * 经办人手机号
     */
    private String agentPhone;

    /**
     * 收单的收钱吧门店id
     */
    private String sqbStoreId;
    /**
     * 校验参数是否合法
     * @param fundManagementCompanyEnum 资管机构枚举
     */
    public void validate(FundManagementCompanyEnum fundManagementCompanyEnum) {
        if (FundManagementCompanyEnum.PAB.equals(fundManagementCompanyEnum)) {
            // 平安银行需要校验手机号、激活方式、校验方式必填
            if (StringUtils.isBlank(mobile)) {
                throw new BrandBusinessException("手机号必填");
            }
            if (StringUtils.isBlank(activateType)) {
                throw new BrandBusinessException("激活方式必填");
            }
            if (StringUtils.isBlank(checkMode)) {
                throw new BrandBusinessException("校验方式必填");
            }

            // 如果是经办人办理，需要校验经办人相关信息
            if ("3".equals(activateType)) {
                if (StringUtils.isBlank(agentName)) {
                    throw new BrandBusinessException("经办人姓名必填");
                }
                if (StringUtils.isBlank(agencyGlobalType)) {
                    throw new BrandBusinessException("经办人证件类型必填");
                }
                if (StringUtils.isBlank(agentIdCode)) {
                    throw new BrandBusinessException("经办人证件号必填");
                }
                if (StringUtils.isBlank(agentPhone)) {
                    throw new BrandBusinessException("经办人手机号必填");
                }
            }
        }
    }
}
