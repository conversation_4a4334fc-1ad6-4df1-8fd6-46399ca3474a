package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.enums.OperationEnum;
import com.wosai.cua.brand.business.api.enums.OperationSceneEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CommonOperationDTO {

    /**
     * 操作场景
     */
    @NotNull(message = "操作场景不能为空！")
    private OperationSceneEnum scene;

    /**
     * 操作类型
     */
    @NotNull(message = "操作类型不能为空！")
    private OperationEnum operation;

    /**
     * 品牌ID
     */
    @NotNull(message = "品牌ID不能为空！")
    private String brandId;

    /**
     * 操作商户ID
     */
    @NotNull(message = "操作商户ID不能为空！")
    private String operationMerchantId;
}
