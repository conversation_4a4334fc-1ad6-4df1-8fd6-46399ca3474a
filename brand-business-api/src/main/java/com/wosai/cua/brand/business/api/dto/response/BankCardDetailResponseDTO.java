package com.wosai.cua.brand.business.api.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BankCardDetailResponseDTO {

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 账户类型 1 个人账户 2 企业账户
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer type;
    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 开户行号
     */
    private String openingNumber;

    /**
     * 银行预留手机号
     */
    private String reservedMobileNumber;

    /**
     * 账户持有人名称
     */
    private String holder;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 支行名称
     */
    private String branchName;
    /**
     * 背景图
     */
    private String bankBackPicture;
    /**
     * 银行图标
     */
    private String bankIcon;

    /**
     * 激活状态
     */
    private Integer activateStatus;
    /**
     * 激活状态描述
     */
    private String activateStatusDesc;
    /**
     * 是否是默认卡
     */
    private Boolean defaultCard;

    /**
     * 激活失败原因
     */
    private String activateFailReason;
}
