package com.wosai.cua.brand.business.api.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CreateWithdrawStrategyDTO extends BaseEditWithdrawStrategyDTO {
    /**
     * 品牌id
     */
    @NotBlank(message = "品牌id不能为空")
    private String brandId;
}
