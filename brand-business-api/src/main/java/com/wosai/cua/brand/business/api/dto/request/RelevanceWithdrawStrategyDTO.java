package com.wosai.cua.brand.business.api.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RelevanceWithdrawStrategyDTO {
    /**
     * 提现策略id
     */
    @NotNull(message = "结算策略id不能为空")
    private Long strategyId;

    @NotBlank(message = "品牌id不能为空")
    private String brandId;

    @NotEmpty(message = "商户编号集合不能为空")
    private List<String> merchantSnList;
}
