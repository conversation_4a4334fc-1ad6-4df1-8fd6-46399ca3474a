package com.wosai.cua.brand.business.api.dto.request.merchant;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
public class BaseCreateMerchantDTO {
    /**
     * 省
     */
    protected String province;
    /**
     * 市
     */
    protected String city;
    /**
     * 区
     */
    protected String district;

    /**
     * 详细地址
     */
    protected String address;

    /**
     * 老板账号（用于登录）
     */
    protected String cellPhoneNumber;

    /**
     * 商户名称
     */
    protected String merchantName;

    /**
     * 门店名称（用于创建门店）
     */
    protected String storeName;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class CompanyMerchantDTO extends BaseCreateMerchantDTO {
        /**
         * 企业证照类型
         */
        private Integer licenseType;
        /**
         * 证照编号
         */
        private String licenseNumber;
        /**
         * 联系人
         */
        private String contactName;
        /**
         * 联系人电话
         */
        private String contactPhone;
        /**
         * 联系人邮箱
         */
        private String contactEmail;
        /**
         * 联系人证件类型
         */
        private Integer personalLicenseType;
        /**
         * 联系人证件号
         */
        private String personalId;
        /**
         * 法人姓名
         */
        private String legalPersonName;
        /**
         * 法人证件类型
         */
        private Integer legalPersonLicenseType;
        /**
         * 法人证件号
         */
        private String legalPersonLicenseNumber;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class PersonalMerchantDTO extends BaseCreateMerchantDTO{
        /**
         * 姓名
         */
        private String name;
        /**
         * 证件类型
         */
        private Integer idType;
        /**
         * 证件号码
         */
        private String idNumber;
        /**
         * 手机号
         */
        private String cellphone;
        /**
         * 邮箱
         */
        private String email;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class IndividualBusinessMerchant extends BaseCreateMerchantDTO{
        /**
         * 商户证照类型
         */
        private Integer licenseType;
        /**
         * 商户证照编号
         */
        private String licenseNumber;
        /**
         * 联系人姓名
         */
        private String contactName;
        /**
         * 联系人电话
         */
        private String contactPhone;
        /**
         * 联系人邮箱
         */
        private String contactEmail;
        /**
         * 法人姓名
         */
        private String legalPersonName;
        /**
         * 法人证件类型
         */
        private Integer legalPersonLicenseType;
        /**
         * 法人证件号
         */
        private String legalPersonLicenseNumber;
    }
}
