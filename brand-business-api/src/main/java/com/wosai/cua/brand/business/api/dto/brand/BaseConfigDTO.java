package com.wosai.cua.brand.business.api.dto.brand;

import com.wosai.cua.brand.business.api.enums.PurposeOfTransferEnum;
import lombok.Data;

import java.util.List;

@Data
public class BaseConfigDTO {
    /**
     * 是否允许登录
     */
    protected Boolean allowLogin;
    /**
     * 是否允许转账
     */
    protected Boolean openTransfer;

    /**
     * 美团appid
     */
    protected String meiTuanAppid;

    /**
     * 美团密钥
     */
    protected String meiTuanSecret;

    /**
     * 转账用途
     */
    protected List<PurposeOfTransferEnum> purposeList;

    /**
     * 是否需要特殊处理
     */
    protected Boolean needSpecialTreatment = false;
}
