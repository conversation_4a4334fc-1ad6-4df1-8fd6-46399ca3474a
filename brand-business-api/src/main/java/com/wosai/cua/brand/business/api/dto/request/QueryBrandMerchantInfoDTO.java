package com.wosai.cua.brand.business.api.dto.request;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class QueryBrandMerchantInfoDTO {
    private String brandId;
    @JsonProperty("merchant_id")
    private String tokenMerchantId;

    private String merchantSn;

    private String merchantId;

    private String sqbStoreId;

    private String meiTuanStoreSn;

    private String elmStoreSn;

    private String dyStoreSn;

    private Boolean needGetConfig;

    private String recordId;

    private Integer deleted = 0;
}
