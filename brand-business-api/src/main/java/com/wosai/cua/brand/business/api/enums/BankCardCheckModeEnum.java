package com.wosai.cua.brand.business.api.enums;

/**
 * 银行卡校验类型
 * <AUTHOR>
 */
public enum BankCardCheckModeEnum {
    /**
     * 往账鉴权
     */
    REMIT_CHECK("1","往账鉴权"),
    /**
     * 银联鉴权
     */
    UNION_PAY_CHECK("3","银联鉴权（短信）")
    ;
    private final String checkMode;

    private final String desc;

    BankCardCheckModeEnum(String checkMode, String desc) {
        this.checkMode = checkMode;
        this.desc = desc;
    }

    public String getCheckMode() {
        return checkMode;
    }

    public String getDesc() {
        return desc;
    }
}
