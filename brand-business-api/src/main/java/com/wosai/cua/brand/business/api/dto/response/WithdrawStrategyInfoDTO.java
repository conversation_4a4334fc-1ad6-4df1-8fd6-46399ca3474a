package com.wosai.cua.brand.business.api.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wosai.cua.brand.business.api.serializer.BigDecimalToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 提现策略对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WithdrawStrategyInfoDTO {
    /**
     * 策略id
     */
    private String strategyId;

    /**
     * 提现方式：AUTO-自动提现，MANUAL_OPERATION-手动提现
     * @see com.wosai.cua.brand.business.api.enums.WithdrawTypeEnum
     */
    private String withdrawType;

    /**
     * 提现周期类型
     * @see com.wosai.cua.brand.business.api.enums.WithdrawCycleTypeEnum
     */
    private String withdrawCycleType;

    /**
     * 提现周期时间
     */
    private Integer withdrawCycleTime;

    /**
     * 提现周期时间集合
     */
    private String withdrawCycleTimes;

    /**
     * 最少单笔提现金额，单位：元
     */
    @JsonSerialize(using = BigDecimalToStringSerializer.class)
    private BigDecimal minWithdrawalAmount;

    /**
     * 提现预留金额，单位：元
     */
    @JsonSerialize(using = BigDecimalToStringSerializer.class)
    private BigDecimal reservedAmount;

    /**
     * 提现策略描述（用于前端展示）
     */
    private String withdrawStrategyDesc;

    /**
     * 提现备注
     */
    private String remark;
}
