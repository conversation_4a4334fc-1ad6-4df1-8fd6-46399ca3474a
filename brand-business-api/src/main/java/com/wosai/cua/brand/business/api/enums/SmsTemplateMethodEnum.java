package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum SmsTemplateMethodEnum {
    SMS("SMS", "短信"),
    APP("APP", "收钱吧APP"),
    MSP("MSP", "收钱吧MSP")
    ;
    private final String method;

    private final String desc;

    SmsTemplateMethodEnum(String method, String desc) {
        this.method = method;
        this.desc = desc;
    }

    public static SmsTemplateMethodEnum getByMethod(String method) {
        for (SmsTemplateMethodEnum smsTemplateMethodEnum : values()) {
            if (smsTemplateMethodEnum.getMethod().equals(method)) {
                return smsTemplateMethodEnum;
            }
        }
        return null;
    }
}
