package com.wosai.cua.brand.business.api.dto.request;

import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/9/24
 */
@Data
public class PaymentModeChangeRequestDTO {

    @NotBlank(message = "商户ID不能为空")
    private String merchantId;

    /**
     * 目标支付模式 2商家模式 3微信品牌模式 4支付宝品牌模式 5微信支付宝品牌模式
     */
    @NotNull(message = "目标支付模式不能为空")
    private Integer targetPaymentMode;

    @AssertTrue(message = "目标支付模式无效")
    public boolean isValidTargetPaymentMode() {
        return 2 == targetPaymentMode || 3 == targetPaymentMode || 4 == targetPaymentMode || 5 == targetPaymentMode;
    }
}
