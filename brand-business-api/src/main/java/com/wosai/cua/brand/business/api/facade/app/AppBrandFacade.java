package com.wosai.cua.brand.business.api.facade.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppDeleteBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageQueryBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppQueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppReRegisterMemberInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppRequestBaseDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppSubmitOpenAccountRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ModifyBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.response.app.AppSubmitOpenAccountResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * 品牌相关方法
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/app/brand")
@Validated
public interface AppBrandFacade {

    /**
     * 根据商户id查询品牌信息
     * @param queryBrandsDto 查询对象
     * @return 品牌简要集合
     */
    List<BrandSimpleInfoDTO>  getBrandInfoListByMerchantId(QueryBrandsDTO queryBrandsDto);

    /**
     * 根据商户id集合查询品牌信息
     * @param queryBrandsDto 查询对象
     * @return 品牌信息集合
     */
    List<BrandSimpleInfoDTO>  getBrandInfoListByMerchantIds(QueryBrandsDTO queryBrandsDto);

    /**
     * 根据品牌id查询品牌详细信息
     * @param queryBrandsDto 查询对象
     * @return 品牌详细信息
     */
    BrandDetailInfoDTO getBrandDetailInfoByBrandId(QueryBrandsDTO queryBrandsDto);


    /**
     * 批量删除品牌下商户
     * @param deleteBrandMerchantDto 删除品牌下商户对象
     * @return 删除结果
     */
    int deleteBrandMerchant(@Valid AppDeleteBrandMerchantDTO deleteBrandMerchantDto);


    /**
     * 分页查询品牌下商户
     * @param pageQueryBrandMerchantsDto 查询条件对象
     * @return 查询结果
     */
    PageBrandMerchantsDTO pageQueryBrandMerchants(AppPageQueryBrandMerchantsDTO pageQueryBrandMerchantsDto);


    /**
     * 编辑品牌商户
     * @param modifyBrandMerchantRequest 编辑品牌商户请求对象
     * @return 调用结果
     */
    Boolean modifyBrandMerchant(@Valid ModifyBrandMerchantDTO modifyBrandMerchantRequest);

    /**
     * 根据商户id查询品牌商户信息
     * @param queryBrandMerchantInfo 查询条件
     * @return 查询结果
     */
    BrandMerchantInfoDTO getBrandMerchantInfoByMerchantSn(QueryBrandMerchantInfoDTO queryBrandMerchantInfo);

    /**
     * 登记
     * @param appRequest 请求参数
     * @return boolean
     */
    Boolean registerBehaviorRecord(AppRequestBaseDTO appRequest);

    /**
     * 重新注册会员信息
     *
     * @param request 请求对象
     * @return 返回结果
     */
    CreateMerchantResponseDTO reRegisterMemberInfo(AppReRegisterMemberInfoDTO request);

    /**
     * 根据门店id查询品牌商户信息
     * @return 查询结果
     */
    BrandMerchantInfoDTO getBrandMerchantInfoByStoreId(AppQueryBrandMerchantInfoDTO queryBrandMerchantRequest);

    /**
     * 提交开通账户
     * @param request 请求对象
     * @return 返回结果
     */
    AppSubmitOpenAccountResponseDTO submitOpenAccount(@Valid AppSubmitOpenAccountRequestDTO request);
}
