package com.wosai.cua.brand.business.api.annotations;

import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MerchantFieldNotEmpty {
    String targetField();

    BrandMerchantTypeEnum compareValue();

    String message() default "value can not be bank!";

    @Target({ElementType.METHOD, ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @interface List {
        MerchantFieldNotEmpty[] value();
    }
}
