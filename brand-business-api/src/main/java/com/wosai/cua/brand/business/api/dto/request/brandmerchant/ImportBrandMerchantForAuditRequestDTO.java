package com.wosai.cua.brand.business.api.dto.request.brandmerchant;

import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/30
 */
@Data
public class ImportBrandMerchantForAuditRequestDTO {

    @NotBlank(message = "品牌SN不能为空")
    private String brandSn;

    @NotNull(message = "导入类型不能为空")
    private ExcelImportTypeEnum auditType;

    @NotBlank(message = "文件链接不能为空")
    private String file;

    @NotBlank(message = "审批流水号不能为空")
    private String auditSn;

    @NotBlank(message = "审批ID不能为空")
    private String auditId;

    @NotBlank(message = "提交人ID不能为空")
    private String operatorId;

    @NotBlank(message = "提交平台不能为空")
    private String platform;

    private Map<String, Object> formData;
}
