package com.wosai.cua.brand.business.api.dto.response;

import com.wosai.cua.brand.business.api.enums.MerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BrandMerchantDTO {

    /**
     * 商户id
     */
    private String merchantId;
    /**
     * 商户名称
     */
    private String merchantName;
    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 商户证照类型：0-无营业执照（个人/小微） 1-个体工商户营业执照 2-企业营业执照 11-统一社会信用代码（企业） 其他：未知
     */
    private Integer merchantBusinessLicenseType;

    /**
     * 商户业务类型
     * @see MerchantTypeEnum
     */
    private String merchantType;

    private String merchantTypeDesc;

    /**
     * 商户对接方式:SEPARATE_ACCOUNT-分账，COLLECTION-归集（两种）
     * 不传，默认：分账模式
     * @see MerchantDockingModeEnum
     */
    private MerchantDockingModeEnum merchantDockingMode;

    private String type;

    private String typeDesc;
    /**
     * 商户联系人
     */
    private String merchantContactName;

    /**
     * 商户联系人联系方式
     */
    private String merchantContactPhone;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 子账号
     */
    private String subAccountNo;

    /**
     * 系统会员id
     */
    private String memberId;

    /**
     * 提现策略
     */
    private String strategyId;

    /**
     * 银行卡激活状态
     */
    private String bankCardActivateStatus;
    /**
     * 银行卡激活状态描述
     */
    private String bankCardActivateStatusDesc;
    /**
     * 关联品牌商户门店编号
     */
    private String associatedSqbStoreId;

    /**
     * 品牌商户门店编号
     */
    private String sqbStoreSn;

    /**
     * 关联美团门店号
     */
    private String associatedMeituanStoreSn;

    /**
     * 美团门店状态
     */
    private String meiTuanStoreStatus;

    /**
     * 关联饿了么门店号
     */
    private String associatedElmStoreSn;

    /**
     * 商户账户开通状态
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum
     */
    private String accountOpenStatus;

    /**
     * 商户账户开通状态描述
     */
    private String accountOpenStatusDesc;

    /**
     * 商户账户开通失败原因
     */
    private String accountOpenFailureReason;
    /**
     * 资管机构编码
     */
    private String fundManagementCompanyCode;
    /**
     * 资管机构，PAB-平安银行
     */
    private String fundManagementCompany;
    /**
     * 激活链接
     */
    private String activationUrl;

    /**
     * 激活短链接
     */
    private String activationShortUrl;

    /**
     * 外部商户号
     */
    private String outMerchantNo;

    /**
     * 商户扩展信息
     */
    private ExtraDTO extra;

    /**
     * 充值账户
     */
    private String topUpAccountNo;

    /**
     * 商户账户开通时间
     */
    private Date accountOpenedTime;

    /**
     * 品牌商户关联时间
     */
    private Date associatedTime;

    /**
     * 抖音门店编号
     */
    private String dyStoreSn;

}
