package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum BrandTaskTypeEnum {
    /**
     *
     */
    MERCHANT_IMPORT(0,"品牌商户导入"),
    MERCHANT_EXPORT(1,"品牌商户导出"),
    BANK_CARD_ACTIVE_STATE_EXPORT(2,"银行卡激活状态导出"),
    NOT_BIND_BANK_CARD_EXPORT(3,"未绑定银行卡商户导出"),
    BUSINESS_OPEN_ADD_MICRO_MERCHANT(4,"批量新增小微商户"),
    BUSINESS_OPEN_ADD_NO_MICRO_MERCHANT(5,"批量新增非小微商户"),
    BUSINESS_OPEN_ASSOCIATE_MERCHANT(6,"批量关联商户"),
    BATCH_CHANGE_PAYMENT_MODE(7,"批量切换支付模式"),
    BATCH_REMOVE_MERCHANT_ASSOCIATION(8,"批量解除关联关系"),
    RECONCILIATION_ADD_MERCHANT(9,"对账申请增加商户"),
    RECONCILIATION_DELETE_MERCHANT(10,"对账申请删除商户"),
    ;
    private final Integer taskType;

    private final String desc;


    BrandTaskTypeEnum(Integer taskType, String desc) {
        this.taskType = taskType;
        this.desc = desc;
    }
}
