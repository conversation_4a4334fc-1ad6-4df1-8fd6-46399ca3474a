package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum ArrangementStatusEnum {
    NOT_EXIST("NOT_EXIST", "不存在"),
    UN_VALID("UN_VALID", "已申请，未生效"),
    CANCELING("VALID", "已生效"),
    CANCELLED("INVALID_TO_BE_CONFIRM", "解约中待平台确认"),
    ;

    private final String code;
    private final String desc;

    ArrangementStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ArrangementStatusEnum getByCode(String code) {
        for (ArrangementStatusEnum arrangementStatusEnum : ArrangementStatusEnum.values()) {
            if (arrangementStatusEnum.getCode().equals(code)) {
                return arrangementStatusEnum;
            }
        }
        return null;
    }
}
