package com.wosai.cua.brand.business.api.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/24
 */
@Data
@Accessors(chain = true)
public class PaymentModeChangeResponseDTO {

    private Boolean success;

    private String msg;

    public static PaymentModeChangeResponseDTO fail(String msg) {
        return new PaymentModeChangeResponseDTO().setSuccess(false).setMsg(msg);
    }

    public static PaymentModeChangeResponseDTO success() {
        return new PaymentModeChangeResponseDTO().setSuccess(true);
    }
}
