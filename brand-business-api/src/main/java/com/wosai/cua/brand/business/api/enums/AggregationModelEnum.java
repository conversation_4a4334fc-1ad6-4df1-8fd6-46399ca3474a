package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum AggregationModelEnum {

    BALANCE_POOLING("BALANCE_POOLING","01", "余额归集"),
    ORDER_PRE_COLLECTION("ORDER_PRE_COLLECTION","03","订单预归集"),
    ORDERS_COLLECTED_AFTERWARDS("ORDERS_COLLECTED_AFTERWARDS","04","订单事后归集");

    private final String code;
    private final String fyCode;
    private final String desc;

    AggregationModelEnum(String code,String fyCode, String desc) {
        this.code = code;
        this.desc = desc;
        this.fyCode = fyCode;
    }

    public static AggregationModelEnum getByCode(String code) {
        for (AggregationModelEnum aggregationModelEnum : AggregationModelEnum.values()) {
            if (aggregationModelEnum.getCode().equals(code)) {
                return aggregationModelEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        for (AggregationModelEnum aggregationModelEnum : AggregationModelEnum.values()) {
            if (aggregationModelEnum.getCode().equals(code)) {
                return aggregationModelEnum.getDesc();
            }
        }
        return null;
    }
}
