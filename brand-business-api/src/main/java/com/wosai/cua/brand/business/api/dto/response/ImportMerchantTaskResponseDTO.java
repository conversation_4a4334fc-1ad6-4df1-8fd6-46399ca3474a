package com.wosai.cua.brand.business.api.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ImportMerchantTaskResponseDTO {

    private Long total;

    private List<TaskApplyLogDTO> records;

    @Data
    @Builder
    public static class TaskApplyLogDTO {

        private String fileName;

        private String applyTime;

        private Integer status;

        private String statusDesc;

        private String result;

        private String downloadUrl;
    }
}
