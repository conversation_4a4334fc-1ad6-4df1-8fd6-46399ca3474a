package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * 资管机构枚举
 * Created by 林尚华 on 2024/07/17.
 */
@Getter
public enum FundManagementCompanyEnum {
    MY_BANK("MY_BANK", "网商银行", false),
    PAB("PAB", "平安银行", true),
    CITIC("CITIC", "中信银行", true),
    FUIOU("FUIOU", "富友支付", true);

    private final String fundManagementCompanyCode;
    private final String fundManagementCompanyName;
    private final Boolean needCreateBankAccount;

    FundManagementCompanyEnum(String fundManagementCompanyCode, String fundManagementCompanyName, Boolean needCreateBankAccount) {
        this.fundManagementCompanyCode = fundManagementCompanyCode;
        this.fundManagementCompanyName = fundManagementCompanyName;
        this.needCreateBankAccount = needCreateBankAccount;
    }

    public static FundManagementCompanyEnum getFundManagementCompanyEnum(String fundManagementCompanyCode) {
        for (FundManagementCompanyEnum fundManagementCompanyEnum : FundManagementCompanyEnum.values()) {
            if (fundManagementCompanyEnum.getFundManagementCompanyCode().equals(fundManagementCompanyCode)) {
                return fundManagementCompanyEnum;
            }
        }
        return null;
    }

    public static String getFundManagementCompanyName(String fundManagementCompanyCode) {
        for (FundManagementCompanyEnum fundManagementCompanyEnum : FundManagementCompanyEnum.values()) {
            if (fundManagementCompanyEnum.getFundManagementCompanyCode().equals(fundManagementCompanyCode)) {
                return fundManagementCompanyEnum.getFundManagementCompanyName();
            }
        }
        return null;
    }
}
