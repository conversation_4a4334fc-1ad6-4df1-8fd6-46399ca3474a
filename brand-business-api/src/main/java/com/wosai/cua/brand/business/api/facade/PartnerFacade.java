package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.CreatePartnerRequestDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/partner")
@Validated
public interface PartnerFacade {
    /**
     * 创建合作方（内部调用）
     * @param createPartnerRequest 创建合作方请求参数
     * @return 合作方id
     */
    String createPartner(@Valid CreatePartnerRequestDTO createPartnerRequest);
}
