package com.wosai.cua.brand.business.api.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 判断是否允许切换收单机构的返回结果
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ChangeAcquirerCheckResponseDTO {

    private Boolean allowed;

    private String msg;

    public static ChangeAcquirerCheckResponseDTO notAllow(String msg) {
        return new ChangeAcquirerCheckResponseDTO().setAllowed(false).setMsg(msg);
    }

    public static ChangeAcquirerCheckResponseDTO allow() {
        return new ChangeAcquirerCheckResponseDTO().setAllowed(true);
    }
}