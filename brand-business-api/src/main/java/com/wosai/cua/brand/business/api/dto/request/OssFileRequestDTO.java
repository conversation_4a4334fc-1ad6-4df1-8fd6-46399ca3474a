package com.wosai.cua.brand.business.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.api.enums.AggregationModelEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.UseOfFundsEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OssFileRequestDTO {

    /**
     * bucket : private-wosai-statics
     * file_url : 文件路径
     * oss_key : osp/render/file/文件测试_1703492540854.pdf
     */
    @NotBlank(message = "bucket不能为空")
    private String bucket;
    @NotBlank(message = "file_url不能为空")
    @JsonProperty("file_url")
    private String fileUrl;
    @NotBlank(message = "oss_key不能为空")
    @JsonProperty("oss_key")
    private String ossKey;
    @NotBlank(message = "branId不能为空")
    private String brandId;
    private Long strategyId;
    /**
     * 对接模式:默认分账
     */
    private BrandMerchantDockingModeEnum dockingMode = BrandMerchantDockingModeEnum.SEPARATE_ACCOUNT;

    /**
     * 归集模式:BALANCE_POOLING-余额归集，ORDERS_COLLECTED_AFTERWARDS-订单事后归集，ORDER_PRE_COLLECTION-订单预归集（三种）
     */
    private AggregationModelEnum aggregationModel = AggregationModelEnum.BALANCE_POOLING;

    /**
     * 归集最大比例
     * 如：100% = 10000
     */
    private Integer concentrateScale = 80;

    /**
     * 资金用途
     */
    private UseOfFundsEnum useOfFunds;
}
