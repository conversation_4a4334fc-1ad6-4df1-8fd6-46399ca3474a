package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum ApplicableSceneEnum {
    CONFIGURATION("配置"),
    INTERFACE("接口");

    private final String desc;

    ApplicableSceneEnum(String desc) {
        this.desc = desc;
    }

    public static ApplicableSceneEnum getEnumByName(String name) {
        for (ApplicableSceneEnum applicableSceneEnum : ApplicableSceneEnum.values()) {
            if (applicableSceneEnum.name().equals(name)) {
                return applicableSceneEnum;
            }
        }
        return null;
    }

    public static String getDescByName(String name) {
        for (ApplicableSceneEnum applicableSceneEnum : ApplicableSceneEnum.values()) {
            if (applicableSceneEnum.name().equals(name)) {
                return applicableSceneEnum.getDesc();
            }
        }
        return "";
    }
}
