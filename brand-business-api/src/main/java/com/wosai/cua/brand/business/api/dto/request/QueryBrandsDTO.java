package com.wosai.cua.brand.business.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 查询品牌的DTO对象
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class QueryBrandsDTO {

    @JsonProperty("merchant_id")
    private String merchantId;

    private List<String> merchantIds;

    private String brandId;

    private String brandSn;

    private Boolean needGetConfig;
}
