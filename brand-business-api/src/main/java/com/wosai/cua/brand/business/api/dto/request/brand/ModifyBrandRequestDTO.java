package com.wosai.cua.brand.business.api.dto.request.brand;

import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ModifyBrandRequestDTO {
    /**
     * 品牌id
     */
    @NotBlank(message = "品牌id必填！")
    private String brandId;
    /**
     * 品牌名称
     */
    private String name;
    /**
     * 简称
     */
    private String alias;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 行业编号
     */
    private String industry;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 品牌配置
     */
    private ConfigDTO config;

    /**
     * 银行账户信息
     */
    private AccountsDTO accounts;

    /**
     * 是否开通收付通：0-否，1-是
     */
    private Integer sftTag;
}
