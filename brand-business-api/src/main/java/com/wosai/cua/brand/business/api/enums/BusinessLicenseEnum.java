package com.wosai.cua.brand.business.api.enums;

/**
 * 证照枚举
 * <AUTHOR>
 */

public enum BusinessLicenseEnum {
    /**
     * 小微商户（个人）
     */
    MICRO_MERCHANT(0,"小微商户（无营业执照）"),
    INDIVIDUAL_BUSINESS_MERCHANT(1,"个体工商户营业执照"),

    ENTERPRISE_BUSINESS_LICENSE(2,"企业营业执照"),

    UNIFIED_SOCIAL_CREDIT_IDENTIFIER(11,"统一社会信用代码")
    ;
    private final Integer type;

    private final String desc;


    BusinessLicenseEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
