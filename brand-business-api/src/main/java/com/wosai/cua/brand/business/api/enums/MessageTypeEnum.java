package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum MessageTypeEnum {
    ENROLL("ENROLL","报备"),
    BRAND_CREATE("BRAND_CREATE", "品牌创建"),
    BRAND_MERCHANT_STATUS_CHANGE("BRAND_MERCHANT_STATUS_CHANGE", "品牌商户状态变更"),
    ;
    private final String messageType;

    private final String desc;


    MessageTypeEnum(String messageType, String desc) {
        this.messageType = messageType;
        this.desc = desc;
    }
}
