package com.wosai.cua.brand.business.api.dto.request.merchant;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ModifyBrandMerchantDTO {

    @NotBlank(message = "品牌ID不能为空")
    private String brandId;

    @NotBlank(message = "商户ID不能为空")
    private String merchantId;

    private String merchantSn;
    /**
     * 法人身份证号
     */
    private String legalPersonNumber;
    /**
     * 银行卡号
     */
    private String bankCarNumber;
    /**
     * 银行预留手机号
     */
    private String reservedMobileNumber;
    /**
     * 统一社会信用代码
     */
    private String unifyTheSocialCreditCode;
    /**
     * 美团门店编号
     */
    private String meiTuanStoreSn;
    /**
     * 饿了么门店编号
     */
    private String elmStoreSn;

    /**
     * 抖音门店编号
     */
    private String dyStoreSn;

    /**
     * 外部商户编号
     */
    private String outMerchantNo;

    /**
     * 提现策略ID
     */
    private Long strategyId;
}
