package com.wosai.cua.brand.business.api.dto.response;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BrandListInfoDTO {
    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 品牌编号
     */
    private String sn;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 商户数量
     */
    private Integer merchantNumber;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 品牌账号
     */
    private String identifier;

    /**
     * 品牌资金托管银行
     */
    private FundManagementCompanyEnum fundManagementCompanyCode;

    /**
     * 是否启用收付通
     */
    private Integer enableSft;
}
