package com.wosai.cua.brand.business.api.dto.brand.mybank;

import com.wosai.cua.brand.business.api.dto.brand.BaseConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MyBankConfigDTO extends BaseConfigDTO {
    /**
     * 是否开通充值账户
     */
    private Boolean openTopUpAccount;
    /**
     * 应用ID
     * <p>
     * 由浙江网商银行统一分配,用于识别合作伙伴应用系统，即对端系统编号。
     * 联调前线下提供。
     */
    private String appId;

    /**
     * 合作方机构号（网商银行分配）
     */
    private String isvOrgId;

    /**
     * ISV请求报文签名私钥
     * 密钥格式：PKCS8
     * 密钥长度：2048
     * 公私钥生成可参考支付宝开放平台JAVA签名工具使用
     */
    private String isvPrivateKey;

    /**
     * isv机构公钥
     * （网商银行）
     */
    private String isvPublicKey;

    /**
     * 网商银行网关公钥
     * <p>
     * 用于验签网商银行网关返回的报文及网关主动发送的通知报文
     */
    private String publicKey;
}
