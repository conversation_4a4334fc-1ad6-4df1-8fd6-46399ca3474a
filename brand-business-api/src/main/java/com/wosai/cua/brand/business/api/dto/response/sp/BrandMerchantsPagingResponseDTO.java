package com.wosai.cua.brand.business.api.dto.response.sp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/29
 */
@Data
@Accessors(chain = true)
public class BrandMerchantsPagingResponseDTO {

    private String merchantId;
    private String merchantSn;
    private String merchantName;
    private String merchantBusinessName;
    private String contactName;
    private String contactCellphone;
    private Integer paymentMode;
    private Date associatedTime;
}
