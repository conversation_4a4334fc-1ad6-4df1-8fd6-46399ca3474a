package com.wosai.cua.brand.business.api.facade.crm;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.SearchBankInfoRequest;
import com.wosai.cua.brand.business.api.dto.response.BankInfoResponseDTO;
import org.springframework.validation.annotation.Validated;
import vo.ApiRequestParam;

import javax.validation.Valid;
import java.util.Map;

@JsonRpcService(value = "rpc/crm/bank/account")
@Validated
public interface CrmBrandMerchantBankAccountFacade {
    /**
     * 查询银行卡详情
     * @param apiRequestParam 请求对象
     * @return 银行卡详情对象
     */
    BankInfoResponseDTO getBankInfoByCardNumber(@Valid ApiRequestParam<SearchBankInfoRequest, Map> apiRequestParam);
}
