package com.wosai.cua.brand.business.api.dto.request.brand;

import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Data
public class BrandPaymentHubOpenRequestDTO {

    /**
     * 品牌SN
     */
    private String brandSn;

    /**
     * 主商户商户号
     */
    private String merchantSn;

    /**
     * BD的ID
     */
    @NotBlank(message = "提交人ID不能为空")
    private String operatorId;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌简称
     */
    private String alias;
    /**
     * 省code
     */
    private String provinceCode;
    /**
     * 省
     */
    private String province;
    /**
     * 市code
     */
    private String cityCode;
    /**
     * 市
     */
    private String city;
    /**
     * 区code
     */
    private String districtCode;
    /**
     * 区
     */
    private String district;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 行业编号
     */
    private String industry;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;
    /**
     * 父品牌id
     */
    private String parentBrandId;
    /**
     * 备注
     */
    private String remark;

    /**
     * 资管机构编号
     */
    private FundManagementCompanyEnum fundManagementCompanyCode;

    /**
     * 资管机构
     */
    private String fundManagementCompany;

    /**
     * 品牌配置
     */
    private ConfigDTO config;

    /**
     * 银行账户信息
     */
    private AccountsDTO accounts;
}
