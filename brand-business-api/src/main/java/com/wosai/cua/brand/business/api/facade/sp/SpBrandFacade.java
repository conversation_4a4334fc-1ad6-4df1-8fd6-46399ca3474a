package com.wosai.cua.brand.business.api.facade.sp;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.sp.PagingBrandMerchantsRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.common.ListResult;
import com.wosai.cua.brand.business.api.dto.response.sp.BrandMerchantsPagingResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.sp.BrandOpenAppInfoQueryResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.sp.BrandSimpleInfoResponseDTO;
import com.wosai.cua.brand.business.api.enums.AppOpenStatusEnum;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@JsonRpcService(value = "rpc/sp/brand")
@Validated
public interface SpBrandFacade {

    /**
     * 查询品牌开通的应用信息
     *
     * @param queryBrandsDTO 品牌ID
     * @return 开通的应用信息列表
     */
    List<BrandOpenAppInfoQueryResponseDTO> queryBrandOpenAppInfos(QueryBrandsDTO queryBrandsDTO);

    /**
     * 品牌分页查询商户信息
     * @param pagingBrandMerchantsRequest 请求参数
     * @return 查询结果
     */
    ListResult<BrandMerchantsPagingResponseDTO> pagingBrandMerchants(@Valid PagingBrandMerchantsRequestDTO pagingBrandMerchantsRequest);

    /**
     * 查询品牌主商户的营业执照信息
     * @param queryBrandsDTO 品牌ID
     * @return 营业执照信息
     */
    Map<String, Object> queryBrandMerchantBusinessLicense(QueryBrandsDTO queryBrandsDTO);

    /**
     * 查询品牌主商户的收付通开通状态
     * @param queryBrandsDTO 品牌ID
     * @return 收付通开通状态
     */
    AppOpenStatusEnum queryBrandPaymentHubOpenStatus(QueryBrandsDTO queryBrandsDTO);

    /**
     * 查询商户 ID对应的基本品牌信息
     * @param queryBrandsDTO 商户ID
     * @return 品牌基本信息
     */
    BrandSimpleInfoResponseDTO queryBrandSimpleInfoByMerchantId(QueryBrandsDTO queryBrandsDTO);
}
