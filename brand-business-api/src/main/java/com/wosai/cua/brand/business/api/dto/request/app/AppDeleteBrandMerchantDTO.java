package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AppDeleteBrandMerchantDTO {

    @JsonProperty("merchant_id")
    private String tokenMerchantId;

    @NotEmpty(message = "商户id集合不能为空！")
    private List<String> merchantIdList;
}
