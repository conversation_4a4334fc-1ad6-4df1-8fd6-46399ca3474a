package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.brand.TemplateConfigDTO;
import com.wosai.cua.brand.business.api.dto.request.QuerySmsTemplateConfigRequest;
import com.wosai.cua.brand.business.api.dto.request.app.DictionaryRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.DictionaryResponseDTO;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/brand/common")
@Validated
public interface CommonFacade {
    /**
     * 获取字典列表
     * @param dto 请求对象
     * @return 枚举对应列表
     */
    List<DictionaryResponseDTO> getDictionaryList(DictionaryRequestDTO dto);

    @Deprecated
    Map<String, Object> getApolloConfig();

    /**
     * 获取模板配置
     * @return 模板配置
     */
    List<TemplateConfigDTO> getSmsTemplateConfig(QuerySmsTemplateConfigRequest request);
}
