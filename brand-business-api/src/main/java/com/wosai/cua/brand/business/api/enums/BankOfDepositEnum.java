package com.wosai.cua.brand.business.api.enums;

/**
 * <AUTHOR>
 */

public enum BankOfDepositEnum {
    /**
     * 银行代码与对应的银行枚举
     */
    PAB("PAB","平安银行"),
    CMBC("CMBC","中国民生银行"),
    CMB("CMB","招商银行"),
    SPDB("SPDB","浦发银行"),
    ECITIC("ECITIC","中信银行"),
    CIB("CIB","兴业银行"),
    CGB("CGB","广发银行"),
    HXB("HXB","华夏银行"),
    CEB("CEB","中国光大银行"),
    ICBC("ICBC","中国工商银行"),
    CCB("CCB","中国建设银行"),
    ABC("ABC","中国农业银行"),
    BOC("BOC","中国银行"),
    BOCOM("BOCOM","交通银行"),

    BOSC("BOSC","上海银行")
    ;
    private final String bankOfDeposit;

    private final String bankName;

    BankOfDepositEnum(String bankOfDeposit, String bankName) {
        this.bankOfDeposit = bankOfDeposit;
        this.bankName = bankName;
    }

    public String getBankOfDeposit() {
        return bankOfDeposit;
    }

    public String getBankName() {
        return bankName;
    }

    public static String getBankOfDepositByName(String bankName){
        for (BankOfDepositEnum value : BankOfDepositEnum.values()){
            if (value.getBankName().equals(bankName)){
                return value.bankOfDeposit;
            }
        }
        return null;
    }

    public static String getBankNameByDeposit(String bankOfDeposit){
        for (BankOfDepositEnum value : BankOfDepositEnum.values()){
            if (value.getBankOfDeposit().equals(bankOfDeposit)){
                return value.bankName;
            }
        }
        return null;
    }
}
