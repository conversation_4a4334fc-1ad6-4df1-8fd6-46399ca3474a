package com.wosai.cua.brand.business.api.dto.kafka;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BrandMerchantStatusChangeMsg {
    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 主商户 ID
     */
    private String mainMerchantId;
    /**
     * 商户 ID
     */
    private List<String> merchantIds;
    /**
     * 状态 1绑定 0解绑
     */
    private Integer status;
    /**
     * 是否删除品牌
     */
    private boolean deleteBrand;
}
