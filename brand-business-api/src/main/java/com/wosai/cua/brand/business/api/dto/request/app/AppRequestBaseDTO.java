package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021-04-16 18:18
 * @Description:
 */
@Data
public class AppRequestBaseDTO {

    private String id;

    @JsonProperty("store_id")
    private String storeId;

    private String role;

    @JsonProperty("merchant_id")
    private String merchantId;

    @JsonProperty("client_version")
    private String clientVersion;

    @JsonProperty("msp_account_id")
    private String mspAccountId;

    @JsonProperty("account_id")
    private String accountId;

    @JsonProperty("merchant_user_id")
    private String merchantUserId;

    @JsonProperty("uc_user_id")
    private String ucUserId;

    @JsonProperty("group_user_id")
    private String groupUserId;

    @JsonProperty("group_id")
    private String groupId;

    @JsonProperty("sub_appid")
    private String subAppid;

    @JsonProperty("sqb_appid")
    private String sqbAppid;

    @JsonProperty("role_id")
    private String roleId;

    private String token;

    private String brandId;
}
