package com.wosai.cua.brand.business.api.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandSmsTemplateConfigDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BrandDetailInfoDTO implements Serializable {
    private static final long serialVersionUID = 151236786109319767L;
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 品牌编号
     */
    private String sn;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌简称
     */
    private String alias;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 对接方式
     *
     * @see com.wosai.cua.brand.business.api.enums.DockingModeEnum
     */
    private String dockingMode;

    /**
     * 对接方式描述
     */
    private String dockingModeDesc;

    /**
     * 对接模式
     */
    private String merchantDockingMode;
    /**
     * 对接模式描述
     */
    private String merchantDockingModeDesc;

    /**
     * 父品牌id
     */
    private String parentId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 行业
     */
    private String industry;

    /**
     * 行业id数组（前端展示需要）
     */
    private List<String> industryArray;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;
    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 合作方id
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在config中
     * @see BrandConfigDTO
     */
    @Deprecated
    private String partnerId;

    /**
     * 资管机构编号，MY_BANK、PAB
     */
    private FundManagementCompanyEnum fundManagementCompanyCode;

    /**
     * 资管机构，PAB-平安银行
     */
    private String fundManagementCompany;

    /**
     * 资金汇总专用账名
     * @deprecated 该字段在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在accounts字段中
     * @see AccountsDTO
     */
    @Deprecated
    private String fundSummaryAccountName;

    /**
     * 资金汇总专用账户
     * @deprecated 该字段在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在accounts字段中
     * @see AccountsDTO
     */
    @Deprecated
    private String fundSummaryAccount;

    /**
     * 资金归集账户
     * @deprecated 该字段在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在accounts字段中
     * @see AccountsDTO
     */
    @Deprecated
    private String fundGatherAccount;

    /**
     * 资金结算账户名称
     * @deprecated 该字段在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在accounts字段中
     * @see AccountsDTO
     */
    @Deprecated
    private String fundSettlementAccountName;

    /**
     * 资金结算账户
     * @deprecated 该字段在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在accounts字段中
     * @see AccountsDTO
     */
    @Deprecated
    private String fundSettlementAccount;

    /**
     * 品牌层级
     */
    private Integer level;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    private List<String> districtCodeArray;

    private BrandConfigDTO config;

    private AccountsDTO accounts;

    /**
     * 是否启用收付通：0-否，1-是
     */
    private Integer enableSft;

    /**
     * 是否开通收付通：0-否，1-是
     */
    private Integer sftTag;

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO");
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO");
    }
}
