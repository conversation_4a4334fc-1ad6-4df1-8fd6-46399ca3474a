package com.wosai.cua.brand.business.api.dto.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreatePartnerRequestDTO {
    @NotBlank(message = "品牌编号不能为空！")
    private String brandSn;
    /**
     * 商户号 不可空
     */
    @JSONField(name = "mrch_code")
    @NotBlank(message = "merchantCode不可为空！")
    private String merchantCode;
    /**
     * 商户名称
     */
    @JSONField(name = "tran_web_name")
    @NotBlank(message = "tranWebName不能为空！")
    private String tranWebName;
    /**
     * 商户网号，不可空
     */
    @JSONField(name = "net_number")
    @NotBlank(message = "netNumber不能为空！")
    private String netNumber;
    /**
     *  app_id 银行提供，不可空
     */
    @JSONField(name = "app_id")
    @NotBlank(message = "appId不能为空！")
    private String appId;
    /**
     * app_secret 银行提供，不可空
     */
    @JSONField(name = "app_secret")
    @NotBlank(message = "appSecret不能为空！")
    private String appSecret;
    /**
     * ecif	银行提供, 不可空
     */
    @JSONField(name = "ecif")
    @NotBlank(message = "eCif不能为空！")
    private String ecif;
    /**
     * 统一编号	银行提供，不可空
     */
    @JSONField(name = "org_code")
    @NotBlank(message = "orgCode不能为空！")
    private String orgCode;
    /**
     * 资金汇总账号	银行提供，不可空
     */
    @JSONField(name = "fund_summary_acct_no")
    @NotBlank(message = "fundSummaryAcctNo不能为空！")
    private String fundSummaryAcctNo;
    /**
     * 私钥	银行提供，不可空
     */
    @JSONField(name = "private_key")
    @NotBlank(message = "privateKey不能为空！")
    private String privateKey;
    /**
     * 公钥	银行提供,不可空
     */
    @JSONField(name = "public_key")
    @NotBlank(message = "publicKey不能为空！")
    private String publicKey;

    /**
     * 扩展信息
     */
    private String extension;
}
