package com.wosai.cua.brand.business.api.dto.brand.fuiou;

import com.wosai.cua.brand.business.api.dto.brand.BaseConfigDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.DepositStrategyConfigEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FuiouConfigDTO extends BaseConfigDTO {
    /**
     * 商户公钥
     */
    private String publicKey;
    /**
     * 商户私钥
     */
    private String privateKey;
    /**
     * 富管家商户号
     */
    private String merchantNo;
    /**
     * 富管家私钥
     */
    private String fyPrivateKey;
    /**
     * 富管家公钥
     */
    private String fyPublicKey;

    /**
     * 对接模式
     */
    private BrandMerchantDockingModeEnum merchantDockingMode;

    /**
     * 入金策略
     * @see DepositStrategyConfigEnum
     */
    private DepositStrategyConfigEnum depositStrategy;
}
