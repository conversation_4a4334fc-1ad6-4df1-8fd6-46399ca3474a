package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.dto.request.merchant.BaseCreateMerchantDTO;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreateMerchantDTO {
    /**
     * 品牌商户类型
     * @see BrandMerchantTypeEnum
     */
    @NotNull(message = "商户类型不能为空！")
    private BrandMerchantTypeEnum brandMerchantType;

    /**
     * brandMerchantType为COMPANY的时候不能为空！
     */
    private BaseCreateMerchantDTO.CompanyMerchantDTO companyMerchant;

    /**
     * brandMerchantType为PERSONAL的时候不能为空！
     */
    private BaseCreateMerchantDTO.PersonalMerchantDTO personalMerchant;

    /**
     * brandMerchantType为COMPANY的时候不能为空！
     */
    private BaseCreateMerchantDTO.IndividualBusinessMerchant individualBusinessMerchant;

    /**
     * 是否需要发送短信通知用户
     */
    private Boolean needSendSms;
}
