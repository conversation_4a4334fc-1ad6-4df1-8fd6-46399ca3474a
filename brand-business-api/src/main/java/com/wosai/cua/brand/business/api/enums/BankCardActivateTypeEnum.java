package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * 银行卡激活方式枚举
 * <AUTHOR>
 */

@Getter
public enum BankCardActivateTypeEnum {
    /**
     * 个人办理
     */
    PERSONAL("1","个人办理"),

    LEGAL_PERSON("2","法人办理"),

    AGENT("3","经办人办理")
    ;

    private final String activateType;

    private final String desc;


    BankCardActivateTypeEnum(String activateType, String desc) {
        this.activateType = activateType;
        this.desc = desc;
    }
}
