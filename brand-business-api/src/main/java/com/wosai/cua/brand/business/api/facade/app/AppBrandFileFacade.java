package com.wosai.cua.brand.business.api.facade.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.app.AppExportBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.ImportMerchantTaskResponseDTO;
import org.springframework.validation.annotation.Validated;

@JsonRpcService(value = "rpc/app/brand/file")
@Validated
public interface AppBrandFileFacade {

    /**
     * 导出品牌商户
     * @param exportBrandMerchantRequest 导出请求对象
     */
    Boolean exportBrandMerchant(AppExportBrandMerchantRequestDTO exportBrandMerchantRequest);

    /**
     * 分页获取任务列表
     * @param pageRequest 分页入参
     * @return 任务结果
     */
    ImportMerchantTaskResponseDTO getTaskList(AppPageRequestDTO pageRequest);
}