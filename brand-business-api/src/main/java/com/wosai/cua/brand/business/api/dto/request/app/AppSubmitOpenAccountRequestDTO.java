package com.wosai.cua.brand.business.api.dto.request.app;

import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;

/**
 * 提交开户接口请求参数
 */
@Data
public class AppSubmitOpenAccountRequestDTO {

    /**
     * 校验必填字段
     */
    public void validateRequiredFields() {
        switch (merchantType) {
            case PERSONAL:
                if (StringUtils.isBlank(name)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "个人类型商户姓名不能为空");
                }
                if (StringUtils.isBlank(idNumber)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "个人类型商户身份证号不能为空");
                }
                if (StringUtils.isBlank(mobile)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "个人类型商户手机号不能为空");
                }
                break;
            case INDIVIDUAL_BUSINESS:
            case COMPANY:
                if (StringUtils.isBlank(businessLicenseName)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "营业执照名称不能为空");
                }
                if (StringUtils.isBlank(socialCreditCode)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "社会信用代码不能为空");
                }
                if (StringUtils.isBlank(legalPersonName)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "法人姓名不能为空");
                }
                if (legalPersonIdType == null) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "法人证件类型不能为空");
                }
                if (StringUtils.isBlank(legalPersonIdNumber)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "法人证件号不能为空");
                }
                if (StringUtils.isBlank(signingMobile)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "签约手机号不能为空");
                }
                break;
        }
    }


    @NotNull(message = "品牌id不能为空")
    private String brandId;

    @NotNull(message = "品牌商户编号不能为空")
    private String brandMerchantSn;

    @NotNull(message = "品牌商户类型不能为空")
    private BrandMerchantTypeEnum merchantType;

    /**
     * 营业执照名称
     */
    private String businessLicenseName;

    /**
     * 社会信用代码
     */
    private String socialCreditCode;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人证件类型
     */
    private Integer legalPersonIdType;

    /**
     * 法人证件号
     */
    private String legalPersonIdNumber;

    /**
     * 签约手机号
     */
    private String signingMobile;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 手机号
     */
    private String mobile;
}