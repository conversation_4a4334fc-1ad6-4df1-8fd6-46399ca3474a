package com.wosai.cua.brand.business.api.dto.brand.extra;

import com.wosai.cua.brand.business.api.enums.AggregationModelEnum;
import com.wosai.cua.brand.business.api.enums.UseOfFundsEnum;
import lombok.Data;

@Data
public class MerchantDockingModeExtraDTO {
    /**
     * 归集模式
     */
    private AggregationModelEnum aggregationModel;

    /**
     * 归集模式描述
     */
    private String aggregationModelDesc;

    /**
     * 归集最大比例
     * 如：100% = 10000
     */
    private Integer concentrateScale;

    /**
     * 资金用途
     */
    private UseOfFundsEnum useOfFunds;
    /**
     * 资金用途描述
     */
    private String useOfFundsDesc;
}
