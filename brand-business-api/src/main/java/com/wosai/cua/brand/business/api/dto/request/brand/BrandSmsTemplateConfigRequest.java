package com.wosai.cua.brand.business.api.dto.request.brand;

import com.wosai.cua.brand.business.api.enums.SmsTemplateMethodEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BrandSmsTemplateConfigRequest {

    @NotBlank(message = "品牌id不能为空")
    private String brandId;

    private List<String> templateCodes;

    private SmsTemplateMethodEnum method;
}
