package com.wosai.cua.brand.business.api.dto.request.crm;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SettlementCardVO {

    /**
     * 账户类型 1 个人账户 2 企业账户
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    @NotBlank(message = "账户持有人名称holder不能为空")
    private String holder;

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号bankCardNo不能为空")
    private String bankCardNo;

    @NotBlank(message = "银行预留手机号不能为空")
    private String openingNumber;

    private String reservedMobileNumber;
}
