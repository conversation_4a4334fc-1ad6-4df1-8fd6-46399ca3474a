package com.wosai.cua.brand.business.api.dto.request.businessopen;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 开通支付业务返回值
 *
 * <AUTHOR>
 * @date 2020-08-10
 */
@Data
@Accessors(chain = true)
public class CheckOpenPaymentHubResponseDTO {

    /**
     * 1:校验通过   0：校验失败
     */
    @JsonProperty("submit_check_result")
    private int submitCheckResult;

    /**
     * 错误码，开通失败时必传
     */
    @JsonProperty("submit_check_code")
    private String submitCheckCode;

    /**
     * 错误信息，开通失败时必传
     */
    @JsonProperty("submit_check_msg")
    private String submitCheckMsg;

    private CheckOpenPaymentHubResponseDTO(int submitCheckResult, String submitCheckCode, String submitCheckMsg) {
        this.submitCheckResult = submitCheckResult;
        this.submitCheckCode = submitCheckCode;
        this.submitCheckMsg = submitCheckMsg;
    }

    private CheckOpenPaymentHubResponseDTO() {}

    @JsonIgnore
    public boolean isCheckSuccess() {
        return submitCheckResult == 1;
    }

    public static CheckOpenPaymentHubResponseDTO success() {
        return new CheckOpenPaymentHubResponseDTO(1, null, null);
    }

    public static CheckOpenPaymentHubResponseDTO fail(String submitCheckCode, String submitCheckMsg) {
        return new CheckOpenPaymentHubResponseDTO(0, submitCheckCode, submitCheckMsg);
    }
}
