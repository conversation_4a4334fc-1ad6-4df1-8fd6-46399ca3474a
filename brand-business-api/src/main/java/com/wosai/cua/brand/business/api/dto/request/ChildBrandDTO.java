package com.wosai.cua.brand.business.api.dto.request;


import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
public class ChildBrandDTO {
    /**
     * 品牌名称
     */
    private String name;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人手机号
     */
    private String contactPhone;
    /**
     * 父品牌id
     */
    private String parentBrandId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 商户编号
     */
    private String merchantSn;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getParentBrandId() {
        return parentBrandId;
    }

    public void setParentBrandId(String parentBrandId) {
        this.parentBrandId = parentBrandId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMerchantSn() {
        return merchantSn;
    }

    public void setMerchantSn(String merchantSn) {
        this.merchantSn = merchantSn;
    }

    @Override
    public String toString() {
        return "ChildBrandDTO{" +
                "name='" + name + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", district='" + district + '\'' +
                ", address='" + address + '\'' +
                ", contactName='" + contactName + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", parentBrandId='" + parentBrandId + '\'' +
                ", remark='" + remark + '\'' +
                ", merchantSn='" + merchantSn + '\'' +
                '}';
    }
}
