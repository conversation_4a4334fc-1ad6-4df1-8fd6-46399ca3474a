package com.wosai.cua.brand.business.api.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MerchantBusinessLicenseInfoDTO {

    private String name;

    private String licenseName;

    private String licenseNumber;

    private String legalPersonName;

    private Integer legalPersonIdType;

    private String legalPersonIdTypeDesc;

    private String legalPersonIdNumber;

    public MerchantBusinessLicenseInfoDTO(String name,String legalPersonName,Integer type, String licenseNumber, String legalPersonIdNumber,Integer legalPersonIdType) {
        this.name = name;
        this.licenseNumber = licenseNumber;
        switch (type){
            case 0: this.licenseName = "身份证号";break;
            case 1: this.licenseName = "个体工商户营业执照"; break;
            case 2: this.licenseName = "企业营业执照"; break;
            default:
        }
        this.legalPersonIdNumber = legalPersonIdNumber;
        //1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；
        switch (legalPersonIdType){
            case 1: this.legalPersonIdTypeDesc = "身份证";break;
            case 2: this.legalPersonIdTypeDesc = "外国护照"; break;
            case 3: this.legalPersonIdTypeDesc = "台胞证"; break;
            case 4: this.legalPersonIdTypeDesc = "港澳通行证"; break;
            default:
        }
        this.legalPersonName = legalPersonName;
        this.legalPersonIdType = legalPersonIdType;
    }
}
