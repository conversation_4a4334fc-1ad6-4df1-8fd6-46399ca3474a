package com.wosai.cua.brand.business.api.enums;

/**
 * 支付模式
 * <AUTHOR>
 * @date 2024/8/29
 */
public enum PaymentModeEnum {
    /**
     * 对账模式
     */
    PAYMENT_MODE_RECONCILIATION(0, "对账模式"),

    /**
     * 收付通模式
     */
    PAYMENT_HUB_MODE(1, "收付通模式"),
    /**
     * 商家模式
     */
    MERCHANT_MODE(2, "商家模式"),
    /**
     * 微信品牌模式
     */
    WX_BRAND_MODE(3, "微信品牌模式"),
    /**
     * 支付宝品牌模式
     */
    ALI_BRAND_MODE(4, "支付宝品牌模式"),
    /**
     * 支付宝和微信都是品牌模式
     */
    ALI_WX_BRAND_MODE(5, "支付宝微信品牌模式"),;

    private final Integer code;
    private final String desc;

    PaymentModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PaymentModeEnum getPaymentModeEnum(Integer mode) {
        for (PaymentModeEnum paymentModeEnum : PaymentModeEnum.values()) {
            if (paymentModeEnum.getCode().equals(mode)) {
                return paymentModeEnum;
            }
        }
        return null;
    }

    public boolean isBrandMode() {
        return this.equals(WX_BRAND_MODE) || this.equals(ALI_BRAND_MODE) || this.equals(ALI_WX_BRAND_MODE);
    }
}
