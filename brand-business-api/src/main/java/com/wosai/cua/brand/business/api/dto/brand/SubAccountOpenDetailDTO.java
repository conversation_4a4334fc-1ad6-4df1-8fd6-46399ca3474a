package com.wosai.cua.brand.business.api.dto.brand;

import lombok.Data;

@Data
public class SubAccountOpenDetailDTO {
    /**
     * 商户号
     */
    private String merchantSn;

    /**
     * 状态: 99 未查到记录
     * 00 协议未签署 01 失效（24小时失效或者3次签约失效） 02 生效 03 禁用 05 冻结 06 待银行开户 07注销 08 银行开户失败
     * 10 未激活 11 已激活生效 12 待处理
     */
    private String status;

    /**
     * 资管机构返回信息
     */
    private Object response;
}
