package com.wosai.cua.brand.business.api.dto.brand;

import com.wosai.cua.brand.business.api.dto.brand.citic.CiticConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.fuiou.FuiouConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.mybank.MyBankConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.pab.PabConfigDTO;
import lombok.Data;

@Data
public class ConfigDTO {

    private String brandId;
    /**
     * 平安银行配置
     */
    private PabConfigDTO pabConfig;

    /**
     * 网商银行配置
     */
    private MyBankConfigDTO myBankConfig;

    /**
     * 中信银行配置
     */
    private CiticConfigDTO citicConfig;

    /**
     * 富友支付配置
     */
    private FuiouConfigDTO fuiouConfig;
}
