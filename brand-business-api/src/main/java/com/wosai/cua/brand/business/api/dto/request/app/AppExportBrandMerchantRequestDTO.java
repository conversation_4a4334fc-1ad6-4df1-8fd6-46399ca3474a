package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;

@Data
public class AppExportBrandMerchantRequestDTO {

    @JsonProperty("merchant_id")
    private String tokenMerchantId;
    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 商户类型
     */
    private MerchantTypeEnum merchantType;

    /**
     * 账户开通状态
     */
    private BrandMerchantAccountOpenStatusEnum accountOpenStatus;

}