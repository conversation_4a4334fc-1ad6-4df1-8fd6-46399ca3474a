package com.wosai.cua.brand.business.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class PageQueryBankCardDTO {

    private Integer page;

    private Integer pageSize;

    @JsonProperty("merchant_id")
    private String merchantId;

    private String merchantSn;

    private String brandId;

    private String sqbStoreId;
}
