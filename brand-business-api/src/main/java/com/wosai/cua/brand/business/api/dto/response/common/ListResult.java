package com.wosai.cua.brand.business.api.dto.response.common;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/29
 */
public class ListResult <T>{

    private long total;
    private List<T> records = new ArrayList<>();

    public ListResult() {
    }

    public ListResult(long total, List<T> records) {
        this.total = total;
        this.records = records;
    }

    public static <T> ListResult<T> emptyListResult() {
        return new ListResult<T>(0L, new ArrayList<>());
    }

    public long getTotal() {
        return this.total;
    }

    public List<T> getRecords() {
        return this.records;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }
}
