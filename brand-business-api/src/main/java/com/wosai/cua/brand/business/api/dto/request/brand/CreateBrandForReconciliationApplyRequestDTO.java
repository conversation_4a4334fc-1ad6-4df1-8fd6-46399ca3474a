package com.wosai.cua.brand.business.api.dto.request.brand;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 从审批创建品牌
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
@Data
public class CreateBrandForReconciliationApplyRequestDTO {

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    private String contactEmail;

    /**
     * 品牌名称
     */
    @NotBlank(message = "品牌名称不能为空！")
    private String name;

    /**
     * 品牌简称
     */
    @NotBlank(message = "品牌简称不能为空")
    private String alias;
    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人姓名不能为空！")
    private String contactName;
    /**
     * 联系人手机号
     */
    @NotBlank(message = "联系人手机号不能为空！")
    private String contactPhone;



}
