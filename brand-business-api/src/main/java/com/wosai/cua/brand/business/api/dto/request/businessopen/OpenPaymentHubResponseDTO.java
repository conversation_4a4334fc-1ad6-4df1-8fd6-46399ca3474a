package com.wosai.cua.brand.business.api.dto.request.businessopen;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 开通支付业务返回值
 *
 * <AUTHOR>
 * @date 2020-08-10
 */
@Data
@Accessors(chain = true)
public class OpenPaymentHubResponseDTO {

    //------------业务开通状态--------------//
    /**
     * 业务开通成功
     */
    public static final int SUCCESS_OPEN = 3;
    /**
     * 业务开通失败
     */
    public static final int FAIL_OPEN = 2;
    /**
     * 业务开通中
     */
    public static final int PROCESS_OPEN = 1;

    /**
     * 状态  1:开通中 2:开通失败 3:开通成功
     */
    private int status;

    /**
     * 错误码，开通失败时必传
     */
    @JsonProperty("fail_code")
    private String failCode;

    /**
     * 错误信息，开通失败时必传
     */
    @JsonProperty("fail_msg")
    private String failMsg;

    @JsonProperty("app_sub_status_text")
    private String appSubStatusText;

    public static OpenPaymentHubResponseDTO process() {
        return new OpenPaymentHubResponseDTO().setStatus(PROCESS_OPEN);
    }

}
