package com.wosai.cua.brand.business.api.dto.request.brand;

import com.wosai.cua.brand.business.api.dto.brand.AccountsDTO;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.dto.request.ChildBrandDTO;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建品牌的DTO对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreateBrandRequestDTO {

    /**
     * 品牌名称
     */
    @NotBlank(message = "品牌名称不能为空！")
    private String name;

    /**
     * 品牌简称
     */
    private String alias;
    /**
     * 省code
     */
    private String provinceCode;
    /**
     * 省
     */
    @NotBlank(message = "所在省不能为空！")
    private String province;
    /**
     * 市code
     */
    private String cityCode;
    /**
     * 市
     */
    @NotBlank(message = "所在市不能为空！")
    private String city;
    /**
     * 区code
     */
    private String districtCode;
    /**
     * 区
     */
    private String district;
    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空！")
    private String address;
    /**
     * 行业编号
     */
    @NotBlank(message = "行业编号不能为空！")
    private String industry;
    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人姓名不能为空！")
    private String contactName;
    /**
     * 联系人手机号
     */
    @NotBlank(message = "联系人手机号不能为空！")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;
    /**
     * 父品牌id
     */
    private String parentBrandId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 商户编号
     */
    @NotBlank(message = "品牌对应的商户编号不能为空！")
    private String merchantSn;

    /**
     * 资管机构编号
     */
    @NotNull(message = "资管机构编号不能为空！")
    private FundManagementCompanyEnum fundManagementCompanyCode;

    /**
     * 资管机构
     */
    private String fundManagementCompany;

    /**
     * 子品牌集合（预留，1.0版本不做，后期如果客户有需求再加入）
     */
    private List<ChildBrandDTO> childBrands;

    /**
     * 品牌配置
     */
    private ConfigDTO config;

    /**
     * 银行账户信息
     */
    private AccountsDTO accounts;

    /**
     * 是否开通收付通：0-否，1-是
     */
    private Integer sftTag = 0;
}
