package com.wosai.cua.brand.business.api.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 创建品牌返回
 * <AUTHOR>
 */
@Data
public class CreateBrandResponseDTO implements Serializable {

    private static final long serialVersionUID = -2923551832607157649L;

    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 品牌编号
     */
    private String sn;
    /**
     * 子品牌数据（预留，收付通1.0不做，后续版本如果有需求再加入）
     */
    private List<CreateBrandResponseDTO> childBrands;

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.api.dto.response.CreateBrandResponseDTO");
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        throw new java.io.NotSerializableException("com.wosai.cua.brand.business.api.dto.response.CreateBrandResponseDTO");
    }
}
