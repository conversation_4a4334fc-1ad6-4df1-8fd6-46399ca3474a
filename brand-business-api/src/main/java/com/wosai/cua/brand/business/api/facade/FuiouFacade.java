package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.fuiou.ResendRequestDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

@JsonRpcService(value = "rpc/brand/fuiou")
@Validated
public interface FuiouFacade {
    /**
     * 重发送短信接口（富友）
     */
    String resend(@Valid ResendRequestDTO request);
}
