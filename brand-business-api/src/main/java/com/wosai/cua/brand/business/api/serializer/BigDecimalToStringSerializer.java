package com.wosai.cua.brand.business.api.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class BigDecimalToStringSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (Objects.nonNull(value)){
            // 将 BigDecimal 转换为字符串并保留末位小数不去零
            String formattedValue = value.toString();
            gen.writeString(formattedValue);
        }
    }
}
