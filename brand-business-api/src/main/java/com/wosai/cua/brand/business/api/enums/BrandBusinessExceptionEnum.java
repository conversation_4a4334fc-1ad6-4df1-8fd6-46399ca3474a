package com.wosai.cua.brand.business.api.enums;


/**
 * <AUTHOR>
 */

public enum BrandBusinessExceptionEnum {

    PARAMS_ERROR(20000, ""),
    /**
     * 商户id不能为空
     */
    MERCHANT_ID_NOT_BE_NULL(20001, "商户id不能为空！"),
    /**
     * 品牌id不能为空
     */
    BRAND_ID_NOT_BE_NULL(20002, "品牌id不能为空！"),
    MERCHANT_SN_NOT_BE_NULL(20003, "商户编号不能为空！"),
    MERCHANT_SN_OR_ID_NOT_BE_NULL_IN_SAME_TIME(20004, "商户编号和商户id不能同时为空！"),

    PAB_ACCOUNTS_CAN_NOT_NULL(20005, "PAB账户信息不能为空"),

    MY_BANK_ACCOUNTS_CAN_NOT_NULL(20006, "MY_BANK账户信息不能为空"),

    PAB_CONFIG_CAN_NOT_NULL(20007, "PAB配置不能为空"),
    CITIC_CONFIG_CAN_NOT_NULL(20008, "CITIC配置不能为空"),
    FUIOU_CONFIG_CAN_NOT_NULL(20009, "富友支付配置不能为空"),
    CONFIG_MODULE_NOT_BE_NULL(20010, "配置模块不能为空"),

    ASSOCIATED_SQB_STORE_ID_NOT_BE_NULL(20011, "关联的收钱吧门店编号不能为空！"),
    /**
     * 外部接口调用失败
     */
    EXTERNAL_INTERFACE_INVOKE_FAIL(21000, "外部接口调用失败！"),

    NOT_PERMISSION(30000, "无接口操作权限"),

    NOT_FIND_METHOD(30001, "未找到相应的方法！"),

    NOT_FIND_SERVICE(30002, "未找到相应的service服务！"),

    GENERATE_BRAND_KEY_ERROR(30003, "创建秘钥失败！"),


    SYSTEM_ERROR(50000, "系统调用异常"),
    /**
     * 未查询到商户
     */
    NOT_FIND_MERCHANT(50001, "未查询到相应的商户"),
    /**
     * 商户已和品牌关联
     */
    MERCHANT_IS_A_BRAND(50002, "该商户已与其他品牌关联，无法创建品牌"),
    /**
     * 无法删除自动创建的商户
     */
    MERCHANT_CANT_DELETE(50003, "品牌商或服务商（收钱吧）商户无法删除"),
    /**
     * 未查询到品牌
     */
    NOT_FIND_BRAND(50004, "未查询到相应的品牌"),
    /**
     * 商户已和品牌关联
     */
    MERCHANT_IS_BELONG_OTHER_BRAND(50005, "该商户已与其他品牌关联，关联到该品牌下。"),
    /**
     * 商户未和品牌关联
     */
    MERCHANT_IS_NOT_BELONG_BRAND(50006, "该商户不属于该品牌。"),
    /**
     * 银行卡未找到
     */
    NOT_FIND_BANK_CARD(50007, "该银行卡未找到。"),
    /**
     * 银行卡未添加
     */
    NOT_ADD_BANK_CARD(50008, "该银行卡未添加，请查看是否已经添加过该银行卡。"),
    /**
     * 银行卡已存在
     */
    BANK_CARD_EXIST(50008, "已添加过银行卡，请先将旧卡删除后再添加。"),
    /**
     *
     */
    MERCHANT_NOT_HAVE_SUPER_ADMIN(50009, "商户没有老板信息。"),
    MERCHANT_INFO_NOT_INTEGRITY(50010, "商户信息不完整"),

    STRATEGY_INFO_NOT_FIND(50011, "结算策略信息未找到！"),

    NOT_SUPPORT_BUSINESS_LICENSE(50012, "当前不支持该证照类型的商户！"),
    NOT_FIND_DICTIONARY(50013, "未找到相应的字典"),

    NOT_ALLOWED_CHECK_MODE(50014, "不允许的鉴权方式"),

    NOT_PASS_FIELD_CHECK(50015, "未通过字段校验"),

    NOT_SUPPORT_FILE(50016, "不支持的文件格式"),

    WITHDRAW_CYCLE_TYPE_CAN_NOT_BE_EMPTY(50017, "结算周期类型withdrawCycleType不能为空"),

    WITHDRAW_CYCLE_TIME_CAN_NOT_BE_NULL(50018, "结算周期时间withdrawCycleTime不能为空"),

    NOT_CREATE_SUB_ACCOUNT(50019, "未创建子账号"),

    WITHDRAW_STRATEGY_DAY_CHECK_FAIL(50020, "结算周期类型为“按天”，时间范围为1-23"),
    WITHDRAW_STRATEGY_WEEK_CHECK_FAIL(50021, "结算周期类型为“按周”，时间范围为1-7"),
    WITHDRAW_STRATEGY_MONTH_CHECK_FAIL(50020, "结算周期类型为“按月”，时间范围为1-31"),

    WITHDRAW_CYCLE_TYPE_CAN_NOT_FIND(50021, "未找到对应的结算周期类型"),
    MERCHANT_NOT_BIND_STRATEGY(50022, "该商户未绑定结算策略！"),

    EXIST_BRAND(50023, "该品牌已存在！"),

    AMOUNT_LESS_THEN_MIN(50024, "输入的金额要在%s~%s范围内。"),

    AMOUNT_BIG_THEN_MAX(50025, "输入的金额要在%s~%s范围内。"),
    NOT_FIND_BRAND_CONFIG(50026, "未找到相应的品牌配置！"),
    NOT_FIND_STORE(50027, "未查到门店信息"),
    UN_SUPPORT_SCENE(50028, "不支持的场景"),
    NOT_FIND_MY_BANK_ARRANGEMENT(50029, "未找到该商户的代扣协议信息"),
    STORE_WAS_BIND_BRAND(50030, "该门店已关联过品牌的商户"),
    CITIC_IDENTITY_IS_NULL(50031, "中信银行绑定银行卡需填写账户持有人证件信息"),
    CITIC_ID_TYPE_IS_NULL(50032, "中信银行绑定银行卡需填写账户持有人证件类型"),
    STRATEGY_ID_IS_NULL(50033, "结算策略不能为空!"),
    STORE_NOT_BELONG_BRAND(50034, "该门店不属于该品牌下"),
    CLOSE_ACCOUNT_FAIL(50035, "注销用户/删除用户失败！"),
    AGGREGATION_MODEL_IS_NULL(50036, "归集模式aggregationModel不能为空！"),
    CONCENTRATE_SCALE_IS_NULL(50037, "归集比例concentrateScale不能为空！"),
    USE_OF_FUNDS_IS_NULL(50038, "资金用途useOfFunds不能为空！"),
    NO_FIND_BRAND_CONFIG(50039, "未找到相应的品牌配置"),
    BANK_CARD_NUM_OUT_OF_RANGE(50040, "每个商户最多同时绑定3张"),
    MERCHANT_IS_BIND_IN_BRAND(50041, "该商户已绑定过该品牌"),
    DEFAULT_BANK_CARD_CAN_NOT_DELETE(50042, "默认卡不允许删除，请先设置其他卡为默认卡"),
    MERCHANT_DOCKING_MODE_IS_COLLECTION(50043, "商户对接模式为“归集”时，不允许添加银行卡"),
    BUSINESS_LICENSE_NOT_EXIST(50044, "商户营业执照不存在"),
    NOT_FIND_GROUP(50045, "集团信息不存在"),
    BRAND_SN_NOT_BE_NULL(50046, "品牌编号不能为空"),
    BRAND_MAIN_MERCHANT_NOT_FIND(50047, "品牌主商户不存在"),
    NOT_FIND_MAIN_MERCHANT_PICTURES(50048, "品牌主商户三张照片不存在"),
    NOT_FIND_MAIN_MERCHANT_FEE_RATE(50049, "品牌主商户费率信息不存在"),
    MAIN_MERCHANT_SN_NOT_NULL(50050, "品牌主商户商户编号不能为空"),
    PAYMENT_HUB_INFO_ALREADY_EXIST(50051, "品牌已开通收付通"),
    BRAND_MAIN_MERCHANT_NOT_OPEN_INDIRECT(50052, "品牌主商户未开通间连扫码"),
    PARENT_BRAND_NOT_FIND(50053, "父品牌信息不存在"),
    BRAND_IS_NOT_PARENT(50054, "品牌不是父品牌"),
    BRAND_MAIN_MERCHANT_NOT_OPEN_PAYMENT_HUB(50055, "品牌主商户未开通收付通"),
    BRAND_SUB_BRAND_FUND_MANAGE_CODE_NOT_EQUAL(50056, "子品牌和父品牌资管机构不一致，子品牌需与品牌收付通通道一致"),
    TRADE_COMBO_NOT_FIND(50057, "未找到入网费率套餐"),
    NOT_FIND_PHOTO_FILE(50058, "未找到图片文件"),
    BANK_INFO_NOT_FIND(50059, "未找到银行信息"),
    NOT_SUPPORT_ACQUIRER(50060, "品牌主商户所在收单机构不允许开通间连扫码"),
    UPLOAD_FILE_ERROR(50061, "图片上传失败"),
    BUSINESS_LICENSE_TYPE_NOT_MATCH(50062, "品牌主商户必须存在营业执照"),
    NOT_SUPPORT_FUND_MANAGEMENT_COMPANY(50063,"未找到对应的资管机构或是不支持的资管机构"),
    BRAND_MERCHANT_NO_CHECK_IN(50064, "商户还未入驻，请先入驻"),
    BANK_CARD_ALREADY_ACTIVATED(50065, "银行卡已激活，无法进行修改"),
    BRAND_NOT_SUPPORT_OPERATION(50066, "该品牌暂不支持此操作！"),
    NOT_FIND_UNACTIVATED_BANK_CARD(50067, "未找到未激活的银行卡"),
    BANK_ACCOUNT_TYPE_IS_NOT_ALLOW(50068, "企业暂不支持绑定/激活对私卡"),
    MERCHANT_CERTIFICATE_TYPE_IS_NOT_ALLOW(50069, "商户的证件类型不支持"),
    BANK_CARD_ACCOUNT_TYPE_IS_NOT_ALLOW(50070, "银行卡类型不支持"),
    MEITUAN_STORE_SN_IS_EXIST(50071, "美团门店编号已存在"),
    ELM_STORE_SN_IS_EXIST(50071, "饿了么门店编号已存在"),
    DY_STORE_SN_IS_EXIST(50071, "抖音门店编号已存在"),
    OUT_MERCHANT_NO_IS_EXIST(50071, "外部商户编号已存在"),
    MERCHANT_SN_OR_OUT_MERCHANT_NO_NOT_BE_NULL_IN_SAME_TIME(50072, "商户编号和外部商户编号不能同时为空！"),
    NOT_FIND_BRAND_MANAGEMENT_COMPANY(50073, "该品牌没对接对应的资管机构。"),
    UNSUPPORTED_FUND_MANAGEMENT_COMPANY(50074, "不支持的资管机构。"),
    INVALID_BRAND_CONFIG(50075, "无效的配置。"),
    BRAND_ID_OR_BRAND_SN_NOT_BE_NULL(50076, "品牌id集合和品牌编号集合不能同时为空。"),
    GET_BANK_INFO_FAIL(50077, "获取银行信息失败。"),


    OPEN_API_PARAM_ERROR(60000, "appid不能为空！"),
    OPEN_API_BRAND_SN_NOT_BE_NULL(60001, "品牌编号brand_sn不能为空！"),
    OPEN_API_BRAND_IS_NOT_EXIST(60002, "该品牌不存在！"),
    OPEN_API_REQUEST_SERIAL_NUMBER_NOT_BE_NULL(60003, "请求流水号client_sn不能为空！"),
    OPEN_API_BRAND_NOT_PERMISSION(60004, "无权限"),
    OPEN_API_THIS_OPERATION_IS_NOT_SUPPORTED(60005, "该操作不支持！")
    ;

    private final Integer code;

    private final String message;


    BrandBusinessExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
