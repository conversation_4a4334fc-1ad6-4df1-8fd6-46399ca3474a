package com.wosai.cua.brand.business.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ModifyBankCardDTO {

    /**
     * 银行卡id
     */
    @NotBlank(message = "银行卡id必传")
    private String bankCardId;

    /**
     * 品牌id
     */
    @NotBlank(message = "品牌id必传")
    private String brandId;

    /**
     * 商户id
     */
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * 商户编号
     */
    @JsonProperty("merchant_sn")
    private String merchantSn;
    /**
     * 账户类型 1 个人账户 2 企业账户
     */
    private Integer type;
    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 开户行号
     */
    private String openingNumber;

    /**
     * 银行预留手机号
     */
    private String reservedMobileNumber;

    /**
     * 账户持有人名称
     */
    private String holder;
}
