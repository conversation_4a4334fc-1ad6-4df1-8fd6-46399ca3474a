package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * 报备渠道枚举
 * <AUTHOR>
 */

@Getter
public enum EnrollChannelEnum {
    /**
     *
     */
    PING_AN_WEI_JIN("PING_AN_WEI_JIN","平安-维金渠道"),

    MY_BANK("MY_BANK","网商银行渠道"),

    CITIC("CITIC","中信银行渠道"),

    FUIOU("FUIOU","富友渠道"),
    ;

    private final String channel;

    private final String desc;


    EnrollChannelEnum(String channel, String desc) {
        this.channel = channel;
        this.desc = desc;
    }
}
