package com.wosai.cua.brand.business.api.dto.request.brand;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CancelEnterRequestDTO {

    public static final String CANCEL_TYPE_REVOKE = "01";

    public static final String CANCEL_TYPE_CANCEL = "02";

    @NotBlank(message = "品牌id不能为空")
    private String brandId;

    @NotBlank(message = "商户id不能为空")
    private String merchantId;

    /**
     * 取消入驻类型:01-撤销入驻,02-取消入驻
     */
    @NotBlank(message = "取消入驻类型不能为空")
    private String cancelType;
}
