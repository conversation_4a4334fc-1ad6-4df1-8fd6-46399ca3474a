package com.wosai.cua.brand.business.api.dto.brand.citic;

import com.wosai.cua.brand.business.api.dto.brand.BaseConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 中信配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CiticConfigDTO extends BaseConfigDTO {
    /**
     * 对应中信的商户id
     */
    private String merchantId;

    /**
     * 对应中信的公钥
     */
    private String publicKey;

    /**
     * 对应中信的私钥
     */
    private String privateKey;

    /**
     * 私钥的密码
     */
    private String privateKeyPassword;
}
