package com.wosai.cua.brand.business.api.enums;

public enum CheckAccountResultEnum {
    SUCCESS("00", "验证成功"),
    FAIL_MERCHANT_NO("01", "商户号不存在"),
    FAIL_MERCHANT_NO_AND_SUBJECT("02", "商户号与主体不一致"),
    UNKNOWN("03", "其他"),
    ;

    private final String code;
    private final String message;

    CheckAccountResultEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
