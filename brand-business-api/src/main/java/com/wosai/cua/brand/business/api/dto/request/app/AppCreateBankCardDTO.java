package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class AppCreateBankCardDTO {

    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 商户id
     */
    @NotBlank(message = "商户di必传")
    @JsonProperty("merchant_id")
    private String merchantId;
    /**
     * 账户类型 1 个人账户 2 企业账户
     */
    @NotNull(message = "type不能为空")
    private Integer type;
    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号bankCardNo不能为空")
    private String bankCardNo;

    /**
     * 开户行号
     */
    @NotBlank(message = "开户行号openingNumber，不能为空")
    private String openingNumber;

    /**
     * 银行卡分支行所在城市
     */
    private String city;

    @NotBlank(message = "账户持有人名称holder不能为空")
    private String holder;

    /**
     * 账户持有人证件类型：1 身份证；2 港澳居民来往内地通行证； 3 台湾居民来往大陆通行证； 4 非中华人民共和国护照； 5 中国护照
     */
    private Integer idType;

    /**
     * 账户持有人证件编号
     */
    private String identity;

    private Boolean setDefault;

    @NotBlank(message = "银行预留手机号不能为空")
    private String reservedMobileNumber;

    private String sqbStoreId;
}
