package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardAdvanceDTO;
import com.wosai.cua.brand.business.api.dto.request.ActivateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.BaseBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.BatchActivateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.DeleteBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyBankCardDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBankCardDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantBankCardsDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/bank/account")
@Validated
public interface BrandMerchantBankAccountFacade {
    /**
     * 批量添加银行卡
     * @param createBankCardList 请求调用集合
     * @return 新增条数
     */
    List<BankCardResponseDTO> batchCreateBankCard(@Valid List<CreateBankCardDTO> createBankCardList);

    /**
     * 新增银行卡
     * @param createBankCard 新增银行卡对象
     * @return 新增条数
     */
    BankCardResponseDTO createBankCard(@Valid CreateBankCardDTO createBankCard);

    /**
     * 激活银行卡
     * @param activateBankCard 激活银行卡对象
     * @return 激活请求是否成功
     */
    Boolean activateBankCard(ActivateBankCardDTO activateBankCard);

    /**
     * 批量激活银行卡（仅中信支持）
     * @param batchActivateBankCard 激活银行卡对象
     * @return 激活请求是否成功
     */
    Boolean batchActivateBankCard(BatchActivateBankCardDTO batchActivateBankCard);

    /**
     * 激活银行卡推进
     * @param activateBankCardAdvanceDto 激活银行卡推进对象
     * @return 调用是否成功
     */
    Boolean activateBankCardAdvance(ActivateBankCardAdvanceDTO activateBankCardAdvanceDto);

    /**
     * 切换默认提现卡
     * @param bankCardId 银行卡id
     * @return 接口调用结果
     */
    Boolean changeDefaultBankCard(BaseBankCardDTO bankCard);

    /**
     * 删除银行卡
     * @param bankCardId 收付通银行卡id
     * @return 接口调用结果
     */
    Boolean deleteBankCard(DeleteBankCardDTO bankCard);

    /**
     * 分页查询银行卡列表
     * @param pageQueryBankCardDto 分页查询条件对象
     * @return 查询结果
     */
    PageBrandMerchantBankCardsDTO pageFindBankCardList(PageQueryBankCardDTO pageQueryBankCardDto);

    /**
     * 修改银行卡信息
     * @param modifyBankCard 修改银行卡信息对象
     * @return 接口调用结果
     */
    BankCardResponseDTO modifyBankCard(ModifyBankCardDTO modifyBankCard);

    /**
     * 查询银行卡详情
     * @param bankCard 银行卡对象
     * @return 银行卡详情对象
     */
    BankCardDetailResponseDTO getBankCardDetail(BaseBankCardDTO bankCard);
}
