package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.enums.AggregationModelEnum;
import com.wosai.cua.brand.business.api.enums.MerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.UseOfFundsEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class CreateBrandMerchantRequestDTO {
    /**
     * 品牌id
     */
    @NotBlank(message = "品牌id不能为空")
    private String brandId;

    /**
     * 商户编号
     */
    @NotBlank(message = "商户编号不能为空")
    private String merchantSn;

    /**
     * 商户类型
     * @see MerchantTypeEnum
     */
    @NotBlank(message = "商户类型不能为空")
    private String merchantType;

    /**
     * 收钱吧门店号
     */
    private String sqbStoreSn;

    /**
     * 关联品牌商户门店编号
     */
    private String associatedSqbStoreId;

    /**
     * 关联美团门店号
     */
    private String associatedMeituanStoreSn;

    /**
     * 关联饿了么门店号
     */
    private String associatedElmStoreSn;

    /**
     * 抖音门店号
     */
    private String dyStoreSn;

    /**
     * 是否需要创建品牌门店
     */
    private Boolean needCreateBrandStore;

    /**
     * 商户对接方式:SEPARATE_ACCOUNT-分账，COLLECTION-归集（两种）
     * 不传，默认：分账模式
     * @see MerchantDockingModeEnum
     */
    private MerchantDockingModeEnum merchantDockingMode = MerchantDockingModeEnum.SEPARATE_ACCOUNT;

    /**
     * 归集模式:BALANCE_POOLING-余额归集，ORDERS_COLLECTED_AFTERWARDS-订单事后归集，ORDER_PRE_COLLECTION-订单预归集（三种）
     */
    private AggregationModelEnum aggregationModel;

    /**
     * 归集最大比例
     * 如：100% = 10000
     */
    private Integer concentrateScale;

    /**
     * 资金用途
     */
    private UseOfFundsEnum useOfFunds;

    /**
     * 被归集商户号
     */
    private String concentrateMerchantNo;
    /**
     * 银行卡信息：富友品牌商户入驻时必传
     */
    private CreateBankCardDTO bankCard;

    /**
     * 外部商户号
     */
    private String outMerchantNo;
}
