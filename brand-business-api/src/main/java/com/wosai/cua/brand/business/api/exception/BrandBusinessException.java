package com.wosai.cua.brand.business.api.exception;

import com.wosai.common.exception.CommonException;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;

/**
 * <AUTHOR>
 */
public class BrandBusinessException extends CommonException {
    private static final long serialVersionUID = 6810699436689353173L;
    private final Integer code;

    public BrandBusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public BrandBusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public BrandBusinessException(BrandBusinessExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMessage());
        this.code = exceptionEnum.getCode();
    }

    public BrandBusinessException(BrandBusinessExceptionEnum exceptionEnum, String message) {
        super(message);
        this.code = exceptionEnum.getCode();
    }

    public BrandBusinessException(String message) {
        super(message);
        this.code = BrandBusinessExceptionEnum.SYSTEM_ERROR.getCode();
    }

    public BrandBusinessException(Integer code , Throwable cause) {
        super(cause);
        this.code = code;
    }

    @Override
    public int getCode() {
        return this.code;
    }
}
