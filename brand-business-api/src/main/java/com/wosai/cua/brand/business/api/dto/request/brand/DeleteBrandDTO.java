package com.wosai.cua.brand.business.api.dto.request.brand;

import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
public class DeleteBrandDTO  {

    @NotBlank(message = "品牌id不能为空！")
    private String brandId;

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    @Override
    public String toString() {
        return "DeleteBrandDTO{" +
                "brandId='" + brandId + '\'' +
                '}';
    }
}
