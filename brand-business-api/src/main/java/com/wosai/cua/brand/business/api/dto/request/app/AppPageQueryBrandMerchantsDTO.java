package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class AppPageQueryBrandMerchantsDTO {
    @JsonProperty("merchant_id")
    private String merchantId;
    /**
     * 当前页
     */
    private Integer page;

    /**
     * 每页展示条数
     */
    private Integer pageSize;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 品牌商户类型集合，参照枚举类
     *
     * @see MerchantTypeEnum
     */
    private List<String> merchantTypes;

    /**
     * 商户类型集合
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum
     */
    private List<String> types;

    /**
     * 提现策略id
     */
    private Long withdrawStrategyId;

    /**
     * 账户开通状态
     * @see BrandMerchantAccountOpenStatusEnum
     */
    private String accountOpenStatus;
    /**
     * 外部商户号
     */
    private String outMerchantNo;

    /**
     * 美团门店编号
     */
    private String meiTuanStoreSn;

    /**
     * 饿了么门店编号
     */
    private String elmStoreSn;

    /**
     * 银行卡状态
     * @see BankCardActivateStatusEnum
     */
    private String bankCardActivateStatus;
}
