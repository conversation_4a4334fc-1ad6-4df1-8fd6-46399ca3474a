package com.wosai.cua.brand.business.api.dto.response;

import com.wosai.cua.brand.business.api.dto.brand.extra.MerchantDockingModeExtraDTO;
import com.wosai.cua.brand.business.api.enums.ArrangementStatusEnum;
import lombok.Data;

@Data
public class ExtraDTO {

    private MyBankExtraDTO myBankExtra;

    private MerchantDockingModeExtraDTO merchantDockingModeExtra;

    @Data
    public static class MyBankExtraDTO {
        /**
         * 当前合约状态
         */
        private ArrangementStatusEnum arrangementStatus;

        /**
         * 合约状态描述
         */
        private String arrangementStatusDesc;


        /**
         * 合约号
         */
        private String arrangementNo;


        /**
         * 合约类型
         */
        private String arrangementType;
    }
}
