package com.wosai.cua.brand.business.api.dto.request.brand;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
@Data
public class CreateBrandForOpenIndirectApplyRequestDTO {

    private String parentBrandId;

    /**
     * 品牌名字
     */
    @NotBlank(message = "品牌名不能为空")
    private String name;

    @NotBlank(message = "品牌简称不能为空")
    private String alias;

    @NotBlank(message = "邮箱不能为空")
    private String contactEmail;

    @NotBlank(message = "联系人姓名不能为空")
    private String contactName;

    @NotBlank(message = "联系人手机号不能为空")
    private String contactPhone;

    @NotBlank(message = "省份不能为空")
    private String province;

    @NotBlank(message = "城市不能为空")
    private String city;

    @NotBlank(message = "区域不能为空")
    private String district;

    @NotBlank(message = "地址不能为空")
    private String address;

    @NotBlank(message = "主商户不能为空")
    private String mainMerchantSn;
}