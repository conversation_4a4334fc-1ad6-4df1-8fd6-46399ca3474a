package com.wosai.cua.brand.business.api.facade.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.CommonOperationDTO;
import com.wosai.cua.brand.business.api.dto.request.app.DictionaryRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.DictionaryResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/app/brand/common")
@Validated
public interface AppCommonFacade {

    /**
     * 获取字典列表
     * @param dto 请求对象
     * @return 枚举对应列表
     */
    List<DictionaryResponseDTO> getDictionaryList(DictionaryRequestDTO dto);

    /**
     * 通用操作
     * @return 是否成功
     */
    Boolean commonOperation(@Valid CommonOperationDTO commonOperation);

}
