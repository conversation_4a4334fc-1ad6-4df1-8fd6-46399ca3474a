package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Iterator;

@Getter
public enum DockingModeEnum {
    /**
     * 接口
     */
    INTERFACE("INTERFACE","接口"),
    /**
     * 配置
     */
    CONFIGURATION("CONFIGURATION","配置")
    ;

    private final String code;

    private final String desc;

    DockingModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code){
        DockingModeEnum[] values = DockingModeEnum.values();
        Iterator<DockingModeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            DockingModeEnum dockingModeEnum = iterator.next();
            if (dockingModeEnum.code.equals(code)){
                return dockingModeEnum.desc;
            }
        }
        return null;
    }

}
