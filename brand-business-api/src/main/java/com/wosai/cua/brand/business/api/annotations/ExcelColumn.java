package com.wosai.cua.brand.business.api.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelColumn {
    /**
     * 列号
     * @return 列号
     */
    int col();

    /**
     * 是否需要转换值
     * @return 是否需要转换值
     */
    boolean convertValue() default false;
    /**
     * 是否需要trim
     * @return 是否需要trim
     */
    boolean needTrim() default false;
}