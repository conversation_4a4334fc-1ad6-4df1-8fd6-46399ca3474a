package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class AppBaseBankCardDTO {
    @NotBlank(message = "银行卡id必填")
    private String bankCardId;

    private String brandId;

    @JsonProperty("merchant_id")
    private String merchantId;

    private String sqbStoreId;
}
