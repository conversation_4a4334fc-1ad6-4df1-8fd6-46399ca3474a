package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Iterator;

/**
 * <AUTHOR>
 */
@Getter
public enum BrandMerchantTypeEnum {
    /**
     *
     */
    PERSONAL("PERSONAL","个人/小微商户"),

    INDIVIDUAL_BUSINESS("INDIVIDUAL_BUSINESS","个体工商户"),

    COMPANY("COMPANY","企业")
    ;
    private final String type;

    private final String desc;

    BrandMerchantTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getMerchantTypeByDesc(String desc){
        if (StringUtils.isEmpty(desc)){
            return null;
        }
        BrandMerchantTypeEnum[] values = BrandMerchantTypeEnum.values();
        Iterator<BrandMerchantTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            BrandMerchantTypeEnum merchantTypeEnum = iterator.next();
            if (merchantTypeEnum.desc.equals(desc)){
                return merchantTypeEnum.type;
            }
        }
        return null;
    }

    public static String getMerchantTypeDescByType(String type){
        if (StringUtils.isEmpty(type)){
            return null;
        }
        BrandMerchantTypeEnum[] values = BrandMerchantTypeEnum.values();
        Iterator<BrandMerchantTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            BrandMerchantTypeEnum merchantTypeEnum = iterator.next();
            if (merchantTypeEnum.type.equals(type)){
                return merchantTypeEnum.desc;
            }
        }
        return null;
    }
}
