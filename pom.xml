<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.wosai.pantheon</groupId>
        <artifactId>uranus</artifactId>
        <version>2.0.3</version>
    </parent>

    <groupId>com.wosai.cua</groupId>
    <artifactId>brand-business</artifactId>
    <version>1.5.2</version>
    <packaging>pom</packaging>

    <modules>
        <module>brand-business-api</module>
        <module>brand-business-service</module>
    </modules>

    <properties>
        <tk-mybatis.version>2.1.5</tk-mybatis.version>
        <wosai-common.version>1.6.7</wosai-common.version>
        <core-business-api.version>3.7.81</core-business-api.version>
        <fastjson.version>2.0.9</fastjson.version>
        <merchant-center.version>1.14.6</merchant-center.version>
        <hera-toolkit-metrics.version>1.5.3</hera-toolkit-metrics.version>
        <xxl-job-spring-boot-starter-version>2.2.4.RELEASE</xxl-job-spring-boot-starter-version>
        <merchant-bank-service.version>1.16.1</merchant-bank-service.version>
        <spring-cloud.version>2.2.4.RELEASE</spring-cloud.version>
        <merchant-user-service.version>1.10.55</merchant-user-service.version>
        <uc-user-service.version>1.12.3</uc-user-service.version>
        <core-crypto-api.version>0.0.2</core-crypto-api.version>
        <ant.version>1.10.1</ant.version>
        <bank-info-api.version>1.3.6</bank-info-api.version>
        <oss.version>3.6.1</oss.version>
        <aliyun-sdk-oss.version>1.0.1</aliyun-sdk-oss.version>
        <crow-api.version>0.0.40</crow-api.version>
        <upay-transaction-api.version>1.9.9</upay-transaction-api.version>
        <commons-csv.version>1.8</commons-csv.version>
        <feign-okhttp.version>10.7.4</feign-okhttp.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <jedis.version>3.7.1</jedis.version>
        <xmlsec.version>1.3.0</xmlsec.version>
        <httpmime.version>4.5.9</httpmime.version>
        <xstream.version>1.4.9</xstream.version>
        <bcprov-jdk15on.version>1.70</bcprov-jdk15on.version>
        <bcpkix-jdk15on.version>1.70</bcpkix-jdk15on.version>
        <oapi-sdk.version>2.4.8</oapi-sdk.version>
        <easyexcel.version>4.0.2</easyexcel.version>
        <sales-system-poi-api.version>1.54.0</sales-system-poi-api.version>
        <trade-manage-api.version>1.11.5</trade-manage-api.version>
        <cua-common.version>0.2.2</cua-common.version>
        <merchant-business-open-api.version>1.86.0</merchant-business-open-api.version>
        <sp-workflow-api.version>1.9.6</sp-workflow-api.version>
        <fastxml.version>2.9.0</fastxml.version>
        <sales-system-service-api.version>2.84.0</sales-system-service-api.version>
        <thumbanator.version>0.4.14</thumbanator.version>
        <shorturl-api.version>1.0.1-RELEASE</shorturl-api.version>
        <marketing-saas-prepaid-card-api.version>1.178.1-RELEASE</marketing-saas-prepaid-card-api.version>
        <profit-sharing-proxy-api.version>1.0.34</profit-sharing-proxy-api.version>
        <hutool.version>5.4.3</hutool.version>
        <logging-api.version>1.5.0</logging-api.version>
        <logstash-encoder-version>6.4</logstash-encoder-version>
        <logging-api.version>1.5.0</logging-api.version>
        <logstash-encoder-version>6.4</logstash-encoder-version>
        <apollo-api.version>2.2.0</apollo-api.version>
        <wosai-database-instrumentation-spring.version>5.1.0</wosai-database-instrumentation-spring.version>
        <vault-sdk.version>1.0.1</vault-sdk.version>
        <redisson.version>3.15.0</redisson.version>
        <shouqianba-tools-service-api.version>1.29.0</shouqianba-tools-service-api.version>
        <sales-system-gateway-api.version>1.1.0</sales-system-gateway-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wosai.cua</groupId>
                <artifactId>brand-business-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.common</groupId>
                <artifactId>wosai-common</artifactId>
                <version>${wosai-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.upay</groupId>
                <artifactId>core-business-api</artifactId>
                <version>${core-business-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.mc</groupId>
                <artifactId>merchant-center-api</artifactId>
                <version>${merchant-center.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>aliyun-java-sdk-core</artifactId>
                        <groupId>com.aliyun</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jsonrpc4j</artifactId>
                        <groupId>com.wosai.middleware</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>hera-toolkit-metrics</artifactId>
                <version>${hera-toolkit-metrics.version}</version>
            </dependency>
            <!--hera trace-->
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>hera-toolkit-trace</artifactId>
                <version>${hera-toolkit-metrics.version}</version>
            </dependency>
            <!-- 接入sls日志模块-->
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>hera-toolkit-logback-1.x</artifactId>
                <version>${hera-toolkit-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-encoder-version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>logging-api</artifactId>
                <version>${logging-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.cua</groupId>
                <artifactId>xxl-job-spring-boot-starter</artifactId>
                <version>${xxl-job-spring-boot-starter-version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.upay.bank</groupId>
                <artifactId>merchant-bank-service-api</artifactId>
                <version>${merchant-bank-service.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>merchant-contract-job-api</artifactId>
                        <groupId>com.wosai.upay</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${spring-cloud.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${feign-okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.app</groupId>
                <artifactId>merchant-user-api</artifactId>
                <version>${merchant-user-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.uc</groupId>
                <artifactId>uc-user-api</artifactId>
                <version>${uc-user-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>core-crypto-api</artifactId>
                <version>${core-crypto-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>${ant.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.upay</groupId>
                <artifactId>bank-info-api</artifactId>
                <version>${bank-info-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.oss</groupId>
                <artifactId>oss-service-sdk</artifactId>
                <version>${oss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.data</groupId>
                <artifactId>crow-api</artifactId>
                <version>${crow-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.sales</groupId>
                <artifactId>sales-system-poi-api</artifactId>
                <version>${sales-system-poi-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.wosai.middleware</groupId>
                        <artifactId>jsonrpc4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.wosai.sales</groupId>
                        <artifactId>sales-system-gateway-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>crm-databus</artifactId>
                        <groupId>com.wosai.sales</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.sales</groupId>
                <artifactId>merchant-business-open-api</artifactId>
                <version>${merchant-business-open-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.trade</groupId>
                <artifactId>manage-api</artifactId>
                <version>${trade-manage-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.wosai</groupId>
                        <artifactId>sales-system-databus</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.upay</groupId>
                <artifactId>upay-transaction-api</artifactId>
                <version>${upay-transaction-api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.marketing.prepaid</groupId>
                <artifactId>marketing-saas-prepaid-card-api</artifactId>
                <version>${marketing-saas-prepaid-card-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shouqianba.workflow</groupId>
                <artifactId>sp-workflow-api</artifactId>
                <version>${sp-workflow-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-annotation</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-extension</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>${commons-csv.version}</version>
            </dependency>
            <dependency>
                <groupId>xml-security</groupId>
                <artifactId>xmlsec</artifactId>
                <version>${xmlsec.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpmime.version}</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>
            <!-- 中信银行依赖类库 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov-jdk15on.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bcpkix-jdk15on.version}</version>
            </dependency>
            <!-- 飞书依赖类库 -->
            <dependency>
                <groupId>com.larksuite.oapi</groupId>
                <artifactId>oapi-sdk</artifactId>
                <version>${oapi-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shouqianba</groupId>
                <artifactId>cua-common</artifactId>
                <version>${cua-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${fastxml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shouqianba</groupId>
                <artifactId>merchant-contract-access-api</artifactId>
                <version>1.6.7</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.sales</groupId>
                <artifactId>sales-system-service-api</artifactId>
                <version>${sales-system-service-api.version}</version>
            </dependency>
            <!-- 图片压缩 -->
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbanator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wosai.shorturl</groupId>
                <artifactId>shorturl-api</artifactId>
                <version>${shorturl-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>profit-sharing-proxy-api</artifactId>
                <version>${profit-sharing-proxy-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.wosai.shorturl</groupId>
                        <artifactId>shorturl-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>wosai-database-instrumentation-springboot2</artifactId>
                <version>${wosai-database-instrumentation-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>vault-sdk</artifactId>
                <version>${vault-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>shouqianba-tools-service-api</artifactId>
                <version>${shouqianba-tools-service-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.sales</groupId>
                <artifactId>merchant-business-open-api</artifactId>
                <version>1.99.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.wosai.middleware</groupId>
                        <artifactId>jsonrpc4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.sales</groupId>
                <artifactId>sales-system-gateway-api</artifactId>
                <version>${sales-system-gateway-api.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>
</project>